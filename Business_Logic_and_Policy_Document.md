# Business Logic and Policy Document
## Member Services and Customer Support Reference Guide

### Document Purpose
This document provides call center managers, customer service representatives, and support staff with clear business rules and policies for member enrollment, product changes, payments, and account management. It translates system requirements into customer-friendly explanations and actionable guidance.

---

## 1. MEMBER ENROLLMENT POLICIES

### 1.1 Eligibility Requirements

**Basic Enrollment Criteria:**
- Valid group code is required for all enrollments
- Group must be active and properly configured
- Member must provide complete personal information including:
  - Full legal name (first and last name required)
  - Valid birth date (cannot be today or future date)
  - Complete benefit address including valid state and ZIP code
  - Valid email address (maximum 320 characters)

**Geographic Restrictions:**
- Members can only enroll in states where the company is licensed to sell products
- Special restrictions apply for insurance states (WV, IA) - Compass360 enrollments are restricted
- Benefit address must be in a valid state with proper state code
- ZIP code is mandatory for United States addresses

### 1.2 Effective Date Rules

**Standard Effective Date Policy:**
- Effective dates can be up to 3 months in the past or 3 months in the future from current date
- For B2B Division groups: Effective date must be the first day of the month
- Cannot set effective dates beyond the 3-month window without special approval

**Customer Explanation:**
"Your membership effective date determines when your coverage begins. We can backdate coverage up to 3 months or set a future start date up to 3 months ahead. For certain business groups, coverage must start on the first day of a month."

---

## 2. PRODUCT AND COVERAGE RULES

### 2.1 Product Types and Family Coverage

**Single vs. Family Products:**
- **Single Products**: Cover only the primary member
- **Family Products**: Can include spouse and dependents
- Emergency products (EM, EMG, EMP) have special rules and may allow dependents even on single products

**Product Change Restrictions:**
- Members with single products cannot add spouse or dependents
- To add family members, member must first upgrade to a family product
- Product changes may require different effective date rules based on division

### 2.2 Dependent Coverage Rules

**Age Limits by Geographic Region:**
- **US Standard**: 26 years maximum age
- **International (Non-US)**: 23 years maximum age
- **Wyoming Sales Channels**: No age limit (unlimited dependent coverage)
- **US Virgin Islands**: 26 years maximum age
- Age is calculated from dependent's birth date to membership effective date
- Dependents aging out must be removed from coverage

**Dependent Restrictions by Group Type:**
- **TRS and Precoa groups**: Dependents are not allowed
- **Single products**: Cannot have dependents (except emergency products EM, EMG, EMP)
- **Emergency products**: May allow dependents even on single coverage
- **Specific group codes**: PRECM group has special restrictions

**Customer Explanation:**
"Dependent coverage varies by your location and plan type. Most US plans cover dependents up to age 26, while international plans cover up to age 23. Wyoming residents have unlimited dependent age coverage. Some membership types don't allow dependents, and we'll let you know if your specific plan has any restrictions."

---

## 3. PAYMENT PROCESSING POLICIES

### 3.1 Accepted Payment Methods

**Credit Card Payments:**
- **Accepted Card Types**: VISA, MasterCard (MAST), American Express (AMER), Discover (DISC), Diners Club (DINE), JCB
- Card must have valid expiration date (future date)
- Card number validation performed
- Credit card processing available for all divisions

**ACH/Bank Payments:**
- Valid bank routing number required
- Bank account number required
- Bank name required for processing
- Electronic Funds Transfer (EFT) processing available

**Other Payment Types:**
- Check payments accepted
- Money orders accepted
- Cash payments (limited scenarios)

**Payment Timing:**
- **Monthly (MN)**: Standard monthly billing cycle
- **Yearly (YR)**: Annual payment option
- **Special Products**: Some products have 20-month payment cycles
- Payment frequency must match product configuration
- Setup fees can be paid one-time or monthly based on member preference

### 3.2 Payment Amount Rules

**Commission and Fee Processing:**
- Different commission rules apply based on division and sales channel
- International division has specific commission calculation methods
- Upgrade products have different commission structures
- Maximum payment amounts enforced to prevent overpayment

**Customer Explanation:**
"We accept credit cards and bank account payments. You can choose to pay monthly or annually, and any setup fees can be paid upfront or spread over monthly payments."

---

## 4. MEMBER STATUS MANAGEMENT

### 4.1 Cancellation Policies

**Cancellation Date Restrictions:**
- **General Rule**: Cancellation date cannot be more than 3 months in past or future from current date
- **New Member Restriction**: Cancellation date cannot be more than 30 days past member effective date for recently enrolled members
- **Effective Date Dependency**: Cancellation effective date cannot be before member's original effective date
- Valid cancellation reason code required (positive integer)

**Cancellation Process:**
1. Verify member identity and account details
2. Document cancellation reason (reason code required - cannot be 0 or negative)
3. Set appropriate cancellation date within allowed timeframes
4. Add detailed notes explaining circumstances
5. Process cancellation in system
6. Handle commission chargeback if applicable

**Default Cancellation Settings:**
- If no cancellation date provided, defaults to current date
- If no cancellation reason provided, defaults to reason code 3 (opt out)
- All cancellations require proper user authorization

**Customer Explanation:**
"We can process your cancellation request. We'll need to document the reason for cancellation and set an appropriate cancellation date. For new memberships, cancellation dates are limited to within 30 days of your start date. For existing memberships, we can set cancellation dates up to 3 months in the past or future."

### 4.2 Suspension and Reactivation

**Suspension Rules:**
- Temporary suspension available with reactivation date
- Suspension reason code required (positive integer)
- Detailed notes must be added to member record
- Reactivation date must be set at time of suspension
- Suspension date follows same 3-month rule as cancellations

**Reactivation Process:**
- Members can be reactivated if within policy guidelines
- **Reactivation Date Rules**: Must follow 3-month effective date window
- **Renew Date Adjustment**: For suspended members, renew date is extended by suspension period
- **Cancelled Member Reactivation**: Renew date is reset to reactivation date
- Payment and product information may need updating
- Coverage lapse tracking may be initiated for compliance

**Member Status Transitions:**
- **Active → Suspended**: Temporary hold with planned reactivation
- **Active → Cancelled**: Permanent termination
- **Suspended → Active**: Reactivation with date adjustments
- **Cancelled → Active**: Full reinstatement process required

---

## 5. ADDRESS AND CONTACT INFORMATION

### 5.1 Address Requirements

**Benefit Address (Primary):**
- Complete street address required
- City and state required
- Valid state code for the country
- ZIP code required for US addresses
- Country code required

**Mailing Address (Optional):**
- If mailing city is provided, complete mailing address is required
- Must have valid state code if provided
- Can be different from benefit address

### 5.2 Contact Information Standards

**Email Requirements:**
- Valid email format required
- Maximum 320 characters
- Used for important account communications

**Phone Numbers:**
- Multiple phone types accepted (cell, work, home)
- Proper formatting encouraged for better communication

---

## 6. AGENT AND SALES CHANNEL REQUIREMENTS

### 6.1 Agent Setup Requirements

**Agent Validation:**
- Agent must be properly set up for the specific group
- Agent commission structure must be configured
- Agent hierarchy must be established for the product
- Valid agent number required

**Sales Channel Restrictions:**
- Members can only enroll through authorized sales channels
- Some sales channels have specific product restrictions
- Geographic limitations may apply to certain sales channels

**Customer Explanation:**
"Your enrollment must be processed through an authorized agent who is set up to sell products in your area. If there are any issues with agent authorization, we'll help connect you with a qualified representative."

---

## 7. DATA QUALITY AND VALIDATION STANDARDS

### 7.1 Name and Personal Information

**Name Requirements:**
- First name: minimum 2 characters, maximum length enforced
- Last name: minimum 2 characters, maximum length enforced
- Middle initial: optional, single character
- Spouse information: same requirements if spouse coverage requested

**Birth Date Validation:**
- Must be valid calendar date
- Cannot be today's date or future date
- Used for age calculations and eligibility determination

### 7.2 Employer Information

**Employer/Funeral Home:**
- Maximum 150 characters
- Used for group identification and billing purposes
- Required for certain group types

---

## 8. ERROR HANDLING AND CUSTOMER COMMUNICATION

### 8.1 Common Error Scenarios and Responses

**Invalid Group Code:**
- **System Message**: "Invalid group code" or "Group inactive" or "Group not found or inactive"
- **Customer Response**: "I'm sorry, but the group code you provided is not valid or the group is currently inactive. Let me verify the correct group information with you."

**Product Not Available:**
- **System Message**: "Cannot find product with payment type(MN/YR) in group"
- **Customer Response**: "The product you're requesting is not available with your selected payment frequency for this group. Let me show you the available options."

**Effective Date Issues:**
- **System Message**: "Effective date cannot be more than 3 months past or 3 months in future"
- **Customer Response**: "The effective date you've requested is outside our allowable range. We can set your coverage to start between [date range]. What date would work best for you?"
- **B2B Groups**: "For business groups, your effective date must be the first day of the month."

**Dependent Age Issues:**
- **System Message**: "Dependent age exceeds limit [X]" (where X is the specific age limit)
- **Customer Response**: "Your dependent is over the age limit for coverage under this plan. The age limit for your specific plan is [X] years. We can discuss alternative coverage options."

**Agent Setup Issues:**
- **System Message**: "Agent not setup for group" or "Agent commission not setup for product"
- **Customer Response**: "There's an issue with the agent authorization for this enrollment. Let me connect you with a supervisor to resolve this agent setup issue."

**Multiple Member Issues:**
- **System Message**: "Multiple active members found for employee id in group"
- **Customer Response**: "We found multiple active memberships with this employee ID. Let me verify your personal information to identify the correct account."

**Birth Date Validation:**
- **System Message**: "Birth date cannot be today or future date"
- **Customer Response**: "The birth date cannot be today's date or a future date. Please provide the correct birth date."

**Address Validation:**
- **System Message**: "Benefit state code invalid" or "Valid benefit country code required"
- **Customer Response**: "The state or country code you provided is not valid. Let me help you with the correct address information."

**Single Product with Dependents:**
- **System Message**: "The chosen product frequency is single and cannot have spouse/dependents!"
- **Customer Response**: "The product you've selected is for single coverage only and cannot include dependents. You would need to upgrade to a family product to add dependents."

**Dependent Restrictions:**
- **System Message**: "Dependents not allowed for TRS & Precoa groups"
- **Customer Response**: "Your group type does not allow dependent coverage. This is a single-member-only plan."

### 8.2 Escalation Procedures

**When to Escalate:**
- Multiple system validation errors that cannot be resolved
- Customer requests outside standard policy parameters
- Technical issues preventing enrollment completion
- Requests for policy exceptions or special accommodations

**Escalation Process:**
1. Document all attempted solutions
2. Gather complete customer information and request details
3. Note specific error messages or system responses
4. Transfer to supervisor with full context
5. Follow up to ensure resolution

---

## 9. REGULATORY COMPLIANCE

### 9.1 State-Specific Requirements

**Insurance State Restrictions:**
- Certain states (WV, IA) have special enrollment restrictions
- Only authorized users can process enrollments in restricted states
- Additional documentation may be required

**Licensing Compliance:**
- Products can only be sold in states where company is licensed
- State-specific benefit requirements must be met
- Proper state codes must be used for all addresses

### 9.2 Documentation Requirements

**Record Keeping:**
- All customer interactions must be documented
- Changes to member records require proper authorization
- Payment processing must follow compliance guidelines
- Cancellation and suspension reasons must be recorded

---

## 10. MEMBER RELATIONSHIPS AND DATA IMPORT RULES

### 10.1 Member Relationship Types

**Relationship Codes:**
- **P (Primary)**: Main member/policyholder
- **S (Spouse)**: Spouse of primary member
- **D (Dependent)**: Child or other dependent

**Relationship-Specific Rules:**
- **Primary Members (P)**: Must have complete address information for non-cancellation actions
- **Spouse Members (S)**: Birth date validation required, subject to dependent age limits in some cases
- **Dependent Members (D)**: Birth date validation required, subject to age limits based on geography
- **Product Type PLS**: Only allows Primary (P) relationship - dependents not applicable

**File Processing Rules:**
- **Add Action**: Requires valid product type (EMGR/EM/EMP/PL), payment code (MN/YR), and complete information
- **Update Action**: Can convert to Add if member not found in system
- **Cancel Action**: Requires cancel date, does not require product type or payment code
- **Effective Date for Primary**: Must be first day of month for certain processing scenarios

### 10.2 Data Import and File Processing

**Required Fields by Action:**
- **All Actions**: Employee ID/Contract Number, Modification type, Relationship, First Name, Last Name
- **Add/Update Only**: Product Type, Payment Code (MN/YR)
- **Cancel Only**: Cancel Date
- **Primary Members**: Benefit address, city, state, ZIP code

**File Validation Rules:**
- **Group Code Validation**: Must be valid and active before processing
- **Agent Setup**: Agent must be set up for group, have commission setup, and hierarchy setup for product
- **Multiple Member Handling**: System checks for duplicate employee IDs and handles accordingly
- **Address Requirements**: If any address field provided, all required address fields must be complete

**Error Handling for File Processing:**
- **EGROUPINVALID**: Invalid group code
- **EAGENTNOTSETUPGRP**: Agent not setup for group
- **EAGENTCOMMNOTSETUP**: Agent commission not setup for product
- **EAGENTHIERNOTSETUP**: Agent hierarchy not setup for product
- **EMULTIPLEMEMBERFOUNDADD**: Multiple members found for employee ID during add operation

## 11. USER ACCESS AND PERMISSIONS

### 11.1 User Types and Access Levels

**User Type Classifications:**
- **LEGACY (0)**: Original PTS system users
- **EXTERNAL (1)**: TPA (Third Party Administrator) users
- **INTERNAL (2)**: Internal company users

**Access Restrictions by User Type:**
- **External Users**: Must have at least one group code assigned, cannot have sales channel assignments
- **Internal Users**: Cannot have group assignments, may have sales channel assignments
- **Role Requirements**: All users must have at least one role assigned

**Security Levels and Authorities:**
- **Level 5**: PTS_ROLE_ADMIN (Administrator access)
- **Level 4**: PTS_ROLE_CLAIM (Claims processing)
- **Level 3**: PTS_ROLE_SERVICE (Customer service)
- **Level 2**: PTS_ROLE_SALE (Sales operations)
- **Level 1**: PTS_ROLE_AGENT (Agent access)

### 11.2 Invoice and Data Access Rules

**Invoice Access Validation:**
- **External Users**: Can only access invoices they have specific permission for
- **Internal Users**: Have broader invoice access based on their role
- **Access Code Validation**: Users must have specific access codes for certain operations

**Customer Service Implications:**
- Staff access levels determine what member information they can view and modify
- External users (TPA staff) have limited access to specific groups only
- Internal staff have broader access but still subject to role-based restrictions

## 12. ADDITIONAL BUSINESS RULES AND SPECIAL SCENARIOS

### 12.1 Multiple Membership and State Restrictions

**Multiple Membership Rules:**
- **General Rule**: Members typically cannot have multiple active memberships
- **Special Sales Channels**: Some sales channels allow new memberships even with existing memberships in other channels
- **Configuration-Based**: Specific sales channels are configured to allow multiple memberships
- **Group Validation**: System checks if group allows multiple memberships before enrollment

**State Exclusion Rules:**
- **Configurable Exclusions**: Certain states can be excluded from member additions (e.g., WA state)
- **Insurance State Restrictions**: WV and IA have special Compass360 enrollment restrictions
- **User Authorization**: Only specific authorized users can process enrollments in restricted states
- **Dynamic Configuration**: State restrictions can be updated through system configuration

### 12.2 Cancelled Member Update Restrictions

**Update Limitations for Cancelled Members:**
- **Spouse Information**: Cannot be modified for cancelled members
- **Dependent Information**: Cannot be updated for cancelled members
- **Exception**: Updates allowed only if spouse and dependent information remains exactly the same
- **Validation**: System validates that no changes are made to family member information

**Customer Service Response:**
"I see this member has been cancelled. We can update basic member information, but we cannot make changes to spouse or dependent information for cancelled memberships. If you need to make family changes, we would need to reactivate the membership first."

### 12.3 Employee ID and Member Identification

**Employee ID Validation:**
- Employee IDs can be up to 9 characters with leading zero padding
- System automatically attempts to add leading zeros if member not found
- Multiple members with same employee ID require additional verification
- Employee ID must be unique within each group

**Member Lookup Rules:**
- Primary lookup by employee ID within group
- Secondary verification by name and date of birth
- Multiple active members trigger special handling procedures

### 12.4 Product Upgrade and Change Rules

**Product Change Validation:**
- Product must be valid for the specific group
- Payment frequency must match product configuration
- Upgrade products have different commission structures
- Product changes may require effective date on first of month (B2B division)

**Special Product Rules:**
- **20-Month Products**: Specific products (IDs 182, 268) have 20-month payment cycles
- **Installment Products**: Special commission calculations apply
- **Upgrade Products**: Different validation and pricing rules

### 12.5 Commission and Payment Processing

**Commission Rules:**
- Different commission structures by division and sales channel
- Flat rate vs. percentage-based commission options
- Advance commission calculations for new sales
- Chargeback handling for cancellations

**Payment Processing Limits:**
- Maximum payment amounts enforced to prevent overpayment
- Product amount cannot be zero for override fee scenarios
- Payment validation includes merchant ID verification
- Specific merchant IDs: 107625, 108443, 138483, 168555, 253748

### 12.6 Geographic and Regulatory Restrictions

**State-Specific Rules:**
- Certain states excluded from member additions (configurable)
- Insurance states (WV, IA) have Compass360 enrollment restrictions
- Only authorized users can process enrollments in restricted states
- Country-specific dependent age limits apply

**Division-Specific Processing:**
- **B2B Division (ID: 3)**: First-of-month effective dates required
- **B2C Division (ID: 4)**: Standard processing rules
- **International Division (ID: 1)**: Special commission and age limit rules
- **Retention Division (ID: 9)**: Existing member retention processing

### 12.7 Data Validation and Quality Standards

**Name Field Requirements:**
- First and last names: minimum 2 characters each
- Spouse names: same 2-character minimum requirement
- Middle initial: optional, single character
- Maximum length limits enforced for all name fields

**Address Validation:**
- Benefit address required for all members
- Mailing address optional but must be complete if city provided
- State codes must be valid for the specified country
- ZIP codes required for United States addresses
- Country codes required and must be valid

**Date Validation:**
- Birth dates cannot be current date or future dates
- Effective dates limited to 3-month window (past or future)
- Cancel dates follow same 3-month rule with additional restrictions
- Date format: MM/dd/yyyy standard

---

## 13. FREQUENTLY ASKED QUESTIONS

### 13.1 Enrollment Questions

**Q: Can I add my spouse later if I start with single coverage?**
A: You would need to upgrade to a family product first, then add your spouse. This may involve a product change process and could affect your premium.

**Q: Why can't I choose any effective date I want?**
A: Effective dates are limited to ensure proper coverage and billing cycles. We can set your coverage to start up to 3 months in the past or future from today's date. For business groups, the effective date must be the first day of the month.

**Q: What if my dependent turns 26?**
A: Dependents must be removed from coverage when they reach the age limit. The age limit varies by location: 26 for US standard plans, 23 for international plans, and no limit for Wyoming residents.

**Q: Why can't I add dependents to my plan?**
A: This depends on your product type and group. Single products cannot have dependents (except emergency products), and some groups like TRS and Precoa don't allow dependents at all.

**Q: What's the difference between suspension and cancellation?**
A: Suspension is temporary with a planned reactivation date, while cancellation is permanent. Suspended members can be reactivated with adjusted renewal dates.

### 13.2 Payment Questions

**Q: Can I change my payment method after enrollment?**
A: Yes, you can update your payment method. We accept VISA, MasterCard, American Express, Discover, Diners Club, JCB, ACH/bank transfers, checks, and money orders.

**Q: What happens if my payment fails?**
A: We'll attempt to process the payment again and notify you of any issues. You'll have an opportunity to update your payment information to avoid any coverage interruption.

**Q: Can I pay annually instead of monthly?**
A: Yes, most products offer both monthly (MN) and yearly (YR) payment options. Some special products have 20-month payment cycles.

### 13.3 Geographic and Special Situations

**Q: I live in Wyoming - are there different rules for me?**
A: Yes, Wyoming residents have unlimited dependent age coverage, unlike other states which have age limits.

**Q: What if I'm an international member?**
A: International members (non-US) have a dependent age limit of 23 years and may have different commission and processing rules.

**Q: Why can't I enroll in certain states?**
A: Some states have special restrictions. For example, West Virginia and Iowa have limitations on Compass360 enrollments, and only authorized users can process these enrollments.

---

## 14. QUICK REFERENCE GUIDES

### 14.1 Division Types and Rules
- **B2B Division (ID: 3)**: Business-to-business, first-of-month effective dates required
- **B2C Division (ID: 4)**: Business-to-consumer, standard processing rules
- **International Division (ID: 1)**: 23-year dependent age limit, special commission rules
- **Retention Division (ID: 9)**: Existing member retention processing

### 14.2 Product Codes and Types
- **EM, EMG, EMP**: Emergency products with special dependent rules
- **PL**: Standard product line
- **PLS**: Single-only product type, no dependents allowed
- **Single (Type 1)**: Primary member only, no dependents allowed
- **Family (Type 2)**: Can include spouse and dependents
- **Upgrade Products**: Different commission and validation rules

### 14.3 Payment Types and Methods
- **MN**: Monthly payment frequency
- **YR**: Yearly payment frequency
- **20-Month**: Special products with 20-month cycles
- **CREDITCARD**: Credit card payments (VISA, MAST, AMER, DISC, DINE, JCB)
- **ACH**: Bank account transfers
- **EFT**: Electronic funds transfer
- **CHECK**: Check payments
- **MONEYORDER**: Money order payments
- **CASH**: Cash payments (limited scenarios)

### 14.4 Member Relationship Codes
- **P (Primary)**: Main member/policyholder, requires complete address
- **S (Spouse)**: Spouse of primary member, birth date validation required
- **D (Dependent)**: Child or other dependent, subject to age limits

### 14.5 User Types and Security Levels
- **LEGACY (0)**: Original PTS system users
- **EXTERNAL (1)**: TPA users, group-restricted access
- **INTERNAL (2)**: Internal users, sales channel access
- **Security Levels**: 1 (Agent) to 5 (Admin)

### 14.6 Geographic Age Limits
- **US Standard**: 26 years maximum dependent age
- **International**: 23 years maximum dependent age
- **Wyoming**: No age limit (unlimited dependent coverage)
- **US Virgin Islands**: 26 years maximum dependent age

### 14.7 Critical Timeframes
- **Effective Date Window**: 3 months past or future from current date
- **Cancellation Window**: 3 months past or future, with 30-day restriction for new members
- **B2B Effective Dates**: Must be first day of month
- **Name Minimums**: 2 characters for first and last names
- **Email Maximum**: 320 characters
- **Employer Field**: 150 characters maximum

### 14.8 Special Group Codes and Restrictions
- **PRECM**: Precoa group with special restrictions
- **TRS Groups**: No dependents allowed
- **Precoa Groups**: No dependents allowed
- **Multiple Membership Groups**: Some sales channels allow multiple active memberships

### 14.9 Restricted States and Special Processing
- **WV, IA**: Insurance states with Compass360 enrollment restrictions
- **WA**: Configurable exclusion from member additions
- **Authorization Required**: Only specific users can process restricted state enrollments
- **Dynamic Configuration**: State restrictions can be updated through system settings

---

*This document should be used in conjunction with system training and regular policy updates. For questions not covered in this guide, please escalate to your supervisor or the appropriate department.*
