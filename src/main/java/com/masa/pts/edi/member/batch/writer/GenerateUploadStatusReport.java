package com.masa.pts.edi.member.batch.writer;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.StepExecutionListener;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;

import com.masa.pts.core.domain.MemberFile;
import com.masa.pts.core.domain.MemberFileExcelDocument;
import com.masa.pts.core.model.MemberFileDTO;
import com.masa.pts.core.service.ExcelExportUtilityService;
import com.masa.pts.core.service.MemberFileService;
import com.masa.pts.edi.member.batch.configuration.BatchProcessResults;
import com.masa.pts.edi.member.report.service.EDIFileProcessReportService;

public class GenerateUploadStatusReport implements Tasklet, StepExecutionListener {

	private static final Logger log = LoggerFactory.getLogger(GenerateUploadStatusReport.class);
	
	BatchProcessResults batchProcessResults;
	EDIFileProcessReportService ediFileProcessReportService;
	File inboundOutDirectory;
	ExcelExportUtilityService excelExportUtilityService;
	MemberFileService memberFileService;
	
	public GenerateUploadStatusReport(BatchProcessResults batchProcessResults,EDIFileProcessReportService ediFileProcessReportService, File inboundOutDirectory
			, ExcelExportUtilityService excelExportUtilityService, MemberFileService memberFileService) {
		this.batchProcessResults = batchProcessResults;
		this.ediFileProcessReportService = ediFileProcessReportService;
		this.inboundOutDirectory = inboundOutDirectory;
		this.excelExportUtilityService = excelExportUtilityService;
		this.memberFileService = memberFileService;
	}

	@Override
	public void beforeStep(StepExecution stepExecution) {
		log.info("Report generation before step Batchprocess results {}",batchProcessResults);
	}

	@Override
	public ExitStatus afterStep(StepExecution stepExecution) {
		log.info("Clearing batch process results after generating report step");
		this.batchProcessResults.clear();
		log.info("Report generation after step Batchprocess results {}",batchProcessResults);
		return ExitStatus.COMPLETED;
	}

	@Override
	public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {
		Map<String, String> jobParamsMap = new HashMap<>();
		DateTimeFormatter dateTimeFormat = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
		String dateTimeOfFile = LocalDateTime.now().format(dateTimeFormat);
		File outputFile = generateOutputFileName(inboundOutDirectory.getAbsolutePath(), ".pdf", dateTimeOfFile,batchProcessResults.getGroupCode());

		List<MemberFile> memberFileDataList = 
				memberFileService.getAllRecordsForThisRun(batchProcessResults.getFileName(),batchProcessResults.getJobExecutionId());
		
		this.ediFileProcessReportService.generatePDFReport(memberFileDataList, "EDI File Upload Processing Report", outputFile, jobParamsMap);
		
		List<MemberFileDTO> memberFileDTOList = memberFileDataList.stream().map(item-> new MemberFileDTO(item)).collect(Collectors.toList());
		byte[] file = excelExportUtilityService.exportToExcel("EDIStatus", new MemberFileExcelDocument(memberFileDTOList));
		File excelFile = generateOutputFileName(inboundOutDirectory.getAbsolutePath(), ".xls", dateTimeOfFile,batchProcessResults.getGroupCode());
		
		try(FileOutputStream fos =new FileOutputStream(excelFile))
		{
			fos.write(file);
		}		
		return RepeatStatus.FINISHED;
	}

	private File generateOutputFileName(String outputFolder, String extn, String dateTimeOfFile,String groupCode) {

		DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern("yyyyMMdd");
		String dateTimeOfFolder = LocalDate.now().format(dateFormat);
		
		File outputFileFolder = new File(outputFolder + File.separator + dateTimeOfFolder + File.separator + groupCode + "_" + dateTimeOfFile);
		outputFileFolder.mkdirs();

		File outputFile = new File(outputFileFolder + File.separator + groupCode + "_" + dateTimeOfFile + extn);
		try {
			outputFile.createNewFile();
			log.info("Output File Name::: [{}]", outputFile.getCanonicalPath());
		} catch (IOException e) {
			log.error("Exceptio occurred while creating output file name, Messsage: [" + e.getMessage() + "] Cause [" + e.getCause() + "]", e);
		}
		return outputFile;
	}
}
