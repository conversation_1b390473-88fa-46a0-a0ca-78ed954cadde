package com.masa.pts.edi.member.batch.processor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.validator.ValidatingItemProcessor;

import com.masa.pts.core.domain.MemberFile;

public class DuplicateMemberItemProcessor extends ValidatingItemProcessor<MemberFile> {
	private static final Logger log = LoggerFactory.getLogger(DuplicateMemberItemProcessor.class);


	@Override
	public MemberFile process(MemberFile input)  {
		log.info("In DuplicateMemberItemProcessor Item Processor , Process method");
		
		input = super.process(input);

		if (input == null) {
			log.info("In process method , MemberFile input after validation is NULL");
			return null;
		}
		
		return input;
	}
}
