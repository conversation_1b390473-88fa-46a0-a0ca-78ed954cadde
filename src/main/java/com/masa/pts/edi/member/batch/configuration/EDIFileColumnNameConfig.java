package com.masa.pts.edi.member.batch.configuration;

import java.util.ArrayList;
import java.util.List;

import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties(prefix = "pts.edifile.columns")
public class EDIFileColumnNameConfig {
	
	private List<String> contractNumber = new ArrayList<>();
	private List<String> modification = new ArrayList<>();
	private List<String> payment = new ArrayList<>();
	private List<String> productType = new ArrayList<>();
	private List<String> effectiveDate = new ArrayList<>();
	private List<String> cancelDate = new ArrayList<>();
	private List<String> relationship = new ArrayList<>();
	private List<String> firstName = new ArrayList<>();
	private List<String> middleName = new ArrayList<>();
	private List<String> lastName = new ArrayList<>();
	private List<String> birthDate = new ArrayList<>();
	private List<String> benefitAddress = new ArrayList<>();
	private List<String> benefitCity = new ArrayList<>();
	private List<String> benefitState = new ArrayList<>();
	private List<String> benefitZip = new ArrayList<>();
	private List<String> mailingAddress = new ArrayList<>();
	private List<String> mailingCity = new ArrayList<>();
	private List<String> mailingState = new ArrayList<>();
	private List<String> mailingZip = new ArrayList<>();
	private List<String> email = new ArrayList<>();
	private List<String> phone = new ArrayList<>();
	public EDIFileColumnNameConfig() {
		super();
	}
	public List<String> getContractNumber() {
		return contractNumber;
	}
	public void setContractNumber(List<String> contractNumber) {
		this.contractNumber = contractNumber;
	}
	public List<String> getModification() {
		return modification;
	}
	public void setModification(List<String> modification) {
		this.modification = modification;
	}
	public List<String> getPayment() {
		return payment;
	}
	public void setPayment(List<String> payment) {
		this.payment = payment;
	}
	public List<String> getProductType() {
		return productType;
	}
	public void setProductType(List<String> productType) {
		this.productType = productType;
	}
	public List<String> getEffectiveDate() {
		return effectiveDate;
	}
	public void setEffectiveDate(List<String> effectiveDate) {
		this.effectiveDate = effectiveDate;
	}
	public List<String> getCancelDate() {
		return cancelDate;
	}
	public void setCancelDate(List<String> cancelDate) {
		this.cancelDate = cancelDate;
	}
	public List<String> getRelationship() {
		return relationship;
	}
	public void setRelationship(List<String> relationship) {
		this.relationship = relationship;
	}
	public List<String> getFirstName() {
		return firstName;
	}
	public void setFirstName(List<String> firstName) {
		this.firstName = firstName;
	}
	public List<String> getMiddleName() {
		return middleName;
	}
	public void setMiddleName(List<String> middleName) {
		this.middleName = middleName;
	}
	public List<String> getLastName() {
		return lastName;
	}
	public void setLastName(List<String> lastName) {
		this.lastName = lastName;
	}
	public List<String> getBirthDate() {
		return birthDate;
	}
	public void setBirthDate(List<String> birthDate) {
		this.birthDate = birthDate;
	}
	public List<String> getBenefitAddress() {
		return benefitAddress;
	}
	public void setBenefitAddress(List<String> benefitAddress) {
		this.benefitAddress = benefitAddress;
	}
	public List<String> getBenefitCity() {
		return benefitCity;
	}
	public void setBenefitCity(List<String> benefitCity) {
		this.benefitCity = benefitCity;
	}
	public List<String> getBenefitState() {
		return benefitState;
	}
	public void setBenefitState(List<String> benefitState) {
		this.benefitState = benefitState;
	}
	public List<String> getBenefitZip() {
		return benefitZip;
	}
	public void setBenefitZip(List<String> benefitZip) {
		this.benefitZip = benefitZip;
	}
	public List<String> getMailingAddress() {
		return mailingAddress;
	}
	public void setMailingAddress(List<String> mailingAddress) {
		this.mailingAddress = mailingAddress;
	}
	public List<String> getMailingCity() {
		return mailingCity;
	}
	public void setMailingCity(List<String> mailingCity) {
		this.mailingCity = mailingCity;
	}
	public List<String> getMailingState() {
		return mailingState;
	}
	public void setMailingState(List<String> mailingState) {
		this.mailingState = mailingState;
	}
	public List<String> getMailingZip() {
		return mailingZip;
	}
	public void setMailingZip(List<String> mailingZip) {
		this.mailingZip = mailingZip;
	}
	public List<String> getEmail() {
		return email;
	}
	public void setEmail(List<String> email) {
		this.email = email;
	}
	public List<String> getPhone() {
		return phone;
	}
	public void setPhone(List<String> phone) {
		this.phone = phone;
	}
}
