package com.masa.pts.edi.member.batch.configuration;

import java.io.IOException;
import java.io.Writer;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.file.FlatFileHeaderCallback;

public class MemberFileErrorHeaderCallback implements FlatFileHeaderCallback {

	private static final Logger log = LoggerFactory.getLogger(MemberFileErrorHeaderCallback.class);

	@Override
	public void writeHeader(Writer writer) throws IOException {
		log.info("In Member File Error Header Callback , writing header ...");
		writer.write("Contract Number,Modification (Refresh/Update/Add/Cancel),Payment,Product Type,Effective Date,Cancel Date,Member Type (P/S/D),"
				+ "First Name,Middle Name,Last Name,Birth Date,Benefit Address,Benefit City,Benefit State,Benefit Zip,Mailing Address,Mailing City,"
				+ "Mailing State,Mailing Zip,Email,Phone,ProcessErrors");
	}
}
