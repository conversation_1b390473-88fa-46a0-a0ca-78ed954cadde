package com.masa.pts.edi.member.sci.batch.configuration;

import java.beans.PropertyEditor;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.validation.ValidationException;

import org.springframework.batch.core.ItemReadListener;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.EnableBatchProcessing;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.step.skip.AlwaysSkipItemSkipPolicy;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemWriter;
import org.springframework.batch.item.file.FlatFileItemReader;
import org.springframework.batch.item.file.FlatFileItemWriter;
import org.springframework.batch.item.file.mapping.BeanWrapperFieldSetMapper;
import org.springframework.batch.item.file.mapping.DefaultLineMapper;
import org.springframework.batch.item.file.transform.DelimitedLineTokenizer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.core.io.FileSystemResource;
import org.springframework.transaction.PlatformTransactionManager;

import com.masa.pts.core.domain.MemberFile;
import com.masa.pts.core.service.AddressService;
import com.masa.pts.core.service.AgentService;
import com.masa.pts.core.service.ExcelExportUtilityService;
import com.masa.pts.core.service.MemberAuditEventService;
import com.masa.pts.core.service.MemberFileService;
import com.masa.pts.core.service.MemberService;
import com.masa.pts.core.service.PTSUserService;
import com.masa.pts.edi.member.batch.configuration.BatchJobParamIncrementer;
import com.masa.pts.edi.member.batch.configuration.BatchProcessResults;
import com.masa.pts.edi.member.batch.configuration.EDIErrorConfig;
import com.masa.pts.edi.member.batch.configuration.MemberFileHeaderHandler;
import com.masa.pts.edi.member.batch.configuration.MemberFileToPTSTasklet;
import com.masa.pts.edi.member.batch.reader.BlankLineRecordSeparatorPolicy;
import com.masa.pts.edi.member.batch.writer.GenerateUploadStatusReport;
import com.masa.pts.edi.member.report.service.EDIFileProcessReportService;
import com.masa.pts.edi.member.sci.batch.processor.SCIFileItemProcessor;
import com.masa.pts.edi.member.sci.batch.reader.SCIFileItemReadListener;
import com.masa.pts.edi.member.sci.batch.validator.SCIFileDataValidator;
import com.masa.pts.edi.member.sci.batch.writer.SCIFileItemWriter;

@Configuration
@EnableBatchProcessing
public class SCIBatchConfiguration {

	@Autowired
	private JobBuilderFactory jobBuilderFactory;

	@Autowired
	private StepBuilderFactory stepBuilderFactory;
	
	@Autowired
	private Environment environment;
	
	@Autowired
	AgentService agentService;
	
	@Autowired
	MemberService memberService;	

	@Autowired
	AddressService addressService;
	
	@Autowired
	PTSUserService ptsUserService;
	
	@Autowired
	PlatformTransactionManager transactionManager;
	
	@Autowired
	MemberAuditEventService memberAuditEventService;
	
	@Autowired
	MemberFileService memberFileService;
	
	@Autowired
	BatchProcessResults batchProcessResults;
	
	@Bean
	protected Job sciMemberFileImport() {
		return jobBuilderFactory.get("sciMemberFileImport")
				.incrementer(new BatchJobParamIncrementer())
				.listener(new SCIMemberBatchListener(batchProcessResults))
				.start(sciMemberFileToStagingTable())
				.on("*")
				.to(startSCIStagingTableToPTS())
				.on("*")
				.to(generateSCIReport(null, null,null,null))
				.end()
				.build();
	}
	
	@Bean
	protected Step generateSCIReport(EDIFileProcessReportService ediFileProcessReportService,File inboundOutDirectory,ExcelExportUtilityService excelExportUtilityService
			,MemberFileService memberFileService) {
		return stepBuilderFactory.get("generateSCIReport")
				.allowStartIfComplete(true)
				.tasklet(new GenerateUploadStatusReport(batchProcessResults,ediFileProcessReportService,inboundOutDirectory,excelExportUtilityService,memberFileService))
				.build();
	}
	
	@Bean
	protected Step startSCIStagingTableToPTS() {
		return stepBuilderFactory.get("startSCIStagingTableToPTS")
					.allowStartIfComplete(true)
					.job(sciStagingTableToPTS())
					.build();					
	}
	@Bean
	protected Job sciStagingTableToPTS() {
		return jobBuilderFactory
				.get("sciStagingTableToPTS")
				.incrementer(new BatchJobParamIncrementer())
				.start(processSCIMembersToPTS(null))
				.build();				
	}
	
	@StepScope
	@Bean
	protected Step processSCIMembersToPTS(EDIErrorConfig ediErrorConfig) {
		return stepBuilderFactory.get("processSCIMembersToPTS")
				.allowStartIfComplete(true)				
				.tasklet(memberFileToPTSProcess(ediErrorConfig)).build();
	}
	
	protected MemberFileToPTSTasklet memberFileToPTSProcess(EDIErrorConfig ediErrorConfig) {
		return new MemberFileToPTSTasklet(memberService,addressService,ptsUserService,transactionManager,batchProcessResults,environment,memberAuditEventService
				,memberFileService,agentService,ediErrorConfig);
	}

	private Step sciMemberFileToStagingTable() {
		return stepBuilderFactory.get("sciMemberFileToStagingTable").<MemberFile, MemberFile>chunk(10)
				.reader(sciFileReader(null, null,null))
				.listener(sciFileIteamReaderListener(null, null))
				.processor(sciFileProcessor(null,null,null,null,null,null))
				.writer(sciFileWriterToStagingTable())
				.faultTolerant()
				.skipPolicy(new AlwaysSkipItemSkipPolicy())
				.listener(sciFileSkipListener(null, null, null,null))
				.noRollback(ValidationException.class)
				.noRetry(ValidationException.class)
				.build();
	}
	
	@Bean
	@StepScope
	protected ItemReadListener<MemberFile> sciFileIteamReaderListener(@Value("#{jobParameters['fileNameWithoutPath']}") String fileNameWithoutPath
			,EDIErrorConfig ediErrorConfig
			) {		
		batchProcessResults.setFileName(fileNameWithoutPath);
		return new SCIFileItemReadListener(batchProcessResults,fileNameWithoutPath,ediErrorConfig,memberFileService);		
	}
	
	@Bean
	@StepScope
	protected FlatFileItemReader<MemberFile> sciFileReader(@Value("#{jobParameters['fileName']}") String fileResource,
			@Qualifier("delimitedLineTokenizer") DelimitedLineTokenizer delimitedLineTokenizer
			,@Qualifier("memberFileHeaderHandler") MemberFileHeaderHandler memberFileHeaderHandler			
			) {
		DefaultLineMapper<MemberFile> defaultLineMapper = new DefaultLineMapper<>();
		defaultLineMapper.setLineTokenizer(delimitedLineTokenizer);

		BeanWrapperFieldSetMapper<MemberFile> beanMapper = new BeanWrapperFieldSetMapper<>();
		beanMapper.setTargetType(MemberFile.class);
		Map<Class<java.util.Date>, PropertyEditor> customEditors = new HashMap<>();
		CustomDateEditor customDateEditor = new CustomDateEditor(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"), true);
		customEditors.put(java.util.Date.class, customDateEditor);
		beanMapper.setCustomEditors(customEditors);
		
		SCIFileFieldSetMapper fieldSetMapper = new SCIFileFieldSetMapper();
		defaultLineMapper.setFieldSetMapper(fieldSetMapper);
		defaultLineMapper.afterPropertiesSet();

		FlatFileItemReader<MemberFile> reader = new FlatFileItemReader<>();
		reader.setSkippedLinesCallback(memberFileHeaderHandler);
		reader.setLineMapper(defaultLineMapper);
		reader.setLinesToSkip(1);
		reader.setResource(new FileSystemResource(fileResource));
		reader.setEncoding("UTF-8");
		reader.setRecordSeparatorPolicy(new BlankLineRecordSeparatorPolicy());
		try {
			reader.afterPropertiesSet();
		} catch (Exception e) {
			
		}
		batchProcessResults.setGroupCode("SCI");
		return reader;
	}
	
	@Bean
	@StepScope
	protected ItemProcessor<MemberFile, MemberFile> sciFileProcessor(@Value("#{jobParameters['fileName']}") String fileResource,
			@Value("#{jobParameters['fileNameWithoutPath']}") String fileNameWithoutPath
			,@Value("#{jobParameters['fileType']}") String fileType
			,@Value("#{jobParameters['soldDate']}") Date soldDate
			,AddressService addressService
			,MemberService memberService
			) {
		SCIFileItemProcessor processor = new SCIFileItemProcessor(fileNameWithoutPath,fileType,soldDate
				,memberFileService,memberService);
		SCIFileDataValidator validator = new SCIFileDataValidator(addressService);
		processor.setValidator(validator);
		return processor;
	}
	
	private ItemWriter<? super MemberFile> sciFileWriterToStagingTable() {
		return new SCIFileItemWriter(memberFileService,batchProcessResults); 
	}
	
	@Bean
	@StepScope
	protected SCIFileSkipListener sciFileSkipListener(@Qualifier("skipItemFlatFileStream") FlatFileItemWriter<MemberFile> skipItemFileItemWriter
			,@Value("#{stepExecution.jobExecution}") JobExecution jobExecution
			,EDIErrorConfig ediErrorConfig
			,@Value("#{jobParameters['fileNameWithoutPath']}") String fileNameWithoutPath
			) {
		return new SCIFileSkipListener(memberFileService,jobExecution,ediErrorConfig,batchProcessResults,fileNameWithoutPath);		
	}
}
