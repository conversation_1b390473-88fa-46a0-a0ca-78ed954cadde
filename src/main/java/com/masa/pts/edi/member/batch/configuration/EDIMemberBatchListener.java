package com.masa.pts.edi.member.batch.configuration;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobExecutionListener;

public class EDIMemberBatchListener implements JobExecutionListener {

	private static final Logger log = LoggerFactory.getLogger(EDIMemberBatchListener.class);
	
	BatchProcessResults batchProcessResults;
	
	public EDIMemberBatchListener(BatchProcessResults batchProcessResults) {
		this.batchProcessResults = batchProcessResults;
	}

	@Override
	public void beforeJob(JobExecution jobExecution) {
		log.info("Before Job EDIMemberBatchListener ,Clearing Batch results data");
		this.batchProcessResults.clear();
	}

	@Override
	public void afterJob(JobExecution jobExecution) {
		log.info("After Job EDIMemberBatchListener ,Clearing Batch results data");
		this.batchProcessResults.clear();
	}
}
