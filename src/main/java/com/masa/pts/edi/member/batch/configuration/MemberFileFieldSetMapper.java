package com.masa.pts.edi.member.batch.configuration;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import org.springframework.batch.item.file.mapping.FieldSetMapper;
import org.springframework.batch.item.file.transform.FieldSet;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindException;

import com.masa.pts.core.domain.Constant;
import com.masa.pts.core.domain.MemberFile;

public class MemberFileFieldSetMapper implements FieldSetMapper<MemberFile> {

	EDIFileColumnNameConfig ediConfig;
	
	public MemberFileFieldSetMapper(EDIFileColumnNameConfig ediConfig) {
		this.ediConfig = ediConfig;
	}

	@Override
	public MemberFile mapFieldSet(FieldSet fieldSet) throws BindException {
		
		MemberFile memberFile = new MemberFile();
		
		String contractNumber = "";
		String product = "";
		String relationShip = "";
		String firstName = "";
		String lastName = "";
		String middleName = "";
		String benefitAddressLine1 = "";
		String benefitAddressCity = "";
		String benefitAddressState = "";
		String benefitAddressZip = "";
		String mailingAddressLine1 = "";
		String mailingAddressCity = "";
		String mailingAddressState = "";
		String mailingAddressZip = "";
		String email = "";
		String phone = "";
		String payment= "";
		String action = "";

		Date birthDate;
		Date effectiveDate;
		Date cancelDate;

		contractNumber = readStringColumnFromFile(fieldSet,ediConfig.getContractNumber());		
		action =  readStringColumnFromFile(fieldSet,ediConfig.getModification());   
		payment = readStringColumnFromFile(fieldSet,ediConfig.getPayment()); 
		product = readStringColumnFromFile(fieldSet,ediConfig.getProductType());
		
		effectiveDate = readDateColumnFromFile(fieldSet,ediConfig.getEffectiveDate()); 
		cancelDate = readDateColumnFromFile(fieldSet,ediConfig.getCancelDate());
		
		relationShip = readStringColumnFromFile(fieldSet,ediConfig.getRelationship()); 
		firstName = readStringColumnFromFile(fieldSet,ediConfig.getFirstName());
		middleName = readStringColumnFromFile(fieldSet,ediConfig.getMiddleName());
		lastName = readStringColumnFromFile(fieldSet,ediConfig.getLastName());
		
		birthDate = readDateColumnFromFile(fieldSet,ediConfig.getBirthDate());
		
		benefitAddressLine1 = readStringColumnFromFile(fieldSet,ediConfig.getBenefitAddress()); 
		benefitAddressCity = readStringColumnFromFile(fieldSet,ediConfig.getBenefitCity()); 
		benefitAddressState = readStringColumnFromFile(fieldSet,ediConfig.getBenefitState()); 
		benefitAddressZip = readStringColumnFromFile(fieldSet,ediConfig.getBenefitZip()); 
		mailingAddressLine1 = readStringColumnFromFile(fieldSet,ediConfig.getMailingAddress()); 
		mailingAddressCity = readStringColumnFromFile(fieldSet,ediConfig.getMailingCity()); 
		mailingAddressState = readStringColumnFromFile(fieldSet,ediConfig.getMailingState()); 
		mailingAddressZip = readStringColumnFromFile(fieldSet,ediConfig.getMailingZip());
		email = readStringColumnFromFile(fieldSet,ediConfig.getEmail()); 
		phone = readStringColumnFromFile(fieldSet,ediConfig.getPhone());

		memberFile.setContractNumber(contractNumber);
		memberFile.setModification(action);
		memberFile.setPaymentCode(payment);
		memberFile.setProductType(product);
		memberFile.setEffectiveDate(effectiveDate);
		memberFile.setCancelDate(cancelDate);
		memberFile.setRelationship(relationShip);
		memberFile.setFirstName(firstName);
		memberFile.setMiddleName(middleName);
		memberFile.setLastName(lastName);
		memberFile.setBirthDate(birthDate);
		try {
			SimpleDateFormat sdf = new SimpleDateFormat("MM/dd/yyyy");
			memberFile.setBirthDateStr(sdf.format(birthDate));					
		}
		catch(Exception e) {
			memberFile.setBirthDateStr("");
		}
		memberFile.setBenefitAddress(benefitAddressLine1);
		memberFile.setBenefitCity(benefitAddressCity);
		memberFile.setBenefitState(benefitAddressState);
		memberFile.setBenefitZip(benefitAddressZip);
		memberFile.setMailingAddress(mailingAddressLine1);
		memberFile.setMailingCity(mailingAddressCity);
		memberFile.setMailingState(mailingAddressState);
		memberFile.setMailingZip(mailingAddressZip);
		memberFile.setEmail(email);
		memberFile.setPhone(phone);
		
		memberFile.setCreatedDate(new Date());
		memberFile.setRenewDate(memberFile.getEffectiveDate());
		memberFile.setThirdPartyId(memberFile.getContractNumber());
		memberFile.setEmployeeId(memberFile.getContractNumber());
		
		return memberFile;
	}
	
	public String readStringColumnFromFile(FieldSet fieldSet, List<String> columnNames) {
		String data = "";
		boolean error=true;
		for (String columnName : columnNames) 
		{
			try
			{
				if(error)
				{
					data = fieldSet.readString(columnName);
					data = StringUtils.trimWhitespace(data);
					error=false;
				}
			}
			catch(Exception e) 
			{
				error=true;
			}
		}
		return data;
	}
	public Date readDateColumnFromFile(FieldSet fieldSet, List<String> columnNames) {
		Date data = Constant.DEFULT_DATE_1900;
		boolean error=true;
		for (String columnName : columnNames) 
		{
			try
			{
				if(error)
				{
					data = fieldSet.readDate(columnName,"MM/dd/yyyy",Constant.DEFULT_DATE_1900);
					error=false;
				}
			}
			catch(Exception e) 
			{
				error=true;
			}
		}
		return data;
	}
}
