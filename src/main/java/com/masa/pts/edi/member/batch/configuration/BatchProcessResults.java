package com.masa.pts.edi.member.batch.configuration;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.masa.pts.core.domain.MemberFile;


@Component
@Scope(scopeName = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class BatchProcessResults implements Serializable {

	private static final long serialVersionUID = -7713959105368870161L;
	
	private String fileName;
	private String groupCode;
	private Long jobExecutionId;
	private Set<MemberFile> success = new HashSet<>();
	private Set<MemberFile> processed = new HashSet<>();
	private Set<MemberFile> failed = new HashSet<>();
	private Set<MemberFile> skip = new HashSet<>();
	
	public BatchProcessResults() {
		super();
	}
	
	@Override
	public String toString() {
		return "BatchProcessResults [fileName=" + fileName + ", groupCode=" + groupCode + ", jobExecutionId="
				+ jobExecutionId + ", success=" + success.size() + ", processed=" + processed.size() + ", failed=" + failed.size()
				+ ", skip=" + skip.size() + "]" + this.hashCode();
	}



	public String getFileName() {
		return fileName;
	}
	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getGroupCode() {
		return groupCode;
	}

	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}

	public Set<MemberFile> getSuccess() {
		return success;
	}

	public void setSuccess(Set<MemberFile> success) {
		this.success = success;
	}

	public Set<MemberFile> getFailed() {
		return failed;
	}

	public void setFailed(Set<MemberFile> failed) {
		this.failed = failed;
	}

	public Set<MemberFile> getSkip() {
		return skip;
	}

	public void setSkip(Set<MemberFile> skip) {
		this.skip = skip;
	}
	public void addSuccess(MemberFile item) {
		this.success.add(item);
		this.removeProcessed(item);
	}
	public void addFailure(MemberFile item) {
		this.failed.add(item);		
		this.removeProcessed(item);
	}
	public void addSkip(MemberFile item) {
		this.skip.add(item);
		this.removeProcessed(item);
	}

	public Set<MemberFile> getProcessed() {
		return processed;
	}

	public void setProcessed(Set<MemberFile> processed) {
		this.processed = processed;
	}
	public void addProcessed(MemberFile item) {
		this.processed.add(item);
	}
	public void removeProcessed(MemberFile item) {		
		MemberFile found = this.processed.stream().filter(i->i.getContractNumber().equalsIgnoreCase(item.getContractNumber()) && i.getRelationship().equalsIgnoreCase(item.getRelationship())).findFirst().orElse(null);
		if(null !=found)
			this.processed.remove(found);		
	}
	public void clear()
	{
		this.success.clear();
		this.processed.clear();
		this.failed.clear();
		this.skip.clear();
		this.fileName = null;
		this.groupCode = null;
		this.jobExecutionId = null;
	}

	public Long getJobExecutionId() {
		return jobExecutionId;
	}

	public void setJobExecutionId(Long jobExecutionId) {
		this.jobExecutionId = jobExecutionId;
	}	
}
