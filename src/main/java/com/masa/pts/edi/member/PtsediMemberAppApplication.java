package com.masa.pts.edi.member;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

import com.masa.pts.core.repository.MemberRepository;
import com.masa.pts.core.repository.ProductTypeMappingRepository;
import com.masa.pts.edi.member.batch.configuration.EDIErrorConfig;
import com.masa.pts.edi.member.batch.configuration.EDIFileColumnNameConfig;

@SpringBootApplication
@EnableConfigurationProperties({EDIFileColumnNameConfig.class,EDIErrorConfig.class})
@ComponentScan({"com.masa.pts.edi.member","com.masa.pts.core.service","com.masa.pts.edi.member.batch.configuration","com.masa.pts.edi.member.sci.batch.configuration"
	,"com.masa.pts.edi.member.service"
	,"com.masa.pts.edi.member.integration.configuration","com.masa.pts.edi.member.sci.integration.configuration"
	,"com.masa.pts.edi.member.report.service"})
@EntityScan( basePackages = {"com.masa.pts.core.domain","com.masa.pts.edi.member","com.masa.pts.edi.member.domain"} )
@EnableJpaRepositories(basePackages = {"com.masa.pts.core.repository","com.masa.pts.edi.member.repository"})

public class PtsediMemberAppApplication extends SpringBootServletInitializer {

	@Autowired
	MemberRepository memberRepository;
	
	public static void main(String[] args) {
		new SpringApplicationBuilder(PtsediMemberAppApplication.class)
		.properties("spring.config.name:PTSEDIMemberApp")
		.build()
		.run(args);
	}
	
	@Override
	protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
		return application.properties("spring.config.name:PTSEDIMemberApp")
				.sources(PtsediMemberAppApplication.class);
	}
	@Bean
	public CommandLineRunner demo(ProductTypeMappingRepository service) {
		return (args) -> {	
			//memberService.deleteMember(1511951);
			//memberService.deleteMember(1511952);
			//memberService.deleteMember(1511953);		
			//memberFileRepository.deleteAll();
			//memberService.deleteAllMembersInGroup("B2BEZD");//"B2BVVISD");
			//memberService.deleteAllMembersInGroup("B2BCOLSD");
			//memberService.deleteAllMembersInGroup("B2BSILT");
			//memberService.deleteAllMembersInGroup("B2BCOLSD");
			//List<MemberFile> data = StreamSupport.stream(memberFileRepository.findAll().spliterator(), false).collect(Collectors.toList());
			//String fileName = String.valueOf(System.currentTimeMillis());
			//reportSrevice.generatePDFReport(data, "EDI File upload process report", new File(fileName+".pdf"), new HashMap());
		};
	}
}
