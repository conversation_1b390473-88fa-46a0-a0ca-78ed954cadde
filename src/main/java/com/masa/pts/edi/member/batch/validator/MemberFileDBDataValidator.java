package com.masa.pts.edi.member.batch.validator;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.validator.ValidationException;
import org.springframework.batch.item.validator.Validator;
import org.springframework.core.env.Environment;

import com.masa.pts.core.constant.MemberActiveStatus;
import com.masa.pts.core.domain.Constant;
import com.masa.pts.core.domain.GroupEntity;
import com.masa.pts.core.domain.Member;
import com.masa.pts.core.domain.Member.MemberSummary;
import com.masa.pts.core.domain.Member.MemberSummaryWithGroup;
import com.masa.pts.core.domain.MemberAction;
import com.masa.pts.core.domain.MemberFile;
import com.masa.pts.core.service.AgentService;
import com.masa.pts.core.service.GroupService;
import com.masa.pts.core.service.MemberEmployeeIdService;
import com.masa.pts.core.service.MemberService;
import com.masa.pts.core.service.ProductService;

public class MemberFileDBDataValidator implements Validator<MemberFile> {

	private static final Logger log = LoggerFactory.getLogger(MemberFileDBDataValidator.class);
	
	AgentService agentService;
	MemberService memberService;
	ProductService productService;
	GroupService groupService;
	Environment environment;
	MemberEmployeeIdService memberEmployeeIdService;
	
	public MemberFileDBDataValidator(Environment environment,MemberService memberService, AgentService agentService,
			ProductService productService, GroupService groupService,MemberEmployeeIdService memberEmployeeIdService) {
		this.environment = environment;
		this.agentService = agentService;
		this.memberService = memberService;
		this.productService = productService;
		this.groupService = groupService;
		this.memberEmployeeIdService = memberEmployeeIdService;
	}

	@Override
	public void validate(MemberFile input) {
		validateMemberDataWithDB(input);		
	}
	
	private void validateMemberDataWithDB(MemberFile input) {
		
		if("Add".equalsIgnoreCase(input.getModification()))
		{
			validateForAdd(input);
			validateEffectiveDate(input);
		}		
		else if("Update".equalsIgnoreCase(input.getModification()))
		{
			validateForUpdate(input);
			validateEffectiveDate(input);
		}
		else if("Cancel".equalsIgnoreCase(input.getModification()))
		{
			 validateForCancel(input);
		}		
		else if("Refresh".equalsIgnoreCase(input.getModification())) 
		{
			input.setMemberAction(MemberAction.REFRESH);
		}
		else 
		{
			throw new ValidationException("EINVALIDFILEACTION");
		}
	}
	
	private boolean foundMultipleActiveMember(Set<MemberSummary> memberSummarySet)
	{	
		return memberSummarySet.stream().filter(member->MemberActiveStatus.ACTIVE.getStatus().compareTo(member.getActive())==0).count() > 1 ;		
	}
	
	/**
	 * @param memberFile
	 * @param errors
	 */
	private void validateEffectiveDate(MemberFile memberFile) 
	{
		if(("P".equalsIgnoreCase(memberFile.getRelationship())) && ( memberFile.getEffectiveDate()==null || (memberFile.getEffectiveDate() != null && Constant.DEFULT_DATE_1900.after(memberFile.getEffectiveDate()))) )
		{
			throw new ValidationException("EEFFDATEREQUIRED");
		}			
		if("P".equalsIgnoreCase(memberFile.getRelationship()) && MemberAction.ADD.compareTo(memberFile.getMemberAction())==0)
		{
			LocalDate soldDate = new java.util.Date(memberFile.getSoldDate().getTime()).toInstant()
					.atZone(java.time.ZoneId.systemDefault()).toLocalDate();
			
			LocalDate effDateLocal = new java.util.Date(memberFile.getEffectiveDate().getTime()).toInstant()
					.atZone(java.time.ZoneId.systemDefault()).toLocalDate();
			
			if (effDateLocal.getDayOfMonth() != 1) {
				throw new ValidationException("EEFFDATEFIRSTMONTH");
			}
			
			long months = ChronoUnit.MONTHS.between(effDateLocal, soldDate.withDayOfMonth(1));

			if (months > Constant.MONTHS_RANGE_FOR_ENROLL || months < (-Constant.MONTHS_RANGE_FOR_ENROLL)) {
				throw new ValidationException("EEFFDATE3MONTHINVALID");
			}
		}
	}
	
	private boolean isDateWithRange(Date dtToCompare,Date dtToCompareWith,long range) {
		
		LocalDate compareWithLD = new java.util.Date(dtToCompareWith.getTime()).toInstant()
				.atZone(java.time.ZoneId.systemDefault()).toLocalDate();
		
		LocalDate toCompareLD = new java.util.Date(dtToCompare.getTime()).toInstant()
				.atZone(java.time.ZoneId.systemDefault()).toLocalDate();
		
		long monthDiff = ChronoUnit.MONTHS.between(toCompareLD,compareWithLD);
		
		return !(monthDiff > range || monthDiff < -range);
	}
	
	/**
	 * @param input
	 * @return
	 */
	private void validateForAdd(MemberFile input) {
		
		if("P".equalsIgnoreCase(input.getRelationship()))
		{
			input.setMemberAction(MemberAction.ADD);
			
			Set<MemberSummary> memberSummarySet = memberEmployeeIdService.findByEmployeeIdAndGroupCode(input.getContractNumber(), input.getGroupCode());
			
			if(foundMultipleActiveMember(memberSummarySet))
			{
				throw new ValidationException("EMULTIPLEMEMBERFOUNDADD");
			}
			
			Optional<Member.MemberSummary> optMemSmry = memberSummarySet.stream().filter(member->MemberActiveStatus.ACTIVE.getStatus().compareTo(member.getActive())==0).findFirst();
			  
			if(optMemSmry.isPresent())
			{
				input.setMemberAction(MemberAction.UPDATE);
				
				MemberSummary memberSmry = optMemSmry.get();
				input.setMasaMemberId(memberSmry.getMemberId());
				
				checkMemberFirstNameAndDOBMatch(input,memberSmry);				
								
				isInputEffDateBeforeMembrEffDate(input,memberSmry);
				return ;
			}
			//check if member is in cancelled status and being active now
			Optional<Member.MemberSummary> optMemSmryCancel = memberSummarySet.stream().filter(member->MemberActiveStatus.CANCELLED.getStatus().compareTo(member.getActive())==0).findFirst();
			if(optMemSmryCancel.isPresent())
			{
				MemberSummary memberSmry = optMemSmryCancel.get();
				input.setMasaMemberId(optMemSmryCancel.get().getMemberId());				
				checkMemberFirstNameAndDOBMatch(input,memberSmry);				
				
				input.setMemberAction(MemberAction.ACTIVATE);				
				log.info("Activating member [{}]",optMemSmryCancel.get().getMemberId());
				return ;
			}
			
			List<Integer> sortOrderOfStatus = Arrays.asList(MemberActiveStatus.ACTIVE.getStatus(),MemberActiveStatus.CANCELLED.getStatus(),MemberActiveStatus.SUSPENDED.getStatus());
			
			Comparator<MemberSummaryWithGroup> activeSorter = Comparator.comparing(item->sortOrderOfStatus.indexOf(item.getActive()));
			
			Map<Integer, List<MemberSummaryWithGroup>> memSmryMapList = memberService.getMemberSmryByFirstAndLastNameAndDob(input.getFirstName(),input.getLastName(), input.getBirthDate())
					.stream().sorted(activeSorter)
					.collect(Collectors.groupingBy(MemberSummaryWithGroup::getActive));
			
			if(memSmryMapList == null || (memSmryMapList !=null && memSmryMapList.isEmpty())) {//no existing records found, add member
				input.setMemberAction(MemberAction.ADD);
				return;
			}
			//validate for multiple active,cancel,suspend
			checkForDuplicateMemberships(memSmryMapList);
			
			List<MemberSummaryWithGroup>memSmrySet =  memSmryMapList.getOrDefault(MemberActiveStatus.ACTIVE.getStatus(), 
												memSmryMapList.getOrDefault(MemberActiveStatus.CANCELLED.getStatus(), 
												memSmryMapList.getOrDefault(MemberActiveStatus.SUSPENDED.getStatus(), Collections.EMPTY_LIST)));
			
			for(MemberSummaryWithGroup item: memSmrySet)
			{
				if(item.getGroupGroupCode().equalsIgnoreCase(input.getGroupCode()))
				{
					input.setMemberAction(MemberAction.EMPLOYEEID_UPDATE);
					input.setMasaMemberId(item.getMemberId());
					log.info("Found member [{}] in group with different employee id, Input [{}] Current [{}]",item.getMemberId(),input.getEmployeeId(),item.getEmployeeId());
					return;
				}
			}

			for (MemberSummaryWithGroup item : memSmrySet) {
				GroupEntity group = groupService.getGroupByCode(input.getGroupCode());
				if (item.getGroup().getBusinessLineEntity().getSalesChannel().getDivision().getId()
						.compareTo(group.getBusinessLineEntity().getSalesChannel().getDivision().getId())==0) {
					input.setMemberAction(MemberAction.GROUP_CHANGE);
					input.setMasaMemberId(item.getMemberId());
					log.info("Found member [{}] in another B2B group [{}] , processing group change to [{}]",
							item.getMemberId(), item.getGroupGroupCode(), input.getGroupCode());
					return;
				}
			}
			input.setMemberAction(MemberAction.ADD);
		}				
	}

	private void checkForDuplicateMemberships(Map<Integer, List<MemberSummaryWithGroup>> memSmryMapList) {
		if(	memSmryMapList.getOrDefault(MemberActiveStatus.ACTIVE.getStatus(),Collections.emptyList()).size() > 1
				|| memSmryMapList.getOrDefault(MemberActiveStatus.CANCELLED.getStatus(),Collections.emptyList()).size() > 1
				|| memSmryMapList.getOrDefault(MemberActiveStatus.SUSPENDED.getStatus(),Collections.emptyList()).size() > 1
				) {
			throw new ValidationException("EMULTIPLEMEMBERFOUNDADD");
		}
	}

	/**
	 * @param input
	 * @return
	 */
	private void validateForUpdate(MemberFile input) {			
		if("P".equalsIgnoreCase(input.getRelationship()))
		{
			input.setMemberAction(MemberAction.UPDATE);
			
			Set<MemberSummary> memberSummarySet = memberEmployeeIdService.findByEmployeeIdAndGroupCode(input.getContractNumber(), input.getGroupCode());
			if(memberSummarySet.isEmpty())
			{
				input.setMemberAction(MemberAction.ADD);
				validateForAdd(input);
				validateEffectiveDate(input);
				return ;
			}
			if(memberSummarySet.stream().filter(member->MemberActiveStatus.ACTIVE.getStatus().compareTo(member.getActive())==0).collect(Collectors.toSet()).size() > 1 )
			{
				throw new ValidationException("EMULTIPLEACTMEMBERFOUND");
			}			
			
			if(memberSummarySet.stream().filter(member->(MemberActiveStatus.CANCELLED.getStatus().compareTo(member.getActive())==0 || 
					MemberActiveStatus.SUSPENDED.getStatus().compareTo(member.getActive())==0)).collect(Collectors.toSet()).size() > 1 )
			{
				throw new ValidationException("EMULTIPLEINACTMEMBERFOUND");
			}
			
			Optional<Member.MemberSummary> optActiveMemSmry = memberSummarySet.stream().filter(member->MemberActiveStatus.ACTIVE.getStatus().compareTo(member.getActive())==0).findFirst();
			  
			if(optActiveMemSmry.isPresent()){
				//if product changed but effective date of new product is same old ???				
				MemberSummary memberSmry = optActiveMemSmry.get();
				
				input.setMasaMemberId(memberSmry.getMemberId());
				
				checkMemberFirstNameAndDOBMatch(input, memberSmry);
				
				isInputEffDateBeforeMembrEffDate(input,memberSmry);
				
				log.info("Found member ID [{}] for employee Id [{}] and last name [{}]",memberSmry.getMemberId(),input.getEmployeeId(),input.getLastName());
				return ;
			}
			
			Optional<Member.MemberSummary> optInActiveMemSmry = memberSummarySet.stream().filter(member-> ( MemberActiveStatus.CANCELLED.getStatus().compareTo(member.getActive())==0 ||
					MemberActiveStatus.SUSPENDED.getStatus().compareTo(member.getActive())==0	)).findFirst();
			if(optInActiveMemSmry.isPresent()) {
				//member status cancel/suspended - edi has Update - activate member if effective date is within 3 month
				MemberSummary memberSummary = optInActiveMemSmry.get();
				
				input.setMasaMemberId(memberSummary.getMemberId());
				
				checkMemberFirstNameAndDOBMatch(input, memberSummary);
				
				if(isDateWithRange(input.getEffectiveDate(),new Date(),Constant.MONTHS_RANGE_FOR_ENROLL)) {
					input.setMasaMemberId(memberSummary.getMemberId());
					input.setMemberAction(MemberAction.ACTIVATE);
					return ;
				}
				else {
					input.setMasaMemberId(memberSummary.getMemberId());
					throw new ValidationException("EEFFDATE3MONTHINVALIDACTIVE");					
				}
			}			
			else
			{
				throw new ValidationException("EMEMBERNOTFOUNDUPDATE");
			}			
		}			
	}	
	
	/**
	 * @param input
	 * @return validation message if cancel cannot be performed
	 * empty string if member can be cancelled.
	 */
	private void validateForCancel(MemberFile input) {
		
		if("P".equalsIgnoreCase(input.getRelationship()))
		{
			input.setMemberAction(MemberAction.CANCELL);
			
			Set<MemberSummary> memberSummarySet = memberEmployeeIdService.findByEmployeeIdAndGroupCode(input.getContractNumber(), input.getGroupCode());
			
			if(memberSummarySet.isEmpty())
			{
				Set<MemberSummary>  optMemSmrySet = memberService.findByLastNameAndBirthDateAndGroupGroupCodeAndActive(input.getLastName(), input.getBirthDate(),input.getGroupCode()
						,MemberActiveStatus.ACTIVE.getStatus());
				
				if(optMemSmrySet.isEmpty())	{
					throw new ValidationException("EMEMBERNOTFOUNCANCEL");
				}
				else if(optMemSmrySet.size()>1) {
					throw new ValidationException("EMULTEACTMBRFOUNDBYLNDBINGROUP");
				}
				else { //member found
					Integer memberId = optMemSmrySet.stream().map(MemberSummary::getMemberId).findFirst().orElse(null);
					input.setMasaMemberId(memberId);
					log.info("Record with Cancel action , identified member id [{}] for contract number [{}]",memberId,input.getContractNumber());
					return ;
				}
			}
			
			if(memberSummarySet.size()==1 && memberSummarySet.stream().allMatch(member->MemberActiveStatus.CANCELLED.getStatus().compareTo(member.getActive())==0)) {
				Optional<MemberSummary> optMemSummary = memberSummarySet.stream().findFirst();
				if(optMemSummary.isPresent() && input.getCancelDate().compareTo(optMemSummary.get().getCancelDate())==0) {
					input.setMasaMemberId(optMemSummary.get().getMemberId());
					input.setMemberAction(MemberAction.REFRESH);
					return ;
				}
				else if(optMemSummary.isPresent()) {
					input.setMasaMemberId(optMemSummary.get().getMemberId());					
					return ;
				} 
			}
			/*
			if(memberSummarySet.stream().noneMatch(member->MemberActiveStatus.ACTIVE.getStatus().compareTo(member.getActive())==0))
			{
				throw new ValidationException(String.join("", "No active member with employee id [",input.getContractNumber(),"] found in group [",input.getGroupCode(),"] for cancel."));
			}
			*/
			if(memberSummarySet.stream().filter(member->MemberActiveStatus.ACTIVE.getStatus().compareTo(member.getActive())==0).collect(Collectors.toSet()).size() > 1 )
			{
				throw new ValidationException("EMULTIPLEACTMEMBERFOUNDCNCL");
			}
			
			Optional<Member.MemberSummary> optMemSmry = memberSummarySet.stream().filter(member->MemberActiveStatus.ACTIVE.getStatus().compareTo(member.getActive())==0).findFirst();
					  
			if(optMemSmry.isPresent())
			{
				MemberSummary memberSmry = optMemSmry.get();				
				input.setMasaMemberId(optMemSmry.get().getMemberId());
				checkMemberFirstNameAndDOBMatch(input, memberSmry);				
				log.info("Record with Cancel action , identified member id [{}] for contact number [{}]",memberSmry.getMemberId(),input.getContractNumber());
				return;
			}
			else
				throw new ValidationException("EMEMBERNOACTIVEFORCANCEL");
		}		
	}

	private void checkMemberFirstNameAndDOBMatch(MemberFile input, MemberSummary memberSmry) {
		if(!(memberSmry.getFirstName().trim().equalsIgnoreCase(input.getFirstName())) || (memberSmry.getBirthDate().compareTo(input.getBirthDate())!=0)){
			throw new ValidationException("EMEMBERDIFFFIRSTDOB");
		}		
	}
	
	private void isInputEffDateBeforeMembrEffDate(MemberFile input, MemberSummary memberSmry) {
		if (input.getEffectiveDate().before(memberSmry.getEffectiveDate())) {
			throw new ValidationException("EEFFDATEBEFOREMEMEFFDATE");
		}
	}
}
