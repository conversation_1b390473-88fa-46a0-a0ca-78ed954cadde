package com.masa.pts.edi.member.sci.batch.configuration;

import java.util.Date;

import org.springframework.batch.item.file.mapping.FieldSetMapper;
import org.springframework.batch.item.file.transform.FieldSet;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindException;

import com.masa.pts.core.domain.MemberFile;

public class SCIFileFieldSetMapper implements FieldSetMapper<MemberFile> {

	@Override
	public MemberFile mapFieldSet(FieldSet fieldSet) throws BindException {
		MemberFile memberFile = new MemberFile();

		memberFile.setDivision(readStringColumnFromFile(fieldSet, "Division"));
		memberFile.setContractNumber(readStringColumnFromFile(fieldSet, "Contract"));
		memberFile.setFirstName(readStringColumnFromFile(fieldSet, "Beneficiary_First"));
		memberFile.setLastName(readStringColumnFromFile(fieldSet, "Beneficiary_Last"));
		memberFile.setBenefitAddress(readStringColumnFromFile(fieldSet, "Address"));
		memberFile.setBenefitCity(readStringColumnFromFile(fieldSet, "City"));
		memberFile.setBenefitState(readStringColumnFromFile(fieldSet, "State"));
		memberFile.setBenefitZip(readStringColumnFromFile(fieldSet, "Zip"));
		memberFile.setBenefitCountry(readStringColumnFromFile(fieldSet, "Market"));
		memberFile.setPhone(readStringColumnFromFile(fieldSet, "Phone"));
		memberFile.setEffectiveDate(readDateColumnFromFile(fieldSet, "Issue_Date"));
		memberFile.setProductType(readStringColumnFromFile(fieldSet, "Product_Code"));
		memberFile.setCancelDate(readDateColumnFromFile(fieldSet, "Cancel_Date"));
		memberFile.setEmployer(readStringColumnFromFile(fieldSet, "RFH_Name"));
		memberFile.setBirthDate(readDateColumnFromFile(fieldSet, "Birth_Dt"));
		
		memberFile.setPaymentAmount(readDoubleColumnFromFile(fieldSet,"Total"));
		
		//defaults
		memberFile.setRelationship("P");
		memberFile.setCreatedDate(new Date());
		memberFile.setMiddleName("");
		
		return memberFile;
	}

	private String readStringColumnFromFile(FieldSet fieldSet, String columnName) {
		String data = fieldSet.readString(columnName);
		data = StringUtils.trimWhitespace(data);
		return data;
	}

	private Date readDateColumnFromFile(FieldSet fieldSet, String columnName) {
		Date dateVal = null;
		try {
			dateVal = fieldSet.readDate(columnName, "yyyy-MM-dd HH:mm:ss");
		} catch (Exception e) {
			// TODO: handle exception
		}
		return dateVal;
	}
	
	private double readDoubleColumnFromFile(FieldSet fieldSet,String columnName) {
		return fieldSet.readDouble(columnName);
	}
}
