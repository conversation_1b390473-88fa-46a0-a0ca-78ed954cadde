package com.masa.pts.edi.member.report.service;

import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.itextpdf.text.Document;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.pdf.PdfWriter;
import com.masa.pts.core.domain.MemberFile;
import com.masa.pts.core.domain.UploadStatus;
import com.masa.pts.edi.member.report.pdf.HeaderFooter;
import com.masa.pts.edi.member.report.pdf.PDFCreator;


@Service
public class EDIFileProcessReportService {

private static final Logger log = LoggerFactory.getLogger(EDIFileProcessReportService.class);
	

	/**
	 * @param memberFileDataList
	 * @param header
	 * @param outputFile
	 * @param jobParamsMap
	 */
	public void generatePDFReport(List<MemberFile> memberFileDataList, String header, File outputFile, Map<String, String> jobParamsMap) {

		Document document = null;
		try {
			
			if(memberFileDataList.isEmpty()) {
				log.error("No Data to generate PDF report, exiting.");
				return;
			}			
			document = new Document(PageSize.A4);     
			 
			PdfWriter writer = PdfWriter.getInstance(document, new FileOutputStream(outputFile));

			HeaderFooter event = new HeaderFooter();
			event.setHeader(header);
			writer.setPageEvent(event);

			document.open();

			PDFCreator.addMetaData(document);
					
			//order of the items in PDF Report
			List<UploadStatus> sortOrderOfStatus = Arrays.asList(UploadStatus.ERROR,UploadStatus.FAIL,UploadStatus.SKIP,UploadStatus.UNPROCESSED,UploadStatus.PROCESSED,
					UploadStatus.SUCCESS);
			Comparator<UploadStatus> statusComparator = Comparator.comparing(status -> sortOrderOfStatus.indexOf(status));			
			Map<UploadStatus,List<MemberFile>> statusMapList =  memberFileDataList.stream().collect(Collectors.groupingBy(MemberFile::getStatus)); 			
			Set<UploadStatus> statusSet = statusMapList.keySet();
			List<UploadStatus> statusSetOrdered = statusSet.stream().sorted(statusComparator).collect(Collectors.toList());
			
			for(UploadStatus statusItemKey : statusSetOrdered) {
				
				List<MemberFile> statusListItem = statusMapList.getOrDefault(statusItemKey,new ArrayList<MemberFile>());
				
				try {
					statusListItem = statusListItem.stream().sorted(Comparator.comparing(MemberFile::getModification).thenComparing(MemberFile::getContractNumber))
							.parallel().collect(Collectors.toList());
				}catch(RuntimeException re) {
					log.error("Error during sorting results , Message [{}] Cause [{}]",re.getMessage(),re.getCause());
				}
				
				if(statusItemKey.compareTo(UploadStatus.SUCCESS)==0)
					document.newPage();
				
				PDFCreator.addContent(document, statusListItem, "Status "+statusItemKey.name()+".");				
			}
			
			if(jobParamsMap.size() >0)
				PDFCreator.addJobParameterTable(document, jobParamsMap,"EDI File Processing Job Parameters");
			
		} catch (Exception e) {
			log.error("Exceptio occurred while generating PDF Report, Messsage: [{}] Cause [{}]",e.getMessage(),e.getCause());
		}		
		finally {
			if (null != document) {
				document.close();
			}
		}		
	}
}
