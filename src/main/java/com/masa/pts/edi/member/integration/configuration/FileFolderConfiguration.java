package com.masa.pts.edi.member.integration.configuration;

import java.io.File;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class FileFolderConfiguration {

	@Bean(name="inboundReadDirectory")
    public File inboundReadDirectory(@Value("${pts.edifile.read}") String path) {
        return makeDirectory(path);
    }

    @Bean(name="inboundProcessedDirectory")
    public File inboundProcessedDirectory(@Value("${pts.edifile.processed}") String path) {
        return makeDirectory(path);
    }

    @Bean(name="inboundFailedDirectory")
    public File inboundFailedDirectory(@Value("${pts.edifile.failed}") String path) {
        return makeDirectory(path);
    }

    @Bean(name="inboundOutDirectory")
    public File inboundOutDirectory(@Value("${pts.edifile.output}") String path) {
        return makeDirectory(path);
    }
    
	private File makeDirectory(String path) {
		File file = new File(path);
		file.mkdirs();
		return file;
	}
}
