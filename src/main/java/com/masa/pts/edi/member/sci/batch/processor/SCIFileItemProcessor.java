package com.masa.pts.edi.member.sci.batch.processor;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.validator.ValidationException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.DataBinder;
import org.springframework.validation.ObjectError;
import org.springframework.validation.Validator;

import com.masa.pts.core.constant.MemberActiveStatus;
import com.masa.pts.core.domain.Agent;
import com.masa.pts.core.domain.Member.MemberSummary;
import com.masa.pts.core.domain.MemberAction;
import com.masa.pts.core.domain.MemberFile;
import com.masa.pts.core.domain.Product;
import com.masa.pts.core.service.MemberFileService;
import com.masa.pts.core.service.MemberService;

public class SCIFileItemProcessor implements ItemProcessor<MemberFile, MemberFile> {

	private static final String MODIFICATION_ADD = "ADD";
	private static final String MODIFICATION_CANCEL = "CANCEL";
	private static final String DIVISION_FOR_NEPT = "2100";
	private static final String PRODUCT_CODE_FOR_NEPT = "NEPT";
	private static final String SCI_PRODUCT_PAYMENT_TYPE = "LT";
	private static final List<String> SCI_GROUP_CODES = Arrays.asList("NEPF","NEPT","SCF","SCIC","SMF","SCIM");
	
	private Validator validator;
	private String fileNameWithoutPath;
	private String fileType;
	private Date soldDate;
	private MemberFileService memberFileService;
	private MemberService memberService;

	public SCIFileItemProcessor(String fileNameWithoutPath, String fileType, Date soldDate,MemberFileService memberFileService
			,MemberService memberService) {
		super();
		this.fileNameWithoutPath = fileNameWithoutPath;
		this.fileType = fileType;
		this.soldDate = soldDate;
		this.memberFileService = memberFileService;
		this.memberService = memberService;
	}

	@Override
	public MemberFile process(MemberFile input) throws Exception {
		
		input.setSourceFileName(fileNameWithoutPath);
		input.setModification(fileType);
		input.setSoldDate(soldDate);
		input.setPaymentCode(SCI_PRODUCT_PAYMENT_TYPE);
		input.setProcessed(false);
		
		BindingResult results = bindAndValidate(input);// Throws ValidationException is validation fails.
		if (results.hasErrors()) {
			buildValidationException(results);
		}
		try {
			setGroupInfo(input);
			if (MODIFICATION_ADD.equalsIgnoreCase(input.getModification())) {
				setEffectiveDate(input);
				setProductId(input);
				setAgent(input);
				validateRecordForAdd(input);
			} else if (MODIFICATION_CANCEL.equalsIgnoreCase(input.getModification())) {
				setCancelDate(input);
				validateRecordForCancel(input);
			}
		} catch (Exception e) {
			throw new ValidationException(e.getMessage());
		}
		return input;
	}	
	

	public Validator getValidator() {
		return validator;
	}
	public void setValidator(Validator validator) {
		this.validator = validator;
	}
	
	private void validateRecordForAdd(MemberFile input) {
		Set<MemberSummary> memSmrySet = memberService.getMemberSmryByFirstAndLastNameAndDobAndEmployeeIdInGroups(
				input.getFirstName(), input.getLastName(), input.getBirthDate(),input.getContractNumber(),
				SCI_GROUP_CODES);
		if(!memSmrySet.isEmpty()) {
			throw new ValidationException("EMULTIPLEMEMBERFOUNDADD");
		}
		input.setMemberAction(MemberAction.ADD);
		
	}
	private void validateRecordForCancel(MemberFile input) {
		
		Set<MemberSummary> memSmrySet = memberService.getMemberSmryByFirstAndLastNameAndEmployeeIdInGroups(
				input.getFirstName(), input.getLastName(), input.getContractNumber(), SCI_GROUP_CODES);
		
		//none found
		memSmrySet.stream().findFirst().orElseThrow(()-> new ValidationException("EMEMBERNOTFOUNCANCEL"));

		//no active member found
		memSmrySet.stream()
				.filter(item -> item.getActive().compareTo(MemberActiveStatus.ACTIVE.getStatus()) == 0)
				.findFirst()
				.orElseThrow(() -> new ValidationException("EMEMBERNOACTIVEFORCANCEL"));

		//multiple active member found
		memSmrySet.stream()
				.filter(item -> item.getActive().compareTo(MemberActiveStatus.ACTIVE.getStatus()) == 0)
				.reduce((mem1, mem2) -> {
					throw new ValidationException("EMULTEACTMBRFOUNDBYLNDBINGROUP");
				});
		
		//1 found-exclude if cancel date > 30 days
		MemberSummary memSmry = memSmrySet.stream()
				.filter(item -> item.getActive().compareTo(MemberActiveStatus.ACTIVE.getStatus()) == 0 )
				.findFirst().orElseThrow(() -> new ValidationException("EMEMBERNOTFOUNCANCEL"));
		
		input.setMasaMemberId(memSmry.getMemberId());		
		input.setMemberAction(MemberAction.CANCELL);
	}
	
	private void setAgent(MemberFile input) {	
		Agent agent = memberFileService.getGroupAgent(input.getGroupCode());
			
		if(null !=agent) {
			input.setAgentCode(agent.getAgentNum());
			input.setAgentId(agent.getAgentId());
		}
		else {
			throw new ValidationException("EAGENTNOTSETUPGRP");
		}
	}	
	private void setGroupInfo(MemberFile input) {
		
		boolean isMemberStateFL = isMemberStateFL(input);
		
		if(input.getDivision().equals(DIVISION_FOR_NEPT) && isMemberStateFL	) {
			input.setGroupCode("NEPF");
		}
		else if(input.getDivision().equals(DIVISION_FOR_NEPT) && !isMemberStateFL) {
			input.setGroupCode("NEPT");
		}
		else if(!input.getDivision().equals(DIVISION_FOR_NEPT) 
				&& isMemberStateFL
				&& "TRVPPCRE".equalsIgnoreCase(input.getProductType())
				) {
			input.setGroupCode("SCF");
		}else if(!input.getDivision().equals(DIVISION_FOR_NEPT) 
				&& !isMemberStateFL
				&& "TRVPPCRE".equalsIgnoreCase(input.getProductType())
				) {
			input.setGroupCode("SCIC");
		}
		else if(!input.getDivision().equals(DIVISION_FOR_NEPT) 
				&& isMemberStateFL
				&& "TRVPPBUR".equalsIgnoreCase(input.getProductType())
				) {
			input.setGroupCode("SMF");
		}else if(!input.getDivision().equals(DIVISION_FOR_NEPT) 
				&& !isMemberStateFL
				&& "TRVPPBUR".equalsIgnoreCase(input.getProductType())
				) {
			input.setGroupCode("SCIM");
		}
		else {
			throw new ValidationException("EGROUPINVALIDINACTIVE");
		}
	}
	
	private boolean isMemberStateFL(MemberFile input) {
		return "United States".equalsIgnoreCase(input.getBenefitCountry()) && "FL".equalsIgnoreCase(input.getBenefitState());		
	}
	
	private void setProductId(MemberFile input) {		
		Optional<Product> optProduct;		
		if(input.getDivision().equals(DIVISION_FOR_NEPT)) {
			optProduct = memberFileService.getProductByProductTypeAndPaymentTypeAndGroupCode(
					PRODUCT_CODE_FOR_NEPT
					,input.getPaymentCode()
					,soldDate
					,input.getGroupCode());			
		}
		else {
			optProduct = memberFileService.getProductByProductTypeAndPaymentTypeAndGroupCode(
					input.getProductType()
					,input.getPaymentCode()
					,soldDate
					,input.getGroupCode());		
		}		
		if(optProduct.isPresent()) {
			Product product = optProduct.get();
			input.setProductId(product.getProductId());
			input.setProductName(product.getName());
			//input.setProductCategoryCode(product.getProductCategory().getCode());
		}
		else {
			throw new ValidationException("EPRODUCTINVALIDFORPYMT");
		}
	}

	private void setCancelDate(MemberFile input) {
		if(input.getCancelDate()==null) {
			input.setCancelDate(new Date());
		}		
	}

	private void setEffectiveDate(MemberFile input) {
		if(input.getEffectiveDate()==null) {
			input.setEffectiveDate(new Date());
		}		
	}

	private BindingResult bindAndValidate(MemberFile item) {
		DataBinder binder = new DataBinder(item);
		binder.addValidators(validator);
		binder.validate();
		return binder.getBindingResult();
	}

	private void buildValidationException(BindingResult results) {
		StringBuilder msg = new StringBuilder();
		for (ObjectError error : results.getAllErrors()) {
			msg.append(error.getDefaultMessage()).append(";");
		}
		throw new ValidationException(msg.toString());
	}
}
