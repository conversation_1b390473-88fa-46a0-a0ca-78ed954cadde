package com.masa.pts.edi.member.integration.configuration;

import java.io.File;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.List;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.batch.integration.launch.JobLaunchRequest;
import org.springframework.integration.annotation.Transformer;
import org.springframework.messaging.Message;

public class FileMessageToJobRequest {
	
	private List<? extends Job> jobs;
	private String fileParameterName;
	private String soldDateFormat;

	public FileMessageToJobRequest(List<? extends Job> jobs, String fileParameterName,String soldDateFormat) {
		this.jobs = jobs;
		this.fileParameterName = fileParameterName;
		this.soldDateFormat = soldDateFormat;
	}

	@Transformer
	public JobLaunchRequest toRequest(Message<File> message) {
		
		String jobName = (String)message.getHeaders().get("JOB_NAME");
		
		Job jobToBeLaunched = jobs.stream().filter(jobItem->jobItem.getName().equalsIgnoreCase(jobName))
				.findFirst().orElseThrow(() -> new RuntimeException("Job with name ["+jobName+"] not found.") );
		
		JobParametersBuilder jobParametersBuilder = new JobParametersBuilder();
		
		jobParametersBuilder.addString(fileParameterName, message.getPayload().getAbsolutePath());
		jobParametersBuilder.addString("fileNameWithoutPath", message.getPayload().getName());
		jobParametersBuilder.addLong("run.id", System.currentTimeMillis());		
		
		//sci job specific
		jobParametersBuilder.addString("fileType",(String)message.getHeaders().get("fileType"));
		
		//edi member job
		jobParametersBuilder.addString("groupCode", (String)message.getHeaders().get("groupCode"));
		jobParametersBuilder.addString("agentCode", (String)message.getHeaders().get("agentCode"));
		
		SimpleDateFormat sdf = ((SimpleDateFormat) DateFormat.getInstance());
		sdf.applyPattern(soldDateFormat);
		try {
			jobParametersBuilder.addDate("soldDate", sdf.parse((String)message.getHeaders().get("soldDate")));
		}
		catch(Exception e)
		{
			//
		}
		return new JobLaunchRequest(jobToBeLaunched, jobParametersBuilder.toJobParameters());		
	}
	
}
