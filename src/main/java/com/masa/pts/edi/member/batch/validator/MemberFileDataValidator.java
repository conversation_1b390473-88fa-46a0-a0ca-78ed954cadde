package com.masa.pts.edi.member.batch.validator;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Date;

import org.springframework.util.StringUtils;
import org.springframework.validation.Errors;
import org.springframework.validation.Validator;
import org.springframework.validation.beanvalidation.SpringValidatorAdapter;

import com.masa.pts.core.domain.Constant;
import com.masa.pts.core.domain.FileAction;
import com.masa.pts.core.domain.MemberFile;
import com.masa.pts.core.service.AddressService;
import com.masa.pts.core.service.DependentUtilityService;

public class MemberFileDataValidator implements Validator {

	private static final String BENEFIT_ADDR = "benefitAddress";
	private static final String MAILING_ADDR = "mailingAddress";
	private static final String BIRTH_DATE = "birthDate";
	
	
	private final SpringValidatorAdapter validator;
	private final AddressService addressService;
	private final DependentUtilityService dependentUtilityService;
	
	public MemberFileDataValidator(SpringValidatorAdapter validator
			, AddressService addressService
			,DependentUtilityService dependentUtilityService
			) {
		this.validator = validator;
		this.addressService = addressService;
		this.dependentUtilityService = dependentUtilityService;
	}

	@Override
	public boolean supports(Class<?> clazz) {
		return MemberFile.class.equals(clazz);
	}

	@Override
	public void validate(Object target, Errors errors) {
		//jsr303
	     validator.validate(target, errors);
		
		MemberFile memberFile = (MemberFile)target;

		validateFileInputData(memberFile,errors);
		
		//validate address
		//validate effective date
		//validate birth date
		//if cancel - validate cancel date
		
		if("P".equalsIgnoreCase(memberFile.getRelationship()) && !FileAction.CANCEL.name().equalsIgnoreCase(memberFile.getModification()))
		{
			validateAddress(memberFile,errors);
		}
		if("P".equalsIgnoreCase(memberFile.getRelationship()))
		{
			validateBirthDate(memberFile, errors);
		}
		if("S".equalsIgnoreCase(memberFile.getRelationship()))
		{
			validateBirthDate(memberFile, errors);
		}
		if("D".equalsIgnoreCase(memberFile.getRelationship()))
		{
			validateDependentBirthDate(memberFile,errors);
		}		
		if("Cancel".equalsIgnoreCase(memberFile.getModification()))
		{
			validateCancelDate(memberFile,errors);
		}
	}

	//add validation for following fields
	//@NotBlank(message="Contract Number/Employee ID is required.")
	//@NotBlank(message="Action is required(Add/Update/Cancel).")
	//@NotBlank(message="Product Type is required.")
	//@NotBlank(message="Relationship is required.")
	//@NotBlank(message="First Name is required.")
	//@NotBlank(message="Last Name is required.")
	//@NotBlank(message="Payment Code is required.")
	private void validateFileInputData(MemberFile memberFile, Errors errors) {
		
		if(!StringUtils.hasText(memberFile.getContractNumber()))
		{
			errors.rejectValue("contractNumber","","EEMPLOYEEIDREQD");
		}
		if(!StringUtils.hasText(memberFile.getModification()) && !(
				"Add".equalsIgnoreCase(memberFile.getModification()) ||
				"Update".equalsIgnoreCase(memberFile.getModification()) ||
				"Cancel".equalsIgnoreCase(memberFile.getModification()) 
				))
		{
			errors.rejectValue("modification","","EMODIFICATIONREQD");
		}
		if(!StringUtils.hasText(memberFile.getProductType()) && !("Cancel".equalsIgnoreCase(memberFile.getModification())) )
		{
			errors.rejectValue("productType","","EPRODUCTTYPEREQD");
		}
		if(!(	"P".equalsIgnoreCase(memberFile.getRelationship()) ||
				"S".equalsIgnoreCase(memberFile.getRelationship()) ||
				"D".equalsIgnoreCase(memberFile.getRelationship())
				))
		{
			errors.rejectValue("relationship","","ERELATIONSHIPREQD");
		}
		if(!StringUtils.hasText(memberFile.getFirstName()))
		{
			errors.rejectValue("firstName","","EFIRSTNAMEREQD");
		}
		if(!StringUtils.hasText(memberFile.getLastName()))
		{
			errors.rejectValue("lastName","","ELASTNAMEREQD");
		}
		if(!StringUtils.hasText(memberFile.getPaymentCode()) && !(
				"MN".equalsIgnoreCase(memberFile.getPaymentCode()) ||
				"YR".equalsIgnoreCase(memberFile.getPaymentCode())
				) && !("Cancel".equalsIgnoreCase(memberFile.getModification())) )
		{
			errors.rejectValue("paymentCode","","EPAYMENTTYPEREQD");
		}
		
		if("PLS".equalsIgnoreCase(memberFile.getProductType()) 
				&& !("P".equalsIgnoreCase(memberFile.getRelationship()))
			) {
			errors.rejectValue("productType","","EDEPENDENTINVFORPRD");
		}
	}

	/**
	 * @param memberFile
	 * @param errors
	 */
	private void validateAddress(MemberFile memberFile, Errors errors)
	{
		if(!StringUtils.hasText(memberFile.getBenefitAddress()))
		{
			errors.rejectValue(BENEFIT_ADDR,"","EBENEFITADDRREQD");
		}
		if(!StringUtils.hasText(memberFile.getBenefitCity()))
		{
			errors.rejectValue(BENEFIT_ADDR,"","EBENEFITCITYREQD");
		}
		if(!StringUtils.hasText(memberFile.getBenefitState()))
		{
			errors.rejectValue(BENEFIT_ADDR,"","EBENEFITSTATEREQD");
		}
		if(!StringUtils.hasText(memberFile.getBenefitZip()))
		{
			errors.rejectValue(BENEFIT_ADDR,"","EBENEFITZIPREQD");
		}
				
		if (!memberFile.getMailingCity().isEmpty() && memberFile.getMailingAddress().isEmpty()) {
			errors.rejectValue(MAILING_ADDR,"","EMAILINGADDRREQDCITY");			
		}
		
		String countryCd = StringUtils.hasText(memberFile.getBenefitCountry()) ? memberFile.getBenefitCountry() : "United States";
		Integer countryId = addressService.getCountryByCode(countryCd).getCountryId();
		
		if(StringUtils.hasText(memberFile.getBenefitState()) && !addressService.existsByStateAndCountry(memberFile.getBenefitState(),countryId))
		{
			errors.rejectValue(BENEFIT_ADDR,"", "EBENEFITSTATEINVALID");
		}
		countryCd = StringUtils.hasText(memberFile.getMailingCountry()) ? memberFile.getMailingCountry() : "United States";
		countryId = addressService.getCountryByCode(countryCd).getCountryId();
		
		if(StringUtils.hasText(memberFile.getMailingState()) && !addressService.existsByStateAndCountry(memberFile.getMailingState(),countryId))
		{
			errors.rejectValue(MAILING_ADDR,"", "EMAILINGSTATEINVALID");
		}
	}
	
	/**
	 * @param memberFile
	 * @param errors
	 */
	private void validateBirthDate(MemberFile memberFile,Errors errors) 
	{
		if(memberFile.getBirthDate()==null) {
			errors.rejectValue(BIRTH_DATE,"","EBIRTHDATEREQD");
		}
		
		if(memberFile.getBirthDate() != null &&  memberFile.getBirthDate().compareTo(Constant.DEFULT_DATE_1900) == 0) {
			errors.rejectValue(BIRTH_DATE,"", "EBIRTHDATEINVALID");
			return ;
		}
		if(memberFile.getBirthDate() != null )
		{
			LocalDate birthDateLocal = convertUtilToLocalDate(memberFile.getBirthDate());
			if(LocalDate.now().isBefore(birthDateLocal) || LocalDate.now().isEqual(birthDateLocal))
			{
				errors.rejectValue(BIRTH_DATE,"", "EBIRTHDATEINVALIDF");
			}
		}
	}
	/**
	 * @param memberFile
	 * @param errors
	 */
	private void validateDependentBirthDate(MemberFile memberFile,Errors errors) 
	{
		if(memberFile.getBirthDate()==null) {
			errors.rejectValue(BIRTH_DATE,"","EBIRTHDATEREQD");
		}
		
		if(memberFile.getBirthDate() != null && memberFile.getBirthDate().compareTo(Constant.DEFULT_DATE_1900)==0) {
			errors.rejectValue(BIRTH_DATE,"", "EBIRTHDATEINVALID");
			return ;
		}
		if(memberFile.getBirthDate() != null)
		{
			//age <=26
			LocalDate birthDateLocal = convertUtilToLocalDate(memberFile.getBirthDate());
			long years  = ChronoUnit.YEARS.between(birthDateLocal, LocalDate.now());
			Integer limit = dependentUtilityService.getDepedentAgeLimit(memberFile.getGroupCode());
			if( limit !=null && years > limit.intValue())
			{
				errors.rejectValue(BIRTH_DATE,"", "EBIRTHDATEDEPINVALID");
			}
		}
	}
	
	/**
	 * @param memberFile
	 * @param errors
	 */
	private void validateCancelDate(MemberFile memberFile,Errors errors) 
	{
		if(memberFile.getCancelDate()==null || (memberFile.getCancelDate() != null && Constant.DEFULT_DATE_1900.after(memberFile.getCancelDate())) )
		{
			errors.rejectValue("cancelDate","", "ECANCELDATEREQD");
		}			
		
		LocalDate cancelDateLocal = new java.util.Date(memberFile.getCancelDate().getTime()).toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
		
		long months = ChronoUnit.MONTHS.between(cancelDateLocal, LocalDate.now().withDayOfMonth(LocalDate.now().getMonth().maxLength()));
		
		if(months > 3 || months < -3)
		{
			errors.rejectValue("cancelDate","", "ECANCELDATE3MONINVVALID");
		}
	}
	private LocalDate convertUtilToLocalDate(Date utilDate) {
		return new java.util.Date(utilDate.getTime()).toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
	}	
}
