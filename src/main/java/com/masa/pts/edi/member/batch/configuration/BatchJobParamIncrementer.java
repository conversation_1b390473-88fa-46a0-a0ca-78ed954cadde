package com.masa.pts.edi.member.batch.configuration;

import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.batch.core.JobParametersIncrementer;

public class BatchJobParamIncrementer implements JobParametersIncrementer {

	@Override
	public JobParameters getNext(JobParameters parameters) {
		return new JobParametersBuilder()
				.addLong("run.id", System.currentTimeMillis())
				.toJobParameters();
	}
}
