package com.masa.pts.edi.member.sci.batch.configuration;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobExecutionListener;

import com.masa.pts.edi.member.batch.configuration.BatchProcessResults;

public class SCIMemberBatchListener implements JobExecutionListener {
	
private static final Logger log = LoggerFactory.getLogger(SCIMemberBatchListener.class);
	
	BatchProcessResults batchProcessResults;
	
	public SCIMemberBatchListener(BatchProcessResults batchProcessResults) {
		this.batchProcessResults = batchProcessResults;
	}

	@Override
	public void beforeJob(JobExecution jobExecution) {
		log.info("Before Job SCIMemberBatchListener ,Clearing Batch results data");
		this.batchProcessResults.clear();
	}

	@Override
	public void afterJob(JobExecution jobExecution) {
		log.info("After Job SCIMemberBatchListener ,Clearing Batch results data");
		this.batchProcessResults.clear();
	}

}
