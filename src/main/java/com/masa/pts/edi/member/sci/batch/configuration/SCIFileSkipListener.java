package com.masa.pts.edi.member.sci.batch.configuration;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.listener.SkipListenerSupport;

import com.masa.pts.core.domain.MemberFile;
import com.masa.pts.core.domain.MemberFileRecordErrorEntity;
import com.masa.pts.core.domain.UploadStatus;
import com.masa.pts.core.service.MemberFileService;
import com.masa.pts.edi.member.batch.configuration.BatchProcessResults;
import com.masa.pts.edi.member.batch.configuration.EDIErrorConfig;

public class SCIFileSkipListener extends SkipListenerSupport<MemberFile, MemberFile> {

	private static final Logger log = LoggerFactory.getLogger(SCIFileSkipListener.class);
	
	private MemberFileService memberFileService;
	private JobExecution jobExecution;
	private EDIErrorConfig ediErrorConfig;
	private BatchProcessResults batchProcessResults;
	private String fileNameWithoutPath;

	public SCIFileSkipListener(MemberFileService memberFileService, JobExecution jobExecution,
			EDIErrorConfig ediErrorConfig
			,BatchProcessResults batchProcessResults
			,String fileNameWithoutPath
			) {
		this.memberFileService = memberFileService;
		this.jobExecution = jobExecution;
		this.ediErrorConfig = ediErrorConfig;
		this.batchProcessResults = batchProcessResults;
		this.fileNameWithoutPath = fileNameWithoutPath;
	}

	@Override
	public void onSkipInProcess(MemberFile item, Throwable t) {
		item.setRecordErrorSet(parseAndExtractErrors(t.getMessage(),item));
		item.setStatus(UploadStatus.SKIP);
		item.setGroupCode(Optional.ofNullable(item.getGroupCode()).orElse("SCI"));
		item.setProcessed(true);
		item.setJobExecutionId(this.jobExecution.getId());
		item.setSciFile(Boolean.TRUE);
		item.setSourceFileName(fileNameWithoutPath);
		batchProcessResults.addSkip(item);
		batchProcessResults.setJobExecutionId(this.jobExecution.getId());
		log.info("In SCIFileSkipListener , adding record to skip list..Contract # [{}] Group Code [{}] , Error [{}]",item.getContractNumber(),item.getGroupCode()
				,t.getMessage());
		
		try {
			memberFileService.saveMemberFile(item, Boolean.TRUE);
		} catch (Exception e) {
			log.error("Error in SCIFileSkipListener onSkipInProcess::Message [{}] Cause [{}]",e.getMessage(),e.getCause());
		}
	}

	private Set<MemberFileRecordErrorEntity> parseAndExtractErrors(String recordErrorCode, MemberFile record) {

		Set<MemberFileRecordErrorEntity> dataSet = new HashSet<>();

		if (recordErrorCode == null) {
			return Collections.emptySet();
		}
		if (recordErrorCode.contains(";")) {
			String[] data = recordErrorCode.split(";");

			dataSet = Arrays.asList(data).stream().map(errorCodeItem -> {
				return getEDIFileRecordError(errorCodeItem, record);
			}).collect(Collectors.toSet());
		} else {
			dataSet.add(getEDIFileRecordError(recordErrorCode, record));
		}
		return dataSet;
	}

	private MemberFileRecordErrorEntity getEDIFileRecordError(String recordErrorCode, MemberFile record) {
		return new MemberFileRecordErrorEntity(record, recordErrorCode,
				ediErrorConfig.getMessages().getOrDefault(recordErrorCode, recordErrorCode));
	}
}