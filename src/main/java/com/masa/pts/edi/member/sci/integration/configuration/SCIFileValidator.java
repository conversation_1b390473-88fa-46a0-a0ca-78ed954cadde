package com.masa.pts.edi.member.sci.integration.configuration;

import java.io.File;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;

import com.masa.pts.core.domain.MemberFile;
import com.masa.pts.core.domain.MemberFileRecordErrorEntity;
import com.masa.pts.core.domain.ReviewStatus;
import com.masa.pts.core.domain.UploadStatus;
import com.masa.pts.core.service.MemberFileService;
import com.masa.pts.edi.member.batch.configuration.EDIErrorConfig;

public class SCIFileValidator {

	private static final Logger log = LoggerFactory.getLogger(SCIFileValidator.class);
	private static final String FILE_STATUS = "FILE_STATUS";
	private static final String SOLD_DATE = "soldDate";
	private static final String SOLD_DATE_STATUS = "SOLD_DATE_STATUS";
	private static final String FILE_TYPE = "fileType";
	private static final String FILE_TYPE_STATUS = "FILE_TYPE_STATUS";
	private static final String JOB_NAME_PARAM = "JOB_NAME";
	private static final String ESOLDDATENOTPARSE = "ESOLDDATENOTPARSE";
	private static final String ESCIFILETYPENOTVALID = "ESCIFILETYPENOTVALID";

	private String soldDateFormat;
	private EDIErrorConfig ediErrorConfig;
	private MemberFileService memberFileService;

	public SCIFileValidator(String soldDateFormat, EDIErrorConfig ediErrorConfig, MemberFileService memberFileService) {
		this.soldDateFormat = soldDateFormat;
		this.ediErrorConfig = ediErrorConfig;
		this.memberFileService = memberFileService;
	}

	@ServiceActivator
	public Message<File> validateFile(Message<File> message) {

		log.info("Received File:[{}]", message.getPayload().getAbsolutePath());

		Map<String, String> paramMap = new HashMap<>();

		extractFileTypeFromFileName(message.getPayload().getName(), paramMap);
		extractSoldDateFromFileName(message.getPayload().getName(), paramMap);

		boolean fileStatus = Boolean.parseBoolean(paramMap.get(FILE_TYPE_STATUS))
				&& Boolean.parseBoolean(paramMap.get(SOLD_DATE_STATUS));

		paramMap.put(FILE_STATUS, String.valueOf(fileStatus));
		paramMap.put(JOB_NAME_PARAM, "sciMemberFileImport");

		if (!fileStatus) {
			saveFileFailStatus(message.getPayload().getName(), paramMap);
		}
		return MessageBuilder.withPayload(message.getPayload()).copyHeaders(message.getHeaders())
				.copyHeadersIfAbsent(paramMap).build();
	}

	private void saveFileFailStatus(String fileName, Map<String, String> paramMap) {

		MemberFile failedRec = new MemberFile();

		failedRec.setSourceFileName(fileName);
		failedRec.setStatus(UploadStatus.FAIL);
		failedRec.setCreatedDate(new java.util.Date());
		failedRec.setProcessed(Boolean.TRUE);
		failedRec.setSciFile(true);

		if (!Boolean.parseBoolean(paramMap.get(FILE_TYPE_STATUS))) {
			failedRec.addRecordErrorSet(new MemberFileRecordErrorEntity(failedRec, ESCIFILETYPENOTVALID,
					ediErrorConfig.getMessages().getOrDefault(ESCIFILETYPENOTVALID, ESCIFILETYPENOTVALID)));
		} else if (!Boolean.parseBoolean(paramMap.get(SOLD_DATE_STATUS))) {
			failedRec.addRecordErrorSet(new MemberFileRecordErrorEntity(failedRec, ESOLDDATENOTPARSE,
					ediErrorConfig.getMessages().getOrDefault(ESOLDDATENOTPARSE, ESOLDDATENOTPARSE)));
		}
		failedRec.setJobExecutionId(-1L); // no job triggered yet.
		failedRec.setReviewStatus(ReviewStatus.OPEN);

		memberFileService.saveMemberFile(failedRec, Boolean.TRUE);
	}

	// TU545-20200818-013008-N.csv
	// TU545-20200818-013008-C.csv
	private void extractSoldDateFromFileName(String filename, Map<String, String> paramMap) {
		Pattern pattern = Pattern.compile("(-\\d{6,8}-)");
		Matcher matcher = pattern.matcher(filename);
		if (matcher.find()) {
			String soldDateStr = matcher.group().replaceAll("-", "");
			SimpleDateFormat sdf = ((SimpleDateFormat) DateFormat.getInstance());
			try {
				sdf.applyPattern(soldDateFormat);
				sdf.parse(soldDateStr);
				paramMap.put(SOLD_DATE, soldDateStr);
				paramMap.put(SOLD_DATE_STATUS, String.valueOf(true));
			} catch (Exception e) {
				log.error("Error parsing sold date value [{}], Message [{}]", soldDateStr, e.getMessage());
				paramMap.put(SOLD_DATE_STATUS, String.valueOf(false));
			}
		}
	}

	// TU545-20200818-013008-N.csv
	// TU545-20200818-013008-C.csv
	private void extractFileTypeFromFileName(String filename, Map<String, String> paramMap) {
		Pattern patternAdd = Pattern.compile(".*-N.csv$", Pattern.CASE_INSENSITIVE);
		Pattern patternCancel = Pattern.compile(".*-C.csv$", Pattern.CASE_INSENSITIVE);
		if (patternAdd.matcher(filename).matches()) {
			paramMap.put(FILE_TYPE, "ADD");
			paramMap.put(FILE_TYPE_STATUS, String.valueOf(true));
		} else if (patternCancel.matcher(filename).matches()) {
			paramMap.put(FILE_TYPE, "CANCEL");
			paramMap.put(FILE_TYPE_STATUS, String.valueOf(true));
		} else {
			paramMap.put(FILE_TYPE_STATUS, String.valueOf(false));
		}
	}

}
