package com.masa.pts.edi.member.integration.configuration;

import java.io.File;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.launch.support.SimpleJobLauncher;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.integration.launch.JobLaunchingGateway;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.SyncTaskExecutor;
import org.springframework.integration.config.EnableIntegration;
import org.springframework.integration.dsl.IntegrationFlow;
import org.springframework.integration.dsl.IntegrationFlows;
import org.springframework.integration.dsl.Pollers;
import org.springframework.integration.file.DirectoryScanner;
import org.springframework.integration.file.FileNameGenerator;
import org.springframework.integration.file.FileWritingMessageHandler;
import org.springframework.integration.file.RecursiveDirectoryScanner;
import org.springframework.integration.file.dsl.Files;
import org.springframework.integration.file.filters.AcceptOnceFileListFilter;
import org.springframework.integration.file.filters.CompositeFileListFilter;
import org.springframework.integration.file.filters.FileSystemPersistentAcceptOnceFileListFilter;
import org.springframework.integration.file.filters.SimplePatternFileListFilter;
import org.springframework.integration.handler.LoggingHandler;
import org.springframework.integration.metadata.ConcurrentMetadataStore;
import org.springframework.integration.metadata.PropertiesPersistingMetadataStore;
import org.springframework.integration.sftp.dsl.Sftp;
import org.springframework.integration.sftp.session.DefaultSftpSessionFactory;
import org.springframework.messaging.MessageHandler;

import com.masa.pts.core.repository.FileNameToGroupRepository;
import com.masa.pts.core.service.MemberFileService;
import com.masa.pts.edi.member.batch.configuration.EDIErrorConfig;
import com.masa.pts.edi.member.sci.filter.CustomLastModifiedFileListFilter;
import com.masa.pts.edi.member.sci.integration.configuration.SCIFileValidator;

@Configuration
@EnableIntegration
public class FilePollingIntegrationFlow {
	
	@Autowired
	MemberFileService memberFileService;
	
	@Autowired
	private List<? extends Job> jobs;
	
	@Value("#{T(java.time.LocalDateTime).parse('${pts.scifile.acceptFilesModifiedAfter}')}")
	private LocalDateTime acceptFilesAfterDate;
	
	@Bean
	public IntegrationFlow ediFileIntegrationFlow(DirectoryScanner directoryScanner
			,File inboundReadDirectory,
			@Value("${pts.edifile.soldDate.format}")String soldDateFormat
			,FileNameToGroupRepository fileNameToGroupRepository
			,File inboundProcessedDirectory
			,File inboundFailedDirectory
			,JobLaunchingGateway jobLaunchingGateway
			,EDIErrorConfig ediErrorConfig) {		
		return IntegrationFlows
				.from(Files.inboundAdapter(inboundReadDirectory).scanner(directoryScanner),   
						c -> c.poller(Pollers.fixedDelay(1000) .maxMessagesPerPoll(-1)))
				.handle(validateFileAndExtractParams(soldDateFormat,fileNameToGroupRepository,memberFileService,ediErrorConfig))
				.channel("to-bridge")
				.route("headers." + "FILE_STATUS",
						routerSpec -> 
						routerSpec.subFlowMapping(true, f -> {
										f.handle(fileMessageToJobRequest(jobs,soldDateFormat));
										f.handle(jobLaunchingGateway);
										f.handle(jobProcessToFileMessage());
										f.handle(fileMoveMessageHandler(null,null));
									})
									.subFlowMapping(false, f -> f.handle(failFileMoveMessageHandler(null,null)))
					)					
				.log(LoggingHandler.Level.INFO, "headers.id + ': ' + payload")
				.get();
	}
	
	@Bean
    public DirectoryScanner directoryScanner() {
        DirectoryScanner scanner = new RecursiveDirectoryScanner();
        CompositeFileListFilter<File> filter = new CompositeFileListFilter<>(
                Arrays.asList(new AcceptOnceFileListFilter<>(), new SimplePatternFileListFilter("*.csv"))                		
        );
        scanner.setFilter(filter);
        return scanner;
    }
		
	@Bean
	public MessageHandler fileMoveMessageHandler(File inboundProcessedDirectory,FileNameGenerator outboundFileNameGenerator ) {
		FileWritingMessageHandler handler = new FileWritingMessageHandler(inboundProcessedDirectory);
		handler.setFileNameGenerator(outboundFileNameGenerator);
		handler.setDeleteSourceFiles(true);
		return handler;
	}
	
	@Bean
	public MessageHandler failFileMoveMessageHandler(File inboundFailedDirectory,FileNameGenerator outboundFileNameGenerator ) {
		String path = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
		File currentDateFolder = new File(inboundFailedDirectory.getPath().concat(File.separator).concat(path));
		currentDateFolder.mkdirs();
		FileWritingMessageHandler handler = new FileWritingMessageHandler(currentDateFolder);
		handler.setFileNameGenerator(outboundFileNameGenerator);
		handler.setDeleteSourceFiles(true);
		return handler;
	}
	
	@Bean
    public FileNameGenerator outboundFileNameGenerator(@Value("${pts.edifile.fileOutput.dateFormat}") String dateFormat) {
        return message -> {
        		 String fileName = (String)message.getHeaders().get("file_name");        		 
        		 int indexOfDot = fileName.lastIndexOf('.');
		    	 String fileExtn = fileName.substring(indexOfDot+1);
		    	 String fileNameWithoutExtn = fileName.substring(0,indexOfDot);
        		return fileNameWithoutExtn+"_"+DateTimeFormatter.ofPattern(dateFormat).format(LocalDateTime.now())+"."+fileExtn;
        	};
        }
	
	
	@Bean
	public FileMessageToJobRequest fileMessageToJobRequest(List<? extends Job> jobs,String soldDateFormat) {
	    return new FileMessageToJobRequest(jobs,"fileName",soldDateFormat);
	}
	
	@Bean
	public EDIFileValidator validateFileAndExtractParams(String soldDateFormat,FileNameToGroupRepository fileNameToGroupRepository
			,MemberFileService memberFileService,EDIErrorConfig ediErrorConfig) {
		return new EDIFileValidator(soldDateFormat,fileNameToGroupRepository,memberFileService,ediErrorConfig);
	}
	
	public JobProcessToFileMessage jobProcessToFileMessage() {
	    return new JobProcessToFileMessage();	    
	}
	
	@Bean
	public JobLaunchingGateway jobLaunchingGateway(JobRepository jobRepository) {
	    SimpleJobLauncher simpleJobLauncher = new SimpleJobLauncher();
	    simpleJobLauncher.setJobRepository(jobRepository);
	    simpleJobLauncher.setTaskExecutor(new SyncTaskExecutor());
	    return new JobLaunchingGateway(simpleJobLauncher);	    
	}
	
	@Bean
	public DefaultSftpSessionFactory sftpSessionFactory(@Value("${pts.scifile.host}") String host,
														@Value("${pts.scifile.port}") int port, @Value("${pts.scifile.user}") String user,
														@Value("${pts.scifile.password}") String password) {
		DefaultSftpSessionFactory factory = new DefaultSftpSessionFactory();
		factory.setHost(host);
		factory.setPort(port);
		factory.setUser(user);
		factory.setPassword(password);
		factory.setAllowUnknownKeys(true);
		return factory;
	}
	
	@Bean
	public IntegrationFlow getFilesFromSCI(@Value("${pts.scifile.cron}") String sciBatchCron,
			@Value("${pts.scifile.inbound}") String localDirectory
			,@Value("${pts.edifile.soldDate.format}")String soldDateFormat
			,EDIErrorConfig ediErrorConfig
			,Job sciMemberFileImport
			) {
		
		return IntegrationFlows
				.from(
						Sftp.inboundAdapter(sftpSessionFactory(null, 0, null, null))
						.preserveTimestamp(true)
						.remoteDirectory("/")
						.maxFetchSize(-1)						
						.localFilter(new FileSystemPersistentAcceptOnceFileListFilter(getMetadataStore(null),"SCI"))
						.filter(new CustomLastModifiedFileListFilter(acceptFilesAfterDate))						
						.localDirectory(new File(localDirectory))
						, 
						e -> e.id("sciSftpInboundAdapter")
							  .autoStartup(true)
							  .poller(p -> p.cron(sciBatchCron).maxMessagesPerPoll(-1)))
				.log()
				.handle(new  SCIFileValidator(soldDateFormat,ediErrorConfig,memberFileService))
				.channel("to-bridge")//merge to ediFileIntegrationFlow
				.get();
	}
	
	@Bean
	public ConcurrentMetadataStore getMetadataStore(@Value("${pts.scifile.folder}") String storefolderPath) {
		PropertiesPersistingMetadataStore store = new PropertiesPersistingMetadataStore();
		store.setBaseDirectory(storefolderPath);
		store.setFileName("DO_NOT_DELETE.properties");
		store.flush();
		return store;
	}	
}
