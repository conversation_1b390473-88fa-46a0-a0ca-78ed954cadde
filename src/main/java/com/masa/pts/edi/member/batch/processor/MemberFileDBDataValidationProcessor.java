package com.masa.pts.edi.member.batch.processor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.validator.ValidatingItemProcessor;

import com.masa.pts.core.domain.MemberFile;

public class MemberFileDBDataValidationProcessor extends ValidatingItemProcessor<MemberFile> {
	private static final Logger log = LoggerFactory.getLogger(MemberFileDBDataValidationProcessor.class);

	@Override
	public MemberFile process(MemberFile input)  {
		log.info("In MemberFileDBDataValidationProcessor Item Processor , Process method");
		
		input = super.process(input);

		if (input == null) {
			log.info("In process method of MemberFileDBDataValidationProcessor , MemberFile input after validation is NULL");
			return null;
		}
		
		return input;
	}
}
