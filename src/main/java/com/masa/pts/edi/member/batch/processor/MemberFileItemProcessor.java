package com.masa.pts.edi.member.batch.processor;

import java.util.Date;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.validator.ValidationException;
import org.springframework.core.env.Environment;
import org.springframework.validation.BindingResult;
import org.springframework.validation.DataBinder;
import org.springframework.validation.ObjectError;
import org.springframework.validation.Validator;

import com.masa.pts.core.domain.Agent;
import com.masa.pts.core.domain.MemberFile;
import com.masa.pts.core.domain.Product;
import com.masa.pts.core.service.AgentService;
import com.masa.pts.core.service.MemberFileService;

public class MemberFileItemProcessor implements ItemProcessor<MemberFile, MemberFile> {

	private static final Logger log = LoggerFactory.getLogger(MemberFileItemProcessor.class);
	private String fileNameWithoutPath;
	private final Environment environment;
	private Validator validator;
	private MemberFileService memberFileService;
	private AgentService agentService;
	private String groupCode;
	private Date soldDate;
	private String agentNum;

	public MemberFileItemProcessor(Environment environment,  String fileNameWithoutPath, MemberFileService memberFileService, AgentService agentService, String groupCode, Date soldDate,
			String agentNum) {
		this.environment = environment;
		this.fileNameWithoutPath = fileNameWithoutPath;
		this.memberFileService = memberFileService;
		this.agentService = agentService;
		this.groupCode = groupCode;
		this.soldDate = soldDate;
		this.agentNum = agentNum;
	}

	/**
	 *
	 */
	public MemberFile process(MemberFile input) {

		log.info("Processing EDI File member in ItemProcessor");
		input.setGroupCode(groupCode);//set before validation, need group code for dependent age validation.
		BindingResult results = bindAndValidate(input);// Throws ValidationException is validation fails.
		if (results.hasErrors())
			buildValidationException(results);

		try {
			if (!input.getBenefitCity().isEmpty() && !input.getBenefitState().isEmpty()) {
				input.setBenefitCountry(environment.getProperty("pts.edifile.address.country.default"));
			}
			if (!input.getMailingCity().isEmpty() && !input.getMailingState().isEmpty()) {
				input.setMailingCountry(environment.getProperty("pts.edifile.address.country.default"));
			}									
			input.setProcessed(false);
			input.setGroupCode(groupCode);
			input.setAgentCode(agentNum);
			input.setSoldDate(soldDate);
			input.setSourceFileName(fileNameWithoutPath);			
			
			if(!memberFileService.isValidGroup(groupCode))
			{
				throw new ValidationException("EGROUPINVALID");
			}			
			if(!memberFileService.isGroupValidAndActive(groupCode)) {
				throw new ValidationException("EGROUPINACTIVE");
			}
			boolean isCancelAction = "Cancel".equalsIgnoreCase(input.getModification());
			if(!isCancelAction) {
				Optional<Product> optProduct = memberFileService.getProductByProductTypeAndPaymentTypeAndGroupCode(
						input.getProductType(), input.getPaymentCode(), input.getSoldDate(), groupCode);

				if (optProduct.isPresent()) {
					Product prd = optProduct.get();
					input.setProductId(prd.getProductId());
					input.setProductName(prd.getName());
					input.setProductCategoryCode(prd.getProductCategory().getCode());
				} else {
					throw new ValidationException("EPRODUCTINVALIDFORPYMT");
				}
				Agent agent = memberFileService.getAgentByAgentNum(agentNum);
				if (agent != null) {
					input.setAgentCode(agent.getAgentNum());
					input.setAgentId(agent.getAgentId());
				} else {
					throw new ValidationException("EAGENTNOTSETUPGRP");
				}

				if (!agentService.isAgentCommissionSetupForProduct(agent.getAgentId(),
						optProduct.get().getProductId())) {
					throw new ValidationException("EAGENTCOMMNOTSETUP");
				}

				if (!agentService.isAgentHierarchySetupForProduct(agent.getAgentNum(),
						optProduct.get().getProductId())) {
					throw new ValidationException("EAGENTHIERNOTSETUP");
				}
			}			
		} catch (Exception e) {
			log.error("Error in Record Processor::Message [{}], Cause [{}]",e.getMessage(),e.getCause());
			throw new ValidationException(e.getMessage());
		}
		return input;
	}
	
	/**
	 * @param item
	 * @return
	 */
	private BindingResult bindAndValidate(MemberFile item) {
		DataBinder binder = new DataBinder(item);
		binder.addValidators(validator);
		binder.validate();
		return binder.getBindingResult();
	}

	/**
	 * @param results
	 */
	private void buildValidationException(BindingResult results) {
		StringBuilder msg = new StringBuilder();
		for (ObjectError error : results.getAllErrors()) {
			msg.append(error.getDefaultMessage()).append(";");
		}
		throw new ValidationException(msg.toString());
	}

	public void setValidator(Validator validator) {
		this.validator = validator;
	}
}