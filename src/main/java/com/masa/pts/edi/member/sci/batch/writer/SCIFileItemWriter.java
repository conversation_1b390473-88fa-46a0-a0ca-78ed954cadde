package com.masa.pts.edi.member.sci.batch.writer;

import java.util.List;

import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.annotation.BeforeStep;
import org.springframework.batch.item.ItemWriter;

import com.masa.pts.core.domain.MemberFile;
import com.masa.pts.core.domain.UploadStatus;
import com.masa.pts.core.service.MemberFileService;
import com.masa.pts.edi.member.batch.configuration.BatchProcessResults;

public class SCIFileItemWriter implements ItemWriter<MemberFile> {

	private final MemberFileService memberFileService;
	private JobExecution jobExecution;
	private BatchProcessResults batchProcessResults;
	
	
	public SCIFileItemWriter(MemberFileService memberFileService
			,BatchProcessResults batchProcessResults) {
		this.memberFileService = memberFileService;
		this.batchProcessResults = batchProcessResults;
	}

	@BeforeStep
    public void beforeStep(StepExecution stepExecution) {
        jobExecution = stepExecution.getJobExecution();
    }
	
	@Override
	public void write(List<? extends MemberFile> items) throws Exception {
		items.forEach(memberFileItem -> {
			memberFileItem.setJobExecutionId(jobExecution.getId());
			memberFileItem.setStatus(UploadStatus.PROCESSED);
			memberFileItem.setSciFile(Boolean.TRUE);
			memberFileService.saveMemberFile(memberFileItem,Boolean.FALSE);
			batchProcessResults.addProcessed(memberFileItem);
			batchProcessResults.setJobExecutionId(jobExecution.getId());
		});
	}
}
