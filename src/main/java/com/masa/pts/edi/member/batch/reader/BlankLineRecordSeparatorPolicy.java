/**
 * 
 */
package com.masa.pts.edi.member.batch.reader;

import org.springframework.batch.item.file.separator.SimpleRecordSeparatorPolicy;

/**
 * <AUTHOR>
 *
 */
public class BlankLineRecordSeparatorPolicy extends SimpleRecordSeparatorPolicy {

	@Override
	public boolean isEndOfRecord(String line) {
		String tmpLine = null;
		tmpLine = line.replace(",", "").trim();

		if (tmpLine.trim().length() == 0)
			line = tmpLine;

		return tmpLine.trim().length() != 0 && super.isEndOfRecord(line);
	}

	@Override
	public String postProcess(String record) {
		String tmpLine = null;
		tmpLine = record.replace(",", "").trim();

		if (tmpLine == null || tmpLine.trim().length() == 0) {
			return null;
		}
		return super.postProcess(record);
	}

}
