package com.masa.pts.edi.member.batch.configuration;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobExecutionListener;

public class StagingTableToPTSJobListener implements JobExecutionListener {

	private static final Logger log = LoggerFactory.getLogger(StagingTableToPTSJobListener.class);
	
	private BatchProcessResults batchProcessResults;
	
	public StagingTableToPTSJobListener(BatchProcessResults batchProcessResults) {
		this.batchProcessResults = batchProcessResults;
	}

	@Override
	public void beforeJob(JobExecution jobExecution) {
		log.info("Before job StagingTableToPTS, Batchprocessresults [{}]",batchProcessResults);
	}

	@Override
	public void afterJob(JobExecution jobExecution) {
		log.info("After job StagingTableToPTS, Batchprocessresults [{}]",batchProcessResults);
	}
}
