package com.masa.pts.edi.member.sci.batch.validator;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import org.springframework.batch.item.validator.ValidationException;
import org.springframework.util.StringUtils;
import org.springframework.validation.Errors;
import org.springframework.validation.Validator;

import com.masa.pts.core.domain.Constant;
import com.masa.pts.core.domain.Country;
import com.masa.pts.core.domain.MemberFile;
import com.masa.pts.core.service.AddressService;
import com.masa.pts.core.service.PTSUtilityService;

public class SCIFileDataValidator implements Validator {

	private static final String BENEFIT_ADDR = "benefitAddress";
	private static final String BIRTH_DATE = "birthDate";
	
	private final AddressService addressService;
	
	private final Map<String, String> SCI_COUNTRY_MAP = new HashMap<String, String>() {{
        put("US", "United States");
        put("Canadian", "Canada");
        put("United States", "United States");
        put("Canada", "Canada");
    }};
	
	public SCIFileDataValidator(AddressService addressService
			) {
		this.addressService = addressService;
	}

	@Override
	public boolean supports(Class<?> clazz) {
		return MemberFile.class.equals(clazz);
	}

	@Override
	public void validate(Object target, Errors errors) {
		MemberFile memberFile = (MemberFile)target;		
		validateForAddAndCancel(memberFile,errors);
		if("ADD".equalsIgnoreCase(memberFile.getModification())){
			validateEffectiveDate(memberFile, errors);
			validateProductType(memberFile,errors);
			validateAddress(memberFile,errors);
			validateBirthDate(memberFile,errors);			
		}		
		if("CANCEL".equalsIgnoreCase(memberFile.getModification())){
			validateCancelDate(memberFile,errors);
		}
		validateAddressStateAndCountry(memberFile,errors);
	}
	
	private void validateAddressStateAndCountry(MemberFile memberFile, Errors errors) {
		String countryName = StringUtils.hasText(memberFile.getBenefitCountry()) ? memberFile.getBenefitCountry() : "United States";
		countryName = SCI_COUNTRY_MAP.getOrDefault(countryName,null);
		Optional<Country> optCountry = Optional.ofNullable(addressService.getCountryByCode(countryName));
		
		if(optCountry.isPresent() && 
				StringUtils.hasText(memberFile.getBenefitState()) && 
				!addressService.existsByStateAndCountry(memberFile.getBenefitState(),optCountry.get().getCountryId())){
				errors.rejectValue(BENEFIT_ADDR,"", "EBENEFITSTATEINVALID");			
		}
		else if(!optCountry.isPresent()) {
			errors.rejectValue(BENEFIT_ADDR,"", "EBENEFITCNTRYINVALID");//
		}
		//set name as United States or Canada instead of US/Canadian
		optCountry.ifPresent(cntry-> memberFile.setBenefitCountry(cntry.getName()));
	}

	private void validateProductType(MemberFile memberFile, Errors errors) {
		
		if(!StringUtils.hasText(memberFile.getProductType())){
			errors.rejectValue("productType","","EPRODUCTTYPEREQD");
		}
	}
	
	private void validateEffectiveDate(MemberFile memberFile, Errors errors) {
		if ((memberFile.getEffectiveDate() == null || (memberFile.getEffectiveDate() != null
				&& Constant.DEFULT_DATE_1900.after(memberFile.getEffectiveDate())))) {
			errors.rejectValue("effectiveDate", "", "EEFFDATEREQUIRED");
		}
		long months = ChronoUnit.MONTHS.between(PTSUtilityService.convertUtilToLocalDate(memberFile.getEffectiveDate()),
				LocalDate.now());
		
		if (months > Constant.MONTHS_RANGE_FOR_ENROLL) {
			throw new ValidationException("EEFFDATE3MONTHINVALID");
		}
	}
	private void validateCancelDate(MemberFile memberFile,Errors errors){
		if(memberFile.getCancelDate()==null || (memberFile.getCancelDate() != null && Constant.DEFULT_DATE_1900.after(memberFile.getCancelDate())) ){
			memberFile.setCancelDate(new Date());//use current date if file date is blank.
		}
	}
	private void validateBirthDate(MemberFile memberFile,Errors errors){
		if(memberFile.getBirthDate()==null) {
			errors.rejectValue(BIRTH_DATE,"","EBIRTHDATEREQD");
		}
		if(memberFile.getBirthDate() != null ){
			LocalDate birthDateLocal = PTSUtilityService.convertUtilToLocalDate(memberFile.getBirthDate());
			if(LocalDate.now().isBefore(birthDateLocal) || LocalDate.now().isEqual(birthDateLocal)){
				errors.rejectValue(BIRTH_DATE,"", "EBIRTHDATEINVALIDF");
			}
		}
	}
	private void validateAddress(MemberFile memberFile, Errors errors){
		if(!StringUtils.hasText(memberFile.getBenefitAddress())){
			errors.rejectValue(BENEFIT_ADDR,"","EBENEFITADDRREQD");
		}
		if(!StringUtils.hasText(memberFile.getBenefitCity())){
			errors.rejectValue(BENEFIT_ADDR,"","EBENEFITCITYREQD");
		}
		if(!StringUtils.hasText(memberFile.getBenefitState())){
			errors.rejectValue(BENEFIT_ADDR,"","EBENEFITSTATEREQD");
		}
		if(!StringUtils.hasText(memberFile.getBenefitZip())){
			errors.rejectValue(BENEFIT_ADDR,"","EBENEFITZIPREQD");
		}		
		if(!StringUtils.hasText(memberFile.getBenefitCountry())){
			errors.rejectValue(BENEFIT_ADDR,"","EBENEFITCNTRYREQD");
		}
	}
	
	private void validateForAddAndCancel(MemberFile memberFile, Errors errors) {
		
		if(!StringUtils.hasText(memberFile.getContractNumber())){
			errors.rejectValue("contractNumber","","EEMPLOYEEIDREQD");
		}
		if(!StringUtils.hasText(memberFile.getFirstName())){
			errors.rejectValue("firstName","","EFIRSTNAMEREQD");
		}
		if(!StringUtils.hasText(memberFile.getLastName())){
			errors.rejectValue("lastName","","ELASTNAMEREQD");
		}
	}	
}
