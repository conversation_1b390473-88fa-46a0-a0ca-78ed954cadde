package com.masa.pts.edi.member.batch.configuration;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.file.LineCallbackHandler;
import org.springframework.batch.item.file.transform.DelimitedLineTokenizer;

public class MemberFileHeaderHandler implements LineCallbackHandler {

	private static final Logger log = LoggerFactory.getLogger(MemberFileHeaderHandler.class);

	DelimitedLineTokenizer delimitedLineTokenizer;
	
	public MemberFileHeaderHandler(DelimitedLineTokenizer delimitedLineTokenizer) {
		this.delimitedLineTokenizer = delimitedLineTokenizer;
	}
	
	@Override
	public void handleLine(String line) {
		log.info("In Member File Header Handler , Header record from file::[{}]",line);
		String [] header = line.split(",");		
		this.delimitedLineTokenizer.setNames(header);
	}

}
