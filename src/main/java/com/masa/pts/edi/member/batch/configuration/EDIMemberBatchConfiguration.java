package com.masa.pts.edi.member.batch.configuration;

import java.beans.PropertyEditor;
import java.io.File;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.validation.ValidationException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.ItemReadListener;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.EnableBatchProcessing;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.step.skip.AlwaysSkipItemSkipPolicy;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemWriter;
import org.springframework.batch.item.file.FlatFileItemReader;
import org.springframework.batch.item.file.FlatFileItemWriter;
import org.springframework.batch.item.file.mapping.BeanWrapperFieldSetMapper;
import org.springframework.batch.item.file.mapping.DefaultLineMapper;
import org.springframework.batch.item.file.transform.BeanWrapperFieldExtractor;
import org.springframework.batch.item.file.transform.DelimitedLineTokenizer;
import org.springframework.batch.item.file.transform.FormatterLineAggregator;
import org.springframework.batch.item.support.CompositeItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.core.io.FileSystemResource;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.validation.beanvalidation.SpringValidatorAdapter;

import com.masa.pts.core.domain.MemberFile;
import com.masa.pts.core.service.AddressService;
import com.masa.pts.core.service.AgentService;
import com.masa.pts.core.service.DependentUtilityService;
import com.masa.pts.core.service.ExcelExportUtilityService;
import com.masa.pts.core.service.GroupService;
import com.masa.pts.core.service.MemberAuditEventService;
import com.masa.pts.core.service.MemberEmployeeIdService;
import com.masa.pts.core.service.MemberFileService;
import com.masa.pts.core.service.MemberService;
import com.masa.pts.core.service.PTSUserService;
import com.masa.pts.core.service.ProductService;
import com.masa.pts.edi.member.batch.processor.MemberFileDBDataValidationProcessor;
import com.masa.pts.edi.member.batch.processor.MemberFileItemProcessor;
import com.masa.pts.edi.member.batch.reader.BlankLineRecordSeparatorPolicy;
import com.masa.pts.edi.member.batch.reader.MemberFileItemReadListener;
import com.masa.pts.edi.member.batch.validator.MemberFileDBDataValidator;
import com.masa.pts.edi.member.batch.validator.MemberFileDataValidator;
import com.masa.pts.edi.member.batch.writer.GenerateUploadStatusReport;
import com.masa.pts.edi.member.batch.writer.MemberFileItemWriter;
import com.masa.pts.edi.member.report.service.EDIFileProcessReportService;

@Configuration
@EnableBatchProcessing
public class EDIMemberBatchConfiguration {
	
	private static final Logger log = LoggerFactory.getLogger(EDIMemberBatchConfiguration.class);
	
	@Autowired
	private JobBuilderFactory jobBuilderFactory;

	@Autowired
	private StepBuilderFactory stepBuilderFactory;
	
	@Autowired
	private Environment environment;
	
	@Autowired
	AgentService agentService;
	
	@Autowired
	MemberService memberService;	

	@Autowired
	AddressService addressService;
	
	@Autowired
	PTSUserService ptsUserService;
	
	@Autowired
	PlatformTransactionManager transactionManager;
	
	@Autowired
	MemberAuditEventService memberAuditEventService;
	
	@Autowired
	MemberFileService memberFileService;
	
	@Autowired
	BatchProcessResults batchProcessResults;
	
	@Bean
	protected Job ediMemberFileImport() {
		return jobBuilderFactory
				.get("ediMemberFileImport")
				.incrementer(new BatchJobParamIncrementer())
				.listener(new EDIMemberBatchListener(batchProcessResults))
				.start(ediMemberFileToStagingTable())
				.on("*")
				.to(startStagingTableToPTS())				
				.on("*")
				.to(generateReport(null,null,null,null))
				.end()
				.build();
	}

	@Bean
	protected Step startStagingTableToPTS( ) {
		return stepBuilderFactory.get("startStagingTableToPTS")
					.allowStartIfComplete(true)
					.job(stagingTableToPTS())
					.listener(new EDIStagingTableToPTSListener())
					.build();					
	}

	@Bean
	protected Job stagingTableToPTS() {
		return jobBuilderFactory
				.get("stagingTableToPTS")
				.incrementer(new BatchJobParamIncrementer())
				.listener(new StagingTableToPTSJobListener(batchProcessResults))
				.start(processMembersToPTS(null))
				.build();
				
	}
	
	private Step ediMemberFileToStagingTable() {
		return stepBuilderFactory.get("ediMemberFileToStagingTable")
				.<MemberFile,MemberFile>chunk(10)
				.reader(fileReader(null,null,null,null,null))				
				.listener(iteamReaderListener(null,null,null))
				.processor(compositeProcessor(null))
				.writer(writeToStagingTable())				
				.faultTolerant()
				.skipPolicy(new AlwaysSkipItemSkipPolicy())
				.listener(memberFileSkipListener(null,null,null,null,null,null))
				.noRollback(ValidationException.class)
				.noRetry(ValidationException.class)
				.stream(skipItemFlatFileStream(null, null, null,null))				
				.build();
	}	
	
	private ItemWriter<? super MemberFile> writeToStagingTable() {
		return new MemberFileItemWriter(memberFileService,batchProcessResults); 
	}

	@Bean
	@StepScope
	protected FlatFileItemReader<MemberFile> fileReader(@Value("#{jobParameters['fileName']}") String fileResource,
			@Qualifier("delimitedLineTokenizer") DelimitedLineTokenizer delimitedLineTokenizer,
			@Qualifier("memberFileHeaderHandler") MemberFileHeaderHandler memberFileHeaderHandler
			,EDIFileColumnNameConfig ediConfig
			,@Value("#{jobParameters['groupCode']}") String groupCode
			) {

		log.info("In FlatFile Item Reader :::FileName::[{}].",fileResource);	

		String dateFormat = environment.getProperty("pts.edifile.birthdate.format","MM/dd/yyyy");
		
		DefaultLineMapper<MemberFile> defaultLineMapper = new DefaultLineMapper<>();
		defaultLineMapper.setLineTokenizer(delimitedLineTokenizer);

		BeanWrapperFieldSetMapper<MemberFile> beanMapper = new BeanWrapperFieldSetMapper<>();
		beanMapper.setTargetType(MemberFile.class);
		Map<Class<java.util.Date>, PropertyEditor> customEditors = new HashMap<>();
		CustomDateEditor customDateEditor = new CustomDateEditor(new SimpleDateFormat(dateFormat), true);
		customEditors.put(java.util.Date.class, customDateEditor);
		beanMapper.setCustomEditors(customEditors);
		
		MemberFileFieldSetMapper fieldSetMapper = new MemberFileFieldSetMapper(ediConfig);
		defaultLineMapper.setFieldSetMapper(fieldSetMapper);		
		defaultLineMapper.afterPropertiesSet();

		FlatFileItemReader<MemberFile> reader = new FlatFileItemReader<>();
	
		reader.setSkippedLinesCallback(memberFileHeaderHandler);
		reader.setLineMapper(defaultLineMapper);
		reader.setLinesToSkip(1);
		reader.setResource(new FileSystemResource(fileResource));
		reader.setEncoding("UTF-8");
		reader.setRecordSeparatorPolicy(new BlankLineRecordSeparatorPolicy());		
		try {
			reader.afterPropertiesSet();
		}
		catch (Exception e) {
			log.error("Error in Flat File Item Reader::Message [{}], Cause [{}]",e.getMessage(),e.getCause());
		}
		batchProcessResults.setGroupCode(groupCode);
		return reader;
	}
		
	@Bean
	@StepScope
	protected ItemReadListener<MemberFile> iteamReaderListener(@Value("#{jobParameters['groupCode']}") String groupCode
			,@Value("#{jobParameters['fileNameWithoutPath']}") String fileNameWithoutPath
			,EDIErrorConfig ediErrorConfig
			) {		
		batchProcessResults.setGroupCode(groupCode);
		batchProcessResults.setFileName(fileNameWithoutPath);
		return new MemberFileItemReadListener(batchProcessResults,groupCode,fileNameWithoutPath,ediErrorConfig,memberFileService);		
	}
	
	@Bean
	@StepScope
	protected ItemProcessor<MemberFile, MemberFile> compositeProcessor(@Value("#{jobParameters['fileName']}") String fileResource) {
		CompositeItemProcessor<MemberFile, MemberFile> compositeProcessor = new CompositeItemProcessor<>();
		compositeProcessor.setDelegates(Arrays.asList(memberFileProcessor(null,null,null,null,null,null,null,null,null),memberFileDBDataValidationProcessor(null,null,null)));
		return compositeProcessor;
	}
	
	@Bean
	@StepScope
	protected ItemProcessor<MemberFile, MemberFile> memberFileProcessor(@Value("#{jobParameters['fileName']}") String fileResource,
			@Value("#{jobParameters['fileNameWithoutPath']}") String fileNameWithoutPath,SpringValidatorAdapter springValidatorAdapter,AddressService addressService
			,MemberFileService memberFileService,@Value("#{jobParameters['groupCode']}") String groupCode,@Value("#{jobParameters['soldDate']}") Date soldDate
			,@Value("#{jobParameters['agentCode']}") String agentCode
			,DependentUtilityService dependentUtilityService
			) {
		
		log.info("In Item Processor, Processing file::[{}] [{}]",fileResource,fileNameWithoutPath);
		
		batchProcessResults.setGroupCode(groupCode);
		batchProcessResults.setFileName(fileNameWithoutPath);
		
		MemberFileItemProcessor processor = new MemberFileItemProcessor(environment,fileNameWithoutPath,memberFileService,agentService,groupCode,soldDate,agentCode);
		MemberFileDataValidator memberFileDataValidator = new MemberFileDataValidator(springValidatorAdapter,addressService,dependentUtilityService);
		processor.setValidator(memberFileDataValidator);
		return processor;
	}
	
	
	@Bean
	@StepScope
	protected ItemProcessor<MemberFile, MemberFile> memberFileDBDataValidationProcessor(ProductService productService,GroupService groupService,MemberEmployeeIdService memberEmployeeIdService){
		MemberFileDBDataValidationProcessor processor = new MemberFileDBDataValidationProcessor();
		MemberFileDBDataValidator validator = new MemberFileDBDataValidator(environment,memberService,agentService,productService,groupService,memberEmployeeIdService);
		processor.setValidator(validator);
		processor.setFilter(false);
		return processor;
	}
	
	@Bean
	@StepScope
	protected MemberFileSkipListener memberFileSkipListener(@Qualifier("skipItemFlatFileStream") FlatFileItemWriter<MemberFile> skipItemFileItemWriter,
			@Value("#{jobParameters['groupCode']}") String groupCode,@Value("#{jobParameters['soldDate']}") Date soldDate
			,@Value("#{jobParameters['fileNameWithoutPath']}") String fileNameWithoutPath,@Value("#{stepExecution.jobExecution}") JobExecution jobExecution
			,EDIErrorConfig ediErrorConfig
			) {
		batchProcessResults.setGroupCode(groupCode);
		batchProcessResults.setFileName(fileNameWithoutPath);
		return new MemberFileSkipListener(skipItemFileItemWriter,batchProcessResults,groupCode,soldDate,fileNameWithoutPath,jobExecution,ediErrorConfig,memberFileService);		
	}
	

	@Bean(name = "memberFileHeaderHandler")
	@StepScope
	protected MemberFileHeaderHandler memberFileHeaderHandler(@Qualifier("delimitedLineTokenizer") DelimitedLineTokenizer delimitedLineTokenizer) {
		return new MemberFileHeaderHandler(delimitedLineTokenizer);		
	}

	@Bean(name = "delimitedLineTokenizer")
	@StepScope
	protected DelimitedLineTokenizer delimitedLineTokenizer() {
		return new DelimitedLineTokenizer();		
	}
	
	@Bean
	@StepScope
	protected FlatFileItemWriter<MemberFile> skipItemFlatFileStream(@Value("#{jobParameters['fileNameWithoutPath']}") String fileName,
			@Value("${pts.edifile.fileOutput.dateFormat}") String dateFormat,
			@Qualifier("memberFileHeaderHandler") MemberFileHeaderHandler memberFileHeaderHandler,File inboundFailedDirectory) {
		
		int indexOfDot = fileName.lastIndexOf('.');
		String fileExtn = fileName.substring(indexOfDot);
		String fileNameWithoutExtn = fileName.substring(0,indexOfDot);
		String errorFileName = inboundFailedDirectory.getAbsolutePath() + File.separator + fileNameWithoutExtn +"_" +DateTimeFormatter.ofPattern(dateFormat).format(LocalDateTime.now())
				+ fileExtn;

		FlatFileItemWriter<MemberFile> skipItemFileItemWriter = new FlatFileItemWriter<>();

		skipItemFileItemWriter.setResource(new FileSystemResource(errorFileName));
		skipItemFileItemWriter.setShouldDeleteIfEmpty(true);
		skipItemFileItemWriter.setAppendAllowed(true);

		BeanWrapperFieldExtractor<MemberFile> extractor = new BeanWrapperFieldExtractor<>();
		extractor.setNames(new String[] {
				"contractNumber","modification","paymentCode","productType","effectiveDate","cancelDate","relationship", "firstName", "middleName", "lastName",
				"birthDate","benefitAddress", "benefitCity", "benefitState", "benefitZip", "mailingAddress", "mailingCity", "mailingState", "mailingZip", "email",
				"phone", "skipMessages" 
				});

		FormatterLineAggregator<MemberFile> formatterLineAggregator = new FormatterLineAggregator<>();
		formatterLineAggregator.setFieldExtractor(extractor);
		formatterLineAggregator.setFormat(  
				"%1$2s,%2$2s,%3$2s,%4$2s,%5$tm/%5$td/%5$tY,%6$tm/%6$td/%6$tY,%7$2s,%8$2s,%9$2s,%10$2s,%11$tm/%11$td/%11$tY,%12$2s,%13$2s,%14$2s,%15$2s,%16$2s,%17$2s,%18$2s"
				+ ",%19$2s,%20$2s,%21$2s,%22$2s");

		skipItemFileItemWriter.setLineAggregator(formatterLineAggregator);
		MemberFileErrorHeaderCallback headerCallback = new MemberFileErrorHeaderCallback();
		skipItemFileItemWriter.setHeaderCallback(headerCallback);
		return skipItemFileItemWriter;
	}
	
	@StepScope
	@Bean
	protected Step processMembersToPTS(EDIErrorConfig ediErrorConfig) {
		return stepBuilderFactory.get("processMembersToPTS")
				.allowStartIfComplete(true)				
				.tasklet(memberFileToPTSProcess(ediErrorConfig)).build();
	}
	
	/**
	 * @return
	 */
	protected MemberFileToPTSTasklet memberFileToPTSProcess(EDIErrorConfig ediErrorConfig) {
		return new MemberFileToPTSTasklet(memberService,addressService,ptsUserService,transactionManager,batchProcessResults,environment,memberAuditEventService
				,memberFileService,agentService,ediErrorConfig);
	}
	
	@Bean
	protected Step generateReport(EDIFileProcessReportService ediFileProcessReportService,File inboundOutDirectory,ExcelExportUtilityService excelExportUtilityService
			,MemberFileService memberFileService) {
		return stepBuilderFactory.get("generateReport")
				.allowStartIfComplete(true)
				.tasklet(new GenerateUploadStatusReport(batchProcessResults,ediFileProcessReportService,inboundOutDirectory,excelExportUtilityService,memberFileService))
				.build();
	}
}
