package com.masa.pts.edi.member.integration.configuration;

import java.io.File;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.StreamSupport;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;

import com.masa.pts.core.domain.Agent;
import com.masa.pts.core.domain.MemberFileRecordErrorEntity;
import com.masa.pts.core.domain.FileNameToGroup;
import com.masa.pts.core.domain.MemberFile;
import com.masa.pts.core.domain.ReviewStatus;
import com.masa.pts.core.domain.UploadStatus;
import com.masa.pts.core.repository.FileNameToGroupRepository;
import com.masa.pts.core.service.MemberFileService;
import com.masa.pts.edi.member.batch.configuration.EDIErrorConfig;

public class EDIFileValidator {

	private static final Logger log = LoggerFactory.getLogger(EDIFileValidator.class);
	
	private static final String SOLD_DATE_STATUS = "SOLD_DATE_STATUS";
	private static final String GROUP_STATUS = "GROUP_STATUS";
	private static final String FILE_STATUS = "FILE_STATUS";
	private static final String AGENT_STATUS = "AGENT_STATUS";
	private static final String GROUP_CODE = "groupCode";
	private static final String AGENT_CODE = "agentCode";
	private static final String SOLD_DATE = "soldDate";
	private static final String EGROUPINVALIDINACTIVE = "EGROUPINVALIDINACTIVE";
	private static final String EAGENTNOTSETUPGRP = "EAGENTNOTSETUPGRP";
	private static final String ESOLDDATENOTPARSE = "ESOLDDATENOTPARSE";
	private static final String JOB_NAME_PARAM = "JOB_NAME";
	
	private String soldDateFormat;
	private FileNameToGroupRepository fileNameToGroupRepository;
	private MemberFileService memberFileService;
	private EDIErrorConfig ediErrorConfig;

	public EDIFileValidator(String soldDateFormat, FileNameToGroupRepository fileNameToGroupRepository,
			MemberFileService memberFileService,EDIErrorConfig ediErrorConfig) {
		this.soldDateFormat = soldDateFormat;
		this.fileNameToGroupRepository = fileNameToGroupRepository;
		this.memberFileService = memberFileService;
		this.ediErrorConfig = ediErrorConfig;
	}

	@ServiceActivator
	public Message<File> validateFile(Message<File> message) {

		log.info("Received File:[{}]", message.getPayload().getAbsolutePath());

		Map<String, String> paramMap = validateGroupCodeAndSoldDateFromFileName(message.getPayload().getName());

		if(Boolean.parseBoolean(paramMap.get(FILE_STATUS)))
		{
			validateAndSetGroupAgent(paramMap);
		}
		boolean fileStatus = Boolean.parseBoolean(paramMap.get(FILE_STATUS)) && Boolean.parseBoolean(paramMap.get(AGENT_STATUS));
		
		paramMap.put(FILE_STATUS, String.valueOf(fileStatus));
		
		if(!fileStatus) {
			saveFileFailStatus(message.getPayload().getName(),paramMap);
		}
		
		log.info("File:[{}] Group valid [{}] Solddate valid [{}] Agent valid [{}]", message.getPayload().getAbsolutePath(),
				Boolean.parseBoolean(paramMap.get(GROUP_STATUS)),Boolean.parseBoolean(paramMap.get(SOLD_DATE_STATUS)),Boolean.parseBoolean(paramMap.get(AGENT_STATUS)));
		
		paramMap.put(JOB_NAME_PARAM, "ediMemberFileImport");
		
		return MessageBuilder.withPayload(message.getPayload()).copyHeaders(message.getHeaders())
				.copyHeadersIfAbsent(paramMap).build();
	}
	
	private void saveFileFailStatus(String fileName,Map<String,String> paramMap) {
		
		MemberFile failedRec = new MemberFile();
		
		failedRec.setSourceFileName(fileName);
		failedRec.setStatus(UploadStatus.FAIL);
		failedRec.setCreatedDate(new java.util.Date());
		failedRec.setGroupCode(paramMap.getOrDefault(GROUP_CODE, ""));
		failedRec.setAgentCode(paramMap.getOrDefault(AGENT_CODE, ""));
		failedRec.setProcessed(Boolean.TRUE);
		
		if(!Boolean.parseBoolean(paramMap.get(GROUP_STATUS))){
			failedRec.addRecordErrorSet(new MemberFileRecordErrorEntity(failedRec,EGROUPINVALIDINACTIVE,ediErrorConfig.getMessages().getOrDefault(EGROUPINVALIDINACTIVE,EGROUPINVALIDINACTIVE)));
		}else if(!Boolean.parseBoolean(paramMap.get(AGENT_STATUS))){
			failedRec.addRecordErrorSet(new MemberFileRecordErrorEntity(failedRec,EAGENTNOTSETUPGRP,ediErrorConfig.getMessages().getOrDefault(EAGENTNOTSETUPGRP,EAGENTNOTSETUPGRP)));
		}else if(!Boolean.parseBoolean(paramMap.get(SOLD_DATE_STATUS))){
			failedRec.addRecordErrorSet(new MemberFileRecordErrorEntity(failedRec,ESOLDDATENOTPARSE,ediErrorConfig.getMessages().getOrDefault(ESOLDDATENOTPARSE,ESOLDDATENOTPARSE)));
		}		
		failedRec.setJobExecutionId(-1L); //no job triggered yet.
		failedRec.setReviewStatus(ReviewStatus.OPEN);
		
		memberFileService.saveMemberFile(failedRec,Boolean.TRUE);
	}

	private void validateAndSetGroupAgent(Map<String,String> paramMap) {
		 
		String groupCode = paramMap.get(GROUP_CODE);
		Agent agent = memberFileService.getGroupAgent(groupCode);
		
		if(null != agent)
		{
			paramMap.put(AGENT_CODE, agent.getAgentNum());
			paramMap.put(AGENT_STATUS,String.valueOf(true));
		}
		else
		{
			paramMap.put(AGENT_STATUS,String.valueOf(false));
		}		
	}

	/**
	 * @param fileNameWithoutPath //MKHG_20190429_XYZ.csv--> mkhg : group code,
	 *                            20190429 : sold date
	 *                            MASA_ClevelandISD_20200219.csv --> ClevelandISD -
	 *                            possible group name, 20200219: sold date *
	 */
	private Map<String, String> validateGroupCodeAndSoldDateFromFileName(String fileNameWithoutPath) {
		Map<String, String> paramMap = new HashMap<>();
		boolean fileStatus = false;
		try {
			log.info("Parsing file name to identify group & sold date [{}]", fileNameWithoutPath);
			int firstIndexOfDash = fileNameWithoutPath.indexOf('_');
			if (firstIndexOfDash != -1) {
				Iterable<FileNameToGroup> data = fileNameToGroupRepository.findAll();

				int indexOfDate = getIndexOfDateFromFileName(fileNameWithoutPath);

				String groupName = fileNameWithoutPath.substring(0, indexOfDate);

				if (groupName.endsWith("_"))
					groupName = fileNameWithoutPath.substring(0, indexOfDate - 1);

				String fileGroupName = groupName;

				String groupCd = StreamSupport.stream(data.spliterator(), false)
						.filter(item -> item.getEdiFileName().equalsIgnoreCase(fileGroupName))
						.map(FileNameToGroup::getGroupCode).findFirst().orElse(fileGroupName);

				paramMap.put(GROUP_CODE, groupCd);

				String soldDate = fileNameWithoutPath.substring((indexOfDate+1), (indexOfDate + 9));
				log.info("Data from file name Group Code [{}] Sold Date [{}]", groupCd, soldDate);

				validateSoldDate(soldDate, paramMap);

				paramMap.put(GROUP_STATUS, String.valueOf(memberFileService.isGroupValidAndActive(groupCd)));
				
				fileStatus = Boolean.valueOf(paramMap.get(SOLD_DATE_STATUS))
						&& Boolean.valueOf(paramMap.get(GROUP_STATUS));
			}
		} catch (Exception e) {
			log.error("Error in validateGroupCodeAndSoldDateFromFileName ::Message [{}], Cause [{}]", e.getMessage(),
					e.getCause());
		}
		paramMap.put(FILE_STATUS, String.valueOf(fileStatus));
		return paramMap;
	}

	private void validateSoldDate(String soldDate, Map<String, String> paramMap) {

		SimpleDateFormat sdf = ((SimpleDateFormat) DateFormat.getInstance());
		try {
			sdf.applyPattern(soldDateFormat);
			sdf.parse(soldDate);
			paramMap.put(SOLD_DATE, soldDate);
			paramMap.put(SOLD_DATE_STATUS, String.valueOf(true));
		} catch (Exception e) {
			log.error("Error parsing sold date value [{}], Message [{}]", soldDate, e.getMessage());
			paramMap.put(SOLD_DATE_STATUS, String.valueOf(false));
		}
	}

	//LSP3_20191014_1.csv -> LSP3
	private int getIndexOfDateFromFileName(String fileNameWithoutPath) {
		int index = -1;
		try {
			
			/*int indexOfUnderScore = fileNameWithoutPath.lastIndexOf('_');
			String fileNametmp = indexOfUnderScore != -1 ? fileNameWithoutPath.substring(0,indexOfUnderScore) : fileNameWithoutPath;
			System.out.println("match::"+new java.util.Scanner(fileNameWithoutPath).next("\\D+"));
			int data = new java.util.Scanner(fileNametmp).useDelimiter("_\\D+")					
					.nextInt();
			index = fileNameWithoutPath.indexOf(String.valueOf(data));
			*/
			Pattern p = Pattern.compile("(_\\d{6,8})");
			Matcher m = p.matcher(fileNameWithoutPath);
			if(m.find()){
				index = fileNameWithoutPath.indexOf(m.group());
			}
		} catch (Exception e) {
		}
		return index;
	}
}
