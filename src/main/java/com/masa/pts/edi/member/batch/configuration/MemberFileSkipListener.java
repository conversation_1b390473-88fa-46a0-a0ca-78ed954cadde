package com.masa.pts.edi.member.batch.configuration;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.listener.SkipListenerSupport;
import org.springframework.batch.item.file.FlatFileItemWriter;

import com.masa.pts.core.domain.MemberFileRecordErrorEntity;
import com.masa.pts.core.domain.MemberFile;
import com.masa.pts.core.domain.UploadStatus;
import com.masa.pts.core.service.MemberFileService;

public class MemberFileSkipListener extends SkipListenerSupport<MemberFile, MemberFile> {
	private static final Logger log = LoggerFactory.getLogger(MemberFileSkipListener.class);
	
	FlatFileItemWriter<MemberFile> skipItemFileItemWriter;
	BatchProcessResults batchProcessResults;
	String groupCode;
	Date soldDate;
	String fileNameWithoutPath;
	JobExecution jobExecution;
	EDIErrorConfig ediErrorConfig;
	MemberFileService memberFileService;
	
	
	public MemberFileSkipListener(FlatFileItemWriter<MemberFile> skipItemFileItemWriter,BatchProcessResults batchProcessResults,  
			String groupCode, Date soldDate, String fileNameWithoutPath, JobExecution jobExecution,EDIErrorConfig ediErrorConfig,
			MemberFileService memberFileService) {
		this.skipItemFileItemWriter = skipItemFileItemWriter;
		this.batchProcessResults = batchProcessResults;
		this.groupCode = groupCode;
		this.soldDate = soldDate;
		this.fileNameWithoutPath = fileNameWithoutPath;
		this.jobExecution = jobExecution;
		this.ediErrorConfig = ediErrorConfig;
		this.memberFileService = memberFileService;
	}

	@Override
	public void onSkipInRead(Throwable t) {
	}

	@Override
	public void onSkipInWrite(MemberFile item, Throwable t) {

	}
		
	@Override
	public void onSkipInProcess(MemberFile item, Throwable t) {
		item.setRecordErrorSet(parseAndExtractErrors(t.getMessage(),item));
		item.setStatus(UploadStatus.SKIP);
		item.setGroupCode(groupCode);
		item.setSoldDate(soldDate);
		item.setProcessed(true);
		item.setJobExecutionId(this.jobExecution.getId());
		item.setSourceFileName(fileNameWithoutPath);
		List<MemberFile> failedItemList = new ArrayList<>();
		failedItemList.add(item);
		batchProcessResults.addSkip(item);
		batchProcessResults.setJobExecutionId(this.jobExecution.getId());
		log.info("In MemberFileSkipListener , adding record to skip list..Contract # [{}] Group Code [{}] , Error [{}]",item.getContractNumber(),item.getGroupCode()
				,t.getMessage());
		try {
			
			Set<MemberFile> dependentSet = new HashSet<>();			
			
			if("P".equalsIgnoreCase(item.getRelationship())) {
				dependentSet = memberFileService
						.getAllDependentRecordsForPrimary(item.getGroupCode(),item.getContractNumber(),"P",item.getJobExecutionId());						
				
				dependentSet.forEach(depItem->{
					depItem.setStatus(UploadStatus.SKIP);
					depItem.setGroupCode(groupCode);
					depItem.setSoldDate(soldDate);
					depItem.setProcessed(true);
					depItem.setJobExecutionId(this.jobExecution.getId());
					depItem.setSourceFileName(fileNameWithoutPath);
					depItem.setRecordErrorSet(parseAndExtractErrors("EPRIMARYHASERRORS",depItem));
					failedItemList.add(depItem);
					batchProcessResults.addSkip(depItem);
				});
			}
			dependentSet.add(item);
			this.skipItemFileItemWriter.afterPropertiesSet();
			this.skipItemFileItemWriter.write(failedItemList);
			memberFileService.saveAll(dependentSet,Boolean.TRUE);
		} catch (Exception e) {
			log.error("Error in Flat File Item writer ::Message [{}] Cause [{}]",e.getMessage(),e.getCause());
		}
	}
	
	private Set<MemberFileRecordErrorEntity> parseAndExtractErrors(String recordErrorCode,MemberFile record){
		
		Set<MemberFileRecordErrorEntity> dataSet = new HashSet<>();
		
		if(recordErrorCode==null) {
			return Collections.emptySet();
		}
		if(recordErrorCode.contains(";")) {
			String [] data = recordErrorCode.split(";");
			
			dataSet = Arrays.asList(data).stream()
						.map(errorCodeItem-> {
							return getEDIFileRecordError(errorCodeItem, record);
						})
						.collect(Collectors.toSet());
		}
		else {
			dataSet.add(getEDIFileRecordError(recordErrorCode,record));
		}
		return dataSet;
	}
	
	private MemberFileRecordErrorEntity getEDIFileRecordError(String recordErrorCode, MemberFile record) {
		return new MemberFileRecordErrorEntity(record,recordErrorCode, ediErrorConfig.getMessages().getOrDefault(recordErrorCode, recordErrorCode));
	}
}
