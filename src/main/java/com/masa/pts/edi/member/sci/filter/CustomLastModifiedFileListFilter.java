package com.masa.pts.edi.member.sci.filter;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.integration.file.filters.FileListFilter;

import com.jcraft.jsch.ChannelSftp.LsEntry;

public class CustomLastModifiedFileListFilter implements FileListFilter<LsEntry> {

	private LocalDateTime lastModifiedDateTime;

	public CustomLastModifiedFileListFilter(LocalDateTime lastModifiedDateTime) {
		this.lastModifiedDateTime = lastModifiedDateTime;
	}

	@Override
	public List<LsEntry> filterFiles(LsEntry[] files) {
		return Arrays.asList(files).stream()
				.filter(item -> (!item.getAttrs().isDir()
						&& lastModifiedDateTime.toEpochSecond(ZoneOffset.UTC) <= item.getAttrs().getMTime()))
				.collect(Collectors.toList());
	}

}
