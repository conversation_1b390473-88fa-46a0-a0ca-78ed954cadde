package com.masa.pts.edi.member.sci.integration.configuration;

import java.io.File;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SCIFileFolderConfiguration {

	@Bean(name="sciInboundDirectory")
    public File sciInboundDirectory(@Value("${pts.scifile.inbound}") String path) {
        return makeDirectory(path);
    }
   
	private File makeDirectory(String path) {
		File file = new File(path);
		file.mkdirs();
		return file;
	}
}
