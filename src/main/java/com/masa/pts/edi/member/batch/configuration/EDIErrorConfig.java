package com.masa.pts.edi.member.batch.configuration;

import java.util.HashMap;
import java.util.Map;

import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties(prefix = "errors")
public class EDIErrorConfig {

	private Map<String,String> messages = new HashMap<>();

	public Map<String, String> getMessages() {
		return messages;
	}

	public void setMessages(Map<String, String> messages) {
		this.messages = messages;
	}

	public EDIErrorConfig() {
		super();
	}	
}
