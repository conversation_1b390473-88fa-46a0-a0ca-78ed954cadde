package com.masa.pts.edi.member.batch.writer;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.annotation.BeforeStep;
import org.springframework.batch.item.ItemWriter;

import com.masa.pts.core.domain.MemberFile;
import com.masa.pts.core.domain.UploadStatus;
import com.masa.pts.core.service.MemberFileService;
import com.masa.pts.edi.member.batch.configuration.BatchProcessResults;

public class MemberFileItemWriter implements ItemWriter<MemberFile> {
	private static final Logger log = LoggerFactory.getLogger(MemberFileItemWriter.class);
	private final MemberFileService memberFileService;
	private BatchProcessResults batchProcessResults;
	private JobExecution jobExecution;
	
	public MemberFileItemWriter(MemberFileService memberFileService, BatchProcessResults batchProcessResults) {
		this.memberFileService = memberFileService;
		this.batchProcessResults = batchProcessResults;
	}

    @BeforeStep
    public void beforeStep(StepExecution stepExecution) {
        jobExecution = stepExecution.getJobExecution();
    }
    
	@Override
	public void write(List<? extends MemberFile> items) throws Exception {
		log.info("EDI File Member Items saving to staging table, Count of records saving..[{}]",items.size());
		items.forEach(memberFileItem -> {
			memberFileItem.setJobExecutionId(jobExecution.getId());
			memberFileItem.setStatus(UploadStatus.PROCESSED);
			memberFileService.saveMemberFile(memberFileItem,Boolean.FALSE);
			batchProcessResults.addProcessed(memberFileItem);
			batchProcessResults.setJobExecutionId(jobExecution.getId());
		});
	}
}
