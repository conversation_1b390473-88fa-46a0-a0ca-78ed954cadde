package com.masa.pts.edi.member.batch.reader;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.ItemReadListener;
import org.springframework.batch.item.file.FlatFileParseException;
import org.springframework.batch.item.file.transform.IncorrectTokenCountException;

import com.masa.pts.core.domain.MemberFileRecordErrorEntity;
import com.masa.pts.core.domain.MemberFile;
import com.masa.pts.core.domain.ReviewStatus;
import com.masa.pts.core.domain.UploadStatus;
import com.masa.pts.core.service.MemberFileService;
import com.masa.pts.edi.member.batch.configuration.BatchProcessResults;
import com.masa.pts.edi.member.batch.configuration.EDIErrorConfig;

public class MemberFileItemReadListener implements ItemReadListener<MemberFile> {

	private static final Logger log = LoggerFactory.getLogger(MemberFileItemReadListener.class);
	private BatchProcessResults batchProcessResults;
	private String groupCode;
	private String fileNameWithoutPath;
	private EDIErrorConfig ediErrorConfig;
	private MemberFileService memberFileService;
	
	public MemberFileItemReadListener(BatchProcessResults batchProcessResults
			,String groupCode
			,String fileNameWithoutPath
			,EDIErrorConfig ediErrorConfig
			,MemberFileService memberFileService) {
		this.batchProcessResults = batchProcessResults;
		this.groupCode = groupCode;
		this.fileNameWithoutPath = fileNameWithoutPath;
		this.ediErrorConfig = ediErrorConfig;
		this.memberFileService = memberFileService;
	}

	@Override
	public void afterRead(MemberFile item) {
		log.info("Member File read [{}]",item);	
		item.setStatus(UploadStatus.READ);
	}

	@Override
	public void onReadError(Exception ex) {
		log.error("MemberFileItemReadListener On Read Error::[{}] Message [{}]",ex.getCause(),ex.getMessage());		
		saveRecordReadError(extractCauseFromException(ex));
	}

	private String extractCauseFromException(Exception exception) {
		StringBuilder causeStr = new StringBuilder();
		if (exception instanceof FlatFileParseException) {
			causeStr.append("Read error on Line Number[")
			.append(((FlatFileParseException) exception).getLineNumber())
			.append("]");
			if (exception.getCause() instanceof IncorrectTokenCountException) {
				IncorrectTokenCountException ce = (IncorrectTokenCountException) exception.getCause();
				causeStr.append("Expected [").append(ce.getExpectedCount()).append("] Actual [")
						.append(ce.getActualCount());
			}
		}
		return causeStr.toString();
	}

	@Override
	public void beforeRead() {
		
	}

	private void saveRecordReadError(String exceptionCause) {
		MemberFile failedRec = new MemberFile();
		failedRec.setSourceFileName(fileNameWithoutPath);
		failedRec.setStatus(UploadStatus.FAIL);
		failedRec.setCreatedDate(new java.util.Date());
		failedRec.setGroupCode(groupCode);
		failedRec.setProcessed(Boolean.TRUE);
		failedRec.setModification("");
		failedRec.addRecordErrorSet(new MemberFileRecordErrorEntity(failedRec,"EREADERROR",ediErrorConfig.getMessages().getOrDefault("EREADERROR",exceptionCause)));
		failedRec.setJobExecutionId(-1L); // no job triggered yet.
		failedRec.setReviewStatus(ReviewStatus.OPEN);
		batchProcessResults.addFailure(failedRec);
		memberFileService.saveMemberFile(failedRec, Boolean.TRUE);
	}
}
