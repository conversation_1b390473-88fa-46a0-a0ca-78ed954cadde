package com.masa.pts.edi.member.batch.configuration;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.StepContribution;
import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.item.validator.ValidationException;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.core.env.Environment;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.StringUtils;

import com.masa.pts.address.validation.AddressValidationService;
import com.masa.pts.address.validation.model.AddressInput;
import com.masa.pts.address.validation.model.AddressValidationResponse;
import com.masa.pts.core.constant.AddressType;
import com.masa.pts.core.constant.AuditEventType;
import com.masa.pts.core.constant.MemberActiveStatus;
import com.masa.pts.core.constant.ProductUpgradeType;
import com.masa.pts.core.domain.Address;
import com.masa.pts.core.domain.Constant;
import com.masa.pts.core.domain.Country;
import com.masa.pts.core.domain.Dependant;
import com.masa.pts.core.domain.Member;
import com.masa.pts.core.domain.MemberAction;
import com.masa.pts.core.domain.MemberCoverageLapse;
import com.masa.pts.core.domain.MemberFee;
import com.masa.pts.core.domain.MemberFile;
import com.masa.pts.core.domain.MemberFileRecordErrorEntity;
import com.masa.pts.core.domain.State;
import com.masa.pts.core.domain.UploadStatus;
import com.masa.pts.core.model.MemberCancelSetup;
import com.masa.pts.core.model.MemberSetup;
import com.masa.pts.core.model.MemberSetupProduct;
import com.masa.pts.core.model.MemberUpdateSetup;
import com.masa.pts.core.service.AddressService;
import com.masa.pts.core.service.AgentService;
import com.masa.pts.core.service.MemberAuditEventService;
import com.masa.pts.core.service.MemberFileService;
import com.masa.pts.core.service.MemberService;
import com.masa.pts.core.service.PTSUserService;
import com.masa.pts.core.service.PTSUtilityService;

public class MemberFileToPTSTasklet implements Tasklet {

	private static final Logger log = LoggerFactory.getLogger(MemberFileToPTSTasklet.class);
	
	private static final String EDI_APPLICATION_SOURCE = "EDI";
	public static final int CANCEL_REASON_ID = 3;

	MemberService memberService;
	AddressService addressService;
	Iterable<State> states;
	PTSUserService ptsUserService;
	PlatformTransactionManager transactionManager;
	BatchProcessResults batchProcessResults;
	Country defaultEDICountry;
	Environment environment;
	MemberAuditEventService memberAuditEventService;
	MemberFileService memberFileService;
	AgentService agentService;
	EDIErrorConfig ediErrorConfig;
	
	private boolean addressValidationEnabled;

	public MemberFileToPTSTasklet(MemberService memberService, AddressService addressService, PTSUserService ptsUserService, 
			PlatformTransactionManager transactionManager, 
			BatchProcessResults batchProcessResults, Environment environment,MemberAuditEventService memberAuditEventService,
			MemberFileService memberFileService
			,AgentService agentService
			,EDIErrorConfig ediErrorConfig) {
		this.memberService = memberService;
		this.addressService = addressService;
		this.ptsUserService = ptsUserService;
		this.transactionManager = transactionManager;
		this.batchProcessResults = batchProcessResults;
		this.environment = environment;
		this.memberAuditEventService = memberAuditEventService;
		this.memberFileService = memberFileService;
		this.agentService = agentService;
		this.ediErrorConfig = ediErrorConfig;
	}
	
	@Override
	public RepeatStatus execute(StepContribution contribution, ChunkContext chunkContext) throws Exception {
		
		log.info("In MemberFileToPTSTasklet ======== {}",batchProcessResults);
		
		if(null == batchProcessResults.getFileName() || null == batchProcessResults.getJobExecutionId())
			return RepeatStatus.FINISHED;
		
		log.info("In MemberFileToPTSTasklet, Adding/Updating members to PTS Database from file [{}] Job ID [{}]",batchProcessResults.getFileName(),batchProcessResults.getJobExecutionId());
		Set<MemberFile> memberList = memberFileService.findAllByRelationshipAndSourceFileNameAndJobExecutionIdAndStatus("P",batchProcessResults.getFileName()
				,batchProcessResults.getJobExecutionId(),UploadStatus.PROCESSED);
		log.info("Count of Member records to process from EDI File Staging table [{}]", memberList.size());
		log.info("Batchprocess results {}",batchProcessResults);
		if(memberList.isEmpty())
			return RepeatStatus.FINISHED;
		
		if(!memberList.isEmpty()) {
			this.defaultEDICountry = addressService.getCountryByCodeWithStates(environment.getProperty("pts.edifile.address.country.default","United States"));
			this.states = this.defaultEDICountry.getStates();
			this.addressValidationEnabled = environment.getProperty("pts.edifile.addressValidationEnabled",Boolean.class,false);
		}
		Integer ptsUserId = ptsUserService.getPTSUserIDByUserName(Constant.DEFAULT_PTS_USERNAME);
		
		String groupCode = memberList.stream().map(MemberFile::getGroupCode).distinct().findFirst().orElse(null);
		
		//boolean groupHasSingleProduct = groupCode !=null ? memberFileService.groupHasSingleProduct(groupCode) : false;
		
		for (MemberFile memberFileRow : memberList) 
		{
			log.info("Processing member [{}], group [{}]",memberFileRow.getMasaMemberId(),memberFileRow.getGroupCode());
			try {
				if (MemberAction.ADD.compareTo(memberFileRow.getMemberAction()) == 0) {
					processMemberAddInTransaction(memberFileRow, ptsUserId);
				} else if (MemberAction.UPDATE.compareTo(memberFileRow.getMemberAction()) == 0
						|| MemberAction.ACTIVATE.compareTo(memberFileRow.getMemberAction()) == 0
						|| MemberAction.EMPLOYEEID_UPDATE.compareTo(memberFileRow.getMemberAction()) == 0
						|| MemberAction.GROUP_CHANGE.compareTo(memberFileRow.getMemberAction()) == 0) {
					processMemberUpdateInTransaction(memberFileRow, ptsUserId);
				} else if (MemberAction.CANCELL.compareTo(memberFileRow.getMemberAction()) == 0) {
					processMemberCancelInTransaction(memberFileRow, ptsUserId);
				} else if (MemberAction.REFRESH.compareTo(memberFileRow.getMemberAction()) == 0) {
					processMemmberRefreshInTransaction(memberFileRow);// mark records as success with comment as
																		// refresh.
				}
			}catch(Exception e) {
				log.error("Error processing member,Contract# [{}] Group [{}] Message[{}] Cause[{}]",memberFileRow.getContractNumber(),memberFileRow.getGroupCode(),e.getMessage(),e.getCause());
				handlePrimaryRecordExceptionInTransaction(memberFileRow,e);
			}
		}
		handleUnprocessedDepedentRecordInTransaction(batchProcessResults);
		return RepeatStatus.FINISHED;
	}

	private String handleUnprocessedDepedentRecords(BatchProcessResults batchProcessResults) {
		
		List<MemberFile> memberFileList = memberFileService.getAllRecordsForThisRun(batchProcessResults.getFileName(), batchProcessResults.getJobExecutionId());
		
		List<MemberFile> unProcessedDepedentList = memberFileList.stream().filter(item->UploadStatus.PROCESSED.equals(item.getStatus()) && !("P".equalsIgnoreCase(item.getRelationship())))
							   .collect(Collectors.toList());
		log.info("In handleUnprocessedDepedentRecord,found [{}] unprocessed dependent records.",unProcessedDepedentList.size());
		
		List<MemberFile> updatedList = unProcessedDepedentList.stream()
					.filter(item-> memberFileList.stream().anyMatch(parentItem-> "P".equalsIgnoreCase(parentItem.getRelationship()) &&
									item.getContractNumber().equalsIgnoreCase(parentItem.getContractNumber())
							))
					.map(item->{							
							Optional<UploadStatus> optStatus=  memberFileList.stream().filter(parentItem-> "P".equalsIgnoreCase(parentItem.getRelationship()) 
									&& item.getContractNumber().equalsIgnoreCase(parentItem.getContractNumber()))
								.map(MemberFile::getStatus)
								.findFirst();
							if(optStatus.isPresent()) {
								item.setStatus(optStatus.get());
								memberFileService.saveMemberFile(item, Boolean.TRUE);
							}
							return item;
					})
					.collect(Collectors.toList());
		log.info("In handleUnprocessedDepedentRecord, updated [{}] unprocessed dependent records.",updatedList.size());
		return "";
	}

	/**
	 * @param memberFileRow
	 */
	private MemberFile processMemberAdd(MemberFile memberFileRow,Integer ptsUserId) 
	{
		MemberSetup memberSetup = new MemberSetup(); 
		Member member = new Member();
		memberSetup.setMember(member);
		
		member.setCreatedBy(ptsUserId);
		member.setCreatedDate(new Date());
		member.setActive(MemberActiveStatus.ACTIVE.getStatus());
		memberSetup.setGroupCd(memberFileRow.getGroupCode());
		memberSetup.setProductId(memberFileRow.getProductId());
		member.setApplicationSource(memberService.getApplicationSourceIDByCode(EDI_APPLICATION_SOURCE));
		
		populatePrimaryInfo(member, memberFileRow.getFirstName(), memberFileRow.getLastName(),
				memberFileRow.getMiddleName(), memberFileRow.getBirthDate(), memberFileRow.getCell(), memberFileRow.getEmail(),
				memberFileRow.getPhone());
		populateMemberDates(memberSetup,memberFileRow,ptsUserId);
		populateOtherInfo(member, memberFileRow);
		populateMemberBenefitAddress(memberSetup,memberFileRow);
		populateMemberMailingAddress(memberSetup,memberFileRow);
		
		Set<MemberFile> depedentFileRows = new HashSet<>();
		depedentFileRows.add(memberFileRow);
		if(memberFileRow.isSciFile()) {
			populateMemberPayment(memberSetup,memberFileRow);
		}
		else {
			depedentFileRows.addAll(checkAndPopulateSpouseAndDependentInfo(memberSetup,memberFileRow));
			/*
			//PL Single product scenario
			if(groupHasSingleProduct && isMemberProductPLS(memberFileRow,depedentFileRows)) {
				//validate agent commission & commission setup
				setSingleProductType(memberFileRow);
				agentCommissionSetupForPLSingleProduct(memberFileRow.getAgentId(),memberFileRow.getProductId());
				agentHierarchySetupForPLSingleProduct(memberFileRow.getAgentCode(),memberFileRow.getProductId());						
			}*/
		}
		
		populateMemberProductForNewMember(memberSetup, memberFileRow);
		
		memberSetup.addNote("Member imported from EDI File ["+memberFileRow.getSourceFileName()+"]");
		boolean addressValid = true;
		
		if(addressValidationEnabled)
		{
			Optional<Map<Boolean,String>> data = doAddressValidation(memberSetup);			
			if(data.isPresent()) {
				memberFileRow.setUspsAddrError(data.get().getOrDefault(Boolean.FALSE, null));
				if(StringUtils.hasText(data.get().getOrDefault(Boolean.TRUE, "")))
				{
					memberSetup.addNote(data.get().getOrDefault(Boolean.TRUE, ""));
				}
				else if(StringUtils.hasText(data.get().getOrDefault(Boolean.FALSE, "")))
				{
					memberSetup.addNote(data.get().getOrDefault(Boolean.FALSE, ""));
					//if address validation had error, skip fulfillment
					addressValid = false;
				}
			}
		}
		memberSetup.setFulfillment(!memberFileRow.isSciFile() && addressValid);
		
		memberFileRow.setFulfillmentPending(!memberFileRow.isSciFile() && !addressValid);
		
		memberService.createMember(memberSetup);
				
		if((null == memberSetup.getErrorMessage() ) && memberSetup.getMember().getMemberId() != null
				&& memberSetup.getMember().getMemberId().intValue()>0)
		{
			log.info("Member Created in PTS with member ID [{}] for Group [{}] Contract Number [{}]",memberSetup.getMember().getMemberId(),memberFileRow.getGroupCode(),memberFileRow.getContractNumber());
			
			depedentFileRows.forEach(item->{
				item.setMasaMemberId(memberSetup.getMember().getMemberId());
				item.setProcessed(Boolean.TRUE);
				item.setStatus(UploadStatus.SUCCESS);
				item.setMemberProcessedDate(new Date());
				batchProcessResults.addSuccess(item);
			});			
		}
		else if(!memberSetup.getErrorMessage().isEmpty() && memberSetup.getMember().getMemberId() != null
				&& memberSetup.getMember().getMemberId().intValue()>0)
		{
			log.error("Error while creating member for Group [{}] Contract Number [{}] Member ID[{}] Message [{}]",memberFileRow.getGroupCode(),memberFileRow.getContractNumber(),
					memberSetup.getMember().getMemberId(),memberSetup.getErrorMessage());
			depedentFileRows.forEach(item->{
				item.setMasaMemberId(memberSetup.getMember().getMemberId());
				item.setProcessed(Boolean.TRUE);
				item.setStatus(UploadStatus.SUCCESS);
				item.setMemberProcessedDate(new Date());				
				batchProcessResults.addSuccess(item);
			});			
		}
		else
		{
			log.error("Error creating member, Message [{}]",memberSetup.getErrorMessage());
			depedentFileRows.forEach(item->{
				item.setMasaMemberId(0);
				item.setProcessed(Boolean.TRUE);
				item.setStatus(UploadStatus.ERROR);
				item.setMemberProcessedDate(new Date());
				if("P".equalsIgnoreCase(item.getRelationship())) {
					item.addRecordErrorSet(new MemberFileRecordErrorEntity(item, "ERECORDUPDATE", "Error updating record ["+memberSetup.getErrorMessage()+"]"));
				}
				else {
					item.addRecordErrorSet(new MemberFileRecordErrorEntity(item, "EPRIMARYHASERRORS", "Primary record has errors"));
				}
				batchProcessResults.addFailure(item);
			});	
		}
		memberFileService.saveAll(depedentFileRows,Boolean.TRUE);
		return memberFileRow;
	}
	
	private void populateMemberPayment(MemberSetup memberSetup, MemberFile memberFileRow) {
		Optional.ofNullable(memberFileRow.getPaymentAmount())
		.ifPresent(amt -> memberSetup.setPaymentAmount(BigDecimal.valueOf(amt)));
	}

	/**
	 * @param memberFileRow
	 * @param exceptionMsg
	 * @return
	 */
	private MemberFile handlePrimaryRecordException(MemberFile memberFileRow,Exception recordException) {	
		
		Set<MemberFile> allRecordsForTheMember = new HashSet<>();
		allRecordsForTheMember.add(memberFileRow);
		
		String primaryErrorCode = "ERECORDUPDATE";
		String primaryErrorMsg = "Error updating record ["+recordException.getMessage()+"]";
		UploadStatus status = UploadStatus.ERROR;
		if(recordException instanceof ValidationException) {
			primaryErrorCode = recordException.getMessage();
			primaryErrorMsg = ediErrorConfig.getMessages().getOrDefault(primaryErrorCode, primaryErrorCode);
			status = UploadStatus.SKIP;
		}		
		memberFileRow.setProcessed(Boolean.TRUE);
		memberFileRow.setStatus(status);
		memberFileRow.setMemberProcessedDate(new Date());
		memberFileRow.addRecordErrorSet(new MemberFileRecordErrorEntity(memberFileRow, primaryErrorCode, primaryErrorMsg));
		batchProcessResults.addFailure(memberFileRow);
		
		Set<MemberFile> depedentFileRows = getDependentMemberFileRecords(memberFileRow); 

		depedentFileRows.forEach(item->{
			item.setMasaMemberId(memberFileRow.getMasaMemberId());
			item.setProcessed(Boolean.TRUE);
			item.setStatus(UploadStatus.ERROR);
			item.setMemberProcessedDate(new Date());
				item.addRecordErrorSet(new MemberFileRecordErrorEntity(item, "EPRIMARYHASERRORS", "Primary record has errors"));
			batchProcessResults.addFailure(item);
		});	
		allRecordsForTheMember.addAll(depedentFileRows);
		memberFileService.saveAll(allRecordsForTheMember,Boolean.TRUE);
		return memberFileRow;
	}
	
	/**
	 * @param memberFileRow
	 * @param depedentFileRows
	 */
	/*
	private void setSingleProductType(MemberFile memberFileRow) {
		
		Optional<Product> optProduct = memberFileService.getProductByProductTypeAndPaymentTypeAndGroupCode(memberFileRow.getProductType(),memberFileRow.getPaymentCode(),memberFileRow.getSoldDate(),
				memberFileRow.getGroupCode(),ProductFamilyType.SINGLE);
		
		if(optProduct.isPresent()) {
			Product prd = optProduct.get();
			memberFileRow.setProductId(prd.getProductId());
			memberFileRow.setProductName(prd.getName());
			memberFileRow.setProductCategoryCode(prd.getProductCategory().getCode());
		}
		else {
			log.error("Error in setSingleProductType,cannot find single product. Employee ID [{}]",memberFileRow.getContractNumber());
		}		
	}
	
	
	private void agentCommissionSetupForPLSingleProduct(Integer agentId,Integer productId) {
		if (!agentService.isAgentCommissionSetupForProduct(agentId,productId)) {
			throw new ValidationException("EAGENTCOMMNOTSETUP");
		}
	}
	
	private void agentHierarchySetupForPLSingleProduct(String agentNum,Integer productId) {
		if (!agentService.isAgentHierarchySetupForProduct(agentNum,productId)) {
			throw new ValidationException("EAGENTHIERNOTSETUP");
		}
	}
	*/
	
	private void updateFileRecordsWithStatus(MemberSetup memberSetup,Set<MemberFile> depedentFileRows,MemberFile memberFileRow)
	{
		if((memberSetup.isSuccess()) && memberSetup.getMember().getMemberId() != null
				&& memberSetup.getMember().getMemberId().intValue()>0)
		{
			log.info("Member Created/Updated in PTS with member ID [{}] for Group [{}] Contract Number [{}]",memberSetup.getMember().getMemberId(),memberFileRow.getGroupCode(),memberFileRow.getContractNumber());
			
			depedentFileRows.forEach(item->{
				item.setMasaMemberId(memberSetup.getMember().getMemberId());
				item.setMemberAction(memberFileRow.getMemberAction());
				item.setProcessed(Boolean.TRUE);
				item.setStatus(UploadStatus.SUCCESS);
				item.setMemberProcessedDate(new Date());
				batchProcessResults.addSuccess(item);
			});			
		}
		else if(!memberSetup.isSuccess() && memberSetup.getMember().getMemberId() != null
				&& memberSetup.getMember().getMemberId().intValue()>0)
		{
			log.error("Error processing member for Group [{}] Contract Number [{}] Member ID[{}] Message [{}]",memberFileRow.getGroupCode(),memberFileRow.getContractNumber(),
					memberSetup.getMember().getMemberId(),memberSetup.getErrorMessage());
			depedentFileRows.forEach(item->{
				item.setMasaMemberId(memberSetup.getMember().getMemberId());
				item.setMemberAction(memberFileRow.getMemberAction());
				item.setProcessed(Boolean.TRUE);
				item.setStatus(UploadStatus.ERROR);
				item.setMemberProcessedDate(new Date());
				if("P".equalsIgnoreCase(item.getRelationship())) {
					item.addRecordErrorSet(new MemberFileRecordErrorEntity(item, "ERECORDUPDATE", "Error updating record ["+memberSetup.getErrorMessage()+"]"));
				}
				else {
					item.addRecordErrorSet(new MemberFileRecordErrorEntity(item, "EPRIMARYHASERRORS", "Primary record has errors"));
				}
				batchProcessResults.addSuccess(item);
			});			
		}
		else
		{
			log.error("Error processing member, Message [{}]",memberSetup.getErrorMessage());
			depedentFileRows.forEach(item->{
				item.setMasaMemberId(0);
				item.setMemberAction(memberFileRow.getMemberAction());
				item.setProcessed(Boolean.TRUE);
				item.setStatus(UploadStatus.ERROR);
				item.setMemberProcessedDate(new Date());
				if("P".equalsIgnoreCase(item.getRelationship())) {
					item.addRecordErrorSet(new MemberFileRecordErrorEntity(item, "ERECORDUPDATE", "Error updating record ["+memberSetup.getErrorMessage()+"]"));
				}
				else {
					item.addRecordErrorSet(new MemberFileRecordErrorEntity(item, "EPRIMARYHASERRORS", "Primary record has errors"));
				}
				batchProcessResults.addFailure(item);
			});	
		}
		memberFileService.saveAll(depedentFileRows,Boolean.TRUE);
	}

	/**
	 * @param memberSetup
	 */
	private Optional<Map<Boolean, String>> doAddressValidation(MemberSetup memberSetup) {

		AddressValidationService addressValidation = new AddressValidationService();
		Set<AddressInput> addressRequestSet = new HashSet<>();
		Optional<Map<Boolean,String>> returnData = Optional.empty();
		try {
			AddressInput addresToBeValidated = createAddressInput(memberSetup.getBenefitAddress(), AddressType.BENEFIT);
			if(null != addresToBeValidated)
				addressRequestSet.add(addresToBeValidated);
			addresToBeValidated = createAddressInput(memberSetup.getMailingAddress(), AddressType.MAILING);
			if(null != addresToBeValidated)
				addressRequestSet.add(addresToBeValidated);
			
			Set<AddressValidationResponse> responseSet = addressValidation.validateAddress(addressRequestSet);
			
			return Optional.of(processAddressValidationResponse(responseSet,memberSetup));			
		}
		catch (Exception e) {
			log.error("Error in USPS Address validation , Message [{}] , Cause [{}]",e.getMessage(),e.getCause());
		}
		return returnData;
	}
	
	/**
	 * @param responseSet
	 * @param memberSetup
	 */
	private Map<Boolean, String> processAddressValidationResponse(Set<AddressValidationResponse> responseSet,MemberSetup memberSetup) {
		
		StringBuilder sucess = new StringBuilder();		
		StringBuilder error = new StringBuilder();
		Map<Boolean,String> returnData = new HashMap<>();
		
		for(AddressValidationResponse item: responseSet)
		{
			if(item.isSuccess() &&  StringUtils.isEmpty(item.getReturnText()))
			{
				if(AddressType.BENEFIT.getValue().compareTo(item.getId())==0 && !isSameAddress(item, memberSetup.getBenefitAddress()))
				{
					sucess.append("\n").append("Benefit address from EDI File ["+memberSetup.getBenefitAddress().formatAddress()+"]");					
					Address address = createAddressFromAddrVldnResponse(item);
					memberSetup.setBenefitAddress(address);
					sucess.append("\n").append("Benefit address updated from USPS validation.");
				}
				else if(AddressType.MAILING.getValue().compareTo(item.getId())==0 && !isSameAddress(item, memberSetup.getMailingAddress()))
				{	
					sucess.append("\n").append("Mailing address from EDI File ["+memberSetup.getMailingAddress().formatAddress()+"]");
					Address address = createAddressFromAddrVldnResponse(item);
					memberSetup.setMailingAddress(address);
					sucess.append("\n").append("Mailing address updated from USPS validation.");
				}
			}
			else {//usps call has error
				error.append(getAddressType(item.getId())+"["+item.getReturnText()+"]\n");
			}
		}
		if(StringUtils.hasText(error.toString())) {
			returnData.put(Boolean.FALSE,error.toString());
		}		
		if(StringUtils.hasText(sucess.toString())) {
			returnData.put(Boolean.TRUE, sucess.toString());
		}
		return returnData;
	}

	private String getAddressType(Integer typeId) {
		return (AddressType.BENEFIT.getValue().compareTo(typeId) == 0 ) ?  AddressType.BENEFIT.name() : AddressType.MAILING.name();
	}
	
	/**
	 * @param addressValidationResponse
	 * @return
	 */
	private Address createAddressFromAddrVldnResponse(AddressValidationResponse addressValidationResponse) {
		Address address = new Address();
		address.setAddress1(addressValidationResponse.getAddress1());
		address.setAddress2(addressValidationResponse.getAddress2());
		address.setCity(addressValidationResponse.getCity());
		address.setZip(addressValidationResponse.getZip5());
		address.setZip4(addressValidationResponse.getZip4());
		address.setLastModifiedDate(new Date());
		
		address.setCountry(this.defaultEDICountry.getCountryId());
		address.setCountryDetails(this.defaultEDICountry);
		address.setCountryCode(this.defaultEDICountry.getName());
		
		State state = StreamSupport.stream(states.spliterator(), false).filter(item->addressValidationResponse.getState().equalsIgnoreCase(item.getSymbol())).findFirst().orElse(null);
		if(null != state) 
		{
			address.setState(state.getStateId());
			address.setStateDetails(state);
			address.setStateCode(state.getSymbol());
		}
		return address;
	}

	private boolean isSameAddress(AddressValidationResponse addressResponse, Address address) {
		
		if(! stringEqual(address.getAddress1(),addressResponse.getAddress1()))
			return false;
		
		if(! stringEqual(address.getAddress2(),addressResponse.getAddress2()))
			return false;
		
		if(! stringEqual(address.getCity(),addressResponse.getCity()))
			return false;
		
		if(! stringEqual(address.getZip(),addressResponse.getZip5()))
			return false;
		
		if(! stringEqual(address.getZip4(),addressResponse.getZip4()))
			return false;
		
		return true;
	}
	
	private boolean stringEqual(String one,String two) {
		
		if(one == null && two == null)
			return true;
		
		if((one !=null && one.isEmpty() && (two !=null && two.isEmpty())))
			return true;
		
		if( (one !=null && one.isEmpty()) && (two == null) )
			return true;
		
		if( (one ==null)  && (two != null  && two.isEmpty()))
			return true;
		
		if( one !=null && two != null && one.equalsIgnoreCase(two) )
			return true;
			
		return false;
	}

	
	/**
	 * @param address
	 * @param addressType
	 * @return
	 */
	private AddressInput createAddressInput(Address address,AddressType addressType)
	{
		AddressInput addressInput = null;
		if(address !=null && StringUtils.hasText(address.getAddress1()) && StringUtils.hasText(address.getZip()))
		{
			addressInput = new AddressInput();
			addressInput.setOrderId(addressType.getValue());
			addressInput.setAddress1(address.getAddress1());
			addressInput.setAddress2(address.getAddress2());
			addressInput.setCity(address.getCity());
			addressInput.setState(address.getStateDetails().getSymbol());
			addressInput.setZip(address.getZip());
			addressInput.setZip4(address.getZip4());
			addressInput.setCountry(address.getCountryDetails().getName());
		}
		return addressInput;
	}	
	
	private Set<MemberFile> getDependentMemberFileRecords(MemberFile memberFileRow) {
		return memberFileService
				.getAllDependentRecordsForPrimary(memberFileRow.getGroupCode(), memberFileRow.getContractNumber(), "P",memberFileRow.getJobExecutionId());				
	}
	
	private Set<MemberFile> checkAndPopulateSpouseAndDependentInfo(MemberSetup memberSetup, MemberFile memberFileRow) {
		
		Set<MemberFile> dependentSet = memberFileService
				.getAllDependentRecordsForPrimary(memberFileRow.getGroupCode(),memberFileRow.getContractNumber(), "P",
						memberFileRow.getJobExecutionId() ,false);
		
		Set<MemberFile> dependentFileRowsToReturn = new HashSet<>();
		
		for(MemberFile dependentRow: dependentSet)
		{			
			if("S".equalsIgnoreCase(dependentRow.getRelationship()) )
			{
				memberSetup.getMember().setSpouseFirst(dependentRow.getFirstName());
				memberSetup.getMember().setSpouseLast(dependentRow.getLastName());
				memberSetup.getMember().setSpouseMi(dependentRow.getMiddleName());
				memberSetup.getMember().setSpouseBirthDate(dependentRow.getBirthDate());
				memberSetup.getMember().setSpouseEmail(dependentRow.getEmail());
				memberSetup.getMember().setCellPhone(dependentRow.getPhone());
				dependentFileRowsToReturn.add(dependentRow);
			}
			else if("D".equalsIgnoreCase(dependentRow.getRelationship())) 
			{
				Dependant dependent = new Dependant();
				dependent.setFirstName(dependentRow.getFirstName());
				dependent.setLastName(dependentRow.getLastName());
				dependent.setMiName(dependentRow.getMiddleName());
				dependent.setBirthDate(dependentRow.getBirthDate());
				dependent.setActive(Boolean.TRUE);
				dependent.setEnrolledCollege(Boolean.FALSE);
				memberSetup.addDependant(dependent);
				dependentFileRowsToReturn.add(dependentRow);
			}
		}		
		return dependentFileRowsToReturn;
	}
	

	private MemberFile processMemberUpdate(MemberFile memberFileRow,Integer ptsUserId)
	{
		MemberUpdateSetup memberSetup = new MemberUpdateSetup();
		Integer memberId = memberFileRow.getMasaMemberId();				
		Set<MemberFile> depedentFileRows = new HashSet<>();
		depedentFileRows.add(memberFileRow);
		Optional<Member> optMem = Optional.empty();
		
		if(memberId !=null)
			optMem = memberService.getMemberDetails(memberId);
		
		if(optMem.isPresent())
		{
			Member member = optMem.get();			
			memberSetup.setMember(member);
			
			depedentFileRows.addAll(checkAndPopulateSpouseAndDependentInfo(memberSetup,memberFileRow));
			
			member.setModifiedBy(ptsUserId);
			member.setModifiedDate(new Date());
			
			member.setMi(memberFileRow.getMiddleName());
			member.setEmail(memberFileRow.getEmail());
			member.setPhone(memberFileRow.getPhone());
			
			if(StringUtils.hasText(memberFileRow.getLastName())) {
				member.setLastName(memberFileRow.getLastName());
			}
			
			if(member.getBirthDate()==null || (member.getBirthDate() !=null && Constant.DEFULT_DATE_1900.compareTo(member.getBirthDate())==0)) {
				//old data - some member do not have birth date, update now from file
				member.setBirthDate(memberFileRow.getBirthDate());
			}
					
			boolean isMemberPrdChanged   = isMemberProductChanged(member,memberFileRow);
			boolean isMemberGroupChanged = isMemberGroupChanged(member,memberFileRow);
			
			//if product changed and new product is PLS remove spouse and dependents
			if(isMemberPrdChanged && isMemberProductPLS(memberFileRow,depedentFileRows)) {
				//validate agent commission & commission setup
				//setSingleProductType(memberFileRow);
				//agentCommissionSetupForPLSingleProduct(memberFileRow.getAgentId(),memberFileRow.getProductId());
				//agentHierarchySetupForPLSingleProduct(memberFileRow.getAgentCode(),memberFileRow.getProductId());				
				clearMemberSpouseAndDependents(memberSetup);
			}	
			
			Integer oldStatus = member.getActive(); 
			Date oldCancelDate = member.getCancelDate();
			Integer oldCancelReason = Optional.ofNullable(member.getCancelCode()).orElse(11);//default use 11 as cancel code if empty.
			
			if(oldStatus.equals(MemberActiveStatus.CANCELLED.getStatus()) || oldStatus.equals(MemberActiveStatus.SUSPENDED.getStatus()) || MemberAction.ACTIVATE.compareTo(memberFileRow.getMemberAction())==0)
			{ //change member status to active.
				member.setActive(MemberActiveStatus.ACTIVE.getStatus());
				member.setCancelDate(Constant.DEFULT_DATE_1900);
				member.setCancelCode(0);
				Date oldRenewDate = member.getRenewDate();
				Date newRenewDate = memberFileRow.getEffectiveDate();
				member.setRenewDate(newRenewDate);
				memberSetup.addNote("Member activated from EDI File ["+memberFileRow.getSourceFileName()+
						"] Old renew date ["+PTSUtilityService.formatUtilDate(oldRenewDate, "MMddyyyy")+"] "
						+ " New renew date ["+PTSUtilityService.formatUtilDate(newRenewDate, "MMddyyyy")+"]");
				
			}
			
			memberSetup.setGroupCd(memberFileRow.getGroupCode());
					
			populateMemberBenefitAddress(memberSetup,memberFileRow);
			populateMemberMailingAddress(memberSetup,memberFileRow);
			
			if(isMemberPrdChanged || isMemberGroupChanged || MemberAction.EMPLOYEEID_UPDATE.compareTo(memberFileRow.getMemberAction())==0)
			{
				memberSetup.setProductUpdate(true);				
				memberSetup.setEffectiveDate(new java.util.Date(memberFileRow.getEffectiveDate().getTime()).toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate());
				member.setEmployeeId(memberFileRow.getEmployeeId());
				populateMemberProduct(memberSetup, memberFileRow);
				
				if(isMemberPrdChanged) {
					memberFileRow.setMemberAction(MemberAction.PRODUCT_CHANGE);
				}
				else if(MemberAction.EMPLOYEEID_UPDATE.compareTo(memberFileRow.getMemberAction())==0) {
					memberSetup.setCopyProductAndCommission(true);
				}
			}
			
			boolean memberProductChanged = isMemberProductChanged(memberSetup,memberFileRow);
			boolean memberAddressZipChanged = isMemberAddressZipChanged(memberSetup,memberFileRow);
			boolean addressValid = true;
			
			memberSetup.addNote("Member updated from EDI File ["+memberFileRow.getSourceFileName()+"]");	
			
			if(addressValidationEnabled && (memberProductChanged || memberAddressZipChanged) ) {
				Optional<Map<Boolean,String>> data = doAddressValidation(memberSetup);
				
				if(data.isPresent()) {
					memberFileRow.setUspsAddrError(data.get().getOrDefault(Boolean.FALSE, null));
					if(StringUtils.hasText(data.get().getOrDefault(Boolean.TRUE, "")))
					{
						memberSetup.addNote(data.get().getOrDefault(Boolean.TRUE, ""));
					}
					else if(StringUtils.hasText(data.get().getOrDefault(Boolean.FALSE, "")))
					{
						memberSetup.addNote(data.get().getOrDefault(Boolean.FALSE, ""));
						addressValid=false;
					}
				}								
			}	
			
			memberSetup.setFulfillment(addressValid && memberProductChanged);
			
			memberFileRow.setFulfillmentPending(memberProductChanged && !addressValid);
			
			memberService.updateMember(memberSetup);
			
			if(memberSetup.isSuccess() && oldStatus.equals(MemberActiveStatus.CANCELLED.getStatus()) || oldStatus.equals(MemberActiveStatus.SUSPENDED.getStatus()) || MemberAction.ACTIVATE.compareTo(memberFileRow.getMemberAction())==0)
			{
				LocalDate lapseStartdate = new java.util.Date(oldCancelDate.getTime()).toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
				LocalDate lapseEndDate = new java.util.Date(memberFileRow.getEffectiveDate().getTime()).toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
				long months = ChronoUnit.MONTHS.between(lapseStartdate, lapseEndDate);
				if(months>=1)
				{
					//lapse coverage
					Optional<MemberCoverageLapse> optMemberCoverageLapse = memberService.getMemberCoverageLapseRequest(memberSetup.getMember(),
							MemberActiveStatus.CANCELLED.getStatus(),oldCancelReason,oldCancelDate,memberFileRow.getEffectiveDate());
					
					if(optMemberCoverageLapse.isPresent())
						memberService.addMemberCoverageLapseRequest(optMemberCoverageLapse.get());					
				}
				memberAuditEventService.addMemberAuditEvent(memberSetup.getMember(), AuditEventType.ACTIVE, memberSetup.getMember().getModifiedBy(), EDI_APPLICATION_SOURCE);
			}
			
			if(MemberAction.PRODUCT_CHANGE.compareTo(memberFileRow.getMemberAction())==0) {
				memberAuditEventService.addMemberAuditEvent(memberSetup.getMember(), AuditEventType.PRODUCT , memberSetup.getMember().getModifiedBy(), EDI_APPLICATION_SOURCE);
			}
			else if(MemberAction.GROUP_CHANGE.compareTo(memberFileRow.getMemberAction())==0)  {
				memberAuditEventService.addMemberAuditEvent(memberSetup.getMember(), AuditEventType.GROUP , memberSetup.getMember().getModifiedBy(), EDI_APPLICATION_SOURCE);
			}
			
			if(!memberSetup.isSuccess()) {
				log.error("Error updating member [{}] for employee id [{}] Message [{}] ",memberId,memberFileRow.getEmployeeId()
						,memberSetup.getErrorMessage());
			}
		}
		else
		{ //member not present
			log.error("Member not found [{}] for employee id [{}]",memberId,memberFileRow.getEmployeeId());
			memberSetup.setSuccess(false);
			memberSetup.addError("Member not found ["+memberFileRow.getMasaMemberId()+"] for employee id["+memberFileRow.getEmployeeId()+"] for update.");
		}
		updateFileRecordsWithStatus(memberSetup,depedentFileRows,memberFileRow);
		
		return memberFileRow;
	}

	private boolean isMemberProductPLS(MemberFile memberFileRow, Set<MemberFile> depedentFileRows) {
		return ("PLS".equalsIgnoreCase(memberFileRow.getProductType()));
	}

	private void clearMemberSpouseAndDependents(MemberUpdateSetup memberSetup) {
		// clear spouse and dependent data//file does not have any S/D rows,//remove
		// them on member
		if (StringUtils.hasText(memberSetup.getMember().getSpouseFirst())) {
			String note = Stream
					.of("Member spouse removed [", memberSetup.getMember().getSpouseFirst(),
							memberSetup.getMember().getSpouseMi(), memberSetup.getMember().getSpouseLast(),
							PTSUtilityService.formatUtilDate(memberSetup.getMember().getSpouseBirthDate(),
									"MM-dd-yyyy"),
							memberSetup.getMember().getSpousePhone(), memberSetup.getMember().getSpouseEmail(),
							"]")
					.filter(StringUtils::hasText).collect(Collectors.joining(" "));

			memberSetup.addNote(note);
			memberSetup.getMember().setSpouseFirst(null);
			memberSetup.getMember().setSpouseLast(null);
			memberSetup.getMember().setSpouseMi(null);
			memberSetup.getMember().setSpouseBirthDate(null);
			memberSetup.getMember().setSpouseEmail(null);
			memberSetup.getMember().setCellPhone(null);
		}
		
		if(!memberSetup.getMember().getDependants().isEmpty()) {
			memberSetup.getMember().getDependants().forEach(item->{
				String note = Stream.of("Member dependant removed [",item.getFirstName(),item.getMiName(),item.getLastName()
						,PTSUtilityService.formatUtilDate(item.getBirthDate(),"MM-dd-yyyy"),"]")
						.filter(StringUtils::hasText)
						.collect(Collectors.joining(" "));
				memberSetup.addNote(note);
			});					
			memberSetup.getMember().setDependants(Collections.emptySet());
		}
		
	}

	private boolean isMemberAddressZipChanged(MemberUpdateSetup memberSetup, MemberFile memberFileRow) {
		
		return ( (StringUtils.hasText(memberFileRow.getBenefitZip()) 
				&& memberSetup.getMember().getAddressDetails() !=null
				&& !memberFileRow.getBenefitZip().equalsIgnoreCase(memberSetup.getMember().getAddressDetails().getZip())
			) ||
			(StringUtils.hasText(memberFileRow.getMailingZip()) 
						&& memberSetup.getMember().getAlternateAddressDetails() !=null
						&& !memberFileRow.getMailingZip().equalsIgnoreCase(memberSetup.getMember().getAlternateAddressDetails().getZip())
			)
			||
			( StringUtils.hasText(memberFileRow.getBenefitZip()) && memberSetup.getMember().getAddressDetails()==null	)
			||
			( StringUtils.hasText(memberFileRow.getMailingZip()) && memberSetup.getMember().getAlternateAddressDetails()==null	)
			) ;
	}
	
	//for update member process, check if fulfillment required
	private boolean isMemberProductChanged(MemberUpdateSetup memberSetup, MemberFile memberFileRow) {
	
		//product change scenario
		Date productEffDate = getUpgradeProductEffectiveDate(memberFileRow.getEffectiveDate());
		
		//check if member has b2b upgrade product
		//b2buprade-emgr-emp ---> new product emp ::no fulfillment
		Optional<MemberFee> optUpgradePrd = memberSetup.getMember().getMemberFee().stream()
		.filter(memFee -> PTSUtilityService.isDateWithInRange(productEffDate,memFee.getEffectiveStartDate(),memFee.getEffectiveEndDate())
					&& memFee.getUpgradeType().compareTo(ProductUpgradeType.PRIMARY.getValue()) !=0
				)
		.findFirst();
		if(optUpgradePrd.isPresent()) {
			String existingUpgradePrdCode = optUpgradePrd.get().getProduct().getProductCategory().getCode();
			String newPrdCode = memberFileRow.getProductCategoryCode();
			if(existingUpgradePrdCode !=null && existingUpgradePrdCode.equalsIgnoreCase(newPrdCode)) {
				return false;
			}
		}		
		Optional<MemberFee> primaryActivePrd = memberSetup.getMember().getMemberFee().stream()
		.filter(memFee -> PTSUtilityService.isDateWithInRange(productEffDate, memFee.getEffectiveStartDate(), memFee.getEffectiveEndDate())
				&& memFee.getUpgradeType().compareTo(ProductUpgradeType.PRIMARY.getValue()) ==0				
				)
		.findFirst();
		
		//old emergent to new emergent scenario-ignore fulfillment
		//existing active product-category code != file product category code -> send fulfillment.
		if(primaryActivePrd.isPresent() && !memberFileRow.getProductCategoryCode()
				.equalsIgnoreCase(primaryActivePrd.get().getProduct().getProductCategory().getCode())) {
			return true;
		}		
		return false;				
	}
	
	private boolean isMemberGroupChanged(Member member,MemberFile memberFileRow)
	{
		return member.getMemberFee().stream()
		.filter(item-> (item.getUpgradeType().compareTo(ProductUpgradeType.PRIMARY.getValue())==0) && (memberFileRow.getEffectiveDate().compareTo(item.getEffectiveStartDate()) >=0  &&  memberFileRow.getEffectiveDate().compareTo(item.getEffectiveEndDate()) <=0 )
		&& (memberFileRow.getGroupCode().equalsIgnoreCase(item.getGroup().getGroupCode()))).count()==0;		
	}
	//hasDepedants :true  PL family
	//hasDependants :false PL Single if group has
	private boolean isMemberProductChanged(Member member,MemberFile memberFileRow)
	{
		Date productEffDate = getUpgradeProductEffectiveDate(memberFileRow.getEffectiveDate());
		//check if member has b2b upgrade product
		
		boolean upgradePrdExits = member.getMemberFee().stream()
		.filter(memFee -> PTSUtilityService.isDateWithInRange(productEffDate,memFee.getEffectiveStartDate(),memFee.getEffectiveEndDate())
					&& memFee.getUpgradeType().compareTo(ProductUpgradeType.PRIMARY.getValue()) !=0
				)
		.count()>=1;
		
		if(upgradePrdExits) {
			return true;
		}
		
		Optional<MemberFee> primaryActivePrd = member.getMemberFee().stream()
				.filter(memFee -> PTSUtilityService.isDateWithInRange(productEffDate, memFee.getEffectiveStartDate(), memFee.getEffectiveEndDate())
						&& memFee.getUpgradeType().compareTo(ProductUpgradeType.PRIMARY.getValue()) ==0				
						)
				.findFirst();
		
		return primaryActivePrd.isPresent() && primaryActivePrd.get().getProduct().getProductId().compareTo(memberFileRow.getProductId())!=0;
		
		/*
		return member.getMemberFee().stream()
		.filter(item-> (item.getUpgradeType().compareTo(ProductUpgradeType.PRIMARY.getValue())==0) 
				&& (PTSUtilityService.isDateWithInRange(productEffDate,item.getEffectiveStartDate(),item.getEffectiveEndDate()))
		&& (memberFileRow.getProductId().compareTo(item.getProduct().getProductId())!=0)).count()==0;
		*/		
	}
	
	// rules- if effective start date is > 3 months then set effective date as
	// current month first day
	// --possible scenario of b2b upgrade product added earlier
	private Date getUpgradeProductEffectiveDate(Date productEffectiveDate) {

		boolean effDateMoreThan3months = false;
		LocalDate currentDayFirstOfMonth = LocalDate.now().withDayOfMonth(1);
		long months = ChronoUnit.MONTHS.between(PTSUtilityService.convertUtilToLocalDate(productEffectiveDate),
				LocalDate.now().withDayOfMonth(1));
		if (months > Constant.MONTHS_RANGE_FOR_ENROLL) {
			effDateMoreThan3months = true;
		}
		return effDateMoreThan3months ? PTSUtilityService.convertLocalToUtilDate(currentDayFirstOfMonth)
				: productEffectiveDate;
	}
	

	/**
	 * @param memberFileRow
	 */
	private MemberFile processMemberCancel(MemberFile memberFileRow,Integer ptsUserId) 
	{
		Set<MemberFile> memberFileRecordToSave = new HashSet<>();
		memberFileRecordToSave.add(memberFileRow);
				
		MemberCancelSetup memberCancelSetup = new MemberCancelSetup();
		
		populateMemberDependentsRows(memberFileRow,memberFileRecordToSave);
		
		Integer memberId = memberFileRow.getMasaMemberId();
		if(memberId != null && memberId > 0) {
			//if member is already canceled and cancel date is different then update cancel date.
			String note =
					"Member status changed to cancelled from EDI File [" + memberFileRow.getSourceFileName() + "]";
			memberService
					.cancelMember(memberId, memberFileRow.getCancelDate(), ptsUserId, note,
							CANCEL_REASON_ID,
							EDI_APPLICATION_SOURCE);
		}
		else
		{
			memberCancelSetup.setError(true);
			memberCancelSetup.setErrorMessage("No active member found with Contract Number ["+memberFileRow.getContractNumber()+"] to process cancel");
			log.error("No active member found with Contract Number [{}] in Group [{}] for cancel.",memberFileRow.getContractNumber(),memberFileRow.getGroupCode());			
		}
		updateMemberFileRecordWithCancelStatus(memberFileRecordToSave,memberCancelSetup,memberId);		
		return memberFileRow;
	}
	
	/**
	 * @param memberFileRow
	 * @param ptsUserId
	 * @return
	 */
	private MemberFile processMemberRefresh(MemberFile memberFileRow) {
		
		Set<MemberFile> memberFileRecordToSave = new HashSet<>();
		memberFileRecordToSave.add(memberFileRow);
		
		populateMemberDependentsRows(memberFileRow,memberFileRecordToSave);
		
		updateMemberFileRecordWithRefreshStatus(memberFileRecordToSave);
		
		return memberFileRow;
	}
	
	/**
	 * @param memberFileRecordToSave
	 */
	private void updateMemberFileRecordWithRefreshStatus(Set<MemberFile> memberFileRecordToSave) {

		memberFileRecordToSave.forEach(item -> {
			item.setProcessed(Boolean.TRUE);
			item.setStatus(UploadStatus.SUCCESS);
			batchProcessResults.addSuccess(item);
			item.setProcessComments("Refresh action,No updates to member.");
			item.setMemberProcessedDate(new Date());
		});
		memberFileService.saveAll(memberFileRecordToSave,Boolean.TRUE);
	}
	
	/**
	 * @param memberFileRecordToSave
	 * @param cancelComments
	 * @param cancelError
	 * @param memberId
	 */
	private void updateMemberFileRecordWithCancelStatus(Set<MemberFile> memberFileRecordToSave,MemberCancelSetup memberCancelSetup,Integer memberId) {
		
		memberFileRecordToSave.forEach(item->{
			String comm=memberCancelSetup.getErrorMessage();
			item.setProcessed(Boolean.TRUE);
			if(memberCancelSetup.isError())
			{
				item.setStatus(UploadStatus.ERROR);
				batchProcessResults.addFailure(item);
			}
			else
			{
				item.setStatus(UploadStatus.SUCCESS);
				batchProcessResults.addSuccess(item);
			}			
			if(!"Cancel".equalsIgnoreCase(item.getModification()) && ("S".equalsIgnoreCase(item.getRelationship()) || "D".equalsIgnoreCase(item.getRelationship())) ) 
			{
				comm = comm.concat(" ").concat("Invalid Modification for dependent when Primary is being cancelled.");				
			}
			item.setProcessComments(comm);
			if("P".equalsIgnoreCase(item.getRelationship()) && memberCancelSetup.isError()) {
				item.addRecordErrorSet(new MemberFileRecordErrorEntity(item, "ERECORDUPDATE", "Error updating record ["+comm+"]"));
			}
			else if(memberCancelSetup.isError()){
				item.addRecordErrorSet(new MemberFileRecordErrorEntity(item, "EPRIMARYHASERRORS", "Primary record has errors"));
			}
			item.setMemberProcessedDate(new Date());
			if(memberId.intValue()>0)
				item.setMasaMemberId(memberId);			
		});
		memberFileService.saveAll(memberFileRecordToSave,Boolean.TRUE);
	}

	/**
	 * @param memberFileRow
	 * @param memberFileRecordToSave
	 */
	private void populateMemberDependentsRows(MemberFile memberFileRow, Set<MemberFile> memberFileRecordToSave) {
		
		Set<MemberFile> dependentSet = memberFileService
				.getAllDependentRecordsForPrimary(memberFileRow.getGroupCode(),memberFileRow.getContractNumber(), "P"
						,memberFileRow.getJobExecutionId(), false);
		
		for(MemberFile dependentRow: dependentSet)
		{
			memberFileRecordToSave.add(dependentRow);			
		}
	}
	
	private void populateMemberDates(MemberSetup memberSetup, MemberFile memberFileInput,Integer ptsUserId) {
		Member member = memberSetup.getMember();		
		member.setEffectiveDate(memberFileInput.getEffectiveDate());
		member.setRenewDate(memberFileInput.getEffectiveDate());
		member.setModifiedDate(new java.util.Date());
		member.setModifiedBy(ptsUserId);
		
	}
	private void populatePrimaryInfo(Member member,String firstName,String lastName,String mi,Date birthDate,String cellPhone,String email,String phone) {		
		member.setFirstName(firstName);
		member.setLastName(lastName);
		if(!StringUtils.isEmpty(mi))
			member.setMi(mi);
		member.setBirthDate(birthDate);
		
		if(!StringUtils.isEmpty(cellPhone))
			member.setCellPhone(cellPhone);
		
		if(!StringUtils.isEmpty(email))
			member.setEmail(email);
		
		if(!StringUtils.isEmpty(phone))
				member.setPhone(phone);
	}
	private void populateOtherInfo(Member member, MemberFile memberFileInput) {
		if(StringUtils.hasText(memberFileInput.getContractNumber())) {
			member.setEmployeeId(memberFileInput.getContractNumber());
			member.setAlterId(memberFileInput.getContractNumber());
			member.setEmployer(memberFileInput.getEmployer());
		}
	}
	
	private void populateMemberBenefitAddress(MemberSetup memberSetup,MemberFile memberFileItem) 
	{
		Address address;
		if(StringUtils.hasText(memberFileItem.getBenefitAddress()))
		{
			address = new Address();
			address.setAddress1(memberFileItem.getBenefitAddress());
			address.setAddress2(memberFileItem.getBenefitAddressLine2());
			address.setAddress3("");
			address.setCity(memberFileItem.getBenefitCity());		
			
			address.setCountry(this.defaultEDICountry.getCountryId());
			address.setCountryDetails(this.defaultEDICountry);
			address.setCountryCode(this.defaultEDICountry.getName());
						
			State state = StreamSupport.stream(states.spliterator(), false).filter(item->memberFileItem.getBenefitState().equalsIgnoreCase(item.getSymbol())).findFirst().orElse(null);
			if(null != state) 
			{
				address.setState(state.getStateId());
				address.setStateDetails(state);
				memberSetup.getMember().setSoldRegion(state.getStateId());			
			}			
			
			if(null ==  state)
			{
				log.error("Could not find state [{}] or country[{}] to set in benefit address",memberFileItem.getBenefitState(),memberFileItem.getBenefitCountry());
			}
			if(StringUtils.hasText(memberFileItem.getBenefitZip()))
			{
				String [] zipValues = memberFileItem.getBenefitZip().split("-");
				if(zipValues.length==2)
				{
					address.setZip(zipValues[0]);
					address.setZip4(zipValues[1]);	
				}
				else
				{
					address.setZip(memberFileItem.getBenefitZip());
					address.setZip4("");
				}				
			}
			address.setLastModifiedDate(new Date());
			memberSetup.setBenefitAddress(address);
		}
		
	}
	
	/**
	 * @param memberSetup
	 * @param memberFileItem
	 */
	private void populateMemberMailingAddress(MemberSetup memberSetup,MemberFile memberFileItem) 
	{
		if(StringUtils.hasText(memberFileItem.getMailingAddress()))
		{
			Address address = new Address();
			address.setAddress1(memberFileItem.getMailingAddress());
			address.setAddress2(memberFileItem.getMailingAddressLine2());
			address.setAddress3("");
			address.setCity(memberFileItem.getMailingCity());						
			
			address.setCountry(this.defaultEDICountry.getCountryId());
			address.setCountryDetails(this.defaultEDICountry);
			address.setCountryCode(this.defaultEDICountry.getName());
			
			State state = StreamSupport.stream(states.spliterator(), false).filter(item->memberFileItem.getMailingState().equalsIgnoreCase(item.getSymbol())).findFirst().orElse(null);
			if(null != state) 
			{
				address.setState(state.getStateId());
				address.setStateDetails(state);
			}			
			if(null ==  state)
			{
				log.error("Could not find state [{}] or country[{}] to set in mailing address",memberFileItem.getMailingState(),memberFileItem.getMailingCountry());
			}
			if(StringUtils.hasText(memberFileItem.getMailingZip()))
			{
				String [] zipValues = memberFileItem.getMailingZip().split("-");
				if(zipValues.length==2)
				{
					address.setZip(zipValues[0]);
					address.setZip4(zipValues[1]);	
				}
				else
				{
					address.setZip(memberFileItem.getMailingZip());
					address.setZip4("");
				}				
			}			
			address.setLastModifiedDate(new Date());
			memberSetup.setMailingAddress(address);
		}
		else {
			memberSetup.setMailingAddress(null);
		}
	}
	
	private void populateMemberProduct(MemberSetup memberSetup,MemberFile memberFileInput)
	{	
		MemberSetupProduct product = new MemberSetupProduct();
		product.setProductId(memberFileInput.getProductId());
		product.setAgentNum(memberFileInput.getAgentCode());
		product.setProductUpgradeType(ProductUpgradeType.PRIMARY.getValue());		
		product.setSoldDate(memberFileInput.getSoldDate());		
		product.setOverrideFee(false);	
		product.setGroupCode(memberFileInput.getGroupCode());
		product.setEffectiveStartDate(memberFileInput.getEffectiveDate());
		product.setEffectiveEndDate(Constant.DEFAULT_PRODUCT_END_DATE);
		memberSetup.addProduct(product);		
	}
	private void populateMemberProductForNewMember(MemberSetup memberSetup,MemberFile memberFileInput)
	{	
		MemberSetupProduct product = new MemberSetupProduct();
		product.setProductId(memberFileInput.getProductId());
		product.setAgentNum(memberFileInput.getAgentCode());
		product.setProductUpgradeType(ProductUpgradeType.PRIMARY.getValue());
		product.setSoldDate(memberFileInput.getSoldDate());
		product.setOverrideFee(false);
		product.setGroupCode(memberFileInput.getGroupCode());
		product.setEffectiveStartDate(memberFileInput.getEffectiveDate());
		product.setEffectiveEndDate(Constant.DEFAULT_PRODUCT_END_DATE);
		memberSetup.addProduct(product);		
	}
	
	private String handleUnprocessedDepedentRecordInTransaction(BatchProcessResults batchProcessResults) {
		TransactionTemplate transactionTemplate = createNewTransaction("UNPROCESSEDDEPRECORDS");
		return transactionTemplate.execute(action->handleUnprocessedDepedentRecords(batchProcessResults));
	}
	
	private MemberFile handlePrimaryRecordExceptionInTransaction(MemberFile memberFileRow, Exception recordException) {
		TransactionTemplate transactionTemplate = createNewTransaction("PRIMARYRECEXCEP");
		return transactionTemplate.execute(action->handlePrimaryRecordException(memberFileRow,recordException));
	}
	
	private MemberFile processMemmberRefreshInTransaction(MemberFile memberFileRow) {
		TransactionTemplate transactionTemplate = createNewTransaction("MEMBERUPDATE");
		return transactionTemplate.execute(action->processMemberRefresh(memberFileRow));			
	}
	
	private MemberFile processMemberUpdateInTransaction(MemberFile memberFileRow,Integer ptsUserId) {
		TransactionTemplate transactionTemplate = createNewTransaction("MEMBERUPDATE");
		return transactionTemplate.execute(action->processMemberUpdate(memberFileRow,ptsUserId));			
	}

	private MemberFile processMemberCancelInTransaction(MemberFile memberFileRow,Integer ptsUserId) {
		TransactionTemplate transactionTemplate = createNewTransaction("MEMBERCANCEL");
		return transactionTemplate.execute(action->processMemberCancel(memberFileRow,ptsUserId));		
	}

	private MemberFile processMemberAddInTransaction(MemberFile memberFileRow,Integer ptsUserId) {
		TransactionTemplate transactionTemplate = createNewTransaction("MEMBERADD");
		return transactionTemplate.execute(action->processMemberAdd(memberFileRow,ptsUserId));	
	}
	private TransactionTemplate createNewTransaction(String name) {
		TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
		transactionTemplate.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
		transactionTemplate.setName(name);
		return transactionTemplate;
	}
}
