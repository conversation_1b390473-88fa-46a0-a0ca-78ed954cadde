package com.masa.pts.edi.member.report.pdf;

import java.text.DateFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.itextpdf.text.BaseColor;
import com.itextpdf.text.Chunk;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Element;
import com.itextpdf.text.Font;
import com.itextpdf.text.Paragraph;
import com.itextpdf.text.Phrase;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.masa.pts.core.domain.MemberFile;

public class PDFCreator {


    private static final String[] HEADER_ARRAY = {"Action","Mem ID","Employee Id","Member Name","P/S/D","Product","Agent","Group","Effective Date","Renew Date","Status","Comments"};
 
    private static final String[] JOB_PARAMS_HEADER_ARRAY = {"Parameter", "Value"};
   
    private static final float[] HEADER_COLUMN_WIDTH =  { 25,30,40,65,20,25,35,35,40,40,40,100};
   
    private static final float[] JOB_PARAMS_HEADER_COLUMN_WIDTH = {90,82};
    
    public static final  Font MEDIUM_BOLD = new Font(Font.FontFamily.TIMES_ROMAN, 12, Font.BOLD);
    
    public static final  Font SMALL_BOLD = new Font(Font.FontFamily.TIMES_ROMAN, 6, Font.BOLD);

    public  static final  Font NORMAL_FONT = new Font(Font.FontFamily.TIMES_ROMAN, 6,Font.NORMAL);
       
    public static void addMetaData(Document document) {

        document.addTitle("Forte Disputes Process Results");

        document.addSubject("Using iText");

        document.addAuthor("Venu Sukka");
    }
    
    /**
     * @param document
     * @param params
     * @throws DocumentException
     */
    public static void addJobParameterTable(Document document,Map<String,String> params,String heading) throws DocumentException {
		Paragraph preface = new Paragraph();
		preface.add(new Phrase(heading, PDFCreator.SMALL_BOLD));
		preface.setAlignment(Element.ALIGN_LEFT);
		PdfPTable paramsTable = new PdfPTable(JOB_PARAMS_HEADER_ARRAY.length);
		paramsTable.setTotalWidth(JOB_PARAMS_HEADER_COLUMN_WIDTH);
		paramsTable.setLockedWidth(true);

		paramsTable.setHorizontalAlignment(Element.ALIGN_LEFT);
		addHeaderInTable(JOB_PARAMS_HEADER_ARRAY, paramsTable);
		
		Iterator<String> itr = params.keySet().iterator();
		while(itr.hasNext()) {
			String key = itr.next();
            addToTable(paramsTable, key,null,Element.ALIGN_LEFT);
            addToTable(paramsTable, params.get(key),null,Element.ALIGN_LEFT);
		}
		preface.add(paramsTable);		
		document.add(preface);
    }
    
    public static void addEndOfReport(Document document, String message) throws DocumentException {
    	 Paragraph paragraph = new Paragraph();
    	 paragraph.add(new Chunk(message,SMALL_BOLD));
    	 document.add(paragraph);
    }

    public static void addContent(Document document, List<MemberFile> processMessagesList,String header) throws DocumentException {

        Paragraph paragraph = new Paragraph();

        paragraph.setFont(NORMAL_FONT);

        createReportTable(paragraph, processMessagesList,header);

        addEmptyLine(paragraph, 2);
        
        document.add(paragraph);

    }

    private static void createReportTable(Paragraph paragraph, List<MemberFile> memberFileList,String header)

    throws DocumentException {

        PdfPTable table = new PdfPTable(HEADER_ARRAY.length);
        table.setTotalWidth(HEADER_COLUMN_WIDTH);
        table.setLockedWidth(true);
        table.setHeaderRows(1);
        table.setHorizontalAlignment(Element.ALIGN_LEFT);

        paragraph.add(new Chunk(header, SMALL_BOLD));

        if(null == memberFileList){
            paragraph.add(new Chunk("No data to display."));
            return;
        }
        addHeaderInTable(HEADER_ARRAY, table);

        int count = 0;
       
        SimpleDateFormat dateFormat = (SimpleDateFormat) DateFormat.getInstance();
		dateFormat.applyPattern("MM/dd/yyyy");
        
        for(MemberFile dataObject : memberFileList){

        	addToTable(table, dataObject.getModification(),null,Element.ALIGN_LEFT);
        	    	
        	addToTable(table, String.valueOf(dataObject.getMasaMemberId()==null? "" : dataObject.getMasaMemberId()),null,Element.ALIGN_LEFT);

            addToTable(table, dataObject.getEmployeeId(),null,Element.ALIGN_LEFT);
            
            addToTable(table, dataObject.getFirstName() + " "+ dataObject.getMiddleName() + " " + dataObject.getLastName() ,null,Element.ALIGN_LEFT);
            
            addToTable(table, dataObject.getRelationship(),null,Element.ALIGN_LEFT);
            
            addToTable(table, dataObject.getProductType(),null,Element.ALIGN_LEFT);
          
            addToTable(table, dataObject.getAgentCode(),null,Element.ALIGN_LEFT);
            
            addToTable(table, dataObject.getGroupCode(),null,Element.ALIGN_LEFT);
            
            String dateStr =   dataObject.getEffectiveDate() == null ? "": dateFormat.format(dataObject.getEffectiveDate());
            addToTable(table, dateStr,null,Element.ALIGN_LEFT);
            
            dateStr =   dataObject.getRenewDate() == null ? "": dateFormat.format(dataObject.getRenewDate());
            addToTable(table, dateStr,null,Element.ALIGN_LEFT);
            
            addToTable(table, dataObject.getStatus().toString(),null,Element.ALIGN_LEFT);
            
            addToTable(table, "Errors ["+dataObject.concatenateErrorMessages() != null ? dataObject.concatenateErrorMessages() : "" + "] Comments [ "+ dataObject.getProcessComments() != null ? dataObject.getProcessComments() : "" + "]",
            		null,Element.ALIGN_LEFT);
            
            count++;
            
        }             
        PdfPCell cell5 = new PdfPCell(new Phrase( "Total Count = "+ NumberFormat.getIntegerInstance().format(count), PDFCreator.SMALL_BOLD));
    	cell5.setHorizontalAlignment(Element.ALIGN_RIGHT);
    	cell5.setColspan(4);
        table.addCell(cell5);
        
        PdfPCell cell4 = new PdfPCell(new Phrase("", PDFCreator.NORMAL_FONT));
    	cell4.setHorizontalAlignment(Element.ALIGN_LEFT);
    	cell4.setColspan(9);
        table.addCell(cell4);
        
        paragraph.add(table);
        
    }
    
       
    public static void addEmptyLine(Paragraph paragraph, int number) {

        for (int i = 0; i < number; i++) {

            paragraph.add(new Paragraph(" "));

        }

    }

    public static void addHeaderInTable(String[] headerArray, PdfPTable table){

        PdfPCell c1 = null;

        for(String header : headerArray) {

            c1 = new PdfPCell(new Phrase(header, PDFCreator.SMALL_BOLD));

            c1.setBackgroundColor(BaseColor.GRAY);

            c1.setHorizontalAlignment(Element.ALIGN_LEFT);
            
            table.addCell(c1);

        }

        table.setHeaderRows(1);

    }

    public static void addToTable(PdfPTable table, String data,BaseColor color,int elignment){        

    	PdfPCell cell = new PdfPCell(new Phrase(data, PDFCreator.NORMAL_FONT));
    	if(null != color)
    		cell.setBackgroundColor(color);
    	cell.setHorizontalAlignment(elignment);
        table.addCell(cell);

    }

    public  Paragraph getParagraph(){        

        Paragraph paragraph = new Paragraph();

        paragraph.setFont(PDFCreator.NORMAL_FONT);

        addEmptyLine(paragraph, 1);

        return paragraph;

    }	
}
