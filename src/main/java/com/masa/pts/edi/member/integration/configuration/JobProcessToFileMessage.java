package com.masa.pts.edi.member.integration.configuration;

import java.io.File;

import org.springframework.integration.annotation.Transformer;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;

public class JobProcessToFileMessage {
	
	@Transformer
	public Message<?> toRequest(Message<?> message) {
		File payloadFile = (File)message.getHeaders().get("file_originalFile");
		return MessageBuilder.withPayload(payloadFile)
		  				.copyHeaders(message.getHeaders())
		  				.build();
	}
}
