package com.masa.pts.core.constant;

public enum PTSUserType {
	LEGACY(0), //PTS users
	EXTERNAL(1),// TPA Admin
	INTERNAL(2);
	
	private final Integer userType;

	private PTSUserType(Integer userType) {
		this.userType = userType;
	}

	public Integer getUserType() {
		return userType;
	}
	
	public static boolean isEmployee(Integer userType) {
		return (userType.compareTo(LEGACY.userType)==0 || userType.compareTo(INTERNAL.userType)==0);
	}
}
