package com.masa.pts.core.constant;

public enum MemberCreditCardType {
	UNKNOWN(0),
	VISA(1),
	MAST(2),
	AMER(3),
	DISC(4),
	DINE(5),
	JCB(6);
	
	private final int cardType;

	public int getCardType() {
		return cardType;
	}

	private MemberCreditCardType(int cardType) {
		this.cardType = cardType;
	}
	
	/**
	 * @param cardName
	 * @return
	 */
	public static MemberCreditCardType getCreditCardTypeByName(String cardName) {		
		for(MemberCreditCardType cardItem: MemberCreditCardType.values()) {			
			if(cardItem.name().equalsIgnoreCase(cardName))
				return cardItem;
		}		
		return UNKNOWN;
	}
}
