package com.masa.pts.core.constant;

public enum FulfillmentType {
	PACKAGE(1),
	CARD(2),
	MSA(3);
	
	private final Integer type;

	public Integer getType() {
		return type;
	}

	FulfillmentType(Integer type) {
		this.type = type;
	}

	public static FulfillmentType getValue(int value) {
		for(FulfillmentType e: FulfillmentType.values()) {
			if(e.type == value) {
				return e;
			}
		}

		return null;
	}
}
