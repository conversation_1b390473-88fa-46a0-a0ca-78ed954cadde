package com.masa.pts.core.constant;

public class InvoiceConstant {

	public static enum InvoiceType {
		GROUP_INDIVIDUAL(1),
		GROUP_INVOICE(2),
		GROUP_ROSTER(3);
		final Integer type;

		private InvoiceType(Integer type) {
			this.type = type;
		}
		public Integer getType() {
			return type;
		}	
	}
	public static enum InvoicePayType {
		PAID(1),		
		UNPAID(0);
		final Integer type;

		private InvoicePayType(Integer type) {
			this.type = type;
		}
		public Integer getType() {
			return type;
		}	
	}
}
