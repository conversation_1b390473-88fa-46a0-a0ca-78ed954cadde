package com.masa.pts.core.constant;

public enum MemberPayType {
	UNKNOWN(0),
	CREDITCARD(1),
	CHEC<PERSON>(2),
	MONEYORDER(3),
	CASH(4),
	EFT(5),
	ACH(6);
	
	private final int payType;

	public int getPayType() {
		return payType;
	}

	private MemberPayType(int payType) {
		this.payType = payType;
	}	
	
	public static MemberPayType getPayTypeByValue(String payType) {		
		for(MemberPayType item: MemberPayType.values()) {
			if(item.name().equalsIgnoreCase(payType))
				return item;
		}
		return UNKNOWN;
	}
	
	public static MemberPayType getPayTypeById(int payType) {
		for(MemberPayType item: MemberPayType.values()) {
			if(item.payType == payType)
				return item;
		}
		return UNKNOWN;
	}
}
