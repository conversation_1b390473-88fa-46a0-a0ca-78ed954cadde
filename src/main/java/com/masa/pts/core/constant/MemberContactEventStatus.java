package com.masa.pts.core.constant;

public enum MemberContactEventStatus {
	CREATED(1),
	PENDING(2),
	COMPLETED(3),
	CANCELLED(4);
	
	private final Integer status;

	private MemberContactEventStatus(Integer status) {
		this.status = status;
	}

	public Integer getStatus() {
		return status;
	}
	
	public static MemberContactEventStatus getStatus(Integer status) {
		
		for(MemberContactEventStatus eventStatus: MemberContactEventStatus.values()) {
			if(status.compareTo(eventStatus.getStatus())==0)
				return eventStatus;
		}
		return MemberContactEventStatus.CREATED;
	}
}
