package com.masa.pts.core.constant;

import java.util.Arrays;

public enum ProductFamilyType {
	UNKNOWN(0),
	SINGLE(1),
	FAMILY(2);
	
	private final Integer type;

	public Integer getType() {
		return type;
	}

	private ProductFamilyType(Integer type) {
		this.type = type;
	}
	
	public static ProductFamilyType getProductFamilyTypeByType(Integer type) {
		return Arrays.stream(values()).filter(value -> value.getType().equals(type))
				.findFirst().orElse(UNKNOWN);
	}
}
