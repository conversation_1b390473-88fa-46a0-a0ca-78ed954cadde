package com.masa.pts.core.constant;

public enum MemberPayStatus {
	DEFAULT,
	PAID_IN_FULL,
    CURRENT,
    BALANCE_DUE;    
    
    /*private final int payStatus;
    
    MemberPayStatus(int payStatus)
    {
	this.payStatus = payStatus;
    }

    public int getPayStatus()
    {
        return payStatus;
    }	   
 */   
    public static MemberPayStatus getPayStatusByValue(MemberPayStatus payStatus) {
	for(MemberPayStatus item: MemberPayStatus.values()) {
	    if(item.equals(payStatus))
	    {
	    	return item;
	    }
	}
	return MemberPayStatus.DEFAULT;
    }
}