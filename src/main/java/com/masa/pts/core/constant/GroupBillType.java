package com.masa.pts.core.constant;

import java.util.Arrays;
import java.util.Optional;

public enum GroupBillType {

	INDIVIDUAL(1),
	INVOICE(2),
	ROSTER(3);
	
	private final Integer billType;

	public Integer getBillType() {
		return billType;
	}

	GroupBillType(Integer billType) {
		this.billType = billType;
	}

	public static Optional<GroupBillType> getGroupBillTypeByType(Integer type) {
		return Arrays.stream(values()).filter(value -> value.getBillType().equals(type)).findFirst();
	}
}
