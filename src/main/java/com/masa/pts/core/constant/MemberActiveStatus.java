package com.masa.pts.core.constant;

public enum MemberActiveStatus {
	CANCELLED(0),
	ACTIVE(1),	
	SUSPENDED(2);
	
	private final Integer status;
	
	private MemberActiveStatus(Integer status) {
		this.status = status;
	}
	
	public Integer getStatus() {
		return status;
	}	
	
	public static MemberActiveStatus getStatusName(Integer status) {
		
		for(MemberActiveStatus event: MemberActiveStatus.values()) {
			if(status.compareTo(event.getStatus())==0)
					return event;
		}
		return MemberActiveStatus.CANCELLED;
	}
	
	public static boolean isActive(String status)
	{
		return status.equalsIgnoreCase(ACTIVE.name());
	}
}
