package com.masa.pts.core.constant;

public enum MemberContactEventType {
	RETENTION(1),
	ACHREJECT(2),
	CCDECLINE(3),
	CCDISPUTE(4),
	CANCELLATION(5);
	
	private final Integer type;

	public Integer getType() {
		return type;
	}

	private MemberContactEventType(Integer type) {
		this.type = type;
	}
	
	public static MemberContactEventType getType(Integer type) {		
		for(MemberContactEventType event: MemberContactEventType.values()) {
			if(type.compareTo(event.getType())==0)
					return event;
		}		
		return MemberContactEventType.RETENTION;
	}
}
