package com.masa.pts.core.constant;

import java.util.Arrays;
import java.util.Optional;

public enum MemberRenewType {
	RN(1),
	N(0);

	private final Integer renewType;

	public Integer getRenewType() {
		return renewType;
	}

	MemberRenewType(Integer renewType) {
		this.renewType = renewType;
	}

	public static Optional<MemberRenewType> getMemberRenewTypeByType(Integer type) {
		return Arrays.stream(values()).filter(value -> value.getRenewType().equals(type)).findFirst();
	}
}
