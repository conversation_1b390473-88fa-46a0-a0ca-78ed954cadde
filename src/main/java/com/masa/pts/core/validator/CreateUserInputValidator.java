package com.masa.pts.core.validator;

import com.masa.pts.core.constant.PTSUserType;
import com.masa.pts.core.domain.PTSUser;
import com.masa.pts.core.domain.Role;
import com.masa.pts.core.domain.SalesChannel;
import com.masa.pts.core.model.CreateUserInput;
import com.masa.pts.core.service.CommonDataService;
import com.masa.pts.core.service.GroupService;
import com.masa.pts.core.service.PTSUserService;
import org.springframework.stereotype.Component;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class CreateUserInputValidator implements ConstraintValidator<NotValidCreateUserInput, CreateUserInput> {

	GroupService groupService;
	PTSUserService ptsUserService;
	CommonDataService commonDataService;	
	
	public CreateUserInputValidator(GroupService groupService, PTSUserService ptsUserService,
			CommonDataService commonDataService) {
		this.groupService = groupService;
		this.ptsUserService = ptsUserService;
		this.commonDataService = commonDataService;
	}

	@Override
	public boolean isValid(CreateUserInput input, ConstraintValidatorContext context) {
		
		if(!(input instanceof CreateUserInput)) {
			throw new IllegalArgumentException("@NotValidCreateUserInput only applies to CreateUserInput");
		}
		
		if(null==input.getUserType())
		{
			return addValidationMessage("UserType is required", context);
		}
		Optional<PTSUser> optPtsUser = ptsUserService.findByUserName(input.getUserName());
		
		if(optPtsUser.isPresent())
		{
			return addValidationMessage("User with user name ["+input.getUserName()+"] already exists.", context);
		}
		
		if(input.getRoles().isEmpty())
		{
			return addValidationMessage("Atleast 1 user role is required", context);
		}
		
		if(!isValidRoles(input,context))
		{
			return false;
		}
		
		if(PTSUserType.EXTERNAL.compareTo(input.getUserType())==0)
		{
			if(input.getGroups().isEmpty())
			{
				return addValidationMessage("User Type ["+PTSUserType.EXTERNAL+"] require at least 1 group code", context);				
			}
			if(!isValidGroups(input,context))
			{
				return false;
			}			
			if(!input.getSalesChannels().isEmpty())
			{
				return addValidationMessage("User Type ["+PTSUserType.EXTERNAL+"] cannot have sales channel", context);
			}
		}
		else if(PTSUserType.INTERNAL.compareTo(input.getUserType())==0)
		{
			if(input.getSalesChannels().isEmpty())
			{
				return addValidationMessage("User Type ["+PTSUserType.INTERNAL+"] require at least 1 sales channel", context);				
			}
			if(!isValidSalesChannels(input,context))
			{
				return false;
			}
			if(!input.getGroups().isEmpty())
			{
				return addValidationMessage("User Type ["+PTSUserType.INTERNAL+"] cannot have groups", context);
			}
		}
		return true;
	}

	/**
	 * @param input
	 * @param context
	 * @return
	 */
	private boolean isValidSalesChannels(CreateUserInput input, ConstraintValidatorContext context) {
		Iterable<SalesChannel> scSet = commonDataService.getAllSalesChannels();
		Set<Integer> scIdSet = new HashSet<>();
		scSet.iterator().forEachRemaining(item->scIdSet.add(item.getId()));
		for(Integer scId: input.getSalesChannels())
		{
			if(!scIdSet.contains(scId))
			{
				context.disableDefaultConstraintViolation();
				context.buildConstraintViolationWithTemplate("Invalid sales channel id ["+scId+"]").addConstraintViolation();
				return false;
			}
		}
		return true;
	}

	/**
	 * @param input
	 * @param context
	 * @return
	 */
	private boolean isValidGroups(CreateUserInput input, ConstraintValidatorContext context) {
		Map<Integer,String> groupIdCodeMap = groupService.getGroupCodeByIds(input.getGroups());
		for(Integer groupId: input.getGroups())
		{
			if(groupIdCodeMap.getOrDefault(groupId, null) ==null)
			{
				context.disableDefaultConstraintViolation();
				context.buildConstraintViolationWithTemplate("Invalid group id ["+groupId+"]").addConstraintViolation();
				return false;
			}
		}		
		return true;
	}

	/**
	 * @param input
	 * @param context
	 * @return
	 */
	private boolean isValidRoles(CreateUserInput input, ConstraintValidatorContext context) {
		Set<Integer> roles = ptsUserService.getAllRoles().stream()
				.map(Role::getId)
				.collect(Collectors.toSet());

		List<Integer> invalidRoles = input.getRoles().stream()
				.filter(role -> !roles.contains(role))
				.collect(Collectors.toList());

		if(!invalidRoles.isEmpty()) {
			context.disableDefaultConstraintViolation();

			context.buildConstraintViolationWithTemplate(
					"Invalid role ids is" + invalidRoles.toString()).addConstraintViolation();
		}

		return invalidRoles.isEmpty();
	}

	/**
	 * @param message
	 * @param context
	 * @return
	 */
	public boolean addValidationMessage(String message, ConstraintValidatorContext context) {
		context.disableDefaultConstraintViolation();
		context.buildConstraintViolationWithTemplate(message).addConstraintViolation();
		return false; 
	}
}
