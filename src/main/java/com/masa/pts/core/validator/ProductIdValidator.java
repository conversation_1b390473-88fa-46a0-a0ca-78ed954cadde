package com.masa.pts.core.validator;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

import org.springframework.beans.factory.annotation.Autowired;

import com.masa.pts.core.service.ProductService;

public class ProductIdValidator implements ConstraintValidator<NotValidProductId, Integer> {

	@Autowired
	private ProductService productService;
	
	@Override
	public boolean isValid(Integer value, ConstraintValidatorContext context) {
		
		if(value == null)
			return false;
		
		if(value.intValue()<=0)
			return false;
		
		return productService.existsByProductId(value);
	}

}
