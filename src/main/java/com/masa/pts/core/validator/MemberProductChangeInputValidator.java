package com.masa.pts.core.validator;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import com.masa.pts.core.constant.ProductFamilyType;
import com.masa.pts.core.constant.ProductUpgradeType;
import com.masa.pts.core.domain.ConfigParamName;
import com.masa.pts.core.domain.Constant;
import com.masa.pts.core.domain.GroupEntity;
import com.masa.pts.core.domain.Member.MemberSummary;
import com.masa.pts.core.domain.MemberFee;
import com.masa.pts.core.domain.Product;
import com.masa.pts.core.model.MemberDependantInput;
import com.masa.pts.core.model.MemberProductChangeInput;
import com.masa.pts.core.model.MemberProductInput;
import com.masa.pts.core.service.AgentService;
import com.masa.pts.core.service.ConfigParamService;
import com.masa.pts.core.service.GroupService;
import com.masa.pts.core.service.MemberProductChangeService;
import com.masa.pts.core.service.MemberService;
import com.masa.pts.core.service.PaymentService;
import com.masa.pts.core.service.ProductService;

public class MemberProductChangeInputValidator implements ConstraintValidator<NotValidMemberProductChangeInput, MemberProductChangeInput> {

	private GroupService groupService;
	private AgentService agentService;
	private MemberProductChangeService memberProductChangeService;
	private ProductService productService;
	private MemberService memberService;
	private PaymentService paymentService;
	private ConfigParamService configParamService;
		
	@Autowired
	public MemberProductChangeInputValidator(GroupService groupService, AgentService agentService,
			MemberProductChangeService memberProductChangeService, ProductService productService,
			MemberService memberService,PaymentService paymentService, ConfigParamService configParamService) {
		super();
		this.groupService = groupService;
		this.agentService = agentService;
		this.memberProductChangeService = memberProductChangeService;
		this.productService = productService;
		this.memberService = memberService;
		this.paymentService = paymentService;
		this.configParamService=configParamService;
	}

	@Override
	public boolean isValid(MemberProductChangeInput input, ConstraintValidatorContext context) {

		if (!(input instanceof MemberProductChangeInput)) {
			throw new IllegalArgumentException("@NotValidMemberProductChangeInput only applies to MemberProductChangeInput ");
		}
		
		if(input.getProducts().isEmpty())
		{
			return addValidationMessage("Atleast one product is required to create member",context);
		}
		
		Map<Integer,Integer> upgradeTypeCnt = new HashMap<>();
		Map<Integer,Integer> productIdCnt = new HashMap<>();
		
		for (MemberProductInput prdItem : input.getProducts()) 
		{
			if(prdItem.isOverrideFee() && (prdItem.getPayAmount() ==null || (prdItem.getPayAmount() !=null && prdItem.getPayAmount()<=0)))
			{
				return addValidationMessage("Product amount cannot be zero , Product ID ["+prdItem.getProductId()+"] Amount ["+prdItem.getPayAmount()+"]", context);
			}					
			if (!groupService.isProductValidForGroup(input.getGroupCode(), prdItem.getProductId())) 
			{
				return addValidationMessage("Invalid Product ID["+prdItem.getProductId()+"] for Group ["+input.getGroupCode()+"]",context);				
			}
			/*
			if(!agentService.isAgentCommissionSetupForProduct(prdItem.getAgentNum(),prdItem.getProductId()))
			{
				return	addValidationMessage("Invalid Product ID["+prdItem.getProductId()+"] for Agent ["+prdItem.getAgentNum()+"]",context);
			}
			//*/
			if(upgradeTypeCnt.get(prdItem.getProductUpgradeType())==null)
				upgradeTypeCnt.put(prdItem.getProductUpgradeType(),Integer.valueOf(1));
			else
			{
				upgradeTypeCnt.put(prdItem.getProductUpgradeType(), (upgradeTypeCnt.get(prdItem.getProductUpgradeType())+1));
			}
			
			if(upgradeTypeCnt.get(prdItem.getProductUpgradeType()).intValue()>1)
			{				
				return addValidationMessage("Invalid Upgrade type value for Product ID["+prdItem.getProductId()+"]",context);				
			}
			
			if(productIdCnt.get(prdItem.getProductId())==null)
				productIdCnt.put(prdItem.getProductId(),Integer.valueOf(1));
			else
			{
				productIdCnt.put(prdItem.getProductId(), (productIdCnt.get(prdItem.getProductId())+1));
			}
			
			if(productIdCnt.get(prdItem.getProductId()).intValue()>1)
			{
				return addValidationMessage("Multiple Prodcts provided with same Product ID["+prdItem.getProductId()+"]",context);				
			}
		}
		if(upgradeTypeCnt.get(ProductUpgradeType.PRIMARY.getValue())==null)
		{
			return addValidationMessage("No primary product provided to update member",context);
		}
		
		if(checkNameLength(input,context))
			return false;
		
		if(checkDependantsNameLength(input,context))
			return false;
		
		if(checkForDependants(input,context))
			return false;
		
		if(isValidProductUpgrade(input,context))
			return false;
		
		if(isValidProductEffectiveDate(input,context))
			return false;
		
		if(canProductBeUpgraded(input,context))
			return false;
		
		return true;
	}

	/**
	 * @param input
	 * @param context
	 * @return
	 * 
	 * scenario - changing active product 
	 * allow - if there are no valid payments
	 * restrict - if there are valid payments
	 */
	private boolean canProductBeUpgraded(MemberProductChangeInput input, ConstraintValidatorContext context) {

		Optional<MemberProductInput> optProductInput = input.getProducts().stream()
				.filter(item -> item.getProductUpgradeType().intValue() == 0).findFirst();

		if (optProductInput.isPresent()) {
			Set<MemberFee> memFees = memberService.getMemberProducts(input.getMemberId());

			//get effective date matching and future products
			Set<MemberFee> currentFutureFees = memFees.stream()
					.filter(item -> (input.getProductEffectiveDate().compareTo(item.getEffectiveStartDate()) == 0 || input.getProductEffectiveDate().before(item.getEffectiveStartDate()))
							&& item.getGroup().getGroupCode().equalsIgnoreCase(input.getGroupCode())
							&& item.getProduct().getUpgradeProduct().intValue() == 0)
					.collect(Collectors.toSet());

			for (MemberFee memFee : currentFutureFees) {
				if (paymentService.isValidPaymentExistsForMemberProduct(input.getMemberId(), memFee.getId())) {
					context.disableDefaultConstraintViolation();
					context.buildConstraintViolationWithTemplate(
							"Product change not allowed as Member has active product withe same effectivedate.")
							.addConstraintViolation();
					return true;
				}
			}
		}
		return false;
	}

	private boolean isValidProductEffectiveDate(MemberProductChangeInput input, ConstraintValidatorContext context) {
		
		GroupEntity group = groupService.getGroupByCode(input.getGroupCode());
		
		if(group.getBusinessLineEntity().getSalesChannel().getDivision().getId() == 3 )
		{ //b2b division - effective date , first day of the month
			LocalDate effectiveDate = new java.util.Date(input.getProductEffectiveDate().getTime()).toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();

			if(effectiveDate.getDayOfMonth() !=1)
			{
				context.disableDefaultConstraintViolation();
				context.buildConstraintViolationWithTemplate("Product Effective date should be first of the month.")
						.addConstraintViolation();
				return true;
			}
		}
		return false;
	}

	private boolean checkDependantsNameLength(MemberProductChangeInput input, ConstraintValidatorContext context) {

		for (MemberDependantInput depInput : input.getDependents()) {
			if (StringUtils.hasText(depInput.getFirstName()) && depInput.getFirstName().length() < 2) {
				context.disableDefaultConstraintViolation();
				context.buildConstraintViolationWithTemplate("Dependant First name should be atleast 2 characters.")
						.addConstraintViolation();
				return true;
			}
			if (StringUtils.hasText(depInput.getLastName()) && depInput.getLastName().length() < 2) {
				context.disableDefaultConstraintViolation();
				context.buildConstraintViolationWithTemplate("Dependant Last name should be atleast 2 characters.")
						.addConstraintViolation();
				return true;
			}
		}
		return false;
	}

	private boolean checkNameLength(MemberProductChangeInput input, ConstraintValidatorContext context) {

		if (StringUtils.hasText(input.getSpouseFirst()) && input.getSpouseFirst().length() < 2) {
			context.disableDefaultConstraintViolation();
			context.buildConstraintViolationWithTemplate("Spouse First name should be atleast 2 characters.")
					.addConstraintViolation();
			return true;
		}

		if (StringUtils.hasText(input.getSpouseLast()) && input.getSpouseLast().length() < 2) {
			context.disableDefaultConstraintViolation();
			context.buildConstraintViolationWithTemplate("Spouse First name should be atleast 2 characters.")
					.addConstraintViolation();
			return true;
		}

		return false;
	}

	/**
	 * @param input
	 * @param context
	 * @return
	 */
	private boolean isValidProductUpgrade(MemberProductChangeInput input, ConstraintValidatorContext context) {
		
		MemberProductInput prdItem = input.getProducts().stream().filter(item->item.getProductUpgradeType().intValue()==0).findFirst().orElse(null);
		
		if(null == prdItem)
			return false;
		
		if(!memberProductChangeService.isValidMemberProductChange(input.getMemberId(),input.getGroupCode(),prdItem.getProductId()))
		{
			context.disableDefaultConstraintViolation();
			context.buildConstraintViolationWithTemplate("Invalid Product ID["+prdItem.getProductId()+"] for upgrade").addConstraintViolation();			
			return true;
		}
		
		
		return false;
	}

	/**
	 * @param message
	 * @param context
	 * @return
	 */
	public boolean addValidationMessage(String message, ConstraintValidatorContext context) {
		context.disableDefaultConstraintViolation();
		context.buildConstraintViolationWithTemplate(message).addConstraintViolation();
		return false; 
	}
	
	/**
	 * @param input
	 * @param context
	 * @return
	 */
	private boolean checkForDependants(MemberProductChangeInput input, ConstraintValidatorContext context) {
		
		GroupEntity group = groupService.getGroupByCode(input.getGroupCode());

		if(null == group)
			return false;

		// todo add check to null
		List<String> createMemberShip = ConfigParamService.convertCommaSeparatedStringToList(
    			configParamService.getParamValue(ConfigParamName.SINGLE_TO_ALTERNATE_SETUP_SC_NAMES.name()));
		if(createMemberShip.contains(group.getBusinessLineEntity().getSalesChannel().getName()) && !input.getDependents().isEmpty())
		{
			context.disableDefaultConstraintViolation();
			context.buildConstraintViolationWithTemplate("Dependants not allowed for TRS & Precoa groups").addConstraintViolation();
			return true;
		}
		
		for (MemberProductInput prdItem : input.getProducts()) 
		{
			if(!Constant.EMERGENT_PRD_CODES.contains(prdItem.getProductCode()) && productIsSingle(prdItem) && memberHasDependents(input)){
				context.disableDefaultConstraintViolation();
				context.buildConstraintViolationWithTemplate("The chosen product frequency is single and cannot have spouse/dependents!").addConstraintViolation();			
				return true;
			}
		}
		
		return false;
	}

	/**
	 * @param prdItem
	 * @return
	 */
	private boolean productIsSingle(MemberProductInput prdItem) 
	{
		Optional<Product> optPrd = productService.getProductById(prdItem.getProductId());
		
		if(optPrd.isPresent())
		{
			return optPrd.get().getType().compareTo(ProductFamilyType.SINGLE.getType())==0;
		}
		
		return false;
	}

	/**
	 * @param input
	 * @return
	 */
	private boolean memberHasDependents(MemberProductChangeInput input) {
		
		if(input.getRemoveDependants())
			return false;
		
		if(!input.getDependents().isEmpty())
			return true;
		
		if( (StringUtils.hasText(input.getSpouseFirst()) || StringUtils.hasText(input.getSpouseLast()) ))
			return true;
			
		if(!memberService.getMemberDependants(input.getMemberId()).isEmpty())
			return true;
		
		Optional<MemberSummary> optMemSumm = memberService.getMemberDetails(input.getMemberId(), MemberSummary.class);
		if(optMemSumm.isPresent())
		{
			MemberSummary memSummary = optMemSumm.get();
			if(StringUtils.hasText(memSummary.getSpouseFirst()) || StringUtils.hasText(memSummary.getSpouseLast()))
				return true;
		}		
		return false;
	}
}
