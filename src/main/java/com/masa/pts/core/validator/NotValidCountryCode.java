package com.masa.pts.core.validator;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import javax.validation.Constraint;
import javax.validation.Payload;

@Target({FIELD})
@Retention(RUNTIME)
@Constraint(validatedBy = CountryCodeValidator.class)
@Documented

public @interface NotValidCountryCode {
	String message() default "Valid Country code is required.";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}
