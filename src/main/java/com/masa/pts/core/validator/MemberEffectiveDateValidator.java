package com.masa.pts.core.validator;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Date;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

import com.masa.pts.core.domain.Constant;
import com.masa.pts.core.service.PTSUtilityService;

public class MemberEffectiveDateValidator implements ConstraintValidator<NotValidMemberEffectiveDate, Date> {

	@Override
	public boolean isValid(Date value, ConstraintValidatorContext context) {
	
		if(null == value)
			return true;
		
		LocalDate localEffDate =  PTSUtilityService.convertUtilToLocalDate(value);
		
		long monthDiff = ChronoUnit.MONTHS.between(LocalDate.now().withDayOfMonth(1),localEffDate);
		
		return !(monthDiff > Constant.MONTHS_RANGE_FOR_ENROLL 
				|| monthDiff < (-Constant.MONTHS_RANGE_FOR_ENROLL));
		
	}

}
