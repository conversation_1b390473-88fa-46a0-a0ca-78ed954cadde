package com.masa.pts.core.validator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

@Target({FIELD})
@Retention(RUNTIME)
@Constraint(validatedBy = FeeIdValidator.class)
@Documented
public @interface NotValidFeeId {

	String message() default "Valid Fee Id is required.";

	Class<?>[] groups() default {};

	Class<? extends Payload>[] payload() default {};
}
