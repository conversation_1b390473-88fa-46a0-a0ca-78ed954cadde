package com.masa.pts.core.validator;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import javax.validation.Constraint;
import javax.validation.Payload;

@Constraint(validatedBy = UpdateMemberInputValidator.class)
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface NotValidUpdateMemberInput {
	String message() default "Error in input data";

	Class<?>[] groups() default {};

	Class<? extends Payload>[] payload() default {};
}
