package com.masa.pts.core.validator;

import com.masa.pts.core.repository.CompanyRepository;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class CompanyIdValidator implements ConstraintValidator<NotValidCompanyId, Integer> {

   private CompanyRepository companyRepository;

   public CompanyIdValidator(CompanyRepository companyRepository) {
      this.companyRepository = companyRepository;
   }

   public boolean isValid(Integer companyId, ConstraintValidatorContext context) {
      if (companyId != null) {
         return companyRepository.existsById(companyId);
      }

      return true;
   }
}
