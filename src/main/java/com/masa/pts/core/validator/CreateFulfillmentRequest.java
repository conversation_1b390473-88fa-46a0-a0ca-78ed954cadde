package com.masa.pts.core.validator;

import javax.validation.constraints.NotNull;

public class CreateFulfillmentRequest {
	@NotNull
	Integer productId;

	@NotNull
	Integer fulfillmentType;

	public Integer getProductId() {
		return productId;
	}

	public void setProductId(Integer productId) {
		this.productId = productId;
	}

	public Integer getFulfillmentType() {
		return fulfillmentType;
	}

	public void setFulfillmentType(Integer fulfillmentType) {
		this.fulfillmentType = fulfillmentType;
	}
}
