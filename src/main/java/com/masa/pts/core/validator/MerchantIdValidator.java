package com.masa.pts.core.validator;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

import static com.masa.pts.core.domain.Constant.MERCHANT_IDS;

public class MerchantIdValidator implements ConstraintValidator<ValidMerchantId, String> {

	@Override
	public boolean isValid(String value, ConstraintValidatorContext context) {
		return MERCHANT_IDS.contains(value);
	}
}
