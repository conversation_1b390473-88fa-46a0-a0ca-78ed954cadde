package com.masa.pts.core.validator;

import com.masa.pts.core.repository.FeeRepository;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class FeeIdValidator implements ConstraintValidator<NotValidFeeId, Integer> {

	private FeeRepository feeRepository;

	public FeeIdValidator(FeeRepository feeRepository) {
		this.feeRepository = feeRepository;
	}

	@Override
	public boolean isValid(Integer feeId, ConstraintValidatorContext context) {
		return feeId != null && feeRepository.existsById(feeId);
	}

}
