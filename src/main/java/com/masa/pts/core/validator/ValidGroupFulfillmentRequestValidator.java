package com.masa.pts.core.validator;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

@Component
public class ValidGroupFulfillmentRequestValidator
        implements ConstraintValidator<ValidGroupFulfillmentRequest, GroupFulfillmentRequest> {

    @Autowired
    private AddressInputValidator addressInputValidator;

    public boolean isValid(GroupFulfillmentRequest request, ConstraintValidatorContext context) {
        if (request.isSendToMembers()) {
            return addValidationMessage(
                    request.getContact() == null && request.getShippingAddress() == null,
                    "Contact and shipping address must not be provided.",
                    context);
        } else {
            return addValidationMessage(
                    StringUtils.isNotEmpty(request.getContact())
                    && request.getShippingAddress() != null
                    && addressInputValidator.isValid(request.getShippingAddress(), context),
                    "Address or contact issue.",
                    context
            );
        }
    }

    public boolean addValidationMessage(boolean valid, String message, ConstraintValidatorContext context) {
        if (valid) {
            return true;
        } else {
            context.disableDefaultConstraintViolation();
            context.buildConstraintViolationWithTemplate(message).addConstraintViolation();
            return false;
        }
    }
}
