package com.masa.pts.core.validator;


import com.masa.pts.core.repository.FrequencyRepository;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class FrequencyIdValidator implements ConstraintValidator<NotValidFrequencyId, Integer> {

	private FrequencyRepository frequencyRepository;

	public FrequencyIdValidator(FrequencyRepository frequencyRepository) {
		this.frequencyRepository = frequencyRepository;
	}

	@Override
	public boolean isValid(Integer frequencyId, ConstraintValidatorContext context) {
		return frequencyRepository.existsById(frequencyId);
	}
}
