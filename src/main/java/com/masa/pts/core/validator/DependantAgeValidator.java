package com.masa.pts.core.validator;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Date;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class DependantAgeValidator implements ConstraintValidator<NotValidDependantAge, Date> {

	@Override
	public boolean isValid(Date value, ConstraintValidatorContext context) {
		
		if(null == value)
			return true;
		
		LocalDate depBirthDate =  value.toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
		
		 return (ChronoUnit.YEARS.between(depBirthDate,LocalDate.now()) <= 26);
	}

}
