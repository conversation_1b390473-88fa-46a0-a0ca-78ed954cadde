package com.masa.pts.core.validator;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import com.masa.pts.core.service.AddressService;

public class CountryCodeValidator implements ConstraintValidator<NotValidCountryCode, String> {

	@Autowired
	private AddressService addressService;
	
	@Override
	public boolean isValid(String value, ConstraintValidatorContext context) {
		if(!StringUtils.hasText(value))
			return false;
		
		return addressService.existsByCountryCode(value); 
	}

}
