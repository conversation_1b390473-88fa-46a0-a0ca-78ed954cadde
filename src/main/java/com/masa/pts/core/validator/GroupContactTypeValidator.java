package com.masa.pts.core.validator;

import com.masa.pts.core.repository.FieldValueRepository;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;


public class GroupContactTypeValidator implements ConstraintValidator<NotValidGroupContactType, Integer> {

	private FieldValueRepository fieldValueRepository;

	public GroupContactTypeValidator(FieldValueRepository fieldValueRepository) {
		this.fieldValueRepository = fieldValueRepository;
	}

	@Override
	public boolean isValid(Integer contactType, ConstraintValidatorContext context) {
		return fieldValueRepository.isFieldValueExistForGroupContactType(contactType);
	}
}
