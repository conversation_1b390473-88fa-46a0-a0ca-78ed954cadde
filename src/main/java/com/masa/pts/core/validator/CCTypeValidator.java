package com.masa.pts.core.validator;

import java.util.Arrays;
import java.util.List;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class CCTypeValidator implements ConstraintValidator<NotValidCCType, String> {

	List<String> ccTypeList = Arrays.asList("VISA","MAST","AMER","DISC","DINE","JCB");
	
	@Override
	public boolean isValid(String value, ConstraintValidatorContext context) {
		
		if(null ==value)
			return true;
		
		return ccTypeList.contains(value.toUpperCase());
	}

}
