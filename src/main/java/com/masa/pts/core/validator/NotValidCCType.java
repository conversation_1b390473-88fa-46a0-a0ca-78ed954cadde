package com.masa.pts.core.validator;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import javax.validation.Constraint;
import javax.validation.Payload;

@Target({FIELD})
@Retention(RUNTIME)
@Constraint(validatedBy = CCTypeValidator.class)
@Documented
public @interface NotValidCCType {
	String message() default "Valid CC Type is required.";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}
