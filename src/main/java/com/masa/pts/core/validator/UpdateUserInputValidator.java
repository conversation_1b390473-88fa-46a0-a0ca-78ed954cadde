package com.masa.pts.core.validator;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

import org.springframework.beans.factory.annotation.Autowired;

import com.masa.pts.core.constant.PTSUserType;
import com.masa.pts.core.domain.Role;
import com.masa.pts.core.domain.SalesChannel;
import com.masa.pts.core.model.UpdateUserInput;
import com.masa.pts.core.service.CommonDataService;
import com.masa.pts.core.service.GroupService;
import com.masa.pts.core.service.PTSUserService;

public class UpdateUserInputValidator implements ConstraintValidator<NotValidUpdateUserInput, UpdateUserInput> {

	@Autowired
	GroupService groupService;
	@Autowired
	PTSUserService ptsUserService;
	@Autowired
	CommonDataService commonDataService;

	@Override
	public boolean isValid(UpdateUserInput input, ConstraintValidatorContext context) {
		if (!(input instanceof UpdateUserInput)) {
			throw new IllegalArgumentException("@NotValidUpdateUserInput only applies to UpdateUserInput");
		}

		if (null == input.getUserType()) {
			return addValidationMessage("UserType is required", context);
		}
		
		if (input.getRoles().isEmpty()) {
			return addValidationMessage("Atleast 1 user role is required", context);
		}

		if (!isValidRoles(input, context)) {
			return false;
		}

		if (PTSUserType.EXTERNAL.compareTo(input.getUserType()) == 0) {
			if (input.getGroups().isEmpty()) {
				return addValidationMessage("User Type [" + PTSUserType.EXTERNAL + "] require at least 1 group code",
						context);
			}
			if (!isValidGroups(input, context)) {
				return false;
			}
			if(!input.getSalesChannels().isEmpty())
			{
				return addValidationMessage("User Type ["+PTSUserType.EXTERNAL+"] cannot have sales channel", context);
			}
		} else if (PTSUserType.INTERNAL.compareTo(input.getUserType()) == 0) {
			if (input.getSalesChannels().isEmpty()) {
				return addValidationMessage("User Type [" + PTSUserType.INTERNAL + "] require at least 1 sales channel",
						context);
			}
			if(!isValidSalesChannels(input,context))
			{
				return false;
			}
			if(!input.getGroups().isEmpty())
			{
				return addValidationMessage("User Type ["+PTSUserType.INTERNAL+"] cannot have groups", context);
			}
		}
		return true;
	}

	/**
	 * @param input
	 * @param context
	 * @return
	 */
	private boolean isValidSalesChannels(UpdateUserInput input, ConstraintValidatorContext context) {
		Iterable<SalesChannel> scSet = commonDataService.getAllSalesChannels();
		Set<Integer> scIdSet = new HashSet<>();
		scSet.iterator().forEachRemaining(item->scIdSet.add(item.getId()));
		for(Integer scId: input.getSalesChannels())
		{
			if(!scIdSet.contains(scId))
			{
				context.disableDefaultConstraintViolation();
				context.buildConstraintViolationWithTemplate("Invalid sales channel id ["+scId+"]").addConstraintViolation();
				return false;
			}
		}
		return true;
	}
	
	/**
	 * @param input
	 * @param context
	 * @return
	 */
	private boolean isValidGroups(UpdateUserInput input, ConstraintValidatorContext context) {
		Map<Integer, String> groupIdCodeMap = groupService.getGroupCodeByIds(input.getGroups());
		for (Integer groupId : input.getGroups()) {
			if (groupIdCodeMap.getOrDefault(groupId, null) == null) {
				context.disableDefaultConstraintViolation();
				context.buildConstraintViolationWithTemplate("Invalid group id [" + groupId + "]")
						.addConstraintViolation();
				return false;
			}
		}
		return true;
	}

	/**
	 * @param input
	 * @param context
	 * @return
	 */
	private boolean isValidRoles(UpdateUserInput input, ConstraintValidatorContext context) {
		Set<Integer> roleIds = ptsUserService.getAllRoles().stream().map(Role::getId).collect(Collectors.toSet());
		for (Integer roleId : input.getRoles()) {
			if (!roleIds.contains(roleId)) {
				context.disableDefaultConstraintViolation();
				context.buildConstraintViolationWithTemplate("Invalid role id [" + roleId + "]")
						.addConstraintViolation();
				return false;
			}
		}
		return true;
	}

	/**
	 * @param message
	 * @param context
	 * @return
	 */
	public boolean addValidationMessage(String message, ConstraintValidatorContext context) {
		context.disableDefaultConstraintViolation();
		context.buildConstraintViolationWithTemplate(message).addConstraintViolation();
		return false;
	}
}
