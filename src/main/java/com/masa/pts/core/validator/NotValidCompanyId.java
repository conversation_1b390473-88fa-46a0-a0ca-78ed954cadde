package com.masa.pts.core.validator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Constraint(validatedBy = CompanyIdValidator.class)
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface NotValidCompanyId {
	String message() default "Company id doesn't exist";

	Class<?>[] groups() default {};

	Class<? extends Payload>[] payload() default {};
}
