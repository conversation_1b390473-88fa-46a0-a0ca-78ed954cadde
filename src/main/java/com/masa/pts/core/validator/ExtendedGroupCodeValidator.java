package com.masa.pts.core.validator;

import com.masa.pts.core.service.GroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class ExtendedGroupCodeValidator implements ConstraintValidator<NotValidGroupCodeOrInactive, String> {

	@Autowired
	private GroupService groupService;
	
	@Override
	public boolean isValid(String value, ConstraintValidatorContext context) {

		if(!StringUtils.hasText(value))
			return false;

		return groupService.isExistsAndActiveByGroupCode(value);		
	}

}
