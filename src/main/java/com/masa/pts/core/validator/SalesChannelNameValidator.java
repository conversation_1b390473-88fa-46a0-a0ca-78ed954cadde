package com.masa.pts.core.validator;

import com.masa.pts.core.service.SalesChannelService;
import org.springframework.util.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class SalesChannelNameValidator implements ConstraintValidator<ValidSalesChannelName, String> {

	private final SalesChannelService salesChannelService;

	public SalesChannelNameValidator(SalesChannelService salesChannelService) {
		this.salesChannelService = salesChannelService;
	}

	@Override
	public boolean isValid(String value, ConstraintValidatorContext context) {
		return StringUtils.hasText(value) && !salesChannelService.isSalesChannelNameExist(value);
	}
}
