package com.masa.pts.core.validator;

import com.masa.pts.core.model.GroupInfoRequestModel;
import com.masa.pts.core.repository.GroupRepository;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class GroupInfoRequestModelValidator implements ConstraintValidator<NotValidUpdateGroupInfoRequestModel, GroupInfoRequestModel> {

   private GroupRepository groupRepository;

   public GroupInfoRequestModelValidator(GroupRepository groupRepository) {
      this.groupRepository = groupRepository;
   }

   public boolean isValid(GroupInfoRequestModel requestModel, ConstraintValidatorContext context) {
      boolean isValid = true;

      if (requestModel.getBrokerAgentId() != null && requestModel.getBrokerAgentId() != 0) {
         isValid = groupRepository.checkAgentIdExistenceByGroupCode(
                 requestModel.getGroupCode(), requestModel.getBrokerAgentId(), true);
      }

      if (requestModel.getAgentId() != null && requestModel.getAgentId() != 0) {
         isValid = groupRepository.checkAgentIdExistenceByGroupCode(
                 requestModel.getGroupCode(), requestModel.getAgentId(), false);
      }

      return isValid;
   }
}
