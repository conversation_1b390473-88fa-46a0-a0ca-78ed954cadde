package com.masa.pts.core.validator;

import java.util.Arrays;
import java.util.List;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class PayTypeValidator implements ConstraintValidator<NotValidPayType, String> {

	List<String> payTypeList = Arrays.asList("CREDITCARD","CHECK","MONEYORDER","CASH","EFT","ACH");
	
	@Override
	public boolean isValid(String value, ConstraintValidatorContext context) {
		
		if(null == value)
			return true;
		
		return payTypeList.contains(value.toUpperCase());
	}

}
