package com.masa.pts.core.validator;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

import org.springframework.beans.factory.annotation.Autowired;

import com.masa.pts.core.service.MemberService;


public class MemberIdValidator implements ConstraintValidator<NotValidMemberId, Integer> {

	@Autowired
	private MemberService memberService;
	
	@Override
	public boolean isValid(Integer value, ConstraintValidatorContext context) {
		
		if(value==null)
			return false;
		
		return memberService.isMemberIdExists(value);
	}

}
