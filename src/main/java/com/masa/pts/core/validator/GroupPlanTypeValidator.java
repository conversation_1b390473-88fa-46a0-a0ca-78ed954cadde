package com.masa.pts.core.validator;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

import org.springframework.beans.factory.annotation.Autowired;

import com.masa.pts.core.repository.PlanTypeRepository;

public class GroupPlanTypeValidator implements ConstraintValidator<NotValidGroupPlanType, Integer> {

	@Autowired
	private PlanTypeRepository planTypeRepository;
	
	@Override
	public boolean isValid(Integer value, ConstraintValidatorContext context) {
		
		if(null == value || value ==0)
			return true;
		
		return planTypeRepository.existsById(value);
	}

}
