package com.masa.pts.core.validator;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import com.masa.pts.core.service.AgentService;

public class AgentNumValidator implements ConstraintValidator<NotValidAgentNum, String> {

	@Autowired
	private AgentService agentService;
	
	@Override
	public boolean isValid(String value, ConstraintValidatorContext context) {

		if(value == null || value.trim().equals(""))
			return true;
		
		if(!StringUtils.hasText(value))
			return false;
		
		return agentService.existsByAgentNum(value);
		
	}

}
