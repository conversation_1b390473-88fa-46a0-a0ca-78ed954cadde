package com.masa.pts.core.config;

import java.util.HashMap;
import java.util.Map;

import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties(prefix="pts.messages")
public class ErrorMessages {

	private Map<String,String> payment = new HashMap<String,String>();
	
	
	public ErrorMessages(Map<String, String> payment) {
		super();
		this.payment = payment;
	}

	public ErrorMessages() {
		super();
	}

	public Map<String, String> getPayment() {
		return payment;
	}

	public void setPayment(Map<String, String> payment) {
		this.payment = payment;
	}
}
