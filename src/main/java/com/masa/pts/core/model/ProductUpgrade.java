package com.masa.pts.core.model;

import com.masa.pts.core.constant.CreditType;

public class ProductUpgrade extends ProductSale {

	private static final long serialVersionUID = -4751392912031561055L;

	private double moneyCredit=0;
	private double timeCredit=0;
	private CreditType creditType;

	public ProductUpgrade() {
		super();
	}
	public double getMoneyCredit() {
		return moneyCredit;
	}
	public void setMoneyCredit(double moneyCredit) {
		this.moneyCredit = moneyCredit;
	}	
	public double getTimeCredit() {
		return timeCredit;
	}
	public void setTimeCredit(double timeCredit) {
		this.timeCredit = timeCredit;
	}
	public CreditType getCreditType() {
		return creditType;
	}
	public void setCreditType(CreditType creditType) {
		this.creditType = creditType;
	}
}
