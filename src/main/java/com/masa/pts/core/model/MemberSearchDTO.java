package com.masa.pts.core.model;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

public interface MemberSearchDTO {

	Integer getMemberId();

	String getEmployeeId();

	String getFirstName();

	String getLastName();

	Date getBirthDate();

	Date getEffectiveDate();

	Integer getActive();

	String getModifiedByUser();

	String getGroupCode();

	String getGroupName();

	String getProductName();

	Integer getSalesChannelId();

	String getSalesChannelName();

}
