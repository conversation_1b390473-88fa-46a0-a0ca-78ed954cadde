package com.masa.pts.core.model;

import com.google.common.collect.ImmutableList;
import com.masa.pts.core.constant.MemberActiveStatus;
import com.masa.pts.core.utils.common.ExcelDocument;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public class MemberExcelDocument implements ExcelDocument<MemberSearchDTO> {

	private List<List<Object>> dataRows;
	private List<List<Object>> totalRow;

	public MemberExcelDocument(List<MemberSearchDTO> detailsList) {
		setDataRow(detailsList);
	}

	@Override
	public void setDataRow(List<MemberSearchDTO> rowData) {
		this.dataRows = rowData.stream()
				.map(member ->
						Arrays.<Object>asList(
								member.getMemberId(),
								member.getEmployeeId(),
								member.getFirstName(),
								member.getLastName(),
								member.getBirthDate(),
								member.getEffectiveDate(),
								MemberActiveStatus.getStatusName(member.getActive()).name(),
								member.getProductName(),
								member.getGroupName())
				).collect(Collectors.toList());
	}

	@Override
	public void setTotalRow(List<MemberSearchDTO> detailsList) {
		this.totalRow = Collections.emptyList();
	}

	@Override
	public List<String> getColumnNames() {
		return ImmutableList.of("Member Id", "Employee Id", "First Name", "Last Name", "Birth Date",
				"Effective Date", "Status", "Product", "GroupEntity");
	}

	@Override
	public List<List<Object>> getDataRows() {
		return this.dataRows;
	}

	@Override
	public List<List<Object>> getTotalRow() {
		return this.totalRow;
	}
}
