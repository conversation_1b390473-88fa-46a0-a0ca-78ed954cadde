package com.masa.pts.core.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

import com.fasterxml.jackson.annotation.JsonView;
import com.masa.pts.core.domain.GroupContactEntity;
import com.masa.pts.core.domain.GroupEntity;
import com.masa.pts.core.domain.GroupProductFee;

public class GroupDetailsDTO {
	
	private AddressDTO mailingAddressDetails;
	
	private String companyName;
	
	private Integer companyId;

	private Integer modifiedBy;
	
	private String modifiedByUser;

	private Integer createdBy;

	private Integer salesChannelId;

	private String salesChannelName;

	private Integer divisionId;

	private String divisionName;

	private Integer brokerAgentId;

	private String brokerName;

	private Integer agentId;

	private String agentName;

	private Set<GroupContactEntity> contacts;

	private Set<GroupProductFee> grpPrdList;

	private Integer groupId;
	
	private String groupCode;

	private String groupName;

	private Integer billType;

	private Integer pastDueInactive;

	private boolean printCard;

	private boolean printRenewal;

	private String note;

	private boolean active;

	private boolean label;

	private boolean billCompany;

	private Integer roundMonthlyFee;

	private Integer paymentMonthsBehind;

	private boolean receivesEmailInvoice;
	
	private Date groupEffectiveDate;
	
	private Date soldDate;

	private Integer planType;

	private Boolean autoPay;
	
	private Date createdDate;
	
	private Date modifiedDate;

	private Boolean downPaymentGrp;

	private Boolean lifetimeGrp;
	
	private Date pastDueEffectiveDate;

	private BigDecimal creditDebitAmount;

	private Integer parentGroupId;

	private Boolean isNetComm;

	private Boolean isAdvComm;

	private List<ProductSale> saleProducts = new ArrayList<>();

	@JsonView({TPAPortalView.Member.class})
	private String logoPath;

	private boolean canDeactivate;

	private Integer membershipSource;
	private Integer brokerPlatform;
	private Integer paymentTerm;
	private boolean autoSuspendCancelMember;

	public GroupDetailsDTO() { }

	public GroupDetailsDTO(GroupEntity entity, boolean canDeactivate) {
		if(entity.getMailingAddressDetails() !=null) {
			mailingAddressDetails = new AddressDTO(entity.getMailingAddressDetails());
		}
		salesChannelId = entity.getBusinessLineEntity().getSalesChannel().getId();
		salesChannelName = entity.getBusinessLineEntity().getSalesChannel().getName();
		divisionId = entity.getBusinessLineEntity().getSalesChannel().getDivision().getId();
		divisionName = entity.getBusinessLineEntity().getSalesChannel().getDivision().getName();

		if (entity.getBrokerAgent() != null) {
			brokerAgentId = entity.getBrokerAgent().getAgentId();
			brokerName = entity.getBrokerAgent().getAgentFullName();
		}

		if (entity.getAgent() != null) {
			agentId = entity.getAgent().getAgentId();
			agentName = entity.getAgent().getAgentFullName();
		}

		contacts = entity.getContacts();
		companyId = entity.getCompany().getCompanyId();
		companyName = entity.getCompany().getCompanyName();
		grpPrdList = entity.getGrpPrdList();

		modifiedBy = entity.getUserWhoModified() != null ? entity.getUserWhoModified().getEmployeeId() : null;
		createdBy = entity.getUserWhoCreated() != null ? entity.getUserWhoCreated().getEmployeeId() : null;
		modifiedByUser = entity.getUserWhoModified() !=null ? entity.getUserWhoModified().getUsername() : null;
		groupCode = entity.getGroupCode();
		groupName = entity.getGroupName();
		billType = entity.getBillType();
		pastDueInactive = entity.getPastDueInactive();
		printCard = entity.getPrintCard();
		printRenewal = entity.getPrintRenewal();
		note = entity.getNote();
		active = entity.isActive();
		label = entity.getLabel();
		billCompany = entity.isBillCompany();
		roundMonthlyFee = entity.getRoundMonthlyFee();
		paymentMonthsBehind = entity.getPaymentMonthsBehind();
		receivesEmailInvoice = entity.isReceivesEmailInvoice();
		groupEffectiveDate = entity.getGroupEffectiveDate();
		soldDate = entity.getSoldDate();
		planType = entity.getPlanType();
		autoPay = entity.getAutoPay();
		createdDate = entity.getCreatedDate();
		modifiedDate = entity.getModifiedDate();
		downPaymentGrp = entity.getDownPaymentGrp();
		lifetimeGrp = entity.getLifetimeGrp();
		pastDueEffectiveDate = entity.getPastDueEffectiveDate();
		creditDebitAmount = entity.getCreditDebitAmount();
		parentGroupId = entity.getParentGroupId();
		isNetComm = entity.getIsNetComm();
		isAdvComm = entity.getIsAdvComm();
		this.canDeactivate = canDeactivate;
		membershipSource = entity.getMembershipSource();
		brokerPlatform = entity.getBrokerPlatforms();
		paymentTerm = entity.getPaymentTerms();
		autoSuspendCancelMember = entity.isAutoSuspendCancelMember();
	}

	public AddressDTO getMailingAddressDetails() {
		return mailingAddressDetails;
	}

	public void setMailingAddressDetails(AddressDTO mailingAddressDetails) {
		this.mailingAddressDetails = mailingAddressDetails;
	}

	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	public Integer getModifiedBy() {
		return modifiedBy;
	}

	public void setModifiedBy(Integer modifiedBy) {
		this.modifiedBy = modifiedBy;
	}

	public Integer getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}

	public Integer getSalesChannelId() {
		return salesChannelId;
	}

	public void setSalesChannelId(Integer salesChannelId) {
		this.salesChannelId = salesChannelId;
	}

	public String getSalesChannelName() {
		return salesChannelName;
	}

	public void setSalesChannelName(String salesChannelName) {
		this.salesChannelName = salesChannelName;
	}

	public Integer getDivisionId() {
		return divisionId;
	}

	public void setDivisionId(Integer divisionId) {
		this.divisionId = divisionId;
	}

	public String getDivisionName() {
		return divisionName;
	}

	public void setDivisionName(String divisionName) {
		this.divisionName = divisionName;
	}

	public Set<GroupProductFee> getGrpPrdList() {
		return grpPrdList;
	}

	public void setGrpPrdList(Set<GroupProductFee> grpPrdList) {
		this.grpPrdList = grpPrdList;
	}

	public Integer getBrokerAgentId() {
		return brokerAgentId;
	}

	public void setBrokerAgentId(Integer brokerAgentId) {
		this.brokerAgentId = brokerAgentId;
	}
	public String getBrokerName() {
		return brokerName;
	}

	public void setBrokerName(String brokerName) {
		this.brokerName = brokerName;
	}

	public String getAgentName() {
		return agentName;
	}

	public void setAgentName(String agentName) {
		this.agentName = agentName;
	}

	public Integer getAgentId() {
		return agentId;
	}

	public void setAgentId(Integer agentId) {
		this.agentId = agentId;
	}

	
	public Set<GroupContactEntity> getContacts() {
		return contacts;
	}

	public void setContacts(Set<GroupContactEntity> contacts) {
		this.contacts = contacts;
	}
	
	public Integer getGroupId() {
		return groupId;
	}

	public void setGroupId(Integer groupId) {
		this.groupId = groupId;
	}

	public String getGroupCode() {
		return groupCode;
	}

	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}

	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

	public Integer getBillType() {
		return billType;
	}

	public void setBillType(Integer billType) {
		this.billType = billType;
	}

	public Integer getPastDueInactive() {
		return pastDueInactive;
	}

	public void setPastDueInactive(Integer pastDueInactive) {
		this.pastDueInactive = pastDueInactive;
	}

	public boolean isPrintCard() {
		return printCard;
	}

	public void setPrintCard(boolean printCard) {
		this.printCard = printCard;
	}

	public boolean isPrintRenewal() {
		return printRenewal;
	}

	public void setPrintRenewal(boolean printRenewal) {
		this.printRenewal = printRenewal;
	}

	public String getNote() {
		return note;
	}

	public void setNote(String note) {
		this.note = note;
	}

	public boolean isActive() {
		return active;
	}

	public void setActive(boolean active) {
		this.active = active;
	}

	public boolean isLabel() {
		return label;
	}

	public void setLabel(boolean label) {
		this.label = label;
	}

	public boolean isBillCompany() {
		return billCompany;
	}

	public void setBillCompany(boolean billCompany) {
		this.billCompany = billCompany;
	}

	public Integer getRoundMonthlyFee() {
		return roundMonthlyFee;
	}

	public void setRoundMonthlyFee(Integer roundMonthlyFee) {
		this.roundMonthlyFee = roundMonthlyFee;
	}

	public Integer getPaymentMonthsBehind() {
		return paymentMonthsBehind;
	}

	public void setPaymentMonthsBehind(Integer paymentMonthsBehind) {
		this.paymentMonthsBehind = paymentMonthsBehind;
	}

	public boolean isReceivesEmailInvoice() {
		return receivesEmailInvoice;
	}

	public void setReceivesEmailInvoice(boolean receivesEmailInvoice) {
		this.receivesEmailInvoice = receivesEmailInvoice;
	}

	public Date getGroupEffectiveDate() {
		return groupEffectiveDate;
	}

	public void setGroupEffectiveDate(Date groupEffectiveDate) {
		this.groupEffectiveDate = groupEffectiveDate;
	}

	public Date getSoldDate() {
		return soldDate;
	}

	public void setSoldDate(Date soldDate) {
		this.soldDate = soldDate;
	}

	public Integer getPlanType() {
		return planType;
	}

	public void setPlanType(Integer planType) {
		this.planType = planType;
	}

	public Boolean getAutoPay() {
		return autoPay;
	}

	public void setAutoPay(Boolean autoPay) {
		this.autoPay = autoPay;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public Date getModifiedDate() {
		return modifiedDate;
	}

	public void setModifiedDate(Date modifiedDate) {
		this.modifiedDate = modifiedDate;
	}

	public Boolean getDownPaymentGrp() {
		return downPaymentGrp;
	}

	public void setDownPaymentGrp(Boolean downPaymentGrp) {
		this.downPaymentGrp = downPaymentGrp;
	}

	public Boolean getLifetimeGrp() {
		return lifetimeGrp;
	}

	public void setLifetimeGrp(Boolean lifetimeGrp) {
		this.lifetimeGrp = lifetimeGrp;
	}

	public Date getPastDueEffectiveDate() {
		return pastDueEffectiveDate;
	}

	public void setPastDueEffectiveDate(Date pastDueEffectiveDate) {
		this.pastDueEffectiveDate = pastDueEffectiveDate;
	}

	public BigDecimal getCreditDebitAmount() {
		return creditDebitAmount;
	}

	public void setCreditDebitAmount(BigDecimal creditDebitAmount) {
		this.creditDebitAmount = creditDebitAmount;
	}

	public Integer getParentGroupId() {
		return parentGroupId;
	}

	public void setParentGroupId(Integer parentGroupId) {
		this.parentGroupId = parentGroupId;
	}

	public Boolean getIsNetComm() {
		return isNetComm;
	}

	public void setIsNetComm(Boolean isNetComm) {
		this.isNetComm = isNetComm;
	}

	public Boolean getIsAdvComm() {
		return isAdvComm;
	}

	public void setIsAdvComm(Boolean isAdvComm) {
		this.isAdvComm = isAdvComm;
	}

	public List<ProductSale> getSaleProducts() {
		return saleProducts;
	}

	public void setSaleProducts(List<ProductSale> saleProducts) {
		this.saleProducts = saleProducts;
	}

	public String getLogoPath() {
		return logoPath;
	}

	public void setLogoPath(String logoPath) {
		this.logoPath = logoPath;
	}

	public Integer getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Integer companyId) {
		this.companyId = companyId;
	}

	public String getModifiedByUser() {
		return modifiedByUser;
	}

	public void setModifiedByUser(String modifiedByUser) {
		this.modifiedByUser = modifiedByUser;
	}

	public boolean isCanDeactivate() {
		return canDeactivate;
	}

	public void setCanDeactivate(boolean canDeactivate) {
		this.canDeactivate = canDeactivate;
	}

	public Integer getMembershipSource() {
		return membershipSource;
	}

	public void setMembershipSource(Integer membershipSource) {
		this.membershipSource = membershipSource;
	}

	public Integer getBrokerPlatform() {
		return brokerPlatform;
	}

	public void setBrokerPlatform(Integer brokerPlatform) {
		this.brokerPlatform = brokerPlatform;
	}

	public Integer getPaymentTerm() {
		return paymentTerm;
	}

	public void setPaymentTerm(Integer paymentTerm) {
		this.paymentTerm = paymentTerm;
	}

	public boolean isAutoSuspendCancelMember() {
		return autoSuspendCancelMember;
	}

	public void setAutoSuspendCancelMember(boolean autoSuspendCancelMember) {
		this.autoSuspendCancelMember = autoSuspendCancelMember;
	}
}
