package com.masa.pts.core.model;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

import com.fasterxml.jackson.annotation.JsonView;
import com.masa.pts.core.domain.Benefit;
import com.masa.pts.core.domain.MobileView;

public class MemberProduct implements Serializable {

	private static final long serialVersionUID = -8347437335527957128L;
	
	@JsonView(MobileView.Member.class)
	private String productImage;
	
	@JsonView(MobileView.Member.class)
	private String productName;
	
	@JsonView(MobileView.Member.class)
	private String productDescription;	
	
	@JsonView(MobileView.Member.class)
	private String productFrequency;
	
	@JsonView(MobileView.Member.class)
	private Double premiumAmount=0.0;
	
	@JsonView(MobileView.Member.class)
	private Double maxAmountDue=0.0;
	
	@JsonView(MobileView.Member.class)
	private Double amountPaid=0.0;
	
	@JsonView(MobileView.Member.class)
	private Double balanceDue=0.0;
	
	@JsonView(MobileView.Member.class)
	private Double outstanndingBalance=0.0;

	@JsonView(MobileView.Member.class)
	private Double setupFeeAmount=0.0;
	
	@JsonView(MobileView.Member.class)
	private Double setupFeeBalanceAmount=0.0;
	
	@JsonView(MobileView.Member.class)
	private Double setupFeeOutstandingBalanceAmt=0.0;
	
	@JsonView(MobileView.Member.class)
	private Set<Benefit> benefits = new HashSet<>();

	@JsonView(MobileView.Member.class)
	private String productType;
	
	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public Double getPremiumAmount() {
		return premiumAmount;
	}

	public void setPremiumAmount(Double premiumAmount) {
		this.premiumAmount = premiumAmount;
	}

	public Double getMaxAmountDue() {
		return maxAmountDue;
	}

	public void setMaxAmountDue(Double maxAmountDue) {
		this.maxAmountDue = maxAmountDue;
	}

	public Double getAmountPaid() {
		return amountPaid;
	}

	public void setAmountPaid(Double amountPaid) {
		this.amountPaid = amountPaid;
	}

	public Double getBalanceDue() {
		return balanceDue;
	}

	public void setBalanceDue(Double balanceDue) {
		this.balanceDue = balanceDue;
	}

	public Double getOutstanndingBalance() {
		return outstanndingBalance;
	}

	public void setOutstanndingBalance(Double outstanndingBalance) {
		this.outstanndingBalance = outstanndingBalance;
	}

	public Double getSetupFeeAmount() {
		return setupFeeAmount;
	}

	public void setSetupFeeAmount(Double setupFeeAmount) {
		this.setupFeeAmount = setupFeeAmount;
	}

	public Double getSetupFeeBalanceAmount() {
		return setupFeeBalanceAmount;
	}

	public void setSetupFeeBalanceAmount(Double setupFeeBalanceAmount) {
		this.setupFeeBalanceAmount = setupFeeBalanceAmount;
	}

	public Double getSetupFeeOutstandingBalanceAmt() {
		return setupFeeOutstandingBalanceAmt;
	}

	public void setSetupFeeOutstandingBalanceAmt(Double setupFeeOutstandingBalanceAmt) {
		this.setupFeeOutstandingBalanceAmt = setupFeeOutstandingBalanceAmt;
	}

	public Set<Benefit> getBenefits() {
		return benefits;
	}

	public void setBenefits(Set<Benefit> benefits) {
		this.benefits = benefits;
	}
	
	public void addBenefits(Set<Benefit> benefits) {
		this.benefits.addAll(benefits);
	}

	public String getProductDescription() {
		return productDescription;
	}

	public void setProductDescription(String productDescription) {
		this.productDescription = productDescription;
	}

	public String getProductFrequency() {
		return productFrequency;
	}

	public void setProductFrequency(String productFrequency) {
		this.productFrequency = productFrequency;
	}

	public String getProductImage() {
		return productImage;
	}

	public void setProductImage(String productImage) {
		this.productImage = productImage;
	}

	public String getProductType() {
		return productType;
	}

	public void setProductType(String productType) {
		this.productType = productType;
	}

	public MemberProduct() {
		super();
	}

	public MemberProduct(String productName, Double premiumAmount, Double maxAmountDue, Double amountPaid, Double balanceDue, Double outstanndingBalance, Double setupFeeAmount,
			Double setupFeeBalanceAmount, Double setupFeeOutstandingBalanceAmt, Set<Benefit> benefits) {
		super();
		this.productName = productName;
		this.premiumAmount = premiumAmount;
		this.maxAmountDue = maxAmountDue;
		this.amountPaid = amountPaid;
		this.balanceDue = balanceDue;
		this.outstanndingBalance = outstanndingBalance;
		this.setupFeeAmount = setupFeeAmount;
		this.setupFeeBalanceAmount = setupFeeBalanceAmount;
		this.setupFeeOutstandingBalanceAmt = setupFeeOutstandingBalanceAmt;
		this.benefits = benefits;
	}
}
