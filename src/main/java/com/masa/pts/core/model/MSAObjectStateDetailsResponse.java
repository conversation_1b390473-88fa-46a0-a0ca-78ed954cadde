package com.masa.pts.core.model;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Generated;
import lombok.Getter;
import lombok.Setter;

@Generated
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class MSAObjectStateDetailsResponse {

    private Integer divisionId;
    private String divisionName;
    private Integer salesChannelId;
    private String salesChannelName;
    private Integer groupId;
    private String groupName;
    private String groupCode;
    private Integer groupStateId;
    private String groupStateName;
    private String groupStateSymbol;
    private Integer memberStateId;
    private String memberStateName;
    private String memberStateSymbol; 
    
}


