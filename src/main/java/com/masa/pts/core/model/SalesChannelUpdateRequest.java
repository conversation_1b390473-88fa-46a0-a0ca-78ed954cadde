package com.masa.pts.core.model;


import com.masa.pts.core.validator.ValidMerchantId;

import java.util.Objects;

public class SalesChannelUpdateRequest {

	private boolean isObsolete;

	@ValidMerchantId
	private String merchantId;

	public boolean getIsObsolete() {
		return isObsolete;
	}

	public void setIsObsolete(boolean obsolete) {
		isObsolete = obsolete;
	}

	public String getMerchantId() {
		return merchantId;
	}

	public void setMerchantId(String merchantId) {
		this.merchantId = merchantId;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) return true;
		if (o == null || getClass() != o.getClass()) return false;
		SalesChannelUpdateRequest that = (SalesChannelUpdateRequest) o;
		return isObsolete == that.isObsolete &&
				Objects.equals(merchantId, that.merchantId);
	}

	@Override
	public int hashCode() {
		return Objects.hash(isObsolete, merchantId);
	}

	@Override
	public String toString() {
		return "SalesChannelUpdateRequest{" +
				"isObsolete=" + isObsolete +
				", merchantId=" + merchantId +
				'}';
	}
}
