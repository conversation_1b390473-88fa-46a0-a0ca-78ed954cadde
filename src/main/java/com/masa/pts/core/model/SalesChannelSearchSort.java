package com.masa.pts.core.model;

public enum SalesChannelSearchSort {

	id("id"),
	name("name"),
	divisionName("division.name"),
	forteMerchant("fm.merchantId"), // todo bad realization if possible find a way how to fix it
	isObsolete("isObsolete");

	private String sortField;

	SalesChannelSearchSort(String sortField) {
		this.sortField = sortField;
	}

	public String getSortField() {
		return sortField;
	}

}
