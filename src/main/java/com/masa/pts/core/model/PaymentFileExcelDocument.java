package com.masa.pts.core.model;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import com.google.common.collect.ImmutableList;
import com.masa.pts.core.domain.PaymentFileProcessOutput;
import com.masa.pts.core.service.PTSUtilityService;
import com.masa.pts.core.utils.common.ExcelDocument;

import static com.masa.pts.core.domain.Constant.DEFAULT_DATE_PATTERN;


public class PaymentFileExcelDocument implements ExcelDocument<PaymentFileProcessOutput> {

	private List<List<Object>> dataRows;
	private List<List<Object>> totalRow;
	
	public PaymentFileExcelDocument(List<PaymentFileProcessOutput> detailsList) {
		setDataRow(detailsList);
	}
	
	@Override
	public void setDataRow(List<PaymentFileProcessOutput> rowData) {
		this.dataRows = rowData.stream().map(paymentFile -> Arrays.<Object>asList(
				paymentFile.getMemberId(),
				paymentFile.getEmployerId(),
				paymentFile.getMemberName(),
				PTSUtilityService.formatUtilDate(paymentFile.getTransactionDate(),DEFAULT_DATE_PATTERN),
				paymentFile.getAmountPaid().toString(),
				paymentFile.getGroupCd(),
				paymentFile.getFileName(), 
				paymentFile.getFileOwner(),
				paymentFile.getStatus().toString(),
				paymentFile.getStatusUpdatedBy(),
				PTSUtilityService.formatUtilDate(paymentFile.getProcessedDate(),DEFAULT_DATE_PATTERN),
				paymentFile.getComments())).collect(Collectors.toList());
	}
	@Override
	public void setTotalRow(List<PaymentFileProcessOutput> detailsList) {
		this.totalRow = Collections.emptyList();		
	}
	@Override
	public List<String> getColumnNames() {
		return ImmutableList.of("Member Id","Employee ID","Member Name",
				"Transaction Date",
				"Amount Paid","Group","FileName","FileOwner","Status","Status Updated By","Processed Date","Comments");
	}
	@Override
	public List<List<Object>> getDataRows() {
		return this.dataRows;
	}
	@Override
	public List<List<Object>> getTotalRow() {
		return this.totalRow;
	}
	
	
}
