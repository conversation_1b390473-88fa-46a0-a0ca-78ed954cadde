package com.masa.pts.core.model;

import com.fasterxml.jackson.annotation.JsonCreator;

import static com.google.common.base.CaseFormat.LOWER_CAMEL;
import static com.google.common.base.CaseFormat.UPPER_UNDERSCORE;

//need this mapping for memberSearch operation, because hibernate didn't know how generate sorting fields
// which not a part of entity
public enum MemberSearchSort {

	MEMBER_ID("memberId"),
	EMPLOYEE_ID("employeeId"),
	FIRST_NAME("firstName"),
	LAST_NAME("lastName"),
	BIRTH_DATE("birthDate"),
	EFFECTIVE_DATE("effectiveDate"),
	ACTIVE("active"),
	CREATED_BY_USER("empCr.username"),
	MODIFIED_BY_USER("empMod.username"),
	G<PERSON><PERSON>_CODE("gr.groupCode"),
	GROUP_NAME("gr.groupName"),
	PRODUCT_NAME("tpc.name"),
	SALES_CHANNEL_ID("sc.id"),
	SALES_CHANNEL_NAME("sc.name");

	private String sortField;

	MemberSearchSort(String sortField) {
		this.sortField = sortField;
	}

	public String getSortField() {
		return sortField;
	}
}
