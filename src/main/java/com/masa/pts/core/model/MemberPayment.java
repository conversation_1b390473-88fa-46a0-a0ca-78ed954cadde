package com.masa.pts.core.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.masa.pts.core.domain.MemberIdentifierType;

public class MemberPayment implements Serializable {

	private static final long serialVersionUID = 2760755122948207165L;
	
	private String groupCode;
	
	private String memberIdentifier;
	
	private BigDecimal paymentAmt;
	
	private Date transactionDate;
	
	private MemberIdentifierType identifierType;
	
	private String forteTransactionId;
	
	private boolean chargeback=false;
	
	private String payMethod;

	public MemberPayment() {
		super();
	}
	
	public MemberPayment(String groupCode, String memberIdentifier, BigDecimal paymentAmt, Date transactionDate, MemberIdentifierType identifierType) {
		super();
		this.groupCode = groupCode;
		this.memberIdentifier = memberIdentifier;
		this.paymentAmt = paymentAmt;
		this.transactionDate = transactionDate;
		this.identifierType = identifierType;
	}


	public MemberIdentifierType getIdentifierType() {
		return identifierType;
	}

	public void setIdentifierType(MemberIdentifierType identifierType) {
		this.identifierType = identifierType;
	}

	public String getGroupCode() {
		return groupCode;
	}

	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}

	public String getMemberIdentifier() {
		return memberIdentifier;
	}

	public void setMemberIdentifier(String memberIdentifier) {
		this.memberIdentifier = memberIdentifier;
	}

	public BigDecimal getPaymentAmt() {
		return paymentAmt;
	}

	public void setPaymentAmt(BigDecimal paymentAmt) {
		this.paymentAmt = paymentAmt;
	}

	public Date getTransactionDate() {
		return transactionDate;
	}

	public void setTransactionDate(Date transactionDate) {
		this.transactionDate = transactionDate;
	}

	public String getForteTransactionId() {
		return forteTransactionId;
	}

	public void setForteTransactionId(String forteTransactionId) {
		this.forteTransactionId = forteTransactionId;
	}

	public boolean isChargeback() {
		return chargeback;
	}

	public void setChargeback(boolean chargeback) {
		this.chargeback = chargeback;
	}

	public String getPayMethod() {
		return payMethod;
	}

	public void setPayMethod(String payMethod) {
		this.payMethod = payMethod;
	}

	@Override
	public String toString() {
		return "MemberPayment [groupCode=" + groupCode + ", memberIdentifier=" + memberIdentifier + ", paymentAmt=" + paymentAmt + ", transactionDate=" + transactionDate + "]";
	}
	
}
