package com.masa.pts.core.model;

import com.masa.pts.core.constant.FulfillmentType;
import com.masa.pts.core.constant.ProductType;

import java.util.Date;
import java.util.Objects;

import static com.masa.pts.core.domain.Constant.DEFULT_DATE_1900;
import static com.masa.pts.core.domain.Constant.DEFULT_MILLISECONDS_1900;

public class MemberFulfillmentDto {

	private Integer id;
	private FulfillmentType type;
	private FulfillmentProductDto product;
	private Date requestedDate;
	private Date processedDate;
	private String userName;
	private String UID;
	private Integer packageCount;
	private AddressInput shippingAddress;
	private String shippingMethod;
	private String status;

	public MemberFulfillmentDto(Integer id,
								Integer type,
								Date requestedDate,
								Date processedDate,
								String UID,
								Integer packageCount,
								String address1,
								String address2,
								String address3,
								String city,
								String state,
								String country,
								String zip,
								String zip4,
								String productName,
								Integer productType,
								String userName,
								String shippingName,
								Integer productId,
								Integer upgradeCounter) {
		this.id = id;
		this.product = new FulfillmentProductDto(
				productId, productName, ProductType.getValue(productType), upgradeCounter);
		this.type = FulfillmentType.getValue(type);
		this.requestedDate = requestedDate;
		this.processedDate = processedDate;
		this.userName = userName;
		this.UID = UID;
		this.packageCount = packageCount;
		this.shippingAddress = createShippingAddress(address1, address2, address3, city, state, country, zip, zip4);
		this.shippingMethod = shippingName;
		this.status = getFulfillmentStatus(processedDate);
	}

	private String getFulfillmentStatus(Date processedDate) {
		if (processedDate.getTime() == DEFULT_MILLISECONDS_1900) {
			return FulfilmentStatus.PENDING.getStatusField();
		}

		return FulfilmentStatus.COMPLETE.getStatusField();
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public FulfillmentProductDto getProduct() {
		return product;
	}

	public void setProduct(FulfillmentProductDto product) {
		this.product = product;
	}

	public FulfillmentType getType() {
		return type;
	}

	public void setType(FulfillmentType type) {
		this.type = type;
	}

	public Date getRequestedDate() {
		return requestedDate;
	}

	public void setRequestedDate(Date requestedDate) {
		this.requestedDate = requestedDate;
	}

	public Date getProcessedDate() {
		return processedDate;
	}

	public void setProcessedDate(Date processedDate) {
		this.processedDate = processedDate;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getUID() {
		return UID;
	}

	public void setUID(String UID) {
		this.UID = UID;
	}

	public Integer getPackageCount() {
		return packageCount;
	}

	public void setPackageCount(Integer packageCount) {
		this.packageCount = packageCount;
	}

	public AddressInput getShippingAddress() {
		return shippingAddress;
	}

	public void setShippingAddress(AddressInput shippingAddress) {
		this.shippingAddress = shippingAddress;
	}

	public String getShippingMethod() {
		return shippingMethod;
	}

	public void setShippingMethod(String shippingMethod) {
		this.shippingMethod = shippingMethod;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	private AddressInput createShippingAddress(String address1,
											   String address2,
											   String address3,
											   String city,
											   String state,
											   String country,
											   String zip,
											   String zip4) {
		AddressInput shippingAddress = new AddressInput();
		shippingAddress.setAddress1(address1);
		shippingAddress.setAddress2(address2);
		shippingAddress.setAddress3(address3);
		shippingAddress.setCity(city);
		shippingAddress.setStateCd(state);
		shippingAddress.setCountryCd(country);
		shippingAddress.setZip(zip);
		shippingAddress.setZip4(zip4);

		return shippingAddress;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) return true;
		if (o == null || getClass() != o.getClass()) return false;
		MemberFulfillmentDto that = (MemberFulfillmentDto) o;
		return Objects.equals(id, that.id) &&
				type == that.type &&
				Objects.equals(product, that.product) &&
				Objects.equals(requestedDate, that.requestedDate) &&
				Objects.equals(processedDate, that.processedDate) &&
				Objects.equals(userName, that.userName) &&
				Objects.equals(UID, that.UID) &&
				Objects.equals(packageCount, that.packageCount) &&
				Objects.equals(shippingAddress, that.shippingAddress) &&
				Objects.equals(shippingMethod, that.shippingMethod) &&
				Objects.equals(status, that.status);
	}

	@Override
	public int hashCode() {
		return Objects.hash(id, type, product, requestedDate, processedDate, userName, UID,
				packageCount, shippingAddress, shippingMethod, status);
	}

	@Override
	public String toString() {
		return "MemberFulfillmentDto{" +
				"id=" + id +
				", type=" + type +
				", productDto=" + product +
				", requestedDate=" + requestedDate +
				", processedDate=" + processedDate +
				", userName='" + userName + '\'' +
				", UID='" + UID + '\'' +
				", packageCount=" + packageCount +
				", shippingAddress=" + shippingAddress +
				", shippingMethod='" + shippingMethod + '\'' +
				", status='" + status + '\'' +
				'}';
	}
}
