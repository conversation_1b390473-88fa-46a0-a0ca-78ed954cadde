package com.masa.pts.core.model;

import com.google.common.collect.ImmutableList;
import com.masa.pts.core.utils.common.ExcelDocument;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public class GroupExcelDocument implements ExcelDocument<GroupSearchDTO> {

	private List<List<Object>> dataRows;
	private List<List<Object>> totalRow;

	public GroupExcelDocument(List<GroupSearchDTO> detailsList) {
		setDataRow(detailsList);
	}

	@Override
	// todo need to return String types instead of integer values
	public void setDataRow(List<GroupSearchDTO> rowData) {
		this.dataRows = rowData.stream()
				.map(group ->
						Arrays.<Object>asList(
								group.getGroupCode(),
								group.getGroupName(),
								group.getSalesChannelName(),
								group.getPlanTypeName(),
								group.isBillCompany(),
								group.getBillType(),
								group.isActive())
				).collect(Collectors.toList());
	}

	@Override
	public void setTotalRow(List<GroupSearchDTO> detailsList) {
		this.totalRow = Collections.emptyList();
	}

	@Override
	public List<String> getColumnNames() {
		return ImmutableList.of("GroupEntity code", "GroupEntity name", "SalesChannel", "Plan type", "Bill type",
				"Invoice Type", "Status");
	}

	@Override
	public List<List<Object>> getDataRows() {
		return dataRows;
	}

	@Override
	public List<List<Object>> getTotalRow() {
		return totalRow;
	}
}
