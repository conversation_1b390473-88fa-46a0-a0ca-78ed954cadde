package com.masa.pts.core.model;

import static com.google.common.base.CaseFormat.LOWER_CAMEL;
import static com.google.common.base.CaseFormat.UPPER_UNDERSCORE;

import com.fasterxml.jackson.annotation.JsonCreator;

public enum InvoiceSearchSort {

	INVOICE_ID("invoiceId"),
	GROUP_NAME("group.groupName"),
	INVOICE_DATE("invoiceDate"),
	IS_PAID("isPaid"),
	TOTAL_DUE("totalDue"),
	INVOICE_STATUS("status");
	
	private String sortField;

	private InvoiceSearchSort(String sortField) {
		this.sortField = sortField;
	}

	public String getSortField() {
		return sortField;
	}
}
