package com.masa.pts.core.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.masa.pts.core.validator.NotValidCCType;
import com.masa.pts.core.validator.NotValidPayType;

import io.swagger.annotations.ApiModelProperty;

public class MemberPaymentInput implements Serializable {

	private static final long serialVersionUID = -336796327923823048L;
	
	@ApiModelProperty(value = "${swagger.member.paytype.values}")
	@NotValidPayType
	private String payType;
	@ApiModelProperty(value = "${swagger.member.cctype.values}")
	@NotValidCCType
	private String ccType;
	
	private Date ccExpirationDate;
	private String ccNum;

	private String achBank;
	private String achRouting;
	private String achNumber;

	@ApiModelProperty(required = true)
	private BigDecimal paymentAmount=BigDecimal.ZERO;

	public MemberPaymentInput() {
		super();
	}

	public String getPayType() {
		return payType;
	}

	public void setPayType(String payType) {
		this.payType = payType;
	}

	public String getCcType() {
		return ccType;
	}

	public void setCcType(String ccType) {
		this.ccType = ccType;
	}

	public Date getCcExpirationDate() {
		return ccExpirationDate;
	}

	public void setCcExpirationDate(Date ccExpirationDate) {
		this.ccExpirationDate = ccExpirationDate;
	}

	public String getCcNum() {
		return ccNum;
	}

	public void setCcNum(String ccNum) {
		this.ccNum = ccNum;
	}

	public String getAchBank() {
		return achBank;
	}

	public void setAchBank(String achBank) {
		this.achBank = achBank;
	}

	public String getAchRouting() {
		return achRouting;
	}

	public void setAchRouting(String achRouting) {
		this.achRouting = achRouting;
	}

	public String getAchNumber() {
		return achNumber;
	}

	public void setAchNumber(String achNumber) {
		this.achNumber = achNumber;
	}

	public BigDecimal getPaymentAmount() {
		return paymentAmount;
	}

	public void setPaymentAmount(BigDecimal paymentAmount) {
		this.paymentAmount = paymentAmount;
	}

	@Override
	public String toString() {
		return "MemberPayment [payType=" + payType + ", ccType=" + ccType + ", ccExpirationDate=" + ccExpirationDate
				+ ", ccNum=" + ccNum + ", achBank=" + achBank + ", achRouting=" + achRouting + ", achNumber="
				+ achNumber + ", paymentAmount=" + paymentAmount + "]";
	}	
}
