package com.masa.pts.core.model;

import com.fasterxml.jackson.annotation.JsonCreator;

import static com.google.common.base.CaseFormat.LOWER_CAMEL;
import static com.google.common.base.CaseFormat.UPPER_UNDERSCORE;

//need this mapping for memberSearch operation, because hibernate didn't know how generate sorting fields
// which not a part of entity
public enum  GroupSearchSort {
	GROUP_ID("groupId"),
	GROUP_CODE("groupCode"),
	GROUP_NAME("groupName"),
	BILL_TYPE("billType"),
	BILL_COMPANY("billCompany"),
	SALES_CHANNEL("sc.name"),
	PLAN_TYPE_NAME("pt.name"),
	ACTIVE("active"),
	DIVISION("di.name")
	;

	private String sortField;

	GroupSearchSort(String sortField) {
		this.sortField = sortField;
	}

	public String getSortField() {
		return sortField;
	}
}
