package com.masa.pts.core.model;

import java.io.Serializable;

public class PaymentGroupProductSummaryDTO implements Serializable {

	private static final long serialVersionUID = 873057721290226381L;

	private Integer groupId;
	
	private Integer productId;
	
	private Double totalAmountPaid;

	
	public PaymentGroupProductSummaryDTO() {
		super();
	}

	public Integer getGroupId() {
		return groupId;
	}

	public void setGroupId(Integer groupId) {
		this.groupId = groupId;
	}

	public Integer getProductId() {
		return productId;
	}

	public void setProductId(Integer productId) {
		this.productId = productId;
	}

	public Double getTotalAmountPaid() {
		return totalAmountPaid;
	}

	public void setTotalAmountPaid(Double totalAmountPaid) {
		this.totalAmountPaid = totalAmountPaid;
	}

	@Override
	public String toString() {
		return "PaymentGroupProductSummaryDTO [groupId=" + groupId + ", productId=" + productId + ", totalAmountPaid="
				+ totalAmountPaid + "]";
	}	
}
