package com.masa.pts.core.model;

import com.masa.pts.core.constant.ProductType;

import java.util.Objects;

public class FulfillmentProductDto {

	private final Integer id;
	private final String name;
	private final ProductType type;
	private final Integer upgradeCounter;

	public FulfillmentProductDto(Integer id, String name, ProductType type, Integer upgradeCounter) {
		this.id = id;
		this.name = name;
		this.type = type;
		this.upgradeCounter = upgradeCounter;
	}

	public Integer getId() {
		return id;
	}

	public String getName() {
		return name;
	}

	public ProductType getType() {
		return type;
	}

	public Integer getUpgradeCounter() {
		return upgradeCounter;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) return true;
		if (o == null || getClass() != o.getClass()) return false;
		FulfillmentProductDto that = (FulfillmentProductDto) o;
		return Objects.equals(id, that.id) &&
				Objects.equals(name, that.name) &&
				type == that.type &&
				Objects.equals(upgradeCounter, that.upgradeCounter);
	}

	@Override
	public int hashCode() {
		return Objects.hash(id, name, type, upgradeCounter);
	}

	@Override
	public String toString() {
		return "FulfillmentProductDto{" +
				"id=" + id +
				", name='" + name + '\'' +
				", type=" + type +
				", upgradeCounter=" + upgradeCounter +
				'}';
	}
}
