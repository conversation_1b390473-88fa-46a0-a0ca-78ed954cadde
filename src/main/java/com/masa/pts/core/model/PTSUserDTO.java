package com.masa.pts.core.model;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.masa.pts.core.domain.PTSUser;

public class PTSUserDTO {

	private Integer employeeId;
	private String username;
	private boolean active;
	private String firstName;
	private String lastName;
	private Integer userType;
	private Date createdDate;
	private Integer createdBy;
	private Date modifiedDate;
	private Integer modifiedBy;
	private String createdByUser;
	private String modifiedByUser;
	private Set<PTSUserGroupDTO> userGroups = new HashSet<>();
	private Set<PTSUserRoleDTO> userRoles = new HashSet<>();
	private Set<PTSUserSalesChannelDTO> userSalesChannels = new HashSet<>();
	
	public PTSUserDTO() {
		super();
	}
	public PTSUserDTO(PTSUser entity) {
		employeeId = entity.getEmployeeId();
		username = entity.getUsername();
		active = entity.isActive();
		firstName = entity.getFirstName();
		lastName = entity.getLastName();
		userType = entity.getUserType();
		createdDate = entity.getCreatedDate();
		createdBy = entity.getCreatedBy();
		modifiedBy = entity.getModifiedBy();
		modifiedDate = entity.getModifiedDate();
		createdByUser = entity.getCreatedByUser();
		modifiedByUser = entity.getModifiedByUser();
		
		userGroups = entity.getUserGroups().stream().map(grpItem->{
			PTSUserGroupDTO userGroupDTO = new PTSUserGroupDTO();
			userGroupDTO.setGroupCode(grpItem.getGroupCode());
			userGroupDTO.setGroupId(grpItem.getUserGroupId().getGroup().getGroupId());
			userGroupDTO.setGroupName(grpItem.getUserGroupId().getGroup().getGroupName());
			return userGroupDTO;
		}).collect(Collectors.toSet());
		
		userRoles = entity.getUserRoles().stream().map(roleItem->{
			PTSUserRoleDTO userRoleDTO = new PTSUserRoleDTO();
			userRoleDTO.setRoleId(roleItem.getUserRoleId().getRole().getId());
			userRoleDTO.setRoleCode(roleItem.getRoleCode());
			userRoleDTO.setDescription(roleItem.getUserRoleId().getRole().getDescription());
			return userRoleDTO;
		}).collect(Collectors.toSet());
		
		userSalesChannels = entity.getUserSalesChannels().stream().map(scItem->{
			PTSUserSalesChannelDTO userScDTO = new PTSUserSalesChannelDTO();
			userScDTO.setSalesChannelId(scItem.getUserSalesChannelId().getSalesChannel().getId());
			userScDTO.setName(scItem.getUserSalesChannelId().getSalesChannel().getName());
			userScDTO.setDivisionName(scItem.getUserSalesChannelId().getSalesChannel().getDivision().getName());
			return userScDTO;
		}).collect(Collectors.toSet());
	}
	
	public Integer getEmployeeId() {
		return employeeId;
	}
	public void setEmployeeId(Integer employeeId) {
		this.employeeId = employeeId;
	}
	public String getUsername() {
		return username;
	}
	public void setUsername(String username) {
		this.username = username;
	}
	public boolean isActive() {
		return active;
	}
	public void setActive(boolean active) {
		this.active = active;
	}
	public String getFirstName() {
		return firstName;
	}
	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}
	public String getLastName() {
		return lastName;
	}
	public void setLastName(String lastName) {
		this.lastName = lastName;
	}
	public Integer getUserType() {
		return userType;
	}
	public void setUserType(Integer userType) {
		this.userType = userType;
	}
	public Date getCreatedDate() {
		return createdDate;
	}
	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}
	public Integer getCreatedBy() {
		return createdBy;
	}
	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}
	public Date getModifiedDate() {
		return modifiedDate;
	}
	public void setModifiedDate(Date modifiedDate) {
		this.modifiedDate = modifiedDate;
	}
	public Integer getModifiedBy() {
		return modifiedBy;
	}
	public void setModifiedBy(Integer modifiedBy) {
		this.modifiedBy = modifiedBy;
	}
	public String getCreatedByUser() {
		return createdByUser;
	}
	public void setCreatedByUser(String createdByUser) {
		this.createdByUser = createdByUser;
	}
	public String getModifiedByUser() {
		return modifiedByUser;
	}
	public void setModifiedByUser(String modifiedByUser) {
		this.modifiedByUser = modifiedByUser;
	}
	public Set<PTSUserGroupDTO> getUserGroups() {
		return userGroups;
	}
	public void setUserGroups(Set<PTSUserGroupDTO> userGroups) {
		this.userGroups = userGroups;
	}
	public Set<PTSUserRoleDTO> getUserRoles() {
		return userRoles;
	}
	public void setUserRoles(Set<PTSUserRoleDTO> userRoles) {
		this.userRoles = userRoles;
	}
	public Set<PTSUserSalesChannelDTO> getUserSalesChannels() {
		return userSalesChannels;
	}
	public void setUserSalesChannels(Set<PTSUserSalesChannelDTO> userSalesChannels) {
		this.userSalesChannels = userSalesChannels;
	}
}
