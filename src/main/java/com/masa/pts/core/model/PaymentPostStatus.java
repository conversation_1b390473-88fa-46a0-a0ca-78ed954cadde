package com.masa.pts.core.model;

public enum PaymentPostStatus {
	
	ERROR("Error"),
	NOT_FOUND("Not Found"),
	SUCCESS("Success"),
	PARTIAL("Partial"),
	NOT_PAYOR("Not Payor"),
	DUPLICATE("Duplicate"),
	SKIP("Skip"),
	VALID("Valid"),
	DECLINE_ACH("Decline ACH");
	
	private final String status;

	private PaymentPostStatus(String status) {
		this.status = status;
	}
	public String getStatus() {
		return status;
	}	
	
	public static boolean isStatusSuccess(PaymentPostStatus postStatus) {
		
		if(postStatus.equals(SUCCESS))
		  return true;
		
		return false;
	}
	
	public static boolean isStatusPartialSuccess(PaymentPostStatus postStatus) {
		
		if(postStatus.equals(PARTIAL))
		  return true;
		
		return false;
	}
	public static boolean isStatusSkipped(PaymentPostStatus postStatus) {
		
		if(postStatus.equals(SKIP))
		  return true;
		
		return false;
	}
	
	public static boolean isStatusError(PaymentPostStatus postStatus) {
		
		if(postStatus.equals(ERROR) || postStatus.equals(NOT_FOUND) || postStatus.equals(NOT_PAYOR) || postStatus.equals(DUPLICATE) )
		  return true;
		
		return false;
	}
}
