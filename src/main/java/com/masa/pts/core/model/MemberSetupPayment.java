package com.masa.pts.core.model;

import java.io.Serializable;
import java.math.BigDecimal;

public class MemberSetupPayment implements Serializable {

	private static final long serialVersionUID = 8524675010363921868L;

	private String productCode;
	private Integer productId;
	private BigDecimal paymentAmount=BigDecimal.ZERO;
	
	public MemberSetupPayment() {
		super();
	}
	public String getProductCode() {
		return productCode;
	}
	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}
	public Integer getProductId() {
		return productId;
	}
	public void setProductId(Integer productId) {
		this.productId = productId;
	}
	public BigDecimal getPaymentAmount() {
		return paymentAmount;
	}
	public void setPaymentAmount(BigDecimal paymentAmount) {
		this.paymentAmount = paymentAmount;
	}
}
