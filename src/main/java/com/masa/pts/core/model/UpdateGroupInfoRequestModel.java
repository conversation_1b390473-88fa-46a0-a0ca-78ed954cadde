package com.masa.pts.core.model;

import com.masa.pts.core.validator.NotValidUpdateGroupInfoRequestModel;
import com.masa.pts.core.validator.ObjectLevelValidator;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@NotValidUpdateGroupInfoRequestModel(groups = ObjectLevelValidator.class)
public class UpdateGroupInfoRequestModel extends GroupInfoRequestModel implements Serializable {

	private static final long serialVersionUID = 4013703682418968876L;

	@NotBlank(message = "Group Code can't be null")
	private String groupCode;

	@Override
	public String getGroupCode() {
		return groupCode;
	}

	@Override
	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}
}
