package com.masa.pts.core.model;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.google.common.collect.ImmutableList;
import com.masa.pts.core.utils.common.ExcelDocument;

public class InvoiceExcelDocument implements ExcelDocument<InvoiceDetailDTO> {

	private List<List<Object>> dataRows;
	private List<List<Object>> totalRow;

	public InvoiceExcelDocument(List<InvoiceDetailDTO> detailsList) {
		setDataRow(detailsList);
		setTotalRow(detailsList);
	}

	@Override
	public List<String> getColumnNames() {
		return ImmutableList.of("Enrollee name", "Member number", "Premium Due", "Total Due", "Enrollment date");
	}

	@Override
	public List<List<Object>> getDataRows() {
		return dataRows;
	}

	@Override
	public List<List<Object>> getTotalRow() {
		return totalRow;
	}

	public void setDataRow(List<InvoiceDetailDTO> rowData) {
		this.dataRows = rowData.stream()
				.map(invoiceDetail ->
						Arrays.<Object>asList(
								invoiceDetail.getMemberFullName(),								
								invoiceDetail.getMemberId(),
								invoiceDetail.getAmountDue(),
								invoiceDetail.getAmountDue(),
								invoiceDetail.getInvoiceDate(),
								invoiceDetail.getRenew())
				).collect(Collectors.toList());
	}

	public void setTotalRow(List<InvoiceDetailDTO> detailsList) {
		this.totalRow = getInvoiceTotalData(detailsList);
	}

	private List<List<Object>> getInvoiceTotalData(List<InvoiceDetailDTO> detailsList) {
		List<List<Object>> totalRowData = new ArrayList<>();

		// add divider between data table and total rows
		totalRowData.add(Collections.emptyList());

		totalRowData.add(Arrays.asList(null, "Amount", "Count Members", "Total " +
				detailsList.stream().mapToDouble(InvoiceDetailDTO::getAmountDue).sum()));

		Map<Double, Long> collect = detailsList.stream()
				.collect(Collectors.groupingBy(InvoiceDetailDTO::getAmountDue, Collectors.counting()));

		collect.forEach((k, v) -> totalRowData.add(Arrays.asList(null, k, v)));

		return totalRowData;
	}
}
