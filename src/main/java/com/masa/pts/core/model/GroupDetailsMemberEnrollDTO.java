package com.masa.pts.core.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.masa.pts.core.domain.GroupEntity;
import com.masa.pts.core.service.DependentUtilityService;

public class GroupDetailsMemberEnrollDTO implements Serializable {

	private static final long serialVersionUID = 2272245188066433571L;

	private boolean active;
	
	private String groupCode;
	
	private String groupName;
	
	private Integer maxDependentAge;
	
	private List<ProductSale> saleProducts = new ArrayList<>();

	public GroupDetailsMemberEnrollDTO() {
		super();
	}
	
	public GroupDetailsMemberEnrollDTO(GroupEntity entity,DependentUtilityService dependentUtilityService) {
		active = entity.isActive();
		groupCode = entity.getGroupCode();
		groupName = entity.getGroupName();		
		maxDependentAge = dependentUtilityService.getDepedentAgeLimit(entity.getBusinessLineEntity().getSalesChannel().getId(),
				entity.getBusinessLineEntity().getSalesChannel().getDivision().getId());
	}

	public boolean isActive() {
		return active;
	}

	public void setActive(boolean active) {
		this.active = active;
	}

	public String getGroupCode() {
		return groupCode;
	}

	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}

	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

	public Integer getMaxDependentAge() {
		return maxDependentAge;
	}

	public void setMaxDependentAge(Integer maxDependentAge) {
		this.maxDependentAge = maxDependentAge;
	}

	public List<ProductSale> getSaleProducts() {
		return saleProducts;
	}

	public void setSaleProducts(List<ProductSale> saleProducts) {
		this.saleProducts = saleProducts;
	}	
}
