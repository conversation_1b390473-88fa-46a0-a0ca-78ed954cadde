package com.masa.pts.core.model;

public class GroupSearchDTO {

	private Integer groupId;

	private String groupCode;

	private String groupName;

	private Integer billType;

	private boolean billCompany;

	private Integer planType;

	private boolean active;	
	
	private String planTypeName;
	
	private String division;

	private Integer salesChannelId;

	private String salesChannelName;

	public GroupSearchDTO() {
		super();
	}			
	
	public GroupSearchDTO(Integer groupId, String groupCode, String groupName, Integer billType, boolean billCompany,
			Integer planType, boolean active, String planTypeName) {
		super();
		this.groupId = groupId;
		this.groupCode = groupCode;
		this.groupName = groupName;
		this.billType = billType;
		this.billCompany = billCompany;
		this.planType = planType;
		this.active = active;
		this.planTypeName = planTypeName;
	}

	public GroupSearchDTO(Integer groupId, String groupCode, String groupName, Integer salesChannelId, String salesChannelName, Integer billType,
			boolean billCompany, Integer planType, boolean active, String planTypeName,String division) {
		super();
		this.groupId = groupId;
		this.groupCode = groupCode;
		this.groupName = groupName;
		this.salesChannelId = salesChannelId;
		this.salesChannelName = salesChannelName;
		this.billType = billType;
		this.billCompany = billCompany;
		this.planType = planType;
		this.active = active;
		this.planTypeName = planTypeName;
		this.division = division;
	}


	public Integer getGroupId() {
		return groupId;
	}

	public void setGroupId(Integer groupId) {
		this.groupId = groupId;
	}

	public String getGroupCode() {
		return groupCode;
	}

	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}

	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

	public Integer getBillType() {
		return this.billType;
	}

	public void setBillType(Integer billType) {
		this.billType = billType;
	}

	public boolean isBillCompany() {
		return billCompany;
	}

	public void setBillCompany(boolean billCompany) {
		this.billCompany = billCompany;
	}

	public Integer getPlanType() {
		return planType;
	}

	public void setPlanType(Integer planType) {
		this.planType = planType;
	}

	public boolean isActive() {
		return active;
	}

	public void setActive(boolean active) {
		this.active = active;
	}

	public String getPlanTypeName() {
		return planTypeName;
	}

	public void setPlanTypeName(String planTypeName) {
		this.planTypeName = planTypeName;
	}

	public String getDivision() {
		return division;
	}

	public void setDivision(String division) {
		this.division = division;
	}

	public Integer getSalesChannelId() {
		return salesChannelId;
	}

	public void setSalesChannelId(Integer salesChannelId) {
		this.salesChannelId = salesChannelId;
	}

	public String getSalesChannelName() {
		return salesChannelName;
	}

	public void setSalesChannelName(String salesChannelName) {
		this.salesChannelName = salesChannelName;
	}
}
