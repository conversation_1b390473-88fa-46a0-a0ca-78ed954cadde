package com.masa.pts.core.model;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDate;

public class DependantDto {

    private int dependantId;
    private String firstName;
    private String lastName;
    private String miName;
    private LocalDate birthDate;

    public DependantDto() {
    }

    public int getDependantId() {
        return dependantId;
    }

    public void setDependantId(int dependantId) {
        this.dependantId = dependantId;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getMiName() {
        return miName;
    }

    public void setMiName(String miName) {
        this.miName = miName;
    }

    public LocalDate getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(LocalDate birthDate) {
        this.birthDate = birthDate;
    }
}
