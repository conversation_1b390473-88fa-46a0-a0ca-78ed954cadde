package com.masa.pts.core.model;

import java.util.Date;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.springframework.util.StringUtils;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.masa.pts.core.constant.MemberRenewType;
import com.masa.pts.core.constant.ProductFamilyType;
import com.masa.pts.core.domain.InvoiceDetail;

public class InvoiceDetailDTO {

	private Integer id;
	
	private Double amountDue;
	
	private Integer memberId;
	
	private String memberFullName;
	
	private Date memberEffectiveDate;
	
	private String employeeId;
	
	private String renew;
	
	private Integer productId;
	
	private String productName;
	
	private Double periodFee;
	
	private Double initFee;
		
	private Date invoiceDate;
	
	public InvoiceDetailDTO() {
		super();
	}

	public InvoiceDetailDTO(InvoiceDetail entity) {
		id = entity.getId();
		amountDue = entity.getAmountDue();
		memberId = entity.getMember().getMemberId();
		memberFullName = Stream.of(entity.getMember().getFirstName(),entity.getMember().getMi()
				,entity.getMember().getLastName())
				.filter(StringUtils::hasText)
				.collect(Collectors.joining(" "));
		memberEffectiveDate = entity.getMember().getEffectiveDate();
		employeeId = entity.getMember().getEmployeeId();
		int renewId = Boolean.TRUE.equals(entity.getMember().getIsRenew()) ? 1 : 0;
		Optional<MemberRenewType> optVal = MemberRenewType.getMemberRenewTypeByType(renewId);
		renew = optVal.isPresent() ? optVal.get().toString() : "";
		if(null != entity.getProduct()) {
			productId = entity.getProduct() != null ? entity.getProduct().getProductId() : 0 ;
			String prdSingleFamilyType = ProductFamilyType.getProductFamilyTypeByType(entity.getProduct().getType()).name();
			productName = Stream.of(entity.getProduct().getProductCategory().getName(), StringUtils.capitalize(prdSingleFamilyType.toLowerCase()))
					.filter(StringUtils::hasText)
					.collect(Collectors.joining(" "));	
		}
		periodFee = entity.getPeriodFee();
		initFee = entity.getInitFee();		
		invoiceDate= entity.getInvoiceDate();
	}
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Double getAmountDue() {
		return amountDue;
	}

	public void setAmountDue(Double amountDue) {
		this.amountDue = amountDue;
	}

	public Integer getMemberId() {
		return memberId;
	}

	public void setMemberId(Integer memberId) {
		this.memberId = memberId;
	}
	
	public String getMemberFullName() {
		return memberFullName;
	}

	public void setMemberFullName(String memberFullName) {
		this.memberFullName = memberFullName;
	}

	public Date getMemberEffectiveDate() {
		return memberEffectiveDate;
	}

	public void setMemberEffectiveDate(Date memberEffectiveDate) {
		this.memberEffectiveDate = memberEffectiveDate;
	}

	public String getEmployeeId() {
		return employeeId;
	}

	public void setEmployeeId(String employeeId) {
		this.employeeId = employeeId;
	}

	public String getRenew() {
		return renew;
	}

	public void setRenew(String renew) {
		this.renew = renew;
	}

	public Integer getProductId() {
		return productId;
	}

	public void setProductId(Integer productId) {
		this.productId = productId;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}
	
	public Double getPeriodFee() {
		return periodFee;
	}

	public void setPeriodFee(Double periodFee) {
		this.periodFee = periodFee;
	}

	public Double getInitFee() {
		return initFee;
	}

	public void setInitFee(Double initFee) {
		this.initFee = initFee;
	}

	public Date getInvoiceDate() {
		return invoiceDate;
	}

	public void setInvoiceDate(Date invoiceDate) {
		this.invoiceDate = invoiceDate;
	}
}
