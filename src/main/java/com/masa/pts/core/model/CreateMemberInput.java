package com.masa.pts.core.model;

import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.PastOrPresent;
import javax.validation.constraints.Size;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.masa.pts.core.constant.MemberActiveStatus;
import com.masa.pts.core.validator.NotValidCreateMemberInput;
import com.masa.pts.core.validator.NotValidGroupCodeOrInactive;
import com.masa.pts.core.validator.NotValidMemberEffectiveDate;
import com.masa.pts.core.validator.NotValidStateCode;
import com.masa.pts.core.validator.ObjectLevelValidator;

import io.swagger.annotations.ApiModelProperty;

// todo need to create general class for UpdateMemberInput and CreateMemberInput because,
//  they have almost similar structure including validation
@NotValidCreateMemberInput(groups = ObjectLevelValidator.class)
public class CreateMemberInput implements Serializable, MemberInput {

	private static final long serialVersionUID = -3859737008408462223L;

	@NotBlank(message = "First name cannot be empty")
	@Size(min = 2, message = "First name should have at least 2 chars")
	@Size(max = 50, message = "First name can't be more than 50 chars")
	@ApiModelProperty(required = true,value="First name should have at least 2 chars")
	private String firstName;	
	
	@NotBlank(message="Last name cannot be empty")
	@Size(min= 2, message="Last name should have at least 2 chars")
	@Size(max = 50, message = "Last name can't be more than 50 chars")
	@ApiModelProperty(required = true,value = "Last name should have at least 2 chars")
	private String lastName;

	@Size(max = 50, message = "Middle name can't be more than 50 chars")
	private String mi;
		
	@PastOrPresent(message="Birth Date cannot be future date.")
	@ApiModelProperty(required = true, value = "date format MM-dd-yyy, cannot be future date")
	private Date birthDate;
	
	@ApiModelProperty(value= "format ************")
	private String phone;
	@ApiModelProperty(value= "format ************")
	private String cellPhone;

	@Size(max = 320, message = "Email can't be more than 320 chars")
	private String email;
	
	@ApiModelProperty(value= "if applicable first name should have at least 2 chars")
	@Size(max = 50, message = "Spouse first name can't be more than 50 chars")
	private String spouseFirst;
	
	private boolean hsaEnrolled;
	
	@ApiModelProperty(value= "if applicable last name should have at least 2 chars")
	@Size(max = 50, message = "Spouse last name can't be more than 50 chars")
	private String spouseLast;

	@Size(max = 50, message = "Spouse middle name can't be more than 50 chars")
	private String spouseMi;
	
	
	@PastOrPresent(message="Birth Date cannot be future date.")
	@ApiModelProperty(required = true,value = "Required if Spouse First/Last name are provided.")
	private Date spouseBirthDate;

	@Size(max = 320, message = "Spouse email can't be more than 320 chars")
	private String spouseEmail;

	@ApiModelProperty(value= "format ************")
	private String spousePhone;
	
	private Boolean physicalNewsLetter = Boolean.FALSE;

	@Size(max = 255, message = "Lead id cannot be more then 255 chars")
	private String leadId;

	private String orderNumber;//go to note?
	private String promotion;//go to note?
	private Boolean billInitFeeOneTime=false;
	private Integer alternatePayer=0;
	
	@NotValidStateCode
	@ApiModelProperty(value= "Valid state code where membership is sold.",required = true)	
	private String soldRegion;
	
	@NotValidMemberEffectiveDate
	private Date effectiveDate;
	
	private Date renewDate;
	
	@NotBlank(message="Member Group code is required")
	@ApiModelProperty(required = true)
	@NotValidGroupCodeOrInactive
	private String groupCode;
	
	@ApiModelProperty(value= "Funeral home or Employer.")
	@Size(max = 150, message = "Employer can't be more than 150 chars")
	private String employer;
	
	private MemberPaymentInput memberPaymentInput = new MemberPaymentInput();
	
	@Valid
	@ApiModelProperty(required = true)
	private Set<MemberProductInput> products = new HashSet<>();
	
	@Valid
	@ApiModelProperty(required = true)
	private MemberAddressInput benefitAddress = new MemberAddressInput();
	private MemberAddressInput mailingAddress = new MemberAddressInput();
	@Valid
	private Set<MemberDependantInput> dependents = new HashSet<>();
	
	@JsonIgnore
	private MemberActiveStatus active = MemberActiveStatus.ACTIVE;	
	
	@ApiModelProperty(required = false,value = "Employee Id of the member.")
	private String employeeId;
	
	private String alterId;

	private String spouseAlterId;

	@ApiModelProperty(required = true,value = "${swagger.member.applicationSource.values}")
	private String applicationSource="PTS";
	
	@Valid
	private EmergencyContactDTO emergencyContact;

	public CreateMemberInput() { }

	public String getFirstName() {
		return firstName;
	}
	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}
	public String getLastName() {
		return lastName;
	}
	public void setLastName(String lastName) {
		this.lastName = lastName;
	}
	public String getMi() {
		return mi;
	}
	public void setMi(String mi) {
		this.mi = mi;
	}	
	public Date getBirthDate() {
		return birthDate;
	}
	public void setBirthDate(Date birthDate) {
		this.birthDate = birthDate;
	}
	public String getSpouseFirst() {
		return spouseFirst;
	}
	public void setSpouseFirst(String spouseFirst) {
		this.spouseFirst = spouseFirst;
	}
	public String getSpouseLast() {
		return spouseLast;
	}
	public void setSpouseLast(String spouseLast) {
		this.spouseLast = spouseLast;
	}
	public String getSpouseMi() {
		return spouseMi;
	}
	public void setSpouseMi(String spouseMi) {
		this.spouseMi = spouseMi;
	}
	public Date getSpouseBirthDate() {
		return spouseBirthDate;
	}
	public void setSpouseBirthDate(Date spouseBirthDate) {
		this.spouseBirthDate = spouseBirthDate;
	}
	
	public boolean isHsaEnrolled() {
		return hsaEnrolled;
	}

	public void setHsaEnrolled(boolean hsaEnrolled) {
		this.hsaEnrolled = hsaEnrolled;
	}
	
	public Set<MemberDependantInput> getDependents() {
		return dependents;
	}
	public void setDependents(Set<MemberDependantInput> dependents) {
		this.dependents = dependents;
	}
	
	public Date getEffectiveDate() {
		return effectiveDate;
	}
	public void setEffectiveDate(Date effectiveDate) {
		this.effectiveDate = effectiveDate;
	}
	public Date getRenewDate() {
		return renewDate;
	}
	public void setRenewDate(Date renewDate) {
		this.renewDate = renewDate;
	}
	
	public MemberAddressInput getMailingAddress() {
		return mailingAddress;
	}
	public void setMailingAddress(MemberAddressInput mailingAddress) {
		this.mailingAddress = mailingAddress;
	}
	
	
	public Set<MemberProductInput> getProducts() {
		return products;
	}

	public void setProducts(Set<MemberProductInput> products) {
		this.products = products;
	}

	public String getGroupCode() {
		return groupCode;
	}
	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}	
	public String getSoldRegion() {
		return soldRegion;
	}
	public void setSoldRegion(String soldRegion) {
		this.soldRegion = soldRegion;
	}
	public MemberAddressInput getBenefitAddress() {
		return benefitAddress;
	}
	public void setBenefitAddress(MemberAddressInput benefitAddress) {
		this.benefitAddress = benefitAddress;
	}

	public Boolean getBillInitFeeOneTime() {
		return billInitFeeOneTime;
	}

	public void setBillInitFeeOneTime(Boolean billInitFeeOneTime) {
		this.billInitFeeOneTime = billInitFeeOneTime;
	}

	public Integer getAlternatePayer() {
		return alternatePayer;
	}

	public void setAlternatePayer(Integer alternatePayer) {
		this.alternatePayer = alternatePayer;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getCellPhone() {
		return cellPhone;
	}

	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public Boolean getPhysicalNewsLetter() {
		return physicalNewsLetter;
	}

	public void setPhysicalNewsLetter(Boolean physicalNewsLetter) {
		this.physicalNewsLetter = physicalNewsLetter;
	}

	public String getLeadId() {
		return leadId;
	}

	public void setLeadId(String leadId) {
		this.leadId = leadId;
	}

	public String getSpouseEmail() {
		return spouseEmail;
	}

	public void setSpouseEmail(String spouseEmail) {
		this.spouseEmail = spouseEmail;
	}

	public String getSpousePhone() {
		return spousePhone;
	}

	public void setSpousePhone(String spousePhone) {
		this.spousePhone = spousePhone;
	}

	public String getOrderNumber() {
		return orderNumber;
	}

	public void setOrderNumber(String orderNumber) {
		this.orderNumber = orderNumber;
	}

	public String getPromotion() {
		return promotion;
	}

	public void setPromotion(String promotion) {
		this.promotion = promotion;
	}

	public MemberPaymentInput getMemberPayment() {
		return memberPaymentInput;
	}

	public void setMemberPayment(MemberPaymentInput memberPaymentInput) {
		this.memberPaymentInput = memberPaymentInput;
	}

	public MemberActiveStatus getActive() {
		return active;
	}

	public void setActive(MemberActiveStatus active) {
		this.active = active;
	}

	public String getEmployer() {
		return employer;
	}

	public void setEmployer(String employer) {
		this.employer = employer;
	}

	public String getEmployeeId() {
		return employeeId;
	}

	public void setEmployeeId(String employeeId) {
		this.employeeId = employeeId;
	}

	public String getAlterId() {
		return alterId;
	}

	public void setAlterId(String alterId) {
		this.alterId = alterId;
	}
	
	public String getApplicationSource() {
		return applicationSource;
	}

	public void setApplicationSource(String applicationSource) {
		this.applicationSource = applicationSource;
	}

	public String getSpouseAlterId() {
		return spouseAlterId;
	}

	public void setSpouseAlterId(String spouseAlterId) {
		this.spouseAlterId = spouseAlterId;
	}

	public EmergencyContactDTO getEmergencyContact() {
		return emergencyContact;
	}

	public void setEmergencyContact(EmergencyContactDTO emergencyContact) {
		this.emergencyContact = emergencyContact;
	}

	@Override
	public String toString() {
		return "CreateMemberInput{" +
				"firstName='" + firstName + '\'' +
				", lastName='" + lastName + '\'' +
				", mi='" + mi + '\'' +
				", birthDate=" + birthDate +
				", phone='" + phone + '\'' +
				", cellPhone='" + cellPhone + '\'' +
				", email='" + email + '\'' +
				", spouseFirst='" + spouseFirst + '\'' +
				", spouseLast='" + spouseLast + '\'' +
				", spouseMi='" + spouseMi + '\'' +
				", spouseBirthDate=" + spouseBirthDate +
				", spouseEmail='" + spouseEmail + '\'' +
				", spousePhone='" + spousePhone + '\'' +
				", physicalNewsLetter=" + physicalNewsLetter +
				", leadId='" + leadId + '\'' +
				", orderNumber='" + orderNumber + '\'' +
				", promotion='" + promotion + '\'' +
				", billInitFeeOneTime=" + billInitFeeOneTime +
				", alternatePayer=" + alternatePayer +
				", soldRegion='" + soldRegion + '\'' +
				", effectiveDate=" + effectiveDate +
				", renewDate=" + renewDate +
				", groupCode='" + groupCode + '\'' +
				", employer='" + employer + '\'' +
				", memberPaymentInput=" + memberPaymentInput +
				", products=" + products +
				", benefitAddress=" + benefitAddress +
				", mailingAddress=" + mailingAddress +
				", dependents=" + dependents +
				", active=" + active +
				", employeeId='" + employeeId + '\'' +
				", alterId='" + alterId + '\'' +
				", spouseAlterId='" + spouseAlterId + '\'' +
				", applicationSource='" + applicationSource + '\'' +
				", emergencyContact='" + emergencyContact + '\'' +
				'}';
	}
}
