package com.masa.pts.core.model;

import java.io.Serializable;
import java.util.Date;
import java.util.Optional;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import com.masa.pts.core.domain.FieldValueEntity;
import com.masa.pts.core.domain.GroupContactEntity;
import com.masa.pts.core.domain.GroupEntity;
import com.masa.pts.core.domain.PTSUser;
import com.masa.pts.core.validator.NotValidGroupContactType;

import io.swagger.annotations.ApiModelProperty;

public class GroupContactsRequestModel implements Serializable {

	private static final long serialVersionUID = -232312619445087823L;

	private Integer id;

	@NotNull(message = "ContactType cannot be null")
	@NotValidGroupContactType
	private Integer contactType;

	@NotBlank(message = "First name cannot be empty")
	@Size(min = 2, message = "First name should have at least 2 chars")
	@Size(max = 50, message = "First name can't be more than 50 chars")
	@ApiModelProperty(value = "First name should have at least 2 chars")
	private String firstName;

	@Size(max = 15, message = "Mi name can't be more than 15 chars")
	private String miName;

	@NotBlank(message = "Last name cannot be empty")
	@Size(min = 2, message="Last name should have at least 2 chars")
	@Size(max = 50, message = "Last name can't be more than 50 chars")
	@ApiModelProperty(value = "Last name should have at least 2 chars")
	private String lastName;

	@ApiModelProperty(value = "format ************")
	private String phone;

	@Size(max = 100, message = "Email can't be more than 100 chars")
	private String email;

	private boolean receivesEmailInvoice;

	public static long getSerialVersionUID() {
		return serialVersionUID;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getContactType() {
		return contactType;
	}

	public void setContactType(Integer contactType) {
		this.contactType = contactType;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getMiName() {
		return miName;
	}

	public void setMiName(String miName) {
		this.miName = miName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public boolean isReceivesEmailInvoice() {
		return receivesEmailInvoice;
	}

	public void setReceivesEmailInvoice(boolean receivesEmailInvoice) {
		this.receivesEmailInvoice = receivesEmailInvoice;
	}


	public GroupContactEntity getGroupContactEntity(PTSUser user, GroupEntity group) {
		FieldValueEntity fieldValueEntity = new FieldValueEntity();
		fieldValueEntity.setId(contactType);

		GroupContactEntity groupContactEntity = new GroupContactEntity();
		groupContactEntity.setId(id);
		groupContactEntity.setGroup(group);
		groupContactEntity.setContactType(fieldValueEntity);
		groupContactEntity.setFirstName(firstName);
		groupContactEntity.setMiName(miName);
		groupContactEntity.setLastName(lastName);
		groupContactEntity.setPhone(phone);
		groupContactEntity.setEmail(email);
		groupContactEntity.setReceivesEmailInvoice(receivesEmailInvoice);

		if (id != null) {
			Optional<GroupContactEntity> previousContact =
					group.getContacts().stream()
							.filter(contactList -> contactList.getId().equals(id))
							.findFirst();

			groupContactEntity.setCreatedDate(
					previousContact.map(GroupContactEntity::getCreatedDate).orElse(null));
			groupContactEntity.setCreatedBy(
					previousContact.map(GroupContactEntity::getCreatedBy).orElse(null));
		} else {
			groupContactEntity.setCreatedDate(new Date());
			groupContactEntity.setCreatedBy(user.getEmployeeId());
		}

		groupContactEntity.setModifiedDate(new Date());
		groupContactEntity.setModifiedBy(user.getEmployeeId());

		return groupContactEntity;
	}

}
