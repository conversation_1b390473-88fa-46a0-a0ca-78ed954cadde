package com.masa.pts.core.model;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;

import com.masa.pts.core.validator.NotValidCountryCode;
import com.masa.pts.core.validator.NotValidMemberAddressInput;
import com.masa.pts.core.validator.NotValidStateCode;

import io.swagger.annotations.ApiModelProperty;

// todo need to change name and refactor it everywhere, because for example the Group use the same type of address
@NotValidMemberAddressInput
public class MemberAddressInput implements Serializable {

	private static final long serialVersionUID = 2473772470579170715L;

	@NotBlank(message="Address 1 is required")
	@ApiModelProperty(required = true)
	private String address1;
	
	private String address2;
	private String address3;
	
	@NotBlank(message="City is required")
	@ApiModelProperty(required = true)
	private String city;
	
	@NotBlank(message="State is required")
	@ApiModelProperty(required = true)
	@NotValidStateCode
	private String stateCd;
	
	@NotBlank(message="Country is required")
	@ApiModelProperty(required = true)
	@NotValidCountryCode
	private String countryCd;
	
	@ApiModelProperty(required = true,notes = "required only for country USA")
	private String zip;
	
	private String zip4;
	
	public MemberAddressInput() { }

	public String getAddress1() {
		return address1;
	}

	public void setAddress1(String address1) {
		this.address1 = address1;
	}

	public String getAddress2() {
		return address2;
	}

	public void setAddress2(String address2) {
		this.address2 = address2;
	}

	public String getAddress3() {
		return address3;
	}

	public void setAddress3(String address3) {
		this.address3 = address3;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getStateCd() {
		return stateCd;
	}

	public void setStateCd(String stateCd) {
		this.stateCd = stateCd;
	}

	public String getCountryCd() {
		return countryCd;
	}

	public void setCountryCd(String countryCd) {
		this.countryCd = countryCd;
	}

	public String getZip() {
		return zip;
	}

	public void setZip(String zip) {
		this.zip = zip;
	}

	public String getZip4() {
		return zip4;
	}

	public void setZip4(String zip4) {
		this.zip4 = zip4;
	}

	@Override
	public String toString() {
		return "MemberAddress [address1=" + address1 + ", address2=" + address2 + ", address3=" + address3 + ", city="
				+ city + ", stateCd=" + stateCd + ", countryCd=" + countryCd + ", zip=" + zip + ", zip4=" + zip4 + "]";
	}	
}
