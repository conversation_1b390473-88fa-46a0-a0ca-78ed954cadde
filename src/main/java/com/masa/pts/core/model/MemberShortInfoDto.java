package com.masa.pts.core.model;

import com.masa.pts.core.constant.MemberActiveStatus;
import com.masa.pts.core.service.PTSUtilityService;

import java.time.LocalDate;
import java.util.Date;
import java.util.Objects;

public class MemberShortInfoDto {
    private final Integer id;
    private final String firstName;
    private final String middleName;
    private final String lastName;
    private final LocalDate effectiveDate;
    private final LocalDate renewDate;
    private final MemberActiveStatus status;
    private final String groupCode;
    private final String groupName;
    private final String division;
    private final String salesChannel;

    public MemberShortInfoDto(Integer id, String firstName, String middleName, String lastName,
                              Date effectiveDate, Date renewDate,
                              Integer activity, String groupCode, String groupName, String division,
                              String salesChannel) {
        this.id = id;
        this.firstName = firstName;
        this.middleName = middleName;
        this.lastName = lastName;
        this.effectiveDate = PTSUtilityService.convertUtilToLocalDate(effectiveDate);
        this.renewDate = PTSUtilityService.convertUtilToLocalDate(renewDate);
        this.status = MemberActiveStatus.getStatusName(activity);
        this.groupCode = groupCode;
        this.groupName = groupName;
        this.division = division;
        this.salesChannel = salesChannel;
    }

    public Integer getId() {
        return id;
    }

    public String getFirstName() {
        return firstName;
    }

    public String getMiddleName() {
        return middleName;
    }

    public String getLastName() {
        return lastName;
    }

    public LocalDate getEffectiveDate() {
        return effectiveDate;
    }

    public LocalDate getRenewDate() {
        return renewDate;
    }

    public MemberActiveStatus getStatus() {
        return status;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public String getGroupName() {
        return groupName;
    }

    public String getDivision() {
        return division;
    }

    public String getSalesChannel() {
        return salesChannel;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof MemberShortInfoDto)) return false;
        MemberShortInfoDto that = (MemberShortInfoDto) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(firstName, that.firstName) &&
                Objects.equals(middleName, that.middleName) &&
                Objects.equals(lastName, that.lastName) &&
                Objects.equals(effectiveDate, that.effectiveDate) &&
                Objects.equals(renewDate, that.renewDate) &&
                status == that.status &&
                Objects.equals(groupCode, that.groupCode) &&
                Objects.equals(groupName, that.groupName) &&
                Objects.equals(division, that.division) &&
                Objects.equals(salesChannel, that.salesChannel);
    }

    @Override
    public int hashCode() {
        return Objects
                .hash(id, firstName, middleName, lastName, effectiveDate, renewDate, status, groupCode, groupName,
                        division,
                        salesChannel);
    }

    @Override
    public String toString() {
        return "MemberShortInfoDto{" +
                "id=" + id +
                ", firstName='" + firstName + '\'' +
                ", middleName='" + middleName + '\'' +
                ", lastName='" + lastName + '\'' +
                ", effectiveDate=" + effectiveDate +
                ", renewDate=" + renewDate +
                ", status=" + status +
                ", groupCode='" + groupCode + '\'' +
                ", groupName='" + groupName + '\'' +
                ", division='" + division + '\'' +
                ", salesChannel='" + salesChannel + '\'' +
                '}';
    }
}
