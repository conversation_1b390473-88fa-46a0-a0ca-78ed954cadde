package com.masa.pts.core.model;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import com.fasterxml.jackson.annotation.JsonFormat;

public class PTSUserSearchDTO {

	private Integer employeeId;
	private String username;
	private boolean active;
	private String firstName;
	private String lastName;
	private Integer userType;
	private Date createdDate;
	private Integer createdBy;
	private Date modifiedDate;
	private Integer modifiedBy;
	private Set<PTSUserRoleDTO> userRoles = new HashSet<>();
	
	public PTSUserSearchDTO() {
		super();
	}

	
	public PTSUserSearchDTO(Integer employeeId, String username, boolean active, String firstName, String lastName,
			Integer userType, Date createdDate, Integer createdBy, Date modifiedDate, Integer modifiedBy
			 ) {
		super();
		this.employeeId = employeeId;
		this.username = username;
		this.active = active;
		this.firstName = firstName;
		this.lastName = lastName;
		this.userType = userType;
		this.createdDate = createdDate;
		this.createdBy = createdBy;
		this.modifiedDate = modifiedDate;
		this.modifiedBy = modifiedBy;
	}


	public Integer getEmployeeId() {
		return employeeId;
	}

	public void setEmployeeId(Integer employeeId) {
		this.employeeId = employeeId;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public boolean isActive() {
		return active;
	}

	public void setActive(boolean active) {
		this.active = active;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public Integer getUserType() {
		return userType;
	}

	public void setUserType(Integer userType) {
		this.userType = userType;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public Integer getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}

	public Date getModifiedDate() {
		return modifiedDate;
	}

	public void setModifiedDate(Date modifiedDate) {
		this.modifiedDate = modifiedDate;
	}

	public Integer getModifiedBy() {
		return modifiedBy;
	}

	public void setModifiedBy(Integer modifiedBy) {
		this.modifiedBy = modifiedBy;
	}

	public Set<PTSUserRoleDTO> getUserRoles() {
		return userRoles;
	}

	public void setUserRoles(Set<PTSUserRoleDTO> userRoles) {
		this.userRoles = userRoles;
	}
}
