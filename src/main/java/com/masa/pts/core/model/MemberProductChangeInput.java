package com.masa.pts.core.model;

import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.PastOrPresent;
import javax.validation.constraints.Size;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.masa.pts.core.validator.NotValidGroupCodeOrInactive;
import com.masa.pts.core.validator.NotValidMemberEffectiveDate;
import com.masa.pts.core.validator.NotValidMemberId;
import com.masa.pts.core.validator.NotValidMemberProductChangeInput;
import com.masa.pts.core.validator.NotValidStateCode;
import com.masa.pts.core.validator.ObjectLevelValidator;

import io.swagger.annotations.ApiModelProperty;

@NotValidMemberProductChangeInput(groups = ObjectLevelValidator.class)
public class MemberProductChangeInput implements Serializable {

	private static final long serialVersionUID = 5277240514761305324L;

	@ApiModelProperty(value = "Valid Member Id is required.")
	@NotValidMemberId
	private Integer memberId;
	
	@NotBlank(message = "GroupEntity code is required.")
	@ApiModelProperty(value = "New GroupEntity code where member is being changed to.")
	@NotValidGroupCodeOrInactive
	private String groupCode;
	
	@ApiModelProperty(value = "Setup fee payment one time or monthly.")
	private Boolean billInitFeeOneTime=false;
		
	@ApiModelProperty(value= "if applicable first name should have atleast 2 chars")
	private String spouseFirst;
	
	@ApiModelProperty(value= "if applicable last name should have atleast 2 chars")
	private String spouseLast;
	private String spouseMi;
	
	@PastOrPresent(message="Birth Date cannot be future date.")
	@ApiModelProperty(required = true,value = "Required if Spouse First/Last name are provided.")
	private Date spouseBirthDate;
	private String spouseEmail;
	
	@Valid
	private Set<MemberProductInput> products = new HashSet<>();
	
	@Valid
	private MemberPaymentInput memberPaymentInput = new MemberPaymentInput();
	
	@Valid
	private Set<MemberDependantInput> dependents = new HashSet<>();

	private String orderNumber;
	private String promotion;
	
	@ApiModelProperty(value = "If set to true, spouse and dependants are removed.")
	private Boolean removeDependants=Boolean.FALSE;
	
	@ApiModelProperty(required = true,value = "${swagger.member.applicationSource.values}")
	private String applicationSource="PTS";
	
	@ApiModelProperty(required = true,value = "If not provided defaults to today's date.")
	@NotValidMemberEffectiveDate
	private Date productEffectiveDate = new Date();
	
	@NotBlank(message = "First name cannot be empty")
	@Size(min = 2, message = "First name should have at least 2 chars")
	@Size(max = 50, message = "First name can't be more than 50 chars")
	@ApiModelProperty(required = true,value="First name should have at least 2 chars")
	private String firstName;	
	
	@NotBlank(message="Last name cannot be empty")
	@Size(min= 2, message="Last name should have at least 2 chars")
	@Size(max = 50, message = "Last name can't be more than 50 chars")
	@ApiModelProperty(required = true,value = "Last name should have at least 2 chars")
	private String lastName;

	@Size(max = 50, message = "Middle name can't be more than 50 chars")
	private String mi;
		
	@PastOrPresent(message="Birth Date cannot be future date.")
	@ApiModelProperty(required = true, value = "date format MM-dd-yyy, cannot be future date")
	private Date birthDate;
	
	@ApiModelProperty(value= "format ************")
	private String phone;
	
	@ApiModelProperty(value= "format ************")
	private String cellPhone;

	@Size(max = 320, message = "Email can't be more than 320 chars")
	private String email;
	
	@NotValidStateCode
	@ApiModelProperty(value= "Valid state code where membership is sold.",required = true)	
	private String soldRegion;
	
	private Boolean isMemberInfoUpdate=Boolean.FALSE;
	
	@Valid
	@ApiModelProperty(required = true)
	private MemberAddressInput benefitAddress = new MemberAddressInput();
	private MemberAddressInput mailingAddress = new MemberAddressInput();
	
	@ApiModelProperty(value= "Funeral home or Employer.")
	@Size(max = 150, message = "Employer can't be more than 150 chars")
	private String employer;
	
	public MemberProductChangeInput() {
		super();
	}

	public String getGroupCode() {
		return groupCode;
	}

	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}

	public Set<MemberProductInput> getProducts() {
		return products;
	}

	public void setProducts(Set<MemberProductInput> products) {
		this.products = products;
	}

	public MemberPaymentInput getMemberPaymentInput() {
		return memberPaymentInput;
	}

	public void setMemberPaymentInput(MemberPaymentInput memberPaymentInput) {
		this.memberPaymentInput = memberPaymentInput;
	}

	public Boolean getBillInitFeeOneTime() {
		return billInitFeeOneTime;
	}

	public void setBillInitFeeOneTime(Boolean billInitFeeOneTime) {
		this.billInitFeeOneTime = billInitFeeOneTime;
	}

	public String getSpouseFirst() {
		return spouseFirst;
	}

	public void setSpouseFirst(String spouseFirst) {
		this.spouseFirst = spouseFirst;
	}

	public String getSpouseLast() {
		return spouseLast;
	}

	public void setSpouseLast(String spouseLast) {
		this.spouseLast = spouseLast;
	}

	public String getSpouseMi() {
		return spouseMi;
	}

	public void setSpouseMi(String spouseMi) {
		this.spouseMi = spouseMi;
	}

	public Date getSpouseBirthDate() {
		return spouseBirthDate;
	}

	public void setSpouseBirthDate(Date spouseBirthDate) {
		this.spouseBirthDate = spouseBirthDate;
	}

	public String getSpouseEmail() {
		return spouseEmail;
	}

	public void setSpouseEmail(String spouseEmail) {
		this.spouseEmail = spouseEmail;
	}

	public Set<MemberDependantInput> getDependents() {
		return dependents;
	}

	public void setDependents(Set<MemberDependantInput> dependents) {
		this.dependents = dependents;
	}

	public Integer getMemberId() {
		return memberId;
	}

	public void setMemberId(Integer memberId) {
		this.memberId = memberId;
	}

	public String getOrderNumber() {
		return orderNumber;
	}

	public void setOrderNumber(String orderNumber) {
		this.orderNumber = orderNumber;
	}

	public String getPromotion() {
		return promotion;
	}

	public void setPromotion(String promotion) {
		this.promotion = promotion;
	}

	public Boolean getRemoveDependants() {
		return removeDependants;
	}

	public void setRemoveDependants(Boolean removeDependants) {
		this.removeDependants = removeDependants;
	}

	public String getApplicationSource() {
		return applicationSource;
	}

	public void setApplicationSource(String applicationSource) {
		this.applicationSource = applicationSource;
	}

	public Date getProductEffectiveDate() {
		return productEffectiveDate;
	}

	public void setProductEffectiveDate(Date productEffectiveDate) {
		this.productEffectiveDate = productEffectiveDate;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public String getMi() {
		return mi;
	}

	public void setMi(String mi) {
		this.mi = mi;
	}

	public Date getBirthDate() {
		return birthDate;
	}

	public void setBirthDate(Date birthDate) {
		this.birthDate = birthDate;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getCellPhone() {
		return cellPhone;
	}

	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getSoldRegion() {
		return soldRegion;
	}

	public void setSoldRegion(String soldRegion) {
		this.soldRegion = soldRegion;
	}

	public Boolean getIsMemberInfoUpdate() {
		return isMemberInfoUpdate;
	}

	public void setIsMemberInfoUpdate(Boolean isMemberInfoUpdate) {
		this.isMemberInfoUpdate = isMemberInfoUpdate;
	}

	public MemberAddressInput getBenefitAddress() {
		return benefitAddress;
	}

	public void setBenefitAddress(MemberAddressInput benefitAddress) {
		this.benefitAddress = benefitAddress;
	}

	public MemberAddressInput getMailingAddress() {
		return mailingAddress;
	}

	public void setMailingAddress(MemberAddressInput mailingAddress) {
		this.mailingAddress = mailingAddress;
	}

	public String getEmployer() {
		return employer;
	}

	public void setEmployer(String employer) {
		this.employer = employer;
	}
	
	@Override
	public String toString() {
		return "MemberProductChangeInput [memberId=" + memberId + ", groupCode=" + groupCode + ", billInitFeeOneTime="
				+ billInitFeeOneTime + ", spouseFirst=" + spouseFirst + ", spouseLast=" + spouseLast + ", spouseMi="
				+ spouseMi + ", spouseBirthDate=" + spouseBirthDate + ", spouseEmail=" + spouseEmail + ", products="
				+ products + ", memberPaymentInput=" + memberPaymentInput + ", dependents=" + dependents
				+ ", orderNumber=" + orderNumber + ", promotion=" + promotion + ", removeDependants=" + removeDependants
				+ ", applicationSource=" + applicationSource + ", productEffectiveDate=" + productEffectiveDate 
				+ "firstName='" + firstName +", lastName='" + lastName + ", mi='" + mi +", birthDate=" + birthDate 
				+ ", phone='" + phone +", cellPhone='" + cellPhone +", email='" + email + ", soldRegion='" + soldRegion 
				+ ", benefitAddress=" + benefitAddress + ", mailingAddress=" + mailingAddress +", employer=" + employer + "]";
	}
}
