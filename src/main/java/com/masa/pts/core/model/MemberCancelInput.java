package com.masa.pts.core.model;

import java.io.Serializable;
import java.util.Date;

import javax.validation.constraints.Positive;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;

public class MemberCancelInput implements Serializable {

	private static final long serialVersionUID = 1793362771696870742L;
	
	private String cancelReason;
	
	@ApiModelProperty(value = "If empty default to today's date.")
	private Date cancelDate;
	
	@Positive(message="Cancel reason id cannot be empty or 0")
	@ApiModelProperty(required = true)
	private Integer cancelReasonId;

	@ApiModelProperty(required = true,value = "${swagger.member.applicationSource.values}")
	private String applicationSource="PTS";
	
	public MemberCancelInput() {
		super();
	}

	public String getCancelReason() {
		return cancelReason;
	}

	public void setCancelReason(String cancelReason) {
		this.cancelReason = cancelReason;
	}

	public Date getCancelDate() {
		return cancelDate;
	}

	public void setCancelDate(Date cancelDate) {
		this.cancelDate = cancelDate;
	}

	public Integer getCancelReasonId() {
		return cancelReasonId;
	}

	public void setCancelReasonId(Integer cancelReasonId) {
		this.cancelReasonId = cancelReasonId;
	}

	public String getApplicationSource() {
		return applicationSource;
	}

	public void setApplicationSource(String applicationSource) {
		this.applicationSource = applicationSource;
	}	
}
