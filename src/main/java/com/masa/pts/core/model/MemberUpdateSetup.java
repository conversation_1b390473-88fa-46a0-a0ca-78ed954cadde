package com.masa.pts.core.model;

import java.time.LocalDate;

public class MemberUpdateSetup extends MemberSetup {

	private static final long serialVersionUID = 5655763633890513586L;

	private boolean productUpdate=false;	
	
	private boolean copyProductAndCommission=false;
	
	private LocalDate effectiveDate;

	public MemberUpdateSetup() {
		super();
	}

	public boolean isProductUpdate() {
		return productUpdate;
	}

	public void setProductUpdate(boolean productUpdate) {
		this.productUpdate = productUpdate;
	}

	public LocalDate getEffectiveDate() {
		return effectiveDate;
	}

	public void setEffectiveDate(LocalDate effectiveDate) {
		this.effectiveDate = effectiveDate;
	}

	public boolean isCopyProductAndCommission() {
		return copyProductAndCommission;
	}

	public void setCopyProductAndCommission(boolean copyProductAndCommission) {
		this.copyProductAndCommission = copyProductAndCommission;
	}
	
}
