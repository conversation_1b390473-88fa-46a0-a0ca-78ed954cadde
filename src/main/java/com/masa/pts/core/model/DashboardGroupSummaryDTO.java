package com.masa.pts.core.model;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

public class DashboardGroupSummaryDTO implements Serializable {
	private static final long serialVersionUID = -5121040115090344792L;

	private Integer groupId;
	private String groupCode;
	private String groupName;
	private boolean active;
	private Long memberCount;
	private Map<String,Object> productMemberCnt = new HashMap<>();
	private String groupLogoURL;
	
	public DashboardGroupSummaryDTO() {
		super();
	}		
	
	public DashboardGroupSummaryDTO(Integer groupId, String groupCode, String groupName, boolean active, Long memberCount) {
		super();
		this.groupId = groupId;
		this.groupCode = groupCode;
		this.groupName = groupName;
		this.active = active;
		this.memberCount = memberCount;
	}


	public Integer getGroupId() {
		return groupId;
	}
	public void setGroupId(Integer groupId) {
		this.groupId = groupId;
	}
	public String getGroupCode() {
		return groupCode;
	}
	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}
	public String getGroupName() {
		return groupName;
	}
	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}
	public boolean isActive() {
		return active;
	}
	public void setActive(boolean active) {
		this.active = active;
	}
	public Long getMemberCount() {
		return memberCount;
	}
	public void setMemberCount(Long memberCount) {
		this.memberCount = memberCount;
	}
	public Map<String, Object> getProductMemberCnt() {
		return productMemberCnt;
	}
	public void setProductMemberCnt(Map<String, Object> productMemberCnt) {
		this.productMemberCnt = productMemberCnt;
	}
	public void addProductMemberCnt(String productName,Object memberCount) {
		this.productMemberCnt.put(productName, memberCount);
	}
	public String getGroupLogoURL() {
		return groupLogoURL;
	}
	public void setGroupLogoURL(String groupLogoURL) {
		this.groupLogoURL = groupLogoURL;
	}
}
