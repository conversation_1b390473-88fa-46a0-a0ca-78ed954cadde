package com.masa.pts.core.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

import static com.google.common.base.CaseFormat.LOWER_CAMEL;
import static com.google.common.base.CaseFormat.UPPER_UNDERSCORE;

public enum GroupsSortField {

    GROUP_CODE,
    GROUP_NAME,
    SALES_CHANNEL,
    PLAN_TYPE,
    BILLING_TYPE,
    INVOICE_TYPE,
    STATUS;

    @JsonValue
    public String getName() {
        return UPPER_UNDERSCORE.to(LOWER_CAMEL, this.name());
    }
}
