package com.masa.pts.core.model;

import java.io.Serializable;

public class PaymentGroupProductDTO implements Serializable {

	private static final long serialVersionUID = 4405899872794482191L;

	private Integer payDetailId;
	
	private Integer groupId;
	
	private Integer productId;
	
	private Double amountPaid;
	
	private Boolean hasTax;
	
	private Double taxRate;
	
	private Double periodFee;


	public PaymentGroupProductDTO(Integer payDetailId, Integer groupId, Integer productId, Double amountPaid,
			Boolean hasTax, Double taxRate,Double periodFee) {
		super();
		this.payDetailId = payDetailId;
		this.groupId = groupId;
		this.productId = productId;
		this.amountPaid = amountPaid;
		this.hasTax = hasTax;
		this.taxRate = taxRate;
		this.periodFee = periodFee;

	}

	public Integer getPayDetailId() {
		return payDetailId;
	}

	public void setPayDetailId(Integer payDetailId) {
		this.payDetailId = payDetailId;
	}

	public Integer getGroupId() {
		return groupId;
	}

	public void setGroupId(Integer groupId) {
		this.groupId = groupId;
	}

	public Integer getProductId() {
		return productId;
	}

	public void setProductId(Integer productId) {
		this.productId = productId;
	}

	public Double getAmountPaid() {
		return amountPaid;
	}

	public void setAmountPaid(Double amountPaid) {
		this.amountPaid = amountPaid;
	}

	public Boolean getHasTax() {
		return hasTax;
	}

	public void setHasTax(Boolean hasTax) {
		this.hasTax = hasTax;
	}

	public Double getTaxRate() {
		return taxRate;
	}

	public void setTaxRate(Double taxRate) {
		this.taxRate = taxRate;
	}

	public Double getPeriodFee() {
		return periodFee;
	}

	public void setPeriodFee(Double periodFee) {
		this.periodFee = periodFee;
	}

	@Override
	public String toString() {
		return "PaymentGroupProductDTO [payDetailId=" + payDetailId + ", groupId=" + groupId + ", productId="
				+ productId + ", amountPaid=" + amountPaid + ", hasTax=" + hasTax + ", taxRate=" + taxRate
				+ ", periodFee=" + periodFee + "]";
	}
	
	
}
