package com.masa.pts.core.model;

public class MemberGroupDetailsDTO {

	private String groupCode;
	
	private String groupName;
	
	private Integer maxDependentAge;
	
	private boolean active;

	private boolean printCard;

	public MemberGroupDetailsDTO() {
		super();
	}

	public String getGroupCode() {
		return groupCode;
	}

	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}

	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

	public Integer getMaxDependentAge() {
		return maxDependentAge;
	}

	public void setMaxDependentAge(Integer maxDependentAge) {
		this.maxDependentAge = maxDependentAge;
	}

	public boolean isActive() {
		return active;
	}

	public void setActive(boolean active) {
		this.active = active;
	}

	public boolean isPrintCard() {
		return printCard;
	}

	public void setPrintCard(boolean printCard) {
		this.printCard = printCard;
	}
}
