package com.masa.pts.core.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.masa.pts.core.domain.Invoice;

public class InvoiceDTO {

	private Integer invoiceId;
	
	private Integer invoiceType;
	
	private Double totalDue;
	
	private Date invoiceDate;
	
	private Boolean isPaid;
	
	private Date createdDate;
	
	private Date modifiedDate;
	
	private Integer createdBy;
	private Integer modifiedBy;
	
	private BigDecimal groupCreditAmount = BigDecimal.ZERO;
	
	private String groupCode;
	
	private String groupName;
	
	private Date paymentPostDate;
	
	private Set<InvoiceDetailDTO> invoiceDetail = new HashSet<>();


	public Integer getInvoiceId() {
		return invoiceId;
	}

	public void setInvoiceId(Integer invoiceId) {
		this.invoiceId = invoiceId;
	}

	public Integer getInvoiceType() {
		return invoiceType;
	}

	public void setInvoiceType(Integer invoiceType) {
		this.invoiceType = invoiceType;
	}

	public Double getTotalDue() {
		return totalDue;
	}

	public void setTotalDue(Double totalDue) {
		this.totalDue = totalDue;
	}

	public Date getInvoiceDate() {
		return invoiceDate;
	}

	public void setInvoiceDate(Date invoiceDate) {
		this.invoiceDate = invoiceDate;
	}

	public Boolean getIsPaid() {
		return isPaid;
	}

	public void setIsPaid(Boolean isPaid) {
		this.isPaid = isPaid;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public Date getModifiedDate() {
		return modifiedDate;
	}

	public void setModifiedDate(Date modifiedDate) {
		this.modifiedDate = modifiedDate;
	}

	public Integer getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}

	public Date getPaymentPostDate() {
		return paymentPostDate;
	}

	public void setPaymentPostDate(Date paymentPostDate) {
		this.paymentPostDate = paymentPostDate;
	}

	public Integer getModifiedBy() {
		return modifiedBy;
	}

	public void setModifiedBy(Integer modifiedBy) {
		this.modifiedBy = modifiedBy;
	}

	public BigDecimal getGroupCreditAmount() {
		return groupCreditAmount;
	}

	public void setGroupCreditAmount(BigDecimal groupCreditAmount) {
		this.groupCreditAmount = groupCreditAmount;
	}

	public Set<InvoiceDetailDTO> getInvoiceDetail() {
		return invoiceDetail;
	}

	public void setInvoiceDetail(Set<InvoiceDetailDTO> invoiceDetail) {
		this.invoiceDetail = invoiceDetail;
	}	

	public String getGroupCode() {
		return groupCode;
	}

	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}

	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

	public InvoiceDTO(Invoice entity) {		
		invoiceId = entity.getInvoiceId();
		invoiceDate = entity.getInvoiceDate();
		invoiceType = entity.getInvoiceType();
		totalDue = entity.getTotalDue();
		isPaid = entity.getIsPaid();
		groupCreditAmount = entity.getGroupCreditAmount();
		createdDate = entity.getCreatedDate();
		modifiedDate = entity.getModifiedDate();
		groupCode = entity.getGroup().getGroupCode();
		groupName = entity.getGroup().getGroupName();
		
		paymentPostDate = entity.getPayment() != null ? entity.getPayment().getPayDate() : null;
		
		entity.getInvoiceDetail().forEach(detailItem->{
			InvoiceDetailDTO detail = new InvoiceDetailDTO(detailItem);			
			invoiceDetail.add(detail);			
		});		
	}
}
