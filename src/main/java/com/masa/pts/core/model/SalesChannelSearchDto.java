package com.masa.pts.core.model;

import java.util.Objects;

public class SalesChannelSearchDto {
	private Integer id;
	private String name;
	private String divisionName;
	private String forteMerchant;
	private boolean isObsolete;

	public SalesChannelSearchDto(Integer id,
								 String name,
								 String divisionName,
								 String forteMerchant,
								 boolean isObsolete) {
		this.id = id;
		this.name = name;
		this.divisionName = divisionName;
		this.forteMerchant = forteMerchant;
		this.isObsolete = isObsolete;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDivisionName() {
		return divisionName;
	}

	public void setDivisionName(String divisionName) {
		this.divisionName = divisionName;
	}

	public String getForteMerchant() {
		return forteMerchant;
	}

	public void setForteMerchant(String forteMerchant) {
		this.forteMerchant = forteMerchant;
	}

	public boolean getIsObsolete() {
		return isObsolete;
	}

	public void setIsObsolete(boolean isObsolete) {
		this.isObsolete = isObsolete;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) return true;
		if (o == null || getClass() != o.getClass()) return false;
		SalesChannelSearchDto searchDto = (SalesChannelSearchDto) o;
		return isObsolete == searchDto.isObsolete &&
				Objects.equals(id, searchDto.id) &&
				Objects.equals(name, searchDto.name) &&
				Objects.equals(divisionName, searchDto.divisionName) &&
				Objects.equals(forteMerchant, searchDto.forteMerchant);
	}

	@Override
	public int hashCode() {
		return Objects.hash(id, name, divisionName, forteMerchant, isObsolete);
	}

	@Override
	public String toString() {
		return "SalesChannelSearchDto{" +
				"id=" + id +
				", name='" + name + '\'' +
				", divisionName='" + divisionName + '\'' +
				", forteMerchant='" + forteMerchant + '\'' +
				", isObsolete=" + isObsolete +
				'}';
	}
}
