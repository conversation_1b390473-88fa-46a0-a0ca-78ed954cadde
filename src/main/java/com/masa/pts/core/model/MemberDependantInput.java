package com.masa.pts.core.model;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.PastOrPresent;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 *
 */
public class MemberDependantInput implements Serializable {

	private static final long serialVersionUID = -4317968964950942649L;

	private Integer dependantId;

	@NotBlank(message = "If provided, Dependant first name should have atleast 2 chars")
	@ApiModelProperty(required=true)
	private String firstName;
	
	@NotBlank(message = "If provided, Dependant last name should have atleast 2 chars")
	@ApiModelProperty(required=true)
	private String lastName;
	
	private String miName;
	
	@PastOrPresent(message="Birth Date cannot be future date.")
	@NotNull(message="Dependant Birth Date cannot be empty.")
	@ApiModelProperty(required=true,value =" format MM-dd-yyyy, cannot be future date")
	private Date birthDate;
	
	private Boolean enrolledCollege;
	
	public MemberDependantInput() {
		super();
	}

	private MemberDependantInput(Builder builder) {
		setDependantId(builder.dependantId);
		setFirstName(builder.firstName);
		setLastName(builder.lastName);
		setMiName(builder.miName);
		setBirthDate(builder.birthDate);
		setEnrolledCollege(builder.enrolledCollege);
	}

	public String getFirstName() {
		return firstName;
	}
	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}
	public String getLastName() {
		return lastName;
	}
	public void setLastName(String lastName) {
		this.lastName = lastName;
	}
	public String getMiName() {
		return miName;
	}
	public void setMiName(String miName) {
		this.miName = miName;
	}
	public Date getBirthDate() {
		return birthDate;
	}
	public void setBirthDate(Date birthDate) {
		this.birthDate = birthDate;
	}
	public Boolean getEnrolledCollege() {
		return enrolledCollege;
	}
	public void setEnrolledCollege(Boolean enrolledCollege) {
		this.enrolledCollege = enrolledCollege;
	}

	public Integer getDependantId() {
		return dependantId;
	}

	public void setDependantId(Integer dependantId) {
		this.dependantId = dependantId;
	}

	@Override
	public String toString() {
		return "MemberDependantInput{" +
				"dependantId=" + dependantId +
				", firstName='" + firstName + '\'' +
				", lastName='" + lastName + '\'' +
				", miName='" + miName + '\'' +
				", birthDate=" + birthDate +
				", enrolledCollege=" + enrolledCollege +
				'}';
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) return true;
		if (!(o instanceof MemberDependantInput)) return false;
		MemberDependantInput that = (MemberDependantInput) o;
		return Objects.equals(dependantId, that.dependantId) &&
				Objects.equals(firstName, that.firstName) &&
				Objects.equals(lastName, that.lastName) &&
				Objects.equals(miName, that.miName) &&
				Objects.equals(birthDate, that.birthDate) &&
				Objects.equals(enrolledCollege, that.enrolledCollege);
	}

	@Override
	public int hashCode() {
		return Objects.hash(dependantId, firstName, lastName, miName, birthDate, enrolledCollege);
	}

	public static final class Builder {
		private Integer dependantId;
		private @NotBlank(message = "If provided, Dependant first name should have atleast 2 chars") String firstName;
		private @NotBlank(message = "If provided, Dependant last name should have atleast 2 chars") String lastName;
		private String miName;
		private @PastOrPresent(message = "Birth Date cannot be future date.") @NotNull(message = "Dependant Birth Date cannot be empty.") Date
				birthDate;
		private Boolean enrolledCollege;

		public Builder() {
		}

		public Builder dependantId(Integer val) {
			dependantId = val;
			return this;
		}

		public Builder firstName(
				@NotBlank(message = "If provided, Dependant first name should have atleast 2 chars") String val) {
			firstName = val;
			return this;
		}

		public Builder lastName(
				@NotBlank(message = "If provided, Dependant last name should have atleast 2 chars") String val) {
			lastName = val;
			return this;
		}

		public Builder miName(String val) {
			miName = val;
			return this;
		}

		public Builder birthDate(@PastOrPresent(message = "Birth Date cannot be future date.")
								 @NotNull(message = "Dependant Birth Date cannot be empty.") Date val) {
			birthDate = val;
			return this;
		}

		public Builder enrolledCollege(Boolean val) {
			enrolledCollege = val;
			return this;
		}

		public MemberDependantInput build() {
			return new MemberDependantInput(this);
		}
	}
}
