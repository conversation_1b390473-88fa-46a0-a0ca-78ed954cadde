package com.masa.pts.core.model;

import java.io.Serializable;
import java.util.Date;

public class MemberSetupProduct implements Serializable {

	private static final long serialVersionUID = 7055197406223855251L;

	private Integer productId;	
	private Double payAmount=0.0;
	private Double maxDueAmount=0.0;
	private Integer productUpgradeType=0;
	private String agentNum;
	private Date soldDate;
	private Double setupFeePayAmount=0.0;	
	private Double setupFeeMaxDueAmount=0.0;	
	private boolean overrideFee = Boolean.FALSE;
	private String groupCode;
	private Integer groupId;
	private Date effectiveStartDate;
	private Date effectiveEndDate;
	private Integer freeTrial =0;
	
	public MemberSetupProduct() {
		super();
	}

	public Integer getProductId() {
		return productId;
	}

	public void setProductId(Integer productId) {
		this.productId = productId;
	}

	public Double getPayAmount() {
		return payAmount;
	}

	public void setPayAmount(Double payAmount) {
		this.payAmount = payAmount;
	}

	public Double getMaxDueAmount() {
		return maxDueAmount;
	}

	public void setMaxDueAmount(Double maxDueAmount) {
		this.maxDueAmount = maxDueAmount;
	}

	public Integer getProductUpgradeType() {
		return productUpgradeType;
	}

	public void setProductUpgradeType(Integer productUpgradeType) {
		this.productUpgradeType = productUpgradeType;
	}

	public String getAgentNum() {
		return agentNum;
	}

	public void setAgentNum(String agentNum) {
		this.agentNum = agentNum;
	}

	public Date getSoldDate() {
		return soldDate;
	}

	public void setSoldDate(Date soldDate) {
		this.soldDate = soldDate;
	}

	public Double getSetupFeePayAmount() {
		return setupFeePayAmount;
	}

	public void setSetupFeePayAmount(Double setupFeePayAmount) {
		this.setupFeePayAmount = setupFeePayAmount;
	}

	public Double getSetupFeeMaxDueAmount() {
		return setupFeeMaxDueAmount;
	}

	public void setSetupFeeMaxDueAmount(Double setupFeeMaxDueAmount) {
		this.setupFeeMaxDueAmount = setupFeeMaxDueAmount;
	}

	public boolean isOverrideFee() {
		return overrideFee;
	}

	public void setOverrideFee(boolean overrideFee) {
		this.overrideFee = overrideFee;
	}

	public String getGroupCode() {
		return groupCode;
	}

	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}

	public Integer getGroupId() {
		return groupId;
	}

	public void setGroupId(Integer groupId) {
		this.groupId = groupId;
	}

	public Date getEffectiveStartDate() {
		return effectiveStartDate;
	}

	public void setEffectiveStartDate(Date effectiveStartDate) {
		this.effectiveStartDate = effectiveStartDate;
	}

	public Date getEffectiveEndDate() {
		return effectiveEndDate;
	}
	public void setEffectiveEndDate(Date effectiveEndDate) {
		this.effectiveEndDate = effectiveEndDate;
	}

	public Integer getFreeTrial() {
		return freeTrial;
	}

	public void setFreeTrial(Integer freeTrial) {
		this.freeTrial = freeTrial;
	}
	
}
