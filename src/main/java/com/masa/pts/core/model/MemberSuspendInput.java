package com.masa.pts.core.model;

import java.util.Date;

import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;

public class MemberSuspendInput extends MemberCancelInput {

	private static final long serialVersionUID = 6927724270629956670L;

	@ApiModelProperty(value = "Date format MM-dd-yyyy.",required = true)		
	@NotNull(message = "Reactivate date is required.")
	private Date reactivateDate;
	
	@ApiModelProperty(required = true,value = "${swagger.member.applicationSource.values}")
	private String applicationSource="PTS";

	public MemberSuspendInput() {
		super();
	}

	public Date getReactivateDate() {
		return reactivateDate;
	}

	public void setReactivateDate(Date reactivateDate) {
		this.reactivateDate = reactivateDate;
	}

	public String getApplicationSource() {
		return applicationSource;
	}

	public void setApplicationSource(String applicationSource) {
		this.applicationSource = applicationSource;
	}	
}
