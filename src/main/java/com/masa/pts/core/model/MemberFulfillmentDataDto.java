package com.masa.pts.core.model;

import com.masa.pts.core.domain.Address;
import com.masa.pts.core.domain.MemberFee;
import io.swagger.models.auth.In;

import java.util.Objects;

// this class describe data which needed to create fulfillment
public class MemberFulfillmentDataDto {

    private Integer memberId;
    private MemberFee memberFee;
    private String firstName;
    private String mi;
    private String lastName;
    private Address addressDetails;
    private Address alternateAddressDetails;
    private Address burialAddressDetails;
    private Integer addressDetailsId;
    private Integer alternateAddressDetailsId;
    private Integer burialAddressDetailsId;

    public MemberFulfillmentDataDto(Integer memberId, String firstName, String mi, String lastName, Integer a1, Integer a2, Integer a3) {
        this.memberId = memberId;
        this.firstName = firstName;
        this.mi = mi;
        this.lastName = lastName;
        this.addressDetailsId = a1;
        this.alternateAddressDetailsId = a2;
        this.burialAddressDetailsId = a3;
    }

    public void setMemberFee(MemberFee memberFee) {
        this.memberFee = memberFee;
    }

    public Integer getMemberId() {
        return memberId;
    }

    public MemberFee getMemberFee() {
        return memberFee;
    }

    public String getFirstName() {
        return firstName;
    }

    public String getMi() {
        return mi;
    }

    public String getLastName() {
        return lastName;
    }

    public Address getAddressDetails() {
        return addressDetails;
    }

    public Address getAlternateAddressDetails() {
        return alternateAddressDetails;
    }

    public Address getBurialAddressDetails() {
        return burialAddressDetails;
    }

    public void setAddressDetails(Address addressDetails) {
        this.addressDetails = addressDetails;
    }

    public void setAlternateAddressDetails(Address alternateAddressDetails) {
        this.alternateAddressDetails = alternateAddressDetails;
    }

    public void setBurialAddressDetails(Address burialAddressDetails) {
        this.burialAddressDetails = burialAddressDetails;
    }

    public Integer getAddressDetailsId() {
        return addressDetailsId;
    }

    public Integer getAlternateAddressDetailsId() {
        return alternateAddressDetailsId;
    }

    public Integer getBurialAddressDetailsId() {
        return burialAddressDetailsId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof MemberFulfillmentDataDto)) return false;
        MemberFulfillmentDataDto that = (MemberFulfillmentDataDto) o;
        return Objects.equals(memberId, that.memberId) &&
                Objects.equals(memberFee, that.memberFee) &&
                Objects.equals(firstName, that.firstName) &&
                Objects.equals(mi, that.mi) &&
                Objects.equals(lastName, that.lastName) &&
                Objects.equals(addressDetails, that.addressDetails) &&
                Objects.equals(alternateAddressDetails, that.alternateAddressDetails) &&
                Objects.equals(burialAddressDetails, that.burialAddressDetails) &&
                Objects.equals(addressDetailsId, that.addressDetailsId) &&
                Objects.equals(alternateAddressDetailsId, that.alternateAddressDetailsId) &&
                Objects.equals(burialAddressDetailsId, that.burialAddressDetailsId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(memberId, memberFee, firstName, mi, lastName, addressDetails, alternateAddressDetails,
                burialAddressDetails, addressDetailsId, alternateAddressDetailsId, burialAddressDetailsId);
    }

    @Override
    public String toString() {
        return "MemberFulfillmentDataDto{" +
                "memberId=" + memberId +
                ", memberFee=" + memberFee +
                ", firstName='" + firstName + '\'' +
                ", mi='" + mi + '\'' +
                ", lastName='" + lastName + '\'' +
                ", addressDetails=" + addressDetails +
                ", alternateAddressDetails=" + alternateAddressDetails +
                ", burialAddressDetails=" + burialAddressDetails +
                ", addressDetailsId=" + addressDetailsId +
                ", alternateAddressDetailsId=" + alternateAddressDetailsId +
                ", burialAddressDetailsId=" + burialAddressDetailsId +
                '}';
    }
}
