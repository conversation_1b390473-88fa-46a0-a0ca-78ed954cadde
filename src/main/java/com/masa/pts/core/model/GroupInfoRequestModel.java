package com.masa.pts.core.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Optional;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import com.masa.pts.core.domain.Constant;
import com.masa.pts.core.validator.NotValidCompanyId;
import com.masa.pts.core.validator.NotValidGroupPlanType;

public abstract class GroupInfoRequestModel {

	protected String groupCode;

	protected boolean printCard;

	// default value should be true
	protected boolean active = true;

	@Size(max = 50)
	@NotEmpty(message = "Group Name can't be empty")
	protected String groupName;

	@Valid
	protected AddressInput mailingAddressDetails;

	// todo need to be refactored and used Enum instead of Integer
	@Min(value = 1, message = "Bill type should be from 1 to 3")
	@Max(value = 3, message = "Bill type should be from 1 to 3")
	@NotNull(message = "Bill type can't be null")
	protected Integer billType;

	@NotValidCompanyId
	@NotNull(message = "Company Id can't be null")
	protected Integer companyId;

	@NotValidGroupPlanType(message = "Plan type can't be null")
	protected Integer planType;

	protected Integer brokerAgentId;

	protected Integer agentId;

	protected boolean autoPay;

	protected Date effectiveDate;

	protected Date soldDate;

	protected boolean downPaymentGrp;

	protected boolean lifetimeGrp;

	protected boolean isNetComm;

	protected boolean isAdvComm;

	protected boolean printRenewal;

	protected String note;

	protected boolean billCompany;

	@NotNull(message = "SalesChannelId cannot be null")
	protected Integer salesChannelId;

	protected Integer paymentMonthsBehind;

	protected Integer pastDueInactive;
	
	protected boolean receivesEmailInvoice;
	
	private BigDecimal creditDebitAmount=BigDecimal.ZERO;

	private Date pastDueEffectiveDate=Constant.DEFULT_DATE_1900;

	private Integer membershipSource;
	private Integer brokerPlatform;
	private Integer paymentTerm;
	private boolean autoSuspendCancelMember;
	
	public String getGroupCode() {
		return groupCode;
	}

	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}

	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

	public Optional<AddressInput> getMailingAddressDetails() {
		return Optional.ofNullable(mailingAddressDetails);
	}

	public void setMailingAddressDetails(AddressInput mailingAddressDetails) {
		this.mailingAddressDetails = mailingAddressDetails;
	}

	public Integer getBillType() {
		return billType;
	}

	public void setBillType(Integer billType) {
		this.billType = billType;
	}

	public Integer getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Integer companyId) {
		this.companyId = companyId;
	}

	public Integer getPlanType() {
		return planType;
	}

	public void setPlanType(Integer planType) {
		this.planType = planType;
	}

	public Integer getBrokerAgentId() {
		return brokerAgentId;
	}

	public void setBrokerAgentId(Integer brokerAgentId) {
		this.brokerAgentId = brokerAgentId;
	}

	public Integer getAgentId() {
		return agentId;
	}

	public void setAgentId(Integer agentId) {
		this.agentId = agentId;
	}

	public Date getEffectiveDate() {
		return effectiveDate;
	}

	public void setEffectiveDate(Date effectiveDate) {
		this.effectiveDate = effectiveDate;
	}

	public boolean isPrintCard() {
		return printCard;
	}

	public void setPrintCard(boolean printCard) {
		this.printCard = printCard;
	}

	public boolean isActive() {
		return active;
	}

	public void setActive(boolean active) {
		this.active = active;
	}

	public boolean isAutoPay() {
		return autoPay;
	}

	public void setAutoPay(boolean autoPay) {
		this.autoPay = autoPay;
	}

	public Date getSoldDate() {
		return soldDate;
	}

	public void setSoldDate(Date soldDate) {
		this.soldDate = soldDate;
	}

	public boolean isDownPaymentGrp() {
		return downPaymentGrp;
	}

	public void setDownPaymentGrp(boolean downPaymentGrp) {
		this.downPaymentGrp = downPaymentGrp;
	}

	public boolean isLifetimeGrp() {
		return lifetimeGrp;
	}

	public void setLifetimeGrp(boolean lifetimeGrp) {
		this.lifetimeGrp = lifetimeGrp;
	}

	public boolean isNetComm() {
		return isNetComm;
	}

	public void setNetComm(boolean netComm) {
		isNetComm = netComm;
	}

	public boolean isAdvComm() {
		return isAdvComm;
	}

	public void setAdvComm(boolean advComm) {
		isAdvComm = advComm;
	}

	public boolean isPrintRenewal() {
		return printRenewal;
	}

	public void setPrintRenewal(boolean printRenewal) {
		this.printRenewal = printRenewal;
	}

	public String getNote() {
		return note;
	}

	public void setNote(String note) {
		this.note = note;
	}

	public boolean isBillCompany() {
		return billCompany;
	}

	public void setBillCompany(boolean billCompany) {
		this.billCompany = billCompany;
	}

	public Integer getSalesChannelId() {
		return salesChannelId;
	}

	public void setSalesChannelId(Integer salesChannelId) {
		this.salesChannelId = salesChannelId;
	}

	public Integer getPaymentMonthsBehind() {
		return paymentMonthsBehind;
	}

	public void setPaymentMonthsBehind(Integer paymentMonthsBehind) {
		this.paymentMonthsBehind = paymentMonthsBehind;
	}

	public Integer getPastDueInactive() {
		return pastDueInactive;
	}

	public void setPastDueInactive(Integer pastDueInactive) {
		this.pastDueInactive = pastDueInactive;
	}

	public boolean isReceivesEmailInvoice() {
		return receivesEmailInvoice;
	}

	public void setReceivesEmailInvoice(boolean receivesEmailInvoice) {
		this.receivesEmailInvoice = receivesEmailInvoice;
	}

	public BigDecimal getCreditDebitAmount() {
		return creditDebitAmount;
	}

	public void setCreditDebitAmount(BigDecimal creditDebitAmount) {
		this.creditDebitAmount = creditDebitAmount;
	}

	public Date getPastDueEffectiveDate() {
		return pastDueEffectiveDate;
	}

	public void setPastDueEffectiveDate(Date pastDueEffectiveDate) {
		this.pastDueEffectiveDate = pastDueEffectiveDate;
	}

	public Integer getMembershipSource() {
		return membershipSource;
	}

	public void setMembershipSource(Integer membershipSource) {
		this.membershipSource = membershipSource;
	}

	public Integer getBrokerPlatform() {
		return brokerPlatform;
	}

	public void setBrokerPlatform(Integer brokerPlatform) {
		this.brokerPlatform = brokerPlatform;
	}

	public Integer getPaymentTerm() {
		return paymentTerm;
	}

	public void setPaymentTerm(Integer paymentTerm) {
		this.paymentTerm = paymentTerm;
	}

	public boolean isAutoSuspendCancelMember() {
		return autoSuspendCancelMember;
	}

	public void setAutoSuspendCancelMember(boolean autoSuspendCancelMember) {
		this.autoSuspendCancelMember = autoSuspendCancelMember;
	}
}
