package com.masa.pts.core.model;

//need this mapping for operation, because hibernate didn't know how generate sorting fields
// which not a part of entity
public enum GroupDocumentSort {

    id("id"),
    type("documentType"),
    name("documentName"),
    documentId("documentId"),
    uploadedBy("createdBy"),
    uploadedDate("createdDate"),
    postDate("documentPostDate"),
    invDepDate("documentInvDepositDate");

    private String sortField;

    GroupDocumentSort(String sortField) {
        this.sortField = sortField;
    }

    public String getSortField() {
        return sortField;
    }
}
