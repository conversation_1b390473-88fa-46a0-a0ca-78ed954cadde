package com.masa.pts.core.model;


import com.masa.pts.core.domain.Fee;
import com.masa.pts.core.domain.Frequency;
import com.masa.pts.core.domain.GroupEntity;
import com.masa.pts.core.domain.GroupProductFee;
import com.masa.pts.core.domain.PTSUser;
import com.masa.pts.core.domain.Product;
import com.masa.pts.core.validator.NotValidFeeId;
import com.masa.pts.core.validator.NotValidFrequencyId;
import com.masa.pts.core.validator.NotValidProductId;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.Optional;

public class GroupProductRequestModel implements Serializable {

	private static final long serialVersionUID = 904408899878659151L;

	private Integer groupProductId;

	@NotValidProductId
	@NotNull(message = "Product id can't be null")
	private Integer productId;

	@NotValidFeeId
	@NotNull(message = "Fee id can't be null")
	private Integer feeId;

	@NotValidFrequencyId
	@NotNull(message = "Frequency id can't be null")
	private Integer frequencyId;

	@DecimalMin(value = "0.0")
	private double amount;

	@DecimalMin(value = "0.0")
	private double maxAmountDue;

	@DecimalMin(value = "0.0")
	private double retailAmount;

	public Integer getGroupProductId() {
		return groupProductId;
	}

	public void setGroupProductId(Integer groupProductId) {
		this.groupProductId = groupProductId;
	}

	public Integer getProductId() {
		return productId;
	}

	public void setProductId(Integer productId) {
		this.productId = productId;
	}

	public Integer getFeeId() {
		return feeId;
	}

	public void setFeeId(Integer feeId) {
		this.feeId = feeId;
	}

	public Integer getFrequencyId() {
		return frequencyId;
	}

	public void setFrequencyId(Integer frequencyId) {
		this.frequencyId = frequencyId;
	}

	public double getAmount() {
		return amount;
	}

	public void setAmount(double amount) {
		this.amount = amount;
	}

	public double getMaxAmountDue() {
		return maxAmountDue;
	}

	public void setMaxAmountDue(double maxAmountDue) {
		this.maxAmountDue = maxAmountDue;
	}

	public double getRetailAmount() {
		return retailAmount;
	}

	public void setRetailAmount(double retailAmount) {
		this.retailAmount = retailAmount;
	}

	public GroupProductFee getGroupProductsFee(GroupEntity group, PTSUser user) {
		GroupProductFee groupProductFee = new GroupProductFee();

		Product product = new Product();
		product.setProductId(getProductId());

		Fee fee = new Fee();
		fee.setFeeId(getFeeId());

		Frequency frequency = new Frequency();
		frequency.setID(getFeeId());

		groupProductFee.setId(getGroupProductId());
		groupProductFee.setGroup(group);
		groupProductFee.setProduct(product);
		groupProductFee.setFeeDetails(fee);
		groupProductFee.setAmount(getAmount());
		groupProductFee.setFrequencyDetails(frequency);
		groupProductFee.setMaxAmountDue(getMaxAmountDue());
		groupProductFee.setRetailAmount(getRetailAmount());

		if (groupProductId != null) {
			Optional<GroupProductFee> previousProduct =
					group.getGrpPrdList().stream()
							.filter(productList -> productList.getId().equals(groupProductId))
							.findFirst();

			groupProductFee.setCreatedDate(previousProduct.map(GroupProductFee::getCreatedDate).orElse(null));
			groupProductFee.setCreatedBy(previousProduct.map(GroupProductFee::getCreatedBy).orElse(null));
		} else {
			groupProductFee.setCreatedDate(new Date());
			groupProductFee.setCreatedBy(user.getEmployeeId());
		}

		groupProductFee.setModifiedDate(new Date());
		groupProductFee.setModifiedBy(user.getEmployeeId());

		return groupProductFee;
	}
}
