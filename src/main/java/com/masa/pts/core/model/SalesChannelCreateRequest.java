package com.masa.pts.core.model;

import com.masa.pts.core.validator.ValidMerchantId;
import com.masa.pts.core.validator.ValidSalesChannelName;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Objects;

public class SalesChannelCreateRequest {
	@Size(min = 1, message = "Sales channel name can't be empty")
	@Size(max = 50, message = "Sales channel name can't be more then 50 symbols")
	@ValidSalesChannelName
	private String name;

	private boolean isObsolete;

	@NotNull
	private Integer divisionId;

	@ValidMerchantId()
	private String merchantId;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public boolean getIsObsolete() {
		return isObsolete;
	}

	public void setObsolete(boolean obsolete) {
		isObsolete = obsolete;
	}

	public Integer getDivisionId() {
		return divisionId;
	}

	public void setDivisionId(Integer divisionId) {
		this.divisionId = divisionId;
	}

	public String getMerchantId() {
		return merchantId;
	}

	public void setMerchantId(String merchantId) {
		this.merchantId = merchantId;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) return true;
		if (o == null || getClass() != o.getClass()) return false;
		SalesChannelCreateRequest that = (SalesChannelCreateRequest) o;
		return isObsolete == that.isObsolete &&
				Objects.equals(name, that.name) &&
				Objects.equals(divisionId, that.divisionId) &&
				Objects.equals(merchantId, that.merchantId);
	}

	@Override
	public int hashCode() {
		return Objects.hash(name, isObsolete, divisionId, merchantId);
	}

	@Override
	public String toString() {
		return "SalesChannelCreateRequest{" +
				"name='" + name + '\'' +
				", isObsolete=" + isObsolete +
				", divisionId=" + divisionId +
				", merchantId='" + merchantId + '\'' +
				'}';
	}
}
