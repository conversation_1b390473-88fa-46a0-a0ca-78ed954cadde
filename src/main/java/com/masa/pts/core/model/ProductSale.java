package com.masa.pts.core.model;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonView;

public class ProductSale implements Serializable {

	private static final long serialVersionUID = -4674391628700804440L;

	@JsonView({TPAPortalView.Group.class,TPAPortalView.Member.class})
	private Integer productFeeId;
	
	@JsonView({TPAPortalView.Group.class,TPAPortalView.Member.class})
	private Integer productId;
	@JsonView({TPAPortalView.Group.class,TPAPortalView.Member.class})
	private String productName;
	@JsonView({TPAPortalView.Group.class,TPAPortalView.Member.class})
	private Integer productType;
	@JsonView({TPAPortalView.Group.class,TPAPortalView.Member.class})
	private Integer upgradeProduct;
	
	@JsonView({TPAPortalView.Group.class,TPAPortalView.Member.class})
	private Integer feeId;
	@JsonView({TPAPortalView.Group.class,TPAPortalView.Member.class})
	private String feeName;
	
	@JsonView({TPAPortalView.Group.class,TPAPortalView.Member.class})
	private Integer frequency;
	@JsonView({TPAPortalView.Group.class,TPAPortalView.Member.class})
	private String  freqDesc;
	
	@JsonView({TPAPortalView.Group.class,TPAPortalView.Member.class})
	private double amount;
	@JsonView({TPAPortalView.Group.class,TPAPortalView.Member.class})
	private double maxAmountDue;
	@JsonView({TPAPortalView.Group.class,TPAPortalView.Member.class})
	private double retailAmount;	
	@JsonView({TPAPortalView.Group.class,TPAPortalView.Member.class})
	private String categoryCode;
	@JsonView({TPAPortalView.Group.class,TPAPortalView.Member.class})
	private String categoryName;
	@JsonView({TPAPortalView.Group.class,TPAPortalView.Member.class})
	private String groupCode;
	@JsonView({TPAPortalView.Group.class,TPAPortalView.Member.class})
	private double setupFeeAmt=0;
	@JsonView({TPAPortalView.Group.class,TPAPortalView.Member.class})
	private double setupFeeMaxAmtDue=0;
	
	@JsonView({TPAPortalView.Group.class,TPAPortalView.Member.class})
	private String agentNum;
	
	@JsonView({TPAPortalView.Group.class,TPAPortalView.Member.class}) 
	private Date effectiveStartDate;
	
	@JsonView({TPAPortalView.Group.class,TPAPortalView.Member.class})
	private Date effectiveEndDate;
	
	private String employeeId;
	
	
	
	public ProductSale(Integer productId, Integer productType, Integer upgradeProduct,
			Integer feeId, String feeName, Integer frequency, String freqDesc, double amount, double maxAmountDue,
			double retailAmount, String categoryCode, String categoryName, String groupCode, double setupFeeAmt,
			double setupFeeMaxAmtDue, String agentNum) {
		super();
		this.productId = productId;
		this.productType = productType;
		this.upgradeProduct = upgradeProduct;
		this.feeId = feeId;
		this.feeName = feeName;
		this.frequency = frequency;
		this.freqDesc = freqDesc;
		this.amount = amount;
		this.maxAmountDue = maxAmountDue;
		this.retailAmount = retailAmount;
		this.categoryCode = categoryCode;
		this.categoryName = categoryName;
		this.groupCode = groupCode;
		this.setupFeeAmt = setupFeeAmt;
		this.setupFeeMaxAmtDue = setupFeeMaxAmtDue;
		this.agentNum = agentNum;
	}
	
	public ProductSale(Integer productFeeId, Integer productId, Integer productType,
			Integer upgradeProduct, Integer feeId, String feeName, Integer frequency, String freqDesc, double amount,
			double maxAmountDue, double retailAmount, String categoryCode, String categoryName, String groupCode,
			double setupFeeAmt, double setupFeeMaxAmtDue, Date effectiveStartDate,
			Date effectiveEndDate, String employeeId) {
		super();
		this.productFeeId = productFeeId;
		this.productId = productId;
		this.productType = productType;
		this.upgradeProduct = upgradeProduct;
		this.feeId = feeId;
		this.feeName = feeName;
		this.frequency = frequency;
		this.freqDesc = freqDesc;
		this.amount = amount;
		this.maxAmountDue = maxAmountDue;
		this.retailAmount = retailAmount;
		this.categoryCode = categoryCode;
		this.categoryName = categoryName;
		this.groupCode = groupCode;
		this.setupFeeAmt = setupFeeAmt;
		this.setupFeeMaxAmtDue = setupFeeMaxAmtDue;
		this.effectiveStartDate = effectiveStartDate;
		this.effectiveEndDate = effectiveEndDate;
		this.employeeId = employeeId;
	}

	public ProductSale() {
		super();
	}
	public String getCategoryCode() {
		return categoryCode;
	}
	public void setCategoryCode(String categoryCode) {
		this.categoryCode = categoryCode;
	}
	public String getCategoryName() {
		return categoryName;
	}
	public void setCategoryName(String categoryName) {
		this.categoryName = categoryName;
	}
	public Integer getProductId() {
		return productId;
	}
	public void setProductId(Integer productId) {
		this.productId = productId;
	}
	public String getProductName() {
		return productName;
	}
	public void setProductName(String productName) {
		this.productName = productName;
	}
	public Integer getProductType() {
		return productType;
	}
	public void setProductType(Integer productType) {
		this.productType = productType;
	}
	public Integer getUpgradeProduct() {
		return upgradeProduct;
	}
	public void setUpgradeProduct(Integer upgradeProduct) {
		this.upgradeProduct = upgradeProduct;
	}
	public Integer getFeeId() {
		return feeId;
	}
	public void setFeeId(Integer feeId) {
		this.feeId = feeId;
	}
	public String getFeeName() {
		return feeName;
	}
	public void setFeeName(String feeName) {
		this.feeName = feeName;
	}
	public Integer getFrequency() {
		return frequency;
	}
	public void setFrequency(Integer frequency) {
		this.frequency = frequency;
	}
	public double getAmount() {
		return amount;
	}
	public void setAmount(double amount) {
		this.amount = amount;
	}
	public double getMaxAmountDue() {
		return maxAmountDue;
	}
	public void setMaxAmountDue(double maxAmountDue) {
		this.maxAmountDue = maxAmountDue;
	}
	public double getRetailAmount() {
		return retailAmount;
	}
	public void setRetailAmount(double retailAmount) {
		this.retailAmount = retailAmount;
	}
	public String getFreqDesc() {
		return freqDesc;
	}
	public void setFreqDesc(String freqDesc) {
		this.freqDesc = freqDesc;
	}
	public String getGroupCode() {
		return groupCode;
	}
	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}
	public double getSetupFeeAmt() {
		return setupFeeAmt;
	}
	public void setSetupFeeAmt(double setupFeeAmt) {
		this.setupFeeAmt = setupFeeAmt;
	}
	public double getSetupFeeMaxAmtDue() {
		return setupFeeMaxAmtDue;
	}
	public void setSetupFeeMaxAmtDue(double setupFeeMaxAmtDue) {
		this.setupFeeMaxAmtDue = setupFeeMaxAmtDue;
	}
	public String getAgentNum() {
		return agentNum;
	}
	public void setAgentNum(String agentNum) {
		this.agentNum = agentNum;
	}
	public Date getEffectiveStartDate() {
		return effectiveStartDate;
	}
	public void setEffectiveStartDate(Date effectiveStartDate) {
		this.effectiveStartDate = effectiveStartDate;
	}
	public Date getEffectiveEndDate() {
		return effectiveEndDate;
	}
	public void setEffectiveEndDate(Date effectiveEndDate) {
		this.effectiveEndDate = effectiveEndDate;
	}
	public Integer getProductFeeId() {
		return productFeeId;
	}
	public void setProductFeeId(Integer productFeeId) {
		this.productFeeId = productFeeId;
	}
	public String getEmployeeId() {
		return employeeId;
	}
	public void setEmployeeId(String employeeId) {
		this.employeeId = employeeId;
	}
}
