package com.masa.pts.core.model;

import com.masa.pts.core.validator.GroupCodeExistence;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Null;
import javax.validation.constraints.Size;
import java.io.Serializable;

public class CreateGroupInfoRequestModel extends GroupInfoRequestModel implements Serializable {

	private static final long serialVersionUID = -8087811183774392335L;

	@Null(message = "AgentId must be null")
	private Integer agentId;

	@Null(message = "BrokerId must be null")
	private Integer brokerAgentId;

	@NotBlank(message = "GroupCode can't be null")
	@GroupCodeExistence
	@Size(max = 50)
	private String groupCode;

	@Override
	public String getGroupCode() {
		return groupCode;
	}

	@Override
	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}

	@Override
	public Integer getAgentId() {
		return agentId;
	}

	@Override
	public void setAgentId(Integer agentId) {
		this.agentId = agentId;
	}

	@Override
	public Integer getBrokerAgentId() {
		return brokerAgentId;
	}

	@Override
	public void setBrokerAgentId(Integer brokerAgentId) {
		this.brokerAgentId = brokerAgentId;
	}
}
