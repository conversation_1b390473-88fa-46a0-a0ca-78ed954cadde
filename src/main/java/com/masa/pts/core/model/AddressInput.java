package com.masa.pts.core.model;

import java.io.Serializable;

import com.masa.pts.core.validator.NotValidAddressInput;

import io.swagger.annotations.ApiModelProperty;

@NotValidAddressInput
public class AddressInput implements Serializable {

	private static final long serialVersionUID = -4000527065855738826L;

	@ApiModelProperty(required = true,notes="required if other address fields are entered")
	private String address1;
	
	private String address2;
	private String address3;
	
	@ApiModelProperty(required = true,notes="required if other address fields are entered")
	private String city;
	
	@ApiModelProperty(required = true,notes="required if other address fields are entered")
	private String stateCd;
	
	@ApiModelProperty(required = true,notes="required if other address fields are entered")
	private String countryCd;
	
	@ApiModelProperty(required = true,notes="required if other address fields are entered")
	private String zip;
	
	private String zip4;

	public AddressInput() {
		super();
	}

	public String getAddress1() {
		return address1;
	}

	public void setAddress1(String address1) {
		this.address1 = address1;
	}

	public String getAddress2() {
		return address2;
	}

	public void setAddress2(String address2) {
		this.address2 = address2;
	}

	public String getAddress3() {
		return address3;
	}

	public void setAddress3(String address3) {
		this.address3 = address3;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getStateCd() {
		return stateCd;
	}

	public void setStateCd(String stateCd) {
		this.stateCd = stateCd;
	}

	public String getCountryCd() {
		return countryCd;
	}

	public void setCountryCd(String countryCd) {
		this.countryCd = countryCd;
	}

	public String getZip() {
		return zip;
	}

	public void setZip(String zip) {
		this.zip = zip;
	}

	public String getZip4() {
		return zip4;
	}

	public void setZip4(String zip4) {
		this.zip4 = zip4;
	}

	@Override
	public String toString() {
		return "AddressInput [address1=" + address1 + ", address2=" + address2 + ", address3=" + address3 + ", city="
				+ city + ", stateCd=" + stateCd + ", countryCd=" + countryCd + ", zip=" + zip + ", zip4=" + zip4 + "]";
	}
}
