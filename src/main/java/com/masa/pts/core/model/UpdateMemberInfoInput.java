package com.masa.pts.core.model;

import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.PastOrPresent;
import javax.validation.constraints.Size;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.masa.pts.core.validator.NotValidGroupCode;
import com.masa.pts.core.validator.NotValidUpdateMemberInput;
import com.masa.pts.core.validator.ObjectLevelValidator;

import io.swagger.annotations.ApiModelProperty;

// todo need to create general class for UpdateMemberInput and CreateMemberInput because,
//  they have almost similar structure including validation
@NotValidUpdateMemberInput(groups = ObjectLevelValidator.class)
public class UpdateMemberInfoInput implements Serializable, MemberInput {

	private static final long serialVersionUID = -4284977012205626755L;
	
	@Size(min = 2, message="First name should have at least 2 chars")
	@Size(max = 50, message = "First name can't be more than 50 chars")
	@ApiModelProperty(value="First name should have at least 2 chars")
	private String firstName;	
	
	@Size(min = 2, message="Last name should have at least 2 chars")
	@Size(max = 50, message = "Last name can't be more than 50 chars")
	@ApiModelProperty(value = "Last name should have at least 2 chars")
	private String lastName;

	@Size(max = 50, message = "Middle name can't be more than 50 chars")
	private String mi;
		
	@PastOrPresent(message="Birth Date cannot be future date.")
	@ApiModelProperty(value = "date format MM-dd-yyy, cannot be future date")
	private Date birthDate;
	
	@ApiModelProperty(value= "format ************")
	private String phone;

	@ApiModelProperty(value= "format ************")
	private String cellPhone;

	@Size(max = 320, message = "Email can't be more than 320 chars")
	private String email;
	
	@ApiModelProperty(value = "if applicable first name should have at least 4 chars")
	@Size(max = 50, message = "Spouse first name can't be more than 50 chars")
	private String spouseFirst;
	
	@ApiModelProperty(value= "if applicable last name should have at least 2 chars")
	@Size(max = 50, message = "Spouse last name can't be more than 50 chars")
	private String spouseLast;

	@Size(max = 50, message = "Spouse middle name can't be more than 50 chars")
	private String spouseMi;
	
	@PastOrPresent(message="Birth Date cannot be future date.")
	@ApiModelProperty(required = true, value = "Required if Spouse First/Last name are provided.")
	private Date spouseBirthDate;

	@Size(max = 320, message = "Spouse email can't be more than 320 chars")
	private String spouseEmail;

	@ApiModelProperty(value= "format ************")
	private String spousePhone;
	
	@ApiModelProperty(value = "Employee Id of the member.")
	private String employeeId;
	
	@ApiModelProperty(value = "Group code of the member.")
	@NotBlank(message= "Member group cannot be empty.")
	@NotValidGroupCode
	private String groupCode;

	@Valid
	private Set<MemberDependantInput> dependents = new HashSet<>();
	
	@Valid
	private EmergencyContactDTO emergencyContact;

	public UpdateMemberInfoInput() { }

	public String getFirstName() {
		return firstName;
	}

	public UpdateMemberInfoInput setFirstName(String firstName) {
		this.firstName = firstName;
		return this;
	}

	public String getLastName() {
		return lastName;
	}

	public UpdateMemberInfoInput setLastName(String lastName) {
		this.lastName = lastName;
		return this;
	}

	public String getMi() {
		return mi;
	}

	public UpdateMemberInfoInput setMi(String mi) {
		this.mi = mi;
		return this;
	}

	public Date getBirthDate() {
		return birthDate;
	}

	public UpdateMemberInfoInput setBirthDate(Date birthDate) {
		this.birthDate = birthDate;
		return this;
	}

	public String getPhone() {
		return phone;
	}

	public UpdateMemberInfoInput setPhone(String phone) {
		this.phone = phone;
		return this;
	}

	public String getCellPhone() {
		return cellPhone;
	}

	public UpdateMemberInfoInput setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
		return this;
	}

	public String getEmail() {
		return email;
	}

	public UpdateMemberInfoInput setEmail(String email) {
		this.email = email;
		return this;
	}

	public String getSpouseFirst() {
		return spouseFirst;
	}

	public UpdateMemberInfoInput setSpouseFirst(String spouseFirst) {
		this.spouseFirst = spouseFirst;
		return this;
	}

	public String getSpouseLast() {
		return spouseLast;
	}

	public UpdateMemberInfoInput setSpouseLast(String spouseLast) {
		this.spouseLast = spouseLast;
		return this;
	}

	public String getSpouseMi() {
		return spouseMi;
	}

	public UpdateMemberInfoInput setSpouseMi(String spouseMi) {
		this.spouseMi = spouseMi;
		return this;
	}

	public Date getSpouseBirthDate() {
		return spouseBirthDate;
	}

	public UpdateMemberInfoInput setSpouseBirthDate(Date spouseBirthDate) {
		this.spouseBirthDate = spouseBirthDate;
		return this;
	}

	public String getSpouseEmail() {
		return spouseEmail;
	}

	public UpdateMemberInfoInput setSpouseEmail(String spouseEmail) {
		this.spouseEmail = spouseEmail;
		return this;
	}

	public String getEmployeeId() {
		return employeeId;
	}

	public UpdateMemberInfoInput setEmployeeId(String employeeId) {
		this.employeeId = employeeId;
		return this;
	}

	public Set<MemberDependantInput> getDependents() {
		return dependents;
	}

	public UpdateMemberInfoInput setDependents(Set<MemberDependantInput> dependents) {
		this.dependents = dependents;
		return this;
	}

	public String getSpousePhone() {
		return spousePhone;
	}

	public UpdateMemberInfoInput setSpousePhone(String spousePhone) {
		this.spousePhone = spousePhone;
		return this;
	}

	public String getGroupCode() {
		return groupCode;
	}

	public UpdateMemberInfoInput setGroupCode(String groupCode) {
		this.groupCode = groupCode;
		return this;
	}

	public EmergencyContactDTO getEmergencyContact() {
		return emergencyContact;
	}

	public UpdateMemberInfoInput setEmergencyContact(EmergencyContactDTO emergencyContact) {
		this.emergencyContact = emergencyContact;
		return this;
	}
}
