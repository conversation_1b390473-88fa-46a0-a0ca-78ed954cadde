package com.masa.pts.core.model;

import java.time.LocalDate;
import java.util.Date;
import java.util.stream.Collectors;


import com.masa.pts.core.domain.MemberAction;
import com.masa.pts.core.domain.MemberFile;
import com.masa.pts.core.domain.MemberFileRecordErrorEntity;
import com.masa.pts.core.domain.ReviewStatus;
import com.masa.pts.core.domain.UploadStatus;
import com.masa.pts.core.service.PTSUtilityService;

public class MemberFileDTO {

	private Integer id;
	private String groupCode;
	private String sourceFileName;
	private String relationship;
	private Date createdDate;
	private Integer masaMemberId;	
	private String contractNumber;
	private UploadStatus status;
	private ReviewStatus reviewStatus;
	private String statusUpdatedBy;
	private String errorCode;
	private String errorMessage;
	private String modification;
	private MemberAction memberAction;
	private String firstName;
	private String middleName;
	private String lastName;
	private String productType;
	private String agentCode;
	private Date effectiveDate;
	private String processComments;
	private LocalDate birthDate; 
	private String fileOwner;
	
	public MemberFileDTO() {
		super();
	}
		
	public MemberFileDTO(Integer id, String groupCode, String sourceFileName, String relationship, Date createdDate,
			Integer masaMemberId, String contractNumber, UploadStatus status, ReviewStatus reviewStatus,
			String statusUpdatedBy, String errorCode, String errorMessage, String modification, MemberAction memberAction,
			String firstName, String middleName, String lastName, String productType, String agentCode,
			Date effectiveDate, String processComments, Date birthDate, String fileOwner) {
		super();
		this.id = id;
		this.groupCode = groupCode;
		this.sourceFileName = sourceFileName;
		this.relationship = relationship;
		this.createdDate = createdDate;
		this.masaMemberId = masaMemberId;
		this.contractNumber = contractNumber;
		this.status = status;
		this.reviewStatus = reviewStatus;
		this.statusUpdatedBy = statusUpdatedBy;
		this.errorCode = errorCode;
		this.errorMessage = errorMessage;
		this.modification = modification;
		this.memberAction = memberAction;
		this.firstName = firstName;
		this.middleName = middleName;
		this.lastName = lastName;
		this.productType = productType;
		this.agentCode = agentCode;
		this.effectiveDate = effectiveDate;
		this.processComments = processComments;
		this.birthDate = PTSUtilityService.convertUtilToLocalDate(birthDate);
		this.fileOwner = fileOwner;
	}


	public MemberFileDTO(MemberFile entity) {
		this.id = entity.getId();
		this.agentCode = entity.getAgentCode();
		this.contractNumber = entity.getContractNumber();
		this.createdDate = entity.getCreatedDate();
		this.effectiveDate = entity.getEffectiveDate();
		this.errorCode = entity.getRecordErrorSet().stream().map(MemberFileRecordErrorEntity::getErrorCode)
				.collect(Collectors.joining(";"));
		this.errorMessage = entity.getRecordErrorSet().stream().map(MemberFileRecordErrorEntity::getErrorMessage)
				.collect(Collectors.joining(";"));
		this.firstName = entity.getFirstName();
		this.middleName = entity.getMiddleName();
		this.lastName = entity.getLastName();
		this.groupCode = entity.getGroupCode();
		this.masaMemberId = entity.getMasaMemberId();
		this.memberAction = entity.getMemberAction();
		this.modification = entity.getModification();
		this.processComments = entity.getProcessComments();
		this.productType = entity.getProductType();
		this.relationship = entity.getRelationship();
		this.reviewStatus = entity.getReviewStatus();
		this.sourceFileName = entity.getSourceFileName();
		this.status = entity.getStatus();
		this.statusUpdatedBy = entity.getStatusUpdatedBy();
		this.birthDate = PTSUtilityService.convertUtilToLocalDate(entity.getBirthDate());
		this.fileOwner = entity.getFileOwner();
	}
	
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public String getGroupCode() {
		return groupCode;
	}
	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}
	public String getSourceFileName() {
		return sourceFileName;
	}
	public void setSourceFileName(String sourceFileName) {
		this.sourceFileName = sourceFileName;
	}
	public String getRelationship() {
		return relationship;
	}
	public void setRelationship(String relationship) {
		this.relationship = relationship;
	}
	public Date getCreatedDate() {
		return createdDate;
	}
	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}
	public Integer getMasaMemberId() {
		return masaMemberId;
	}
	public void setMasaMemberId(Integer masaMemberId) {
		this.masaMemberId = masaMemberId;
	}	
	public String getContractNumber() {
		return contractNumber;
	}
	public void setContractNumber(String contractNumber) {
		this.contractNumber = contractNumber;
	}
	public UploadStatus getStatus() {
		return status;
	}
	public void setStatus(UploadStatus status) {
		this.status = status;
	}
	public ReviewStatus getReviewStatus() {
		return reviewStatus;
	}
	public void setReviewStatus(ReviewStatus reviewStatus) {
		this.reviewStatus = reviewStatus;
	}
	public String getStatusUpdatedBy() {
		return statusUpdatedBy;
	}
	public void setStatusUpdatedBy(String statusUpdatedBy) {
		this.statusUpdatedBy = statusUpdatedBy;
	}
	public String getErrorCode() {
		return errorCode;
	}
	public void setErrorCode(String errorCode) {
		this.errorCode = errorCode;
	}
	public String getErrorMessage() {
		return errorMessage;
	}
	public void setErrorMessage(String errorMessage) {
		this.errorMessage = errorMessage;
	}
	public String getModification() {
		return modification;
	}
	public void setModification(String modification) {
		this.modification = modification;
	}
	public MemberAction getMemberAction() {
		return memberAction;
	}
	public void setMemberAction(MemberAction memberAction) {
		this.memberAction = memberAction;
	}
	public String getFirstName() {
		return firstName;
	}
	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}
	public String getMiddleName() {
		return middleName;
	}
	public void setMiddleName(String middleName) {
		this.middleName = middleName;
	}
	public String getLastName() {
		return lastName;
	}
	public void setLastName(String lastName) {
		this.lastName = lastName;
	}
	public String getProductType() {
		return productType;
	}
	public void setProductType(String productType) {
		this.productType = productType;
	}
	public String getAgentCode() {
		return agentCode;
	}
	public void setAgentCode(String agentCode) {
		this.agentCode = agentCode;
	}
	public Date getEffectiveDate() {
		return effectiveDate;
	}
	public void setEffectiveDate(Date effectiveDate) {
		this.effectiveDate = effectiveDate;
	}
	public String getProcessComments() {
		return processComments;
	}
	public void setProcessComments(String processComments) {
		this.processComments = processComments;
	}

	public LocalDate getBirthDate() {
		return birthDate;
	}

	public void setBirthDate(LocalDate birthDate) {
		this.birthDate = birthDate;
	}

	public String getFileOwner() {
		if(fileOwner!=null) {
			return fileOwner.replace("MASAASSIST\\", "");
		} else {
			return null;
		}
	}

	public void setFileOwner(String fileOwner) {
		this.fileOwner = fileOwner;
	}
	
	
}
