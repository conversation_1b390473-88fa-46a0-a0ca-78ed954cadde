package com.masa.pts.core.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.masa.pts.core.domain.Address;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class MemberDetailsDTO {

	private Integer memberId;
	private String employeeId;
	private String firstName;
	private String lastName;
	private String mi;
	private Date birthDate;
	private String spouseFirst;
	private String spouseLast;
	private String spouseMi;
	private Date spouseBirthDate;
	private String phone;
	private String cellPhone;
	private String email;
	private Integer cancelCode=0;
	private Date cancelDate;
	private Date effectiveDate;
	private Date renewDate;
	private Date reinstateDate;
	private Integer modifiedBy;
	private Date modifiedDate;
	private Integer active;
	private Date createdDate;
	private Integer createdBy;
	private Integer soldRegion=0;
	private String alterId;
	private String spouseEmail;
	private String spousePhone;
	private Date reactiveDate;
	private Integer applicationSource = 0;
	private Address addressDetails;
	private Address alternateAddressDetails;
	private EmergencyContactDTO emergencyContact;
	
	private MemberGroupDetailsDTO group;

	private Set<DependantDto> dependants = new HashSet<>();
	
	private List<ProductSale> saleProducts = new ArrayList<>();

	public MemberDetailsDTO() {
		super();
	}
	public Integer getMemberId() {
		return memberId;
	}

	public void setMemberId(Integer memberId) {
		this.memberId = memberId;
	}

	public String getEmployeeId() {
		return employeeId;
	}

	public void setEmployeeId(String employeeId) {
		this.employeeId = employeeId;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public String getMi() {
		return mi;
	}

	public void setMi(String mi) {
		this.mi = mi;
	}

	public Date getBirthDate() {
		return birthDate;
	}

	public void setBirthDate(Date birthDate) {
		this.birthDate = birthDate;
	}

	public String getSpouseFirst() {
		return spouseFirst;
	}

	public void setSpouseFirst(String spouseFirst) {
		this.spouseFirst = spouseFirst;
	}

	public String getSpouseLast() {
		return spouseLast;
	}

	public void setSpouseLast(String spouseLast) {
		this.spouseLast = spouseLast;
	}

	public String getSpouseMi() {
		return spouseMi;
	}

	public void setSpouseMi(String spouseMi) {
		this.spouseMi = spouseMi;
	}

	public Date getSpouseBirthDate() {
		return spouseBirthDate;
	}

	public void setSpouseBirthDate(Date spouseBirthDate) {
		this.spouseBirthDate = spouseBirthDate;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getCellPhone() {
		return cellPhone;
	}

	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public Date getCancelDate() {
		return cancelDate;
	}

	public void setCancelDate(Date cancelDate) {
		this.cancelDate = cancelDate;
	}

	public Date getEffectiveDate() {
		return effectiveDate;
	}

	public void setEffectiveDate(Date effectiveDate) {
		this.effectiveDate = effectiveDate;
	}

	public Date getRenewDate() {
		return renewDate;
	}

	public void setRenewDate(Date renewDate) {
		this.renewDate = renewDate;
	}

	public Date getReinstateDate() {
		return reinstateDate;
	}

	public void setReinstateDate(Date reinstateDate) {
		this.reinstateDate = reinstateDate;
	}

	public Integer getModifiedBy() {
		return modifiedBy;
	}

	public void setModifiedBy(Integer modifiedBy) {
		this.modifiedBy = modifiedBy;
	}

	public Date getModifiedDate() {
		return modifiedDate;
	}

	public void setModifiedDate(Date modifiedDate) {
		this.modifiedDate = modifiedDate;
	}

	public Integer getActive() {
		return active;
	}

	public void setActive(Integer active) {
		this.active = active;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public Integer getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}

	public Integer getSoldRegion() {
		return soldRegion;
	}

	public void setSoldRegion(Integer soldRegion) {
		this.soldRegion = soldRegion;
	}

	public String getAlterId() {
		return alterId;
	}

	public void setAlterId(String alterId) {
		this.alterId = alterId;
	}

	public String getSpouseEmail() {
		return spouseEmail;
	}

	public void setSpouseEmail(String spouseEmail) {
		this.spouseEmail = spouseEmail;
	}

	public String getSpousePhone() {
		return spousePhone;
	}

	public void setSpousePhone(String spousePhone) {
		this.spousePhone = spousePhone;
	}

	public Date getReactiveDate() {
		return reactiveDate;
	}

	public void setReactiveDate(Date reactiveDate) {
		this.reactiveDate = reactiveDate;
	}

	public Integer getApplicationSource() {
		return applicationSource;
	}

	public void setApplicationSource(Integer applicationSource) {
		this.applicationSource = applicationSource;
	}

	public Address getAddressDetails() {
		return addressDetails;
	}

	public void setAddressDetails(Address addressDetails) {
		this.addressDetails = addressDetails;
	}

	public Address getAlternateAddressDetails() {
		return alternateAddressDetails;
	}

	public void setAlternateAddressDetails(Address alternateAddressDetails) {
		this.alternateAddressDetails = alternateAddressDetails;
	}

	public MemberGroupDetailsDTO getGroup() {
		return group;
	}
	public void setGroup(MemberGroupDetailsDTO group) {
		this.group = group;
	}
	public Set<DependantDto> getDependants() {
		return dependants;
	}

	public void setDependants(Set<DependantDto> dependants) {
		this.dependants = dependants;
	}

	public List<ProductSale> getSaleProducts() {
		return saleProducts;
	}

	public void setSaleProducts(List<ProductSale> saleProducts) {
		this.saleProducts = saleProducts;
	}
	public Integer getCancelCode() {
		return cancelCode;
	}
	public void setCancelCode(Integer cancelCode) {
		this.cancelCode = cancelCode;
	}

	public EmergencyContactDTO getEmergencyContact() {
		return emergencyContact;
	}

	public void setEmergencyContact(EmergencyContactDTO emergencyContact) {
		this.emergencyContact = emergencyContact;
	}
}
