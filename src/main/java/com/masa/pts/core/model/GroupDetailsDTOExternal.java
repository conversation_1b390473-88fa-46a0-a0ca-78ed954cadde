package com.masa.pts.core.model;

import com.masa.pts.core.domain.GroupContactEntity;
import com.masa.pts.core.domain.GroupEntity;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

public class GroupDetailsDTOExternal {

	private AddressDTO mailingAddressDetails;
	
	private String modifiedByUser;
	
	private Set<GroupContactEntity> contacts;
	
	private Integer groupId;
	
	private String groupCode;

	private String groupName;

	private boolean active;
	
	private List<ProductSale> saleProducts = new ArrayList<>();

	private boolean canDeactivate;

	private Integer membershipSource;
	private Integer brokerPlatform;
	private Integer paymentTerm;
	private boolean autoSuspendCancelMember;

	public GroupDetailsDTOExternal() {
		super();
	}
	
	public GroupDetailsDTOExternal(GroupEntity entity, boolean canDeactivate) {
		mailingAddressDetails = new AddressDTO(entity.getMailingAddressDetails());
		contacts = entity.getContacts();
		modifiedByUser = entity.getUserWhoModified() !=null ? entity.getUserWhoModified().getUsername() : null;
		groupCode = entity.getGroupCode();
		groupName = entity.getGroupName();
		active = entity.isActive();
		saleProducts = entity.getSaleProducts();
		this.canDeactivate = canDeactivate;
		membershipSource = entity.getMembershipSource();
		brokerPlatform = entity.getBrokerPlatforms();
		paymentTerm = entity.getPaymentTerms();
		autoSuspendCancelMember = entity.isAutoSuspendCancelMember();
		
	}

	public AddressDTO getMailingAddressDetails() {
		return mailingAddressDetails;
	}

	public void setMailingAddressDetails(AddressDTO mailingAddressDetails) {
		this.mailingAddressDetails = mailingAddressDetails;
	}

	public String getModifiedByUser() {
		return modifiedByUser;
	}

	public void setModifiedByUser(String modifiedByUser) {
		this.modifiedByUser = modifiedByUser;
	}

	public Set<GroupContactEntity> getContacts() {
		return contacts;
	}

	public void setContacts(Set<GroupContactEntity> contacts) {
		this.contacts = contacts;
	}

	public Integer getGroupId() {
		return groupId;
	}

	public void setGroupId(Integer groupId) {
		this.groupId = groupId;
	}

	public String getGroupCode() {
		return groupCode;
	}

	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}

	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

	public boolean isActive() {
		return active;
	}

	public void setActive(boolean active) {
		this.active = active;
	}

	public List<ProductSale> getSaleProducts() {
		return saleProducts;
	}

	public void setSaleProducts(List<ProductSale> saleProducts) {
		this.saleProducts = saleProducts;
	}

	public boolean isCanDeactivate() {
		return canDeactivate;
	}

	public void setCanDeactivate(boolean canDeactivate) {
		this.canDeactivate = canDeactivate;
	}

	public Integer getMembershipSource() {
		return membershipSource;
	}

	public void setMembershipSource(Integer membershipSource) {
		this.membershipSource = membershipSource;
	}

	public Integer getBrokerPlatform() {
		return brokerPlatform;
	}

	public void setBrokerPlatform(Integer brokerPlatform) {
		this.brokerPlatform = brokerPlatform;
	}

	public Integer getPaymentTerm() {
		return paymentTerm;
	}

	public void setPaymentTerm(Integer paymentTerm) {
		this.paymentTerm = paymentTerm;
	}

	public boolean isAutoSuspendCancelMember() {
		return autoSuspendCancelMember;
	}

	public void setAutoSuspendCancelMember(boolean autoSuspendCancelMember) {
		this.autoSuspendCancelMember = autoSuspendCancelMember;
	}
}
