package com.masa.pts.core.model;

import java.io.Serializable;
import java.util.Date;

public class MemberCancelSetup implements Serializable {

	private static final long serialVersionUID = -8222739525104136672L;
	
	private Integer memberId;
	private Date cancelDate;
	private Integer cancelUser;
	private String cancelReasonCode;
	private Integer cancelReasonId;
	private boolean chargebackComm=true;
	private String memberNote;
	private String applicationSource;
	
	private boolean error=false;
	private String errorMessage;
	
	public MemberCancelSetup() {
		super();
	}
	public Integer getMemberId() {
		return memberId;
	}
	public void setMemberId(Integer memberId) {
		this.memberId = memberId;
	}
	public Date getCancelDate() {
		return cancelDate;
	}
	public void setCancelDate(Date cancelDate) {
		this.cancelDate = cancelDate;
	}
	public Integer getCancelUser() {
		return cancelUser;
	}
	public void setCancelUser(Integer cancelUser) {
		this.cancelUser = cancelUser;
	}
	
	public String getCancelReasonCode() {
		return cancelReasonCode;
	}
	public void setCancelReasonCode(String cancelReasonCode) {
		this.cancelReasonCode = cancelReasonCode;
	}
	public Integer getCancelReasonId() {
		return cancelReasonId;
	}
	public void setCancelReasonId(Integer cancelReasonId) {
		this.cancelReasonId = cancelReasonId;
	}
	public boolean isChargebackComm() {
		return chargebackComm;
	}
	public void setChargebackComm(boolean chargebackComm) {
		this.chargebackComm = chargebackComm;
	}
	public boolean isError() {
		return error;
	}
	public void setError(boolean error) {
		this.error = error;
	}
	public String getErrorMessage() {
		return errorMessage;
	}
	public void setErrorMessage(String errorMessage) {
		this.errorMessage = errorMessage;
	}
	public String getMemberNote() {
		return memberNote;
	}
	public void setMemberNote(String memberNote) {
		this.memberNote = memberNote;
	}
	public String getApplicationSource() {
		return applicationSource;
	}
	public void setApplicationSource(String applicationSource) {
		this.applicationSource = applicationSource;
	}	
}
