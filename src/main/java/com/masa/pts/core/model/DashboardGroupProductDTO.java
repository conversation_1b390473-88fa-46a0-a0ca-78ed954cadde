package com.masa.pts.core.model;

public class DashboardGroupProductDTO {

	private Integer groupId;
	private String groupName;
	private String productName;
	private Long memberCount;
	
	public DashboardGroupProductDTO(Integer groupId, String groupName, String productName, Long memberCount) {
		super();
		this.groupId = groupId;
		this.groupName = groupName;
		this.productName = productName;
		this.memberCount = memberCount;
	}

	public Integer getGroupId() {
		return groupId;
	}

	public void setGroupId(Integer groupId) {
		this.groupId = groupId;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public Long getMemberCount() {
		return memberCount;
	}

	public void setMemberCount(Long memberCount) {
		this.memberCount = memberCount;
	}

	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}			
}
