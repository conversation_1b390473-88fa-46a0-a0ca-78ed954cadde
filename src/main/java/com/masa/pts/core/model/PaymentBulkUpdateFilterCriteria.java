package com.masa.pts.core.model;

import com.masa.pts.core.domain.ReviewStatus;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
import java.util.Objects;

public class PaymentBulkUpdateFilterCriteria {

    @NotNull(message = "New review status should be provided.")
    private ReviewStatus newReviewStatus;
    private String groupCode;
    private Integer memberId;
    private String employerId;
    private List<PaymentPostStatus> status;
    private List<ReviewStatus> reviewStatus;
    private String fileName;
    private Date createdDateStart;
    private Date createdDateEnd;

    public ReviewStatus getNewReviewStatus() {
        return newReviewStatus;
    }

    public void setNewReviewStatus(ReviewStatus newReviewStatus) {
        this.newReviewStatus = newReviewStatus;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    public Integer getMemberId() {
        return memberId;
    }

    public void setMemberId(Integer memberId) {
        this.memberId = memberId;
    }

    public String getEmployerId() {
        return employerId;
    }

    public void setEmployerId(String employerId) {
        this.employerId = employerId;
    }

    public List<PaymentPostStatus> getStatus() {
        return status;
    }

    public void setStatus(List<PaymentPostStatus> status) {
        this.status = status;
    }

    public List<ReviewStatus> getReviewStatus() {
        return reviewStatus;
    }

    public void setReviewStatus(List<ReviewStatus> reviewStatus) {
        this.reviewStatus = reviewStatus;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public Date getCreatedDateStart() {
        return createdDateStart;
    }

    public void setCreatedDateStart(Date createdDateStart) {
        this.createdDateStart = createdDateStart;
    }

    public Date getCreatedDateEnd() {
        return createdDateEnd;
    }

    public void setCreatedDateEnd(Date createdDateEnd) {
        this.createdDateEnd = createdDateEnd;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof PaymentBulkUpdateFilterCriteria)) return false;
        PaymentBulkUpdateFilterCriteria that = (PaymentBulkUpdateFilterCriteria) o;
        return newReviewStatus == that.newReviewStatus &&
                Objects.equals(groupCode, that.groupCode) &&
                Objects.equals(memberId, that.memberId) &&
                Objects.equals(employerId, that.employerId) &&
                Objects.equals(status, that.status) &&
                Objects.equals(reviewStatus, that.reviewStatus) &&
                Objects.equals(fileName, that.fileName) &&
                Objects.equals(createdDateStart, that.createdDateStart) &&
                Objects.equals(createdDateEnd, that.createdDateEnd);
    }

    @Override
    public int hashCode() {
        return Objects.hash(newReviewStatus, groupCode, memberId, employerId, status, reviewStatus, fileName,
                createdDateStart, createdDateEnd);
    }
}
