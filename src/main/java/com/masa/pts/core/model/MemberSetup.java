package com.masa.pts.core.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import com.masa.pts.core.domain.Address;
import com.masa.pts.core.domain.Dependant;
import com.masa.pts.core.domain.Member;

public class MemberSetup implements Serializable {

	private static final long serialVersionUID = 5484968734571998365L;
	
	Member member;
	Integer agentId;
	String agentNum;
	Integer groupId;
	String groupCd;
	Integer productId;
	String productName;
	private Set<Integer> upgradeProductIds = new HashSet<>();
	Date soldDate;
	Address mailingAddress = new Address();
	Address benefitAddress = new Address();
	private Set<Dependant> dependantSet = new HashSet<>();
	private Set<MemberSetupProduct> productSet = new HashSet<>();
	private Set<String> notes = new HashSet<>();
	private boolean fulfillment=false;
	private BigDecimal paymentAmount=BigDecimal.ZERO;
	private Integer freeTrial =0;
	
	String errorMessage;
	
	private boolean success=false;
	private Set<String> error = new HashSet<>();
	
	public MemberSetup() {
		super();
	}
	public Member getMember() {
		return member;
	}
	public void setMember(Member member) {
		this.member = member;
	}
	public Integer getAgentId() {
		return agentId;
	}
	public void setAgentId(Integer agentId) {
		this.agentId = agentId;
	}
	public String getAgentNum() {
		return agentNum;
	}
	public void setAgentNum(String agentNum) {
		this.agentNum = agentNum;
	}
	public Integer getGroupId() {
		return groupId;
	}
	public void setGroupId(Integer groupId) {
		this.groupId = groupId;
	}
	public String getGroupCd() {
		return groupCd;
	}
	public void setGroupCd(String groupCd) {
		this.groupCd = groupCd;
	}
	public Integer getProductId() {
		return productId;
	}
	public void setProductId(Integer productId) {
		this.productId = productId;
	}
	public String getProductName() {
		return productName;
	}
	public void setProductName(String productName) {
		this.productName = productName;
	}
	public Set<Integer> getUpgradeProductIds() {
		return upgradeProductIds;
	}
	public void setUpgradeProductIds(Set<Integer> upgradeProductIds) {
		this.upgradeProductIds = upgradeProductIds;
	}
	public Date getSoldDate() {
		return soldDate;
	}
	public void setSoldDate(Date soldDate) {
		this.soldDate = soldDate;
	}
	public String getErrorMessage() {
		return errorMessage;
	}
	public void setErrorMessage(String errorMessage) {
		this.errorMessage = errorMessage;
	}
	public Address getMailingAddress() {
		return mailingAddress;
	}
	public void setMailingAddress(Address mailingAddress) {
		this.mailingAddress = mailingAddress;
	}
	public Address getBenefitAddress() {
		return benefitAddress;
	}
	public void setBenefitAddress(Address benefitAddress) {
		this.benefitAddress = benefitAddress;
	}
	public Set<Dependant> getDependantSet() {
		return dependantSet;
	}
	public void setDependantSet(Set<Dependant> dependantSet) {
		this.dependantSet = dependantSet;
	}	
	public void addDependant(Dependant dependant) {
		this.dependantSet.add(dependant);
	}
	public Set<MemberSetupProduct> getProductSet() {
		return productSet;
	}
	public void setProductSet(Set<MemberSetupProduct> productSet) {
		this.productSet = productSet;
	}
	public void addProduct(MemberSetupProduct product) {
		this.productSet.add(product);
	}
	public Set<String> getNotes() {
		return notes;
	}
	public void setNotes(Set<String> notes) {
		this.notes = notes;
	}
	public void addNote(String note) {
		this.notes.add(note);
	}
	public BigDecimal getPaymentAmount() {
		return paymentAmount;
	}
	public void setPaymentAmount(BigDecimal paymentAmount) {
		this.paymentAmount = paymentAmount;
	}
	public Set<String> getError() {
		return error;
	}
	public void setError(Set<String> error) {
		this.error = error;
	}
	public void addError(String error) {
		this.error.add(error);
	}
	public boolean isSuccess() {
		return success;
	}
	public void setSuccess(boolean success) {
		this.success = success;
	}	
	public boolean isFulfillment() {
		return fulfillment;
	}

	public void setFulfillment(boolean fulfillment) {
		this.fulfillment = fulfillment;
	}
	public Integer getFreeTrial() {
		return freeTrial;
	}
	public void setFreeTrial(Integer freeTrial) {
		this.freeTrial = freeTrial;
	}	
	
}
