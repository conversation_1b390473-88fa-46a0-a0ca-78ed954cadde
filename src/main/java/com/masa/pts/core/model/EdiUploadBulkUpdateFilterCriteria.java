package com.masa.pts.core.model;

import com.masa.pts.core.domain.ReviewStatus;
import com.masa.pts.core.domain.UploadStatus;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
import java.util.Objects;

public class EdiUploadBulkUpdateFilterCriteria {
    @NotNull(message = "New review status should be provided.")
    private ReviewStatus newReviewStatus;
    private String groupCode;
    private Integer masaMemberId;
    private String contractNumber;
    private List<UploadStatus> status;
    private List<ReviewStatus> reviewStatus;
    private String sourceFileName;
    private String relationship;
    private Date createdDateStart;
    private Date createdDateEnd;
    private List<String> errorCode;

    public ReviewStatus getNewReviewStatus() {
        return newReviewStatus;
    }

    public void setNewReviewStatus(ReviewStatus newReviewStatus) {
        this.newReviewStatus = newReviewStatus;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    public Integer getMasaMemberId() {
        return masaMemberId;
    }

    public void setMasaMemberId(Integer masaMemberId) {
        this.masaMemberId = masaMemberId;
    }

    public String getContractNumber() {
        return contractNumber;
    }

    public void setContractNumber(String contractNumber) {
        this.contractNumber = contractNumber;
    }

    public List<UploadStatus> getStatus() {
        return status;
    }

    public void setStatus(List<UploadStatus> status) {
        this.status = status;
    }

    public List<ReviewStatus> getReviewStatus() {
        return reviewStatus;
    }

    public void setReviewStatus(List<ReviewStatus> reviewStatus) {
        this.reviewStatus = reviewStatus;
    }

    public String getSourceFileName() {
        return sourceFileName;
    }

    public void setSourceFileName(String sourceFileName) {
        this.sourceFileName = sourceFileName;
    }

    public String getRelationship() {
        return relationship;
    }

    public void setRelationship(String relationship) {
        this.relationship = relationship;
    }

    public Date getCreatedDateStart() {
        return createdDateStart;
    }

    public void setCreatedDateStart(Date createdDateStart) {
        this.createdDateStart = createdDateStart;
    }

    public Date getCreatedDateEnd() {
        return createdDateEnd;
    }

    public void setCreatedDateEnd(Date createdDateEnd) {
        this.createdDateEnd = createdDateEnd;
    }

    public List<String> getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(List<String> errorCode) {
        this.errorCode = errorCode;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof EdiUploadBulkUpdateFilterCriteria)) return false;
        EdiUploadBulkUpdateFilterCriteria that = (EdiUploadBulkUpdateFilterCriteria) o;
        return newReviewStatus == that.newReviewStatus &&
                Objects.equals(groupCode, that.groupCode) &&
                Objects.equals(masaMemberId, that.masaMemberId) &&
                Objects.equals(contractNumber, that.contractNumber) &&
                Objects.equals(status, that.status) &&
                Objects.equals(reviewStatus, that.reviewStatus) &&
                Objects.equals(sourceFileName, that.sourceFileName) &&
                Objects.equals(relationship, that.relationship) &&
                Objects.equals(createdDateStart, that.createdDateStart) &&
                Objects.equals(createdDateEnd, that.createdDateEnd) &&
                Objects.equals(errorCode, that.errorCode);
    }

    @Override
    public int hashCode() {
        return Objects
                .hash(newReviewStatus, groupCode, masaMemberId, contractNumber, status, reviewStatus, sourceFileName,
                        relationship, createdDateStart, createdDateEnd, errorCode);
    }
}
