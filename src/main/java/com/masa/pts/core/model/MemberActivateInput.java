package com.masa.pts.core.model;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;

public class MemberActivateInput implements Serializable {

	private static final long serialVersionUID = -5959521364302892572L;

	@ApiModelProperty(value = "If empty set to current date.")
    private Date activationDate;

	@ApiModelProperty(required = true,value = "${swagger.member.applicationSource.values}")
	private String applicationSource="PTS";

	public MemberActivateInput() {
		super();
	}

	public Date getActivationDate() {
		return activationDate;
	}

	public void setActivationDate(Date activationDate) {
		this.activationDate = activationDate;
	}

	public String getApplicationSource() {
		return applicationSource;
	}

	public void setApplicationSource(String applicationSource) {
		this.applicationSource = applicationSource;
	}
}
