package com.masa.pts.core.model;

import java.util.Objects;

public class GroupReducedView {

    public GroupReducedView(Integer groupId, String groupCode, String groupName) {
        this.groupId = groupId;
        this.groupCode = groupCode;
        this.groupName = groupName;
    }

    private Integer groupId;

    private String groupCode;

    private String groupName;

    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        GroupReducedView that = (GroupReducedView) o;
        return groupId.equals(that.groupId) &&
                groupCode.equals(that.groupCode) &&
                groupName.equals(that.groupName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(groupId, groupCode, groupName);
    }
}
