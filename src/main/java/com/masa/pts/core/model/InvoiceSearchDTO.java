package com.masa.pts.core.model;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

public class InvoiceSearchDTO {

	private Integer invoiceId;
	
	private String groupCode;
	
	private String groupName;
	
	private Integer invoiceType;
	
	private Double totalDue = 0.0;
	
	private Date invoiceDate;
	
	private boolean isPaid;
	
	private Date createdDate;
	
	private Date modifiedDate;	
	
	public InvoiceSearchDTO(Integer invoiceId, String groupCode, String groupName, Integer invoiceType,
			Double totalDue, Date invoiceDate, boolean isPaid, Date createdDate, Date modifiedDate) {
		super();
		this.invoiceId = invoiceId;
		this.groupCode = groupCode;
		this.groupName = groupName;
		this.invoiceType = invoiceType;
		this.totalDue = totalDue;
		this.invoiceDate = invoiceDate;
		this.isPaid = isPaid;
		this.createdDate = createdDate;
		this.modifiedDate = modifiedDate;
	}

	public InvoiceSearchDTO() {
		super();
	}

	public Integer getInvoiceId() {
		return invoiceId;
	}

	public void setInvoiceId(Integer invoiceId) {
		this.invoiceId = invoiceId;
	}

	public String getGroupCode() {
		return groupCode;
	}

	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}

	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

	public Integer getInvoiceType() {
		return invoiceType;
	}

	public void setInvoiceType(Integer invoiceType) {
		this.invoiceType = invoiceType;
	}	

	public Double getTotalDue() {
		return totalDue;
	}

	public void setTotalDue(Double totalDue) {
		this.totalDue = totalDue;
	}

	public Date getInvoiceDate() {
		return invoiceDate;
	}

	public void setInvoiceDate(Date invoiceDate) {
		this.invoiceDate = invoiceDate;
	}

	public boolean isPaid() {
		return isPaid;
	}

	public void setPaid(boolean isPaid) {
		this.isPaid = isPaid;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public Date getModifiedDate() {
		return modifiedDate;
	}

	public void setModifiedDate(Date modifiedDate) {
		this.modifiedDate = modifiedDate;
	}
}
