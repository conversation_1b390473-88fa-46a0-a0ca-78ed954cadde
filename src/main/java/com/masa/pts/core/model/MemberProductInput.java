package com.masa.pts.core.model;

import java.io.Serializable;
import java.util.Date;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import javax.validation.constraints.PositiveOrZero;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.masa.pts.core.validator.NotValidAgentNum;
import com.masa.pts.core.validator.NotValidProductId;

import io.swagger.annotations.ApiModelProperty;

public class MemberProductInput implements Serializable {

	private static final long serialVersionUID = 1251468470909339707L;

	@NotNull(message="Product ID is required")
	@Positive(message="Product ID is required")
	@NotValidProductId(message="Valid Product ID is required")
	@ApiModelProperty(required = true, value = "Valid Product ID is required")
	private Integer productId;	
	private String productName;	
	private String productCode;
	
	@ApiModelProperty(value="if provided used as override to group product setup amount")
	private Double payAmount=0.0;
	
	@ApiModelProperty(value="if provided used as override to group product setup amount")
	private Double maxDueAmount=0.0;
	
	@ApiModelProperty(value="if provided used as override to group product setup amount")
	private Double setupFeePayAmount=0.0;
	
	@ApiModelProperty(value="if provided used as override to group product setup amount")
	private Double setupFeeMaxDueAmount=0.0;	
	
	@NotNull(message="Product Upgrade Type is required")
	@PositiveOrZero(message="Product Upgrade Type is required")
	@ApiModelProperty(required=true, value="possible values 0-7")
	private Integer productUpgradeType=0;
	
	// @NotBlank(message="Member Agent Code is required")
	@ApiModelProperty(required=true, value="Valid Agent Num is required")
	@NotValidAgentNum()
	private String agentNum;
	
	@ApiModelProperty(value="If empty, current date used as sold date")
	private Date soldDate;
	
	
	@ApiModelProperty(value="If set, setup fee and premium amount provided are used instead of group setup values.")
	private boolean overrideFee = Boolean.FALSE;
	
	private Integer freeTrial =0;
	
	public MemberProductInput() {
		super();
	}
	
	public Integer getProductId() {
		return productId;
	}
	public void setProductId(Integer productId) {
		this.productId = productId;
	}
	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}
	public Double getPayAmount() {
		return payAmount;
	}
	public void setPayAmount(Double payAmount) {
		this.payAmount = payAmount;
	}
	public Date getSoldDate() {
		return soldDate;
	}
	public void setSoldDate(Date soldDate) {
		this.soldDate = soldDate;
	}

	public Double getMaxDueAmount() {
		return maxDueAmount;
	}

	public void setMaxDueAmount(Double maxDueAmount) {
		this.maxDueAmount = maxDueAmount;
	}

	public Integer getProductUpgradeType() {
		return productUpgradeType;
	}

	public void setProductUpgradeType(Integer productUpgradeType) {
		this.productUpgradeType = productUpgradeType;
	}

	public String getAgentNum() {
		return agentNum;
	}

	public void setAgentNum(String agentNum) {
		this.agentNum = agentNum;
	}
	
	public String getProductCode() {
		return productCode;
	}

	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}

	public Double getSetupFeePayAmount() {
		return setupFeePayAmount;
	}

	public void setSetupFeePayAmount(Double setupFeePayAmount) {
		this.setupFeePayAmount = setupFeePayAmount;
	}

	public Double getSetupFeeMaxDueAmount() {
		return setupFeeMaxDueAmount;
	}

	public void setSetupFeeMaxDueAmount(Double setupFeeMaxDueAmount) {
		this.setupFeeMaxDueAmount = setupFeeMaxDueAmount;
	}

	public boolean isOverrideFee() {
		return overrideFee;
	}

	public void setOverrideFee(boolean overrideFee) {
		this.overrideFee = overrideFee;
	}

	public Integer getFreeTrial() {
		return freeTrial;
	}

	public void setFreeTrial(Integer freeTrial) {
		this.freeTrial = freeTrial;
	}

	@Override
	public String toString() {
		return "MemberProductInput [productId=" + productId + ", productName=" + productName + ", productCode="
				+ productCode + ", payAmount=" + payAmount + ", maxDueAmount=" + maxDueAmount + ", setupFeePayAmount="
				+ setupFeePayAmount + ", setupFeeMaxDueAmount=" + setupFeeMaxDueAmount + ", productUpgradeType="
				+ productUpgradeType + ", agentNum=" + agentNum + ", soldDate=" + soldDate + ", overrideFee="
				+ overrideFee + ",freeTrial=" + freeTrial +"]";
	}
}
