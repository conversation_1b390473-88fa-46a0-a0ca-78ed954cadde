package com.masa.pts.core.model;

//need this mapping for operation, because hibernate didn't know how generate sorting fields
// which not a part of entity
public enum MemberDocumentSort {

    id("id"),
    type("documentType"),
    name("documentName"),
    documentId("documentId"),
    uploadedBy("createdBy"),
    uploadedDate("createdDate");

    private String sortField;

    MemberDocumentSort(String sortField) {
        this.sortField = sortField;
    }

    public String getSortField() {
        return sortField;
    }
}
