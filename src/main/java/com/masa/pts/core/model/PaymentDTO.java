package com.masa.pts.core.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDate;
import java.util.Date;
import java.util.Objects;

import static com.masa.pts.core.service.PTSUtilityService.convertUtilToLocalDate;

public class PaymentDTO {

    private final Integer paymentDetailsId;
    private final LocalDate payDate;
    private final LocalDate createDate;
    private final LocalDate invoiceDate;
    private final String productName;
    private final Double amountPaid;
    private final String username;
    private final boolean isVoid;
    private final boolean isChargeback;
    private final boolean isWaived;
    private final CommissionType commissionType;

    public PaymentDTO(Integer paymentDetailsId, Date payDate, Date createDate, Date invoiceDate,
                      Date invoiceDetailsDate, String productName, Double amountPaid, String username,
                      boolean isVoid, boolean isChargeback, boolean payMasa, boolean isNew) {
        this.paymentDetailsId = paymentDetailsId;
        this.payDate = convertUtilToLocalDate(payDate);
        this.createDate = convertUtilToLocalDate(createDate);
        this.invoiceDate = invoiceDetailsDate != null
                ? convertUtilToLocalDate(invoiceDetailsDate)
                : convertUtilToLocalDate(invoiceDate);
        this.productName = productName;
        this.amountPaid = amountPaid;
        this.username = username;
        this.isVoid = isVoid;
        this.isChargeback = isChargeback;
        this.isWaived = payMasa;
        this.commissionType = isNew ? CommissionType.NEW : CommissionType.RENEW;
    }

    public Integer getPaymentDetailsId() {
        return paymentDetailsId;
    }

    public LocalDate getPayDate() {
        return payDate;
    }

    public LocalDate getCreateDate() {
        return createDate;
    }

    public LocalDate getInvoiceDate() {
        return invoiceDate;
    }

    public String getProductName() {
        return productName;
    }

    public Double getAmountPaid() {
        return amountPaid;
    }

    public String getUsername() {
        return username;
    }

    @JsonProperty(value="isVoid")
    public boolean isVoid() {
        return isVoid;
    }

    @JsonProperty(value="isChargeback")
    public boolean isChargeback() {
        return isChargeback;
    }

    @JsonProperty(value="isWaived")
    public boolean isWaived() {
        return isWaived;
    }

    public CommissionType getCommissionType() {
        return commissionType;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof PaymentDTO)) return false;
        PaymentDTO that = (PaymentDTO) o;
        return isVoid == that.isVoid &&
                isChargeback == that.isChargeback &&
                isWaived == that.isWaived &&
                Objects.equals(paymentDetailsId, that.paymentDetailsId) &&
                Objects.equals(payDate, that.payDate) &&
                Objects.equals(createDate, that.createDate) &&
                Objects.equals(invoiceDate, that.invoiceDate) &&
                Objects.equals(productName, that.productName) &&
                Objects.equals(amountPaid, that.amountPaid) &&
                Objects.equals(username, that.username) &&
                commissionType == that.commissionType;
    }

    @Override
    public int hashCode() {
        return Objects
                .hash(paymentDetailsId, payDate, createDate, invoiceDate, productName, amountPaid, username, isVoid,
                        isChargeback, isWaived, commissionType);
    }

    @Override
    public String toString() {
        return "PaymentDTO{" +
                "paymentDetailsId=" + paymentDetailsId +
                ", payDate=" + payDate +
                ", createDate=" + createDate +
                ", invoiceDate=" + invoiceDate +
                ", productName='" + productName + '\'' +
                ", amountPaid=" + amountPaid +
                ", username='" + username + '\'' +
                ", isVoid=" + isVoid +
                ", isChargeback=" + isChargeback +
                ", isWaived=" + isWaived +
                ", commissionType=" + commissionType +
                '}';
    }
}
