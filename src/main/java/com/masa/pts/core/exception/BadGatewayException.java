package com.masa.pts.core.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(code = HttpStatus.BAD_GATEWAY, reason = "Bad Gateway")
public class BadGatewayException extends RuntimeException {
    
    private static final long serialVersionUID = 2127274269329973695L;

    public BadGatewayException(String message) {
        super(message);
    }
}
