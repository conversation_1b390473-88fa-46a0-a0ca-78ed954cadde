package com.masa.pts.core.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.NOT_FOUND)
public class MemberNotFoundException extends RuntimeException {

	private static final long serialVersionUID = -6868924627961701585L;

	public MemberNotFoundException(Integer memberId) {
		super("Member not found "+ memberId);
	}
	public MemberNotFoundException(String memberId) {
		super("Member not found "+ memberId);
	}
}
