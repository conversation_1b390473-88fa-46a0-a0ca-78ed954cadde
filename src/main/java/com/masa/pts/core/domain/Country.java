package com.masa.pts.core.domain;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.fasterxml.jackson.annotation.JsonView;
import com.masa.pts.core.model.TPAPortalView;

@Entity
@Table(name="TLST_COUNTRY2",schema = "dbo")
public class Country implements Serializable {

	private static final long serialVersionUID = -7738558914115975257L;
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="country_id")
	private Integer countryId;	
	
	@JsonView({MobileView.Member.class,TPAPortalView.Group.class})
	@Column(name="country")
	private String name;
	
	@Transient
	private Set<State> states = new HashSet<>();
	
	public Country() {
		super();
	}	
	public Integer getCountryId() {
		return countryId;
	}
	public void setCountryId(Integer countryId) {
		this.countryId = countryId;
	}	
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public Set<State> getStates() {
		return states;
	}
	public void setStates(Set<State> states) {
		this.states = states;
	}	
	public interface CountryInfo {
		Integer getCountryId();
		String getName();
	}
}
