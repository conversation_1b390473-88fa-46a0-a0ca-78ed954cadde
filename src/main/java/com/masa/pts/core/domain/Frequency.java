package com.masa.pts.core.domain;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonView;

@Entity
@Table(name="TLST_FREQUENCY",schema = "dbo")
public class Frequency implements Serializable {

	private static final long serialVersionUID = -651116314796874376L;
	
	@JsonView(MobileView.Member.class)
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="id")
	private Integer ID;
	
	@JsonView(MobileView.Member.class)
	private String description;
	
	@JsonView(MobileView.Member.class)
	private String frequencyType;
	private Double yearParts;
	public Frequency() {
		super();
	}
	public Frequency(Integer iD, String description, String frequencyType, Double yearParts) {
		super();
		ID = iD;
		this.description = description;
		this.frequencyType = frequencyType;
		this.yearParts = yearParts;
	}
	public Integer getID() {
		return ID;
	}
	public void setID(Integer iD) {
		ID = iD;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public String getFrequencyType() {
		return frequencyType;
	}
	public void setFrequencyType(String frequencyType) {
		this.frequencyType = frequencyType;
	}
	public Double getYearParts() {
		return yearParts;
	}
	public void setYearParts(Double yearParts) {
		this.yearParts = yearParts;
	}
	@Override
	public String toString() {
		return "Frequency [ID=" + ID + ", description=" + description + ", frequencyType=" + frequencyType + ", yearParts=" + yearParts + "]";
	}	
}
