package com.masa.pts.core.domain;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 *
 */
@Entity
@Table(name = "TLNK_EMPLOYEE_GROUP", schema = "dbo")
public class PTSUserGroup implements Serializable {

	private static final long serialVersionUID = -7456359290089491909L;

	@EmbeddedId
	private PTSUSerGroupId userGroupId;
		
	@Column(name = "group_code")
	private String groupCode;
	
	/*
	@Formula("(SELECT TDAT_GROUP.GROUP_NAME FROM TDAT_GROUP WHERE TDAT_GROUP.GROUP_ID = GROUP_ID)")
	private String groupName;
	*/
	
	public PTSUserGroup() {
		super();
	}
	public String getGroupCode() {
		return groupCode;
	}
	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}
	public PTSUSerGroupId getUserGroupId() {
		return userGroupId;
	}
	public void setUserGroupId(PTSUSerGroupId userGroupId) {
		this.userGroupId = userGroupId;
	}	
}
