package com.masa.pts.core.domain;

import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

@Entity
@Table(name="TDAT_PAYMENT",schema = "dbo")
public class Payment implements Serializable {

	private static final long serialVersionUID = -894295973253675630L;

	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="pay_id")
	private Integer payId;
	
	private Double totalPaid;
	private Double adjustEntry;
	@Temporal(TemporalType.DATE	)
	private Date payDate;
	private String note;
	private Boolean cancel;
	private Date cancelDate;
	private Integer employeeId;
	private String orderId;
	private Date createDate;
	@Temporal(TemporalType.DATE	)
	private Date depositDate;
	@Temporal(TemporalType.DATE	)
	private Date receiveDate;
	private String checkNumber;
	private Double checkAmount;
	private Boolean payMasa=Boolean.FALSE;
	
	private Integer modifiedBy;
	private Date modifiedDate;
	
	@Temporal(TemporalType.DATE)
	@Column(name = "pay_inv_date")
	private Date payInvoiceDate;
	
	@OneToMany(fetch=FetchType.EAGER,cascade = CascadeType.ALL,mappedBy = "payment", orphanRemoval = true)
	//@Fetch(FetchMode.SUBSELECT)
	private Set<PaymentDetail> paymentDetail = new HashSet<>();
	
	public Payment() {
		super();
	}
	public Payment(Integer payId, Double totalPaid, Double adjustEntry, Date payDate, String note, Boolean cancel, Date cancelDate,
			Integer employeeId, String orderId, Date createDate, Date depositDate, Date receiveDate, String checkNumber, Double checkAmount) {
		super();
		this.payId = payId;
		this.totalPaid = totalPaid;
		this.adjustEntry = adjustEntry;
		this.payDate = payDate;
		this.note = note;
		this.cancel = cancel;
		this.cancelDate = cancelDate;
		this.employeeId = employeeId;
		this.orderId = orderId;
		this.createDate = createDate;
		this.depositDate = depositDate;
		this.receiveDate = receiveDate;
		this.checkNumber = checkNumber;
		this.checkAmount = checkAmount;
	}
	public Integer getPayId() {
		return payId;
	}
	public void setPayId(Integer payId) {
		this.payId = payId;
	}
	public Double getTotalPaid() {
		return totalPaid;
	}
	public void setTotalPaid(Double totalPaid) {
		this.totalPaid = totalPaid;
	}
	public Double getAdjustEntry() {
		return adjustEntry;
	}
	public void setAdjustEntry(Double adjustEntry) {
		this.adjustEntry = adjustEntry;
	}
	public Date getPayDate() {
		return payDate;
	}
	public void setPayDate(Date payDate) {
		this.payDate = payDate;
	}
	public String getNote() {
		return note;
	}
	public void setNote(String note) {
		this.note = note;
	}
	public Boolean getCancel() {
		return cancel;
	}
	public void setCancel(Boolean cancel) {
		this.cancel = cancel;
	}
	public Date getCancelDate() {
		return cancelDate;
	}
	public void setCancelDate(Date cancelDate) {
		this.cancelDate = cancelDate;
	}
	public Integer getEmployeeId() {
		return employeeId;
	}
	public void setEmployeeId(Integer employeeId) {
		this.employeeId = employeeId;
	}
	public String getOrderId() {
		return orderId;
	}
	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}
	public Date getCreateDate() {
		return createDate;
	}
	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	public Date getDepositDate() {
		return depositDate;
	}
	public void setDepositDate(Date depositDate) {
		this.depositDate = depositDate;
	}
	public Date getReceiveDate() {
		return receiveDate;
	}
	public void setReceiveDate(Date receiveDate) {
		this.receiveDate = receiveDate;
	}
	public String getCheckNumber() {
		return checkNumber;
	}
	public void setCheckNumber(String checkNumber) {
		this.checkNumber = checkNumber;
	}
	public Double getCheckAmount() {
		return checkAmount;
	}
	public void setCheckAmount(Double checkAmount) {
		this.checkAmount = checkAmount;
	}
	public Set<PaymentDetail> getPaymentDetail() {
		return paymentDetail;
	}
	public void setPaymentDetail(Set<PaymentDetail> paymentDetail) {
		this.paymentDetail = paymentDetail;
	}
	public void addPaymentDetail(PaymentDetail paymentDetail) {
		this.paymentDetail.add(paymentDetail);
	}
	public Boolean getPayMasa() {
		return payMasa;
	}
	public void setPayMasa(Boolean payMasa) {
		this.payMasa = payMasa;
	}
	public Integer getModifiedBy() {
		return modifiedBy;
	}
	public void setModifiedBy(Integer modifiedBy) {
		this.modifiedBy = modifiedBy;
	}
	public Date getModifiedDate() {
		return modifiedDate;
	}
	public void setModifiedDate(Date modifiedDate) {
		this.modifiedDate = modifiedDate;
	}
	public Date getPayInvoiceDate() {
		return payInvoiceDate;
	}
	public void setPayInvoiceDate(Date payInvoiceDate) {
		this.payInvoiceDate = payInvoiceDate;
	}
}
