package com.masa.pts.core.domain;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name="TLST_FULFILLMENT_MATERIAL",schema = "dbo")
public class FulfillmentMaterial implements Serializable {

	private static final long serialVersionUID = 2087593206947750303L;

	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="material_id")
	private Integer materialId;
	
	@Column(name="name")
	private String name;
	
	@Column(name="code")
	private String code;
	
	@Column(name="type")
	private Integer type;
	
	@Column(name="upgrade_line")
	private Integer upgradeLine;
	
	@Column(name="upgrade_weight")
	private Integer upgradeWeight;
	
	@Column(name="mat_description")
	private String materialDesc;

	public FulfillmentMaterial() {
		super();
	}

	public Integer getMaterialId() {
		return materialId;
	}

	public void setMaterialId(Integer materialId) {
		this.materialId = materialId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Integer getUpgradeLine() {
		return upgradeLine;
	}

	public void setUpgradeLine(Integer upgradeLine) {
		this.upgradeLine = upgradeLine;
	}

	public Integer getUpgradeWeight() {
		return upgradeWeight;
	}

	public void setUpgradeWeight(Integer upgradeWeight) {
		this.upgradeWeight = upgradeWeight;
	}

	public String getMaterialDesc() {
		return materialDesc;
	}

	public void setMaterialDesc(String materialDesc) {
		this.materialDesc = materialDesc;
	}	
}
