package com.masa.pts.core.domain;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.masa.pts.core.constant.CreditType;
import com.masa.pts.core.constant.ProductUpgradeDowngradeType;


@Entity
@Table(name="TDAT_PRODUCT_UPGRADE_DOWNGRADE",schema = "dbo")
public class ProductUpgradeDowngrade implements Serializable {

	private static final long serialVersionUID = -8575290097939235214L;
	
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="id")
	private Integer id;
	
	private String productCode;
	private Integer productId;
	private Integer productFrequency;
	
	private String nextProductCode;
	private Integer nextProductId;
	private Integer nextProductFrequency;

	private String groupCode;
	
	@Column(name="upgrade_downgrade_type")
	@Enumerated(EnumType.ORDINAL)
	private ProductUpgradeDowngradeType upgradeDowngradeType;

	@Enumerated(EnumType.ORDINAL)
	private CreditType creditType;
	
	@Temporal(TemporalType.DATE)
	private Date effectiveDateStart;
	
	@Temporal(TemporalType.DATE)
	private Date effectiveDateEnd;
	
	private Boolean isDelete=Boolean.FALSE;
	private Date deletedDate;
	private Integer deletedBy=0;
	private Date createdDate;
	private Date modifiedDate;
	private Integer createdBy;
	private Integer modifiedBy;
	
	private Integer salesChannelId=0;
	
	public ProductUpgradeDowngrade() {
		super();
	}
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public String getProductCode() {
		return productCode;
	}
	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}
	public Integer getProductId() {
		return productId;
	}
	public void setProductId(Integer productId) {
		this.productId = productId;
	}
	public Integer getProductFrequency() {
		return productFrequency;
	}
	public void setProductFrequency(Integer productFrequency) {
		this.productFrequency = productFrequency;
	}
	public String getNextProductCode() {
		return nextProductCode;
	}
	public void setNextProductCode(String nextProductCode) {
		this.nextProductCode = nextProductCode;
	}
	public Integer getNextProductId() {
		return nextProductId;
	}
	public void setNextProductId(Integer nextProductId) {
		this.nextProductId = nextProductId;
	}
	public Integer getNextProductFrequency() {
		return nextProductFrequency;
	}
	public void setNextProductFrequency(Integer nextProductFrequency) {
		this.nextProductFrequency = nextProductFrequency;
	}
	public ProductUpgradeDowngradeType getUpgradeDowngradeType() {
		return upgradeDowngradeType;
	}
	public void setUpgradeDowngradeType(ProductUpgradeDowngradeType upgradeDowngradeType) {
		this.upgradeDowngradeType = upgradeDowngradeType;
	}
	public CreditType getCreditType() {
		return creditType;
	}
	public void setCreditType(CreditType creditType) {
		this.creditType = creditType;
	}
	public Date getEffectiveDateStart() {
		return effectiveDateStart;
	}
	public void setEffectiveDateStart(Date effectiveDateStart) {
		this.effectiveDateStart = effectiveDateStart;
	}
	public Date getEffectiveDateEnd() {
		return effectiveDateEnd;
	}
	public void setEffectiveDateEnd(Date effectiveDateEnd) {
		this.effectiveDateEnd = effectiveDateEnd;
	}
	public Boolean getIsDelete() {
		return isDelete;
	}
	public void setIsDelete(Boolean isDelete) {
		this.isDelete = isDelete;
	}
	public Date getDeletedDate() {
		return deletedDate;
	}
	public void setDeletedDate(Date deletedDate) {
		this.deletedDate = deletedDate;
	}
	public Integer getDeletedBy() {
		return deletedBy;
	}
	public void setDeletedBy(Integer deletedBy) {
		this.deletedBy = deletedBy;
	}
	public Date getCreatedDate() {
		return createdDate;
	}
	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}
	public Date getModifiedDate() {
		return modifiedDate;
	}
	public void setModifiedDate(Date modifiedDate) {
		this.modifiedDate = modifiedDate;
	}
	public Integer getCreatedBy() {
		return createdBy;
	}
	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}
	public Integer getModifiedBy() {
		return modifiedBy;
	}
	public void setModifiedBy(Integer modifiedBy) {
		this.modifiedBy = modifiedBy;
	}
	public String getGroupCode() {
		return groupCode;
	}
	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}
	public Integer getSalesChannelId() {
		return salesChannelId;
	}
	public void setSalesChannelId(Integer salesChannelId) {
		this.salesChannelId = salesChannelId;
	}
}
