package com.masa.pts.core.domain;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.Table;

@Entity
@Table(name="TDAT_MEMBER_EMPLOYEEID",schema = "dbo")
public class MemberEmployeeIdEntity implements Serializable {

	private static final long serialVersionUID = -6674821266228767197L;

	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="ID")
	private Integer id;
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name="member_id") 
	private Member member;
	
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name="group_id") 
	private GroupEntity group;
	
	/*
	@Formula("(SELECT TDAT_GROUP.GROUP_CODE FROM TDAT_GROUP WHERE TDAT_GROUP.GROUP_ID = GROUP_ID)")
	private String groupCode;
	*/
	
	private String employeeId;
	
	@Column(name = "effective_start_date", columnDefinition = "DATE")
	private LocalDate effectiveStartDate;
	
	@Column(name = "effective_end_date", columnDefinition = "DATE")
	private LocalDate effectiveEndDate;
	
	@OneToOne(fetch = FetchType.LAZY)
	@JoinColumn(name="fee_id")
	private MemberFee memberFee;	
	
	private Integer createdBy;
	
	private Date createdDate;
	
	private Integer modifiedBy;
	
	private Date modifiedDate;

	public MemberEmployeeIdEntity() {
		super();
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}	

	public GroupEntity getGroup() {
		return group;
	}

	public void setGroup(GroupEntity group) {
		this.group = group;
	}

	public String getEmployeeId() {
		return employeeId;
	}

	public void setEmployeeId(String employeeId) {
		this.employeeId = employeeId;
	}	

	public LocalDate getEffectiveStartDate() {
		return effectiveStartDate;
	}

	public void setEffectiveStartDate(LocalDate effectiveStartDate) {
		this.effectiveStartDate = effectiveStartDate;
	}

	public LocalDate getEffectiveEndDate() {
		return effectiveEndDate;
	}

	public void setEffectiveEndDate(LocalDate effectiveEndDate) {
		this.effectiveEndDate = effectiveEndDate;
	}
	public Integer getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public Integer getModifiedBy() {
		return modifiedBy;
	}

	public void setModifiedBy(Integer modifiedBy) {
		this.modifiedBy = modifiedBy;
	}

	public Date getModifiedDate() {
		return modifiedDate;
	}

	public void setModifiedDate(Date modifiedDate) {
		this.modifiedDate = modifiedDate;
	}

	public void setMember(Member member) {
		this.member = member;
	}

	public void setMemberFee(MemberFee memberFee) {
		this.memberFee = memberFee;
	}		
}
