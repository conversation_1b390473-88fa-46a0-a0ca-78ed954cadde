package com.masa.pts.core.domain;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.masa.pts.core.model.DropdownData;

public class DivisionSalesChannel implements Serializable {

	private static final long serialVersionUID = -3606646772353469260L;

	private Map<Integer,String> division = new HashMap<Integer,String>();
	private Map<Integer,String> salesChannel = new HashMap<Integer,String>();
	
	private List<DropdownData> divisionData = new ArrayList<DropdownData>();
	private List<DropdownData> salesChannelData = new ArrayList<DropdownData>();
	
	public DivisionSalesChannel() {
		super();
	}
	public DivisionSalesChannel(Map<Integer, String> division, Map<Integer, String> salesChannel) {
		super();
		this.division = division;
		this.salesChannel = salesChannel;
	}
	public Map<Integer, String> getDivision() {
		return division;
	}
	public void setDivision(Map<Integer, String> division) {
		this.division = division;
	}
	public Map<Integer, String> getSalesChannel() {
		return salesChannel;
	}
	public void setSalesChannel(Map<Integer, String> salesChannel) {
		this.salesChannel = salesChannel;
	}
	public List<DropdownData> getDivisionData() {
		return divisionData;
	}
	public void setDivisionData(List<DropdownData> divisionData) {
		this.divisionData = divisionData;
	}
	public List<DropdownData> getSalesChannelData() {
		return salesChannelData;
	}
	public void setSalesChannelData(List<DropdownData> salesChannelData) {
		this.salesChannelData = salesChannelData;
	}	
}
