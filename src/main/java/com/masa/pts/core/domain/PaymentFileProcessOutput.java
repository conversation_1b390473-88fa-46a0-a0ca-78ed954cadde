package com.masa.pts.core.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.masa.pts.core.model.PaymentPostStatus;

@Entity
@Table(name="TDAT_PAYMENT_FILE_POSTING",schema = "dbo")
public class PaymentFileProcessOutput implements Serializable {

	private static final long serialVersionUID = -5179534749415593905L;

	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="id")
	private Integer id;
	
	@Column(name="member_id")
	private Integer memberId;
	
	@Column(name="employee_id")
	private String employerId;
	
	@Column(name="member_name")
	private String memberName;
	
	@Temporal(TemporalType.DATE)
	@Column(name="effective_date")
	private Date joinDate;
	
	@Temporal(TemporalType.DATE)
	@Column(name="renew_date")
	private Date renewDate;
	
	@Temporal(TemporalType.DATE)
	@Column(name="renew_date_old")
	private Date renewDateOld;
	
	@Temporal(TemporalType.DATE)
	@Column(name="transaction_date")
	private Date transactionDate;
	
	@Column(name="amount_paid")
	private BigDecimal amountPaid=BigDecimal.ZERO;
	
	@Column(name="amount_due")
	private BigDecimal amountDue = BigDecimal.ZERO;
	
	@Enumerated(EnumType.STRING)
	@Column(name="status")
	private PaymentPostStatus status;
	
	@Column(name="comments")
	private String comments;
	
	@Column(name="group_code")
	private String groupCd;
	
	@Column(name="sales_channel")
	private String salesChannel;
	
	@Column(name="division")
	private String division;
	
	@Column(name="processed_date")
	private Date processedDate = new java.util.Date();
	
	@Column(name="processed_by")
	private Integer processedBy = Constant.DEFAULT_USER_ID;
	
	@Column(name="comm_amount")
	private BigDecimal commAmount=BigDecimal.ZERO;
	
	@Column(name="file_name")
	private String fileName;
	
	@Column(name="REVIEW_STATUS")
	@Enumerated(EnumType.STRING)
	private ReviewStatus reviewStatus = ReviewStatus.OPEN;
	
	@Column(name="STATUS_UPDATED_BY")
	private String statusUpdatedBy = Constant.DEFAULT_PAYMENT_USERNAME;
	
	private String fileOwner;
	
	public PaymentFileProcessOutput() {
		super();
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getMemberId() {
		return memberId;
	}

	public void setMemberId(Integer memberId) {
		this.memberId = memberId;
	}

	public String getEmployerId() {
		return employerId;
	}

	public void setEmployerId(String employerId) {
		this.employerId = employerId;
	}

	public String getMemberName() {
		return memberName;
	}

	public void setMemberName(String memberName) {
		this.memberName = memberName;
	}

	public Date getJoinDate() {
		return joinDate;
	}

	public void setJoinDate(Date joinDate) {
		this.joinDate = joinDate;
	}

	public Date getRenewDate() {
		return renewDate;
	}

	public void setRenewDate(Date renewDate) {
		this.renewDate = renewDate;
	}

	public Date getRenewDateOld() {
		return renewDateOld;
	}

	public void setRenewDateOld(Date renewDateOld) {
		this.renewDateOld = renewDateOld;
	}

	public Date getTransactionDate() {
		return transactionDate;
	}

	public void setTransactionDate(Date transactionDate) {
		this.transactionDate = transactionDate;
	}

	public BigDecimal getAmountPaid() {
		return amountPaid;
	}

	public void setAmountPaid(BigDecimal amountPaid) {
		this.amountPaid = amountPaid;
	}

	public BigDecimal getAmountDue() {
		return amountDue;
	}

	public void setAmountDue(BigDecimal amountDue) {
		this.amountDue = amountDue;
	}

	public PaymentPostStatus getStatus() {
		return status;
	}

	public void setStatus(PaymentPostStatus status) {
		this.status = status;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	public String getGroupCd() {
		return groupCd;
	}

	public void setGroupCd(String groupCd) {
		this.groupCd = groupCd;
	}

	public String getSalesChannel() {
		return salesChannel;
	}

	public void setSalesChannel(String salesChannel) {
		this.salesChannel = salesChannel;
	}

	public String getDivision() {
		return division;
	}

	public void setDivision(String division) {
		this.division = division;
	}

	public Date getProcessedDate() {
		return processedDate;
	}

	public void setProcessedDate(Date processedDate) {
		this.processedDate = processedDate;
	}

	public Integer getProcessedBy() {
		return processedBy;
	}

	public void setProcessedBy(Integer processedBy) {
		this.processedBy = processedBy;
	}

	public BigDecimal getCommAmount() {
		return commAmount;
	}

	public void setCommAmount(BigDecimal commAmount) {
		this.commAmount = commAmount;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public ReviewStatus getReviewStatus() {
		return reviewStatus;
	}

	public void setReviewStatus(ReviewStatus reviewStatus) {
		this.reviewStatus = reviewStatus;
	}

	public String getStatusUpdatedBy() {
		return statusUpdatedBy;
	}

	public void setStatusUpdatedBy(String statusUpdatedBy) {
		this.statusUpdatedBy = statusUpdatedBy;
	}

	public String getFileOwner() {
		if(fileOwner!=null) {
			return fileOwner.replace("MASAASSIST\\", "");
		} else {
			return null;
		}
	}

	public void setFileOwner(String fileOwner) {
		this.fileOwner = fileOwner;
	}	
	
	
}
