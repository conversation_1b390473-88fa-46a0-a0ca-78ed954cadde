package com.masa.pts.core.domain;

import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.OrderBy;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

import com.fasterxml.jackson.annotation.JsonView;
import org.springframework.context.annotation.Lazy;

@Entity
@Table(name="TLNK_MEMBER_FEE",schema = "dbo")
public class MemberFee implements Serializable {

	private static final long serialVersionUID = -1417225421919278640L;
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="id")
	private Integer id;

	@Column(name="member_id")
	private Integer memberId;
	
	@JsonView(MobileView.Member.class)
	@ManyToOne(fetch=FetchType.LAZY)
	@JoinColumn(name="product_id")
	private Product product;
	
	@JsonView(MobileView.Member.class)
	@ManyToOne(fetch=FetchType.LAZY)
	@JoinColumn(name="fee_id")
	private Fee feeDetails;

	@JsonView(MobileView.Member.class)
	private Double amount=0.0;
	
	@JsonView(MobileView.Member.class)
	@ManyToOne(fetch=FetchType.LAZY)
	@JoinColumn(name="frequency")
	private Frequency frequencyDetails;
	
	@JsonView(MobileView.Member.class)
	private Date productDate;
	private Integer position;
	private Integer upgradeType;
	
	@JsonView(MobileView.Member.class)
	private Double maxAmountDue=0.0;
	private Double retailAmount;
	
	@Transient
	private Double feeMultipler=0.0;
	@Transient
	private Double premMultipler=0.0;
	@Transient
	private Double initMultipler=0.0;
	
	@JsonView(MobileView.Member.class)
	@Transient
	private Double amountPaid=0.0;
	
	@Transient
	private Double amountPaidMasa=0.0;
	
	@JsonView(MobileView.Member.class)
	@Transient
	private Double balanceDue=0.0;
	
	@JsonView(MobileView.Member.class)
	@Transient
	private Double outstanndingBalance=0.0;
	
	@Transient
	private Double setupFeeAmt=0.0;//setup fee amt per frequency
	
	@Column(name="override_new_comm")
	private Boolean overrideNewComm = Boolean.FALSE;
	
	@Temporal(TemporalType.DATE)
	@Column(name="effective_start_date")
	private Date effectiveStartDate;
	
	@Temporal(TemporalType.DATE)
	@Column(name="effective_end_date")
	private Date effectiveEndDate;
	
	@Column(name="setupfee_ref_id")
	private Integer setupFeeRefId;
		
	@ManyToOne
	@JoinColumn(name="group_id")
	private GroupEntity group;
	
	@Column(name="created_date")
	private Date createdDate;
	
	@Column(name="created_by")
	private Integer createdBy;
	
	@Column(name="modified_date")
	private Date modifiedDate;
	
	@Column(name="modified_by")
	private Integer modifiedBy;
	
	@OneToMany(fetch=FetchType.LAZY,cascade = CascadeType.ALL,mappedBy = "memberFee", orphanRemoval = true)
	@OrderBy(value="agent_position")
	private Set<MemberCommission> commission = new HashSet<>();
	
	@OneToOne(mappedBy = "memberFee", cascade = CascadeType.ALL, fetch = FetchType.LAZY, optional = true,orphanRemoval =  true)
	private MemberEmployeeIdEntity memberEmployeeId ;

	@Column(name="free_trial")
	private Integer freeTrial;
	
	public MemberFee() {
		super();
	}
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}

	public Product getProduct() {
		return product;
	}
	public void setProduct(Product product) {
		this.product = product;
	}
	public Fee getFeeDetails() {
		return feeDetails;
	}
	public void setFeeDetails(Fee feeDetails) {
		this.feeDetails = feeDetails;
	}
	public Double getAmount() {
		return amount;
	}
	public void setAmount(Double amount) {
		this.amount = amount;
	}
	public Frequency getFrequencyDetails() {
		return frequencyDetails;
	}
	public void setFrequencyDetails(Frequency frequencyDetails) {
		this.frequencyDetails = frequencyDetails;
	}
	public Date getProductDate() {
		return productDate;
	}
	public void setProductDate(Date productDate) {
		this.productDate = productDate;
	}
	public Integer getPosition() {
		return position;
	}
	public void setPosition(Integer position) {
		this.position = position;
	}
	public Integer getUpgradeType() {
		return upgradeType;
	}
	public void setUpgradeType(Integer upgradeType) {
		this.upgradeType = upgradeType;
	}
	public Double getMaxAmountDue() {
		return maxAmountDue;
	}
	public void setMaxAmountDue(Double maxAmountDue) {
		this.maxAmountDue = maxAmountDue;
	}
	public Double getRetailAmount() {
		return retailAmount;
	}
	public void setRetailAmount(Double retailAmount) {
		this.retailAmount = retailAmount;
	}
	public Double getFeeMultipler() {
		return feeMultipler;
	}
	public void setFeeMultipler(Double feeMultipler) {
		this.feeMultipler = feeMultipler;
	}
	public Double getPremMultipler() {
		return premMultipler;
	}
	public void setPremMultipler(Double premMultipler) {
		this.premMultipler = premMultipler;
	}
	public Double getInitMultipler() {
		return initMultipler;
	}
	public void setInitMultipler(Double initMultipler) {
		this.initMultipler = initMultipler;
	}
	public Double getAmountPaid() {
		return amountPaid;
	}
	public void setAmountPaid(Double amountPaid) {
		this.amountPaid = amountPaid;
	}
	public Double getBalanceDue() {
		return balanceDue;
	}
	public void setBalanceDue(Double balanceDue) {
		this.balanceDue = balanceDue;
	}
	public Double getOutstanndingBalance() {
		return outstanndingBalance;
	}
	public void setOutstanndingBalance(Double outstanndingBalance) {
		this.outstanndingBalance = outstanndingBalance;
	}
	public Double getSetupFeeAmt() {
		return setupFeeAmt;
	}
	public void setSetupFeeAmt(Double setupFeeAmt) {
		this.setupFeeAmt = setupFeeAmt;
	}
	public Integer getMemberId() {
		return memberId;
	}
	public void setMemberId(Integer memberId) {
		this.memberId = memberId;
	}
	public Double getAmountPaidMasa() {
		return amountPaidMasa;
	}
	public void setAmountPaidMasa(Double amountPaidMasa) {
		this.amountPaidMasa = amountPaidMasa;
	}
	public Boolean getOverrideNewComm() {
		return overrideNewComm;
	}
	public void setOverrideNewComm(Boolean overrideNewComm) {
		this.overrideNewComm = overrideNewComm;
	}	
	public Date getEffectiveStartDate() {
		return effectiveStartDate;
	}
	public void setEffectiveStartDate(Date effectiveStartDate) {
		this.effectiveStartDate = effectiveStartDate;
	}
	public Date getEffectiveEndDate() {
		return effectiveEndDate;
	}
	public void setEffectiveEndDate(Date effectiveEndDate) {
		this.effectiveEndDate = effectiveEndDate;
	}
	public Integer getSetupFeeRefId() {
		return setupFeeRefId;
	}
	public void setSetupFeeRefId(Integer setupFeeRefId) {
		this.setupFeeRefId = setupFeeRefId;
	}	
	public GroupEntity getGroup() {
		return group;
	}
	public void setGroup(GroupEntity group) {
		this.group = group;
	}
	public Date getCreatedDate() {
		return createdDate;
	}
	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}
	public Integer getCreatedBy() {
		return createdBy;
	}
	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}
	public Date getModifiedDate() {
		return modifiedDate;
	}
	public void setModifiedDate(Date modifiedDate) {
		this.modifiedDate = modifiedDate;
	}
	public Integer getModifiedBy() {
		return modifiedBy;
	}
	public void setModifiedBy(Integer modifiedBy) {
		this.modifiedBy = modifiedBy;
	}
	public Set<MemberCommission> getCommission() {
		return commission;
	}

	public void setCommission(Set<MemberCommission> commission) {
		this.commission.clear();
		if(null != commission)
			this.commission.addAll(commission);
	}	
	public void addCommission(MemberCommission memCommission) {
		commission.add(memCommission);
		memCommission.setMemberFee(this);
	}
	public void removeCommission(MemberCommission memCommission) {
		memCommission.setMemberFee(null); 
		this.commission.remove(memCommission);		 
	}	
	
	public MemberEmployeeIdEntity getMemberEmployeeId() {
		return memberEmployeeId;
	}
	public void setMemberEmployeeId(MemberEmployeeIdEntity memberEmployeeId) {
		this.memberEmployeeId = memberEmployeeId;
	}
	
	public Integer getFreeTrial() {
		return freeTrial;
	}
	public void setFreeTrial(Integer freeTrial) {
		this.freeTrial = freeTrial;
	}
	@Override
	public String toString() {
		return "MemberFee [id=" + id + ", memberId=" + memberId + ", product=" + product + ", feeDetails=" + feeDetails
				+ ", amount=" + amount + ", frequencyDetails=" + frequencyDetails + ", productDate=" + productDate
				+ ", position=" + position + ", upgradeType=" + upgradeType + ", maxAmountDue=" + maxAmountDue
				+ ", retailAmount=" + retailAmount + ", feeMultipler=" + feeMultipler + ", premMultipler="
				+ premMultipler + ", initMultipler=" + initMultipler + ", amountPaid=" + amountPaid
				+ ", amountPaidMasa=" + amountPaidMasa + ", balanceDue=" + balanceDue + ", outstanndingBalance="
				+ outstanndingBalance + ", setupFeeAmt=" + setupFeeAmt + ", overrideNewComm=" + overrideNewComm
				+ ", effectiveStartDate=" + effectiveStartDate + ", effectiveEndDate=" + effectiveEndDate
				+ ", setupFeeRefId=" + setupFeeRefId + ", group=" + group + ", createdDate=" + createdDate
				+ ", createdBy=" + createdBy + ", modifiedDate=" + modifiedDate + ", modifiedBy=" + modifiedBy 
				+ ", freeTrial=" + freeTrial + "]";
	}
	
	public boolean isCurrentFeeActive(Date dateToCompare)
	{
		return ( (dateToCompare.equals(this.effectiveStartDate) || dateToCompare.after(this.effectiveStartDate)) && 
					(dateToCompare.equals(this.effectiveEndDate) || dateToCompare.before(this.effectiveEndDate)) );
	}
	
	public boolean isSetupFee() {
		return this.feeDetails.isSetupFee();
	}
}
