package com.masa.pts.core.domain;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonView;

@Entity(name="Fee")
@Table(name="TLST_FEE",schema = "dbo") 
public class Fee implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -2903831923082264832L;

	@JsonView(MobileView.Member.class)
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="fee_id")
	private Integer feeId;
	
	@JsonView(MobileView.Member.class)
	private String name;
	
	@JsonView(MobileView.Member.class)
	@Column(name="fee_type")
	private String feeType;

	public Fee() {
		super();
	}
	public Integer getFeeId() {
		return feeId;
	}

	public void setFeeId(Integer feeId) {
		this.feeId = feeId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getFeeType() {
		return feeType;
	}

	public void setFeeType(String feeType) {
		this.feeType = feeType;
	}
	
	public boolean isSetupFee() {
		return Constant.SETUP_FEE_IDS.contains(feeId);
	}

	@Override
	public String toString() {
		return "Fee [feeId=" + feeId + ", name=" + name + ", feeType=" + feeType + "]";
	}	
}
