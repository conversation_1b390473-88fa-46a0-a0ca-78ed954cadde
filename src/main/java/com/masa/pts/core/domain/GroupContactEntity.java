package com.masa.pts.core.domain;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.fasterxml.jackson.annotation.JsonView;
import com.masa.pts.core.model.TPAPortalView;

@Entity(name="GroupContact")
@Table(name="TDAT_GROUP_CONTACT",schema = "dbo") 
public class GroupContactEntity implements Serializable {

	private static final long serialVersionUID = -1872659717234888570L;

	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="id")
	@JsonView({TPAPortalView.Group.class})
	private Integer id;

	@ManyToOne
	@JoinColumn(name="group_id")
	private GroupEntity group;
	
	@JsonView({TPAPortalView.Group.class})
	@OneToOne
	@JoinColumn(name = "contact_type")
	private FieldValueEntity contactType;

	@JsonView({TPAPortalView.Group.class})
	private String firstName;

	@JsonView({TPAPortalView.Group.class})
	private String miName;

	@JsonView({TPAPortalView.Group.class})
	private String lastName;

	@JsonView({TPAPortalView.Group.class})
	private String phone;

	@JsonView({TPAPortalView.Group.class})
	private String email;

	@JsonView({TPAPortalView.Group.class})
	private Boolean receivesEmailInvoice;

	private Date createdDate;

	private Integer createdBy;

	private Date modifiedDate;

	private Integer modifiedBy;

	@Transient
	private String createdByUser;

	@Transient
	private String modifiedByUser;

	private Integer tpaPortal;

	public GroupContactEntity() {
		super();
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	/*public GroupEntity getGroup() {
		return group;
	}
	*/
	public void setGroup(GroupEntity group) {
		this.group = group;
	}

	public FieldValueEntity getContactType() {
		return contactType;
	}

	public void setContactType(FieldValueEntity contactType) {
		this.contactType = contactType;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getMiName() {
		return miName;
	}

	public void setMiName(String miName) {
		this.miName = miName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public Boolean getReceivesEmailInvoice() {
		return receivesEmailInvoice;
	}

	public void setReceivesEmailInvoice(Boolean receivesEmailInvoice) {
		this.receivesEmailInvoice = receivesEmailInvoice;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public Integer getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}

	public Date getModifiedDate() {
		return modifiedDate;
	}

	public void setModifiedDate(Date modifiedDate) {
		this.modifiedDate = modifiedDate;
	}

	public Integer getModifiedBy() {
		return modifiedBy;
	}

	public void setModifiedBy(Integer modifiedBy) {
		this.modifiedBy = modifiedBy;
	}

	public String getCreatedByUser() {
		return createdByUser;
	}

	public void setCreatedByUser(String createdByUser) {
		this.createdByUser = createdByUser;
	}

	public String getModifiedByUser() {
		return modifiedByUser;
	}

	public void setModifiedByUser(String modifiedByUser) {
		this.modifiedByUser = modifiedByUser;
	}

	public Integer getTpaPortal() {
		return tpaPortal;
	}

	public void setTpaPortal(Integer tpaPortal) {
		this.tpaPortal = tpaPortal;
	}
}
