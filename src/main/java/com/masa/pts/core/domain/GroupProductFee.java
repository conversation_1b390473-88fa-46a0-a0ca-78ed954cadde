package com.masa.pts.core.domain;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@Entity(name="GroupProductFee")
@Table(name="TLNK_GROUP_FEE",schema = "dbo") 
public class GroupProductFee implements Serializable {

	private static final long serialVersionUID = -6506720095052082656L;
	
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="id")
	private Integer id;
	
	@ManyToOne
	@JoinColumn(name="group_id")
	private GroupEntity group;
		
	@OneToOne(fetch=FetchType.LAZY)
	@JoinColumn(name="productId")
	@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"}) 
	private Product product;
	
	
	@OneToOne(fetch=FetchType.LAZY)
	@JoinColumn(name="fee_id")
	@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"}) 
	private Fee feeDetails;
	
	
	private double amount;
	
	@OneToOne(fetch=FetchType.LAZY)
	@JoinColumn(name="frequency")
	@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
	private Frequency frequencyDetails;
	
	private double maxAmountDue;
	
	private double retailAmount;

	@Column(name = "created_date")
	protected Date createdDate;

	@Column(name = "created_by")
	protected Integer createdBy;

	@Column(name = "modified_date")
	protected Date modifiedDate;

	@Column(name = "modified_by")
	protected Integer modifiedBy;

	public GroupProductFee() {
		super();
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	public Product getProduct() {
		return product;
	}

	public void setProduct(Product product) {
		this.product = product;
	}

	public double getAmount() {
		return amount;
	}

	public void setAmount(double amount) {
		this.amount = amount;
	}
	public Fee getFeeDetails() {
		return feeDetails;
	}

	public void setFeeDetails(Fee feeDetails) {
		this.feeDetails = feeDetails;
	}

	public Frequency getFrequencyDetails() {
		return frequencyDetails;
	}

	public void setFrequencyDetails(Frequency frequencyDetails) {
		this.frequencyDetails = frequencyDetails;
	}

	public double getMaxAmountDue() {
		return maxAmountDue;
	}

	public void setMaxAmountDue(double maxAmountDue) {
		this.maxAmountDue = maxAmountDue;
	}

	public double getRetailAmount() {
		return retailAmount;
	}

	public void setRetailAmount(double retailAmount) {
		this.retailAmount = retailAmount;
	}

	public void setGroup(GroupEntity group) {
		this.group = group;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public Integer getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}

	public Date getModifiedDate() {
		return modifiedDate;
	}

	public void setModifiedDate(Date modifiedDate) {
		this.modifiedDate = modifiedDate;
	}

	public Integer getModifiedBy() {
		return modifiedBy;
	}

	public void setModifiedBy(Integer modifiedBy) {
		this.modifiedBy = modifiedBy;
	}
}
