package com.masa.pts.core.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

import com.masa.pts.core.constant.MemberActiveStatus;
import com.masa.pts.core.model.PaymentPostStatus;
import com.masa.pts.core.model.SettlementType;

import static com.masa.pts.core.domain.Constant.DEFAULT_DATE_PATTERN;

@Entity
@Table(name="TDAT_PAYMENT_POSTING",schema = "dbo")
public class PaymentProcessOutput implements Serializable {

	private static final long serialVersionUID = 2726821347494062010L;
	
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="id")
	private Integer id;
	
	@Column(name="member_id")
	private Integer memberId;
	
	@Column(name="employee_id")
	private String employerId;
	
	@Column(name="member_name")
	private String memberName;
	
	@Temporal(TemporalType.DATE)
	@Column(name="effective_date")	
	private Date joinDate;
	
	@Temporal(TemporalType.DATE)
	@Column(name="renew_date")
	private Date renewDate;
	
	@Temporal(TemporalType.DATE)
	@Column(name="renew_date_old")
	private Date renewDateOld;
	
	@Temporal(TemporalType.DATE)
	@Column(name="transaction_date")
	private Date transactionDate;
	
	@Column(name="amount_paid")
	private BigDecimal amountPaid=BigDecimal.ZERO;
	
	@Column(name="amount_due")
	private BigDecimal amountDue = BigDecimal.ZERO;
	
	@Enumerated(EnumType.STRING)
	@Column(name="status")
	private PaymentPostStatus status;
	
	@Column(name="comments")
	private String comments;
	
	@Transient
	private String renewDateStr;
	
	@Transient
	private String renewDateOldStr;
	
	@Transient
	private String transactionDateStr;
	@Transient
	private String joinDateStr;
	
	@Column(name="group_code")
	private String groupCd;
	
	@Column(name="sales_channel")
	private String salesChannel;
	
	@Column(name="division")
	private String division;
	
	@Column(name="trans_response_code")
	private String transactionResponseCode;
	
	@Column(name="processed_date")
	private Date processedDate = new java.util.Date();
	
	@Column(name="processed_by")
	private Integer processedBy = Constant.DEFAULT_USER_ID;
	
	@Column(name="comm_amount")
	private BigDecimal commAmount=BigDecimal.ZERO;
	
	@Enumerated(EnumType.STRING)
	@Column(name="settle_type")
	private SettlementType settleType;
	
	@Column(name="transaction_id")
	private String transactionId;
	
	@Transient
	private Boolean isDecline=Boolean.FALSE;
	
	@Transient
	private String cellPhone;
	
	@Transient
	private String workPhone;
	
	@Transient
	private String memAddress;	
	
	@Transient
	private String ccExpire;
	
	@Transient
	private String ccLastFour;
	
	@Transient
	private String achLastFour;
	
	@Transient
	private String active = MemberActiveStatus.ACTIVE.name();
	
	@Transient
	private String address1;
	@Transient
	private String address2;
	@Transient
	private String address3;
	@Transient
	private String city;
	@Transient
	private String state;
	@Transient
	private String postalCode;
	@Transient
	private String country;
	
	@Transient
	private Boolean customerIdGroup=Boolean.FALSE;
	
	@Transient
	private SimpleDateFormat dateFormat = (SimpleDateFormat) DateFormat.getInstance();
	
	public PaymentProcessOutput() {
		super();
	}

	public Integer getMemberId() {
		return memberId;
	}
	public void setMemberId(Integer memberId) {
		this.memberId = memberId;
	}
	public String getEmployerId() {
		return employerId;
	}
	public void setEmployerId(String employerId) {
		this.employerId = employerId;
	}
	public String getMemberName() {
		return memberName;
	}
	public void setMemberName(String memberName) {
		this.memberName = memberName;
	}
	public Date getJoinDate() {
		return joinDate;
	}
	public void setJoinDate(Date joinDate) {
		this.joinDate = joinDate;
	}
	public Date getRenewDate() {
		return renewDate;
	}
	public void setRenewDate(Date renewDate) {
		this.renewDate = renewDate;
	}
	public Date getTransactionDate() {
		return transactionDate;
	}
	public void setTransactionDate(Date transactionDate) {
		this.transactionDate = transactionDate;
	}
	public BigDecimal getAmountPaid() {
		return amountPaid;
	}
	public void setAmountPaid(BigDecimal amountPaid) {
		this.amountPaid = amountPaid;
	}
	public BigDecimal getAmountDue() {
		return amountDue;
	}
	public void setAmountDue(BigDecimal amountDue) {
		this.amountDue = amountDue;
	}
	public PaymentPostStatus getStatus() {
		return status;
	}
	public void setStatus(PaymentPostStatus status) {
		this.status = status;
	}
	public String getComments() {
		return comments;
	}
	public void setComments(String comments) {
		this.comments = comments;
	}
	public String getRenewDateStr() {
		if(null == renewDate)
			return "";
		
		dateFormat.applyPattern(DEFAULT_DATE_PATTERN);		
		renewDateStr= dateFormat.format(renewDate);
		return renewDateStr;
	}
	public void setRenewDateStr(String renewDateStr) {
		this.renewDateStr = renewDateStr;
	}
	public String getTransactionDateStr() {
		if(null == transactionDate)
			return "";
		dateFormat.applyPattern(DEFAULT_DATE_PATTERN);		
		transactionDateStr= dateFormat.format(transactionDate);		
		return transactionDateStr;
	}
	public void setTransactionDateStr(String transactionDateStr) {
		this.transactionDateStr = transactionDateStr;
	}
	public String getGroupCd() {
		return groupCd;
	}
	public void setGroupCd(String groupCd) {
		this.groupCd = groupCd;
	}
	
	public String getSalesChannel() {
		return salesChannel;
	}
	public void setSalesChannel(String salesChannel) {
		this.salesChannel = salesChannel;
	}
	public String getDivision() {
		return division;
	}
	public void setDivision(String division) {
		this.division = division;
	}
	public String getJoinDateStr() {		
		if(null == joinDate)
			return "";
		dateFormat.applyPattern(DEFAULT_DATE_PATTERN);		
		joinDateStr= dateFormat.format(joinDate);		
		return joinDateStr;
	}
	public void setJoinDateStr(String joinDateStr) {
		this.joinDateStr = joinDateStr;
	}
	
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getTransactionResponseCode() {
		return transactionResponseCode;
	}

	public void setTransactionResponseCode(String transactionResponseCode) {
		this.transactionResponseCode = transactionResponseCode;
	}
	

	public Date getProcessedDate() {
		return processedDate;
	}

	public void setProcessedDate(Date processedDate) {
		this.processedDate = processedDate;
	}

	public Integer getProcessedBy() {
		return processedBy;
	}

	public void setProcessedBy(Integer processedBy) {
		this.processedBy = processedBy;
	}
	
	public BigDecimal getCommAmount() {
		return commAmount;
	}
	
	public void setCommAmount(BigDecimal commAmount) {
		this.commAmount = commAmount;
	}


	public Boolean getIsDecline() {
		return isDecline;
	}


	public void setIsDecline(Boolean isDecline) {
		this.isDecline = isDecline;
	}



	public SettlementType getSettleType() {
		return settleType;
	}


	public void setSettleType(SettlementType settleType) {
		this.settleType = settleType;
	}


	public String getTransactionId() {
		return transactionId;
	}


	public void setTransactionId(String transactionId) {
		this.transactionId = transactionId;
	}


	public String getCellPhone() {
		return cellPhone;
	}


	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}


	public String getWorkPhone() {
		return workPhone;
	}


	public void setWorkPhone(String workPhone) {
		this.workPhone = workPhone;
	}


	public String getMemAddress() {
		return memAddress;
	}


	public void setMemAddress(String memAddress) {
		this.memAddress = memAddress;
	}


	public String getCcExpire() {
		return ccExpire;
	}


	public void setCcExpire(String ccExpire) {
		this.ccExpire = ccExpire;
	}


	public String getCcLastFour() {
		return ccLastFour;
	}


	public void setCcLastFour(String ccLastFour) {
		this.ccLastFour = ccLastFour;
	}


	public String getAchLastFour() {
		return achLastFour;
	}


	public void setAchLastFour(String achLastFour) {
		this.achLastFour = achLastFour;
	}


	public String getActive() {
		return active;
	}


	public void setActive(String active) {
		this.active = active;
	}


	public String getAddress1() {
		return address1;
	}


	public void setAddress1(String address1) {
		this.address1 = address1;
	}


	public String getAddress2() {
		return address2;
	}


	public void setAddress2(String address2) {
		this.address2 = address2;
	}


	public String getAddress3() {
		return address3;
	}


	public void setAddress3(String address3) {
		this.address3 = address3;
	}


	public String getCity() {
		return city;
	}


	public void setCity(String city) {
		this.city = city;
	}


	public String getState() {
		return state;
	}


	public void setState(String state) {
		this.state = state;
	}


	public String getPostalCode() {
		return postalCode;
	}


	public void setPostalCode(String postalCode) {
		this.postalCode = postalCode;
	}


	public String getCountry() {
		return country;
	}


	public void setCountry(String country) {
		this.country = country;
	}


	public SimpleDateFormat getDateFormat() {
		return dateFormat;
	}


	public void setDateFormat(SimpleDateFormat dateFormat) {
		this.dateFormat = dateFormat;
	}


	public Date getRenewDateOld() {
		return renewDateOld;
	}


	public void setRenewDateOld(Date renewDateOld) {
		this.renewDateOld = renewDateOld;
	}


	public String getRenewDateOldStr() {
		if(null == renewDateOld)
			return "";		
		dateFormat.applyPattern(DEFAULT_DATE_PATTERN);		
		renewDateOldStr= dateFormat.format(renewDateOld);		
		return renewDateOldStr;
	}


	public void setRenewDateOldStr(String renewDateOldStr) {
		this.renewDateOldStr = renewDateOldStr;
	}


	public Boolean getCustomerIdGroup() {
		return customerIdGroup;
	}

	public void setCustomerIdGroup(Boolean customerIdGroup) {
		this.customerIdGroup = customerIdGroup;
	}

	@Override
	public String toString() {
		return "PaymentProcessOutput [memberId=" + memberId + ", employerId=" + employerId + ", memberName=" + memberName + ", joinDate=" + joinDate + ", renewDate=" + renewDate + ", renewDateOld="
				+ renewDateOld + ", transactionDate=" + transactionDate + ", amountPaid=" + amountPaid + ", amountDue=" + amountDue + ", status=" + status + ", comments=" + comments
				+ ", renewDateStr=" + renewDateStr + ", renewDateOldStr=" + renewDateOldStr + ", transactionDateStr=" + transactionDateStr + ", joinDateStr=" + joinDateStr + ", groupCd=" + groupCd
				+ ", salesChannel=" + salesChannel + ", division=" + division + ", transactionResponseCode=" + transactionResponseCode + ", processedDate=" + processedDate + ", processedBy="
				+ processedBy + ", commAmount=" + commAmount + ", settleType=" + settleType + ", transactionId=" + transactionId + ", isDecline=" + isDecline + ", cellPhone=" + cellPhone
				+ ", workPhone=" + workPhone + ", memAddress=" + memAddress + ", ccExpire=" + ccExpire + ", ccLastFour=" + ccLastFour + ", achLastFour=" + achLastFour + ", active=" + active
				+ ", address1=" + address1 + ", address2=" + address2 + ", address3=" + address3 + ", city=" + city + ", state=" + state + ", postalCode=" + postalCode + ", country=" + country + "]";
	}	
}
