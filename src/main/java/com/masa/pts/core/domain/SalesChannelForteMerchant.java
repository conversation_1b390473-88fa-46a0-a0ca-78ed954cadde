package com.masa.pts.core.domain;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.annotations.Where;

import com.fasterxml.jackson.annotation.JsonIgnore;

import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;

@Entity
@Table(name="TLNK_SALES_CHANNEL_FORTE_MERCHANT", schema = "dbo")
@Where(clause = "is_insurance=0")
public class SalesChannelForteMerchant implements Serializable {

	private static final long serialVersionUID = 2793814656537373185L;

	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="id")
	private Integer id;

	@JsonIgnore
    @ToString.Exclude
	@OneToOne(optional = false, fetch = FetchType.LAZY)
	@JoinColumn(name = "sales_channel_id", referencedColumnName = "id")
	private SalesChannel salesChannel;
	
	@Column(name="merchant_id")
	private String merchantId;

	@CreationTimestamp
	@Column(name="created_date")
	private Date createdDate;

	@Column(name="created_by")
	private Integer createdBy;

	@UpdateTimestamp
	@Column(name="modified_date")
	private Date modifiedDate;
	
	@Column(name="modified_by")
	private Integer modifiedBy;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public SalesChannel getSalesChannel() {
		return salesChannel;
	}

	public void setSalesChannel(SalesChannel salesChannel) {
		this.salesChannel = salesChannel;
	}

	public String getMerchantId() {
		return merchantId;
	}

	public void setMerchantId(String merchantId) {
		this.merchantId = merchantId;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public Integer getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}

	public Date getModifiedDate() {
		return modifiedDate;
	}

	public void setModifiedDate(Date modifiedDate) {
		this.modifiedDate = modifiedDate;
	}

	public Integer getModifiedBy() {
		return modifiedBy;
	}

	public void setModifiedBy(Integer modifiedBy) {
		this.modifiedBy = modifiedBy;
	}

	public SalesChannelForteMerchant() {
		super();
	}

	public SalesChannelForteMerchant(Integer id, Integer salesChannelId, String merchantId, Date createdDate, Integer createdBy, Date modifiedDate, Integer modifiedBy) {
		super();
		this.id = id;
		this.salesChannel = null;
		this.merchantId = merchantId;
		this.createdDate = createdDate;
		this.createdBy = createdBy;
		this.modifiedDate = modifiedDate;
		this.modifiedBy = modifiedBy;
	}
}
