package com.masa.pts.core.domain;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Entity
@Table(name="ex_edi_record_error",schema = "dbo")
public class MemberFileRecordErrorEntity implements Serializable {

	private static final long serialVersionUID = 3324405216059555374L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "ID")
	private Integer id;
	
	@ManyToOne()
	@JoinColumn(name="record_id")
	private MemberFile memberFile;
	
	private String errorCode;
	
	private String errorMessage;
	
	public MemberFileRecordErrorEntity(MemberFile memberFile, String errorCode, String errorMessage) {
		this.memberFile = memberFile;
		this.errorCode = errorCode;
		this.errorMessage = errorMessage;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public void setMemberFile(MemberFile memberFile) {
		this.memberFile = memberFile;
	}

	public String getErrorCode() {
		return errorCode;
	}

	public void setErrorCode(String errorCode) {
		this.errorCode = errorCode;
	}

	public String getErrorMessage() {
		return errorMessage;
	}

	public void setErrorMessage(String errorMessage) {
		this.errorMessage = errorMessage;
	}

	public MemberFileRecordErrorEntity() {
		super();
	}
}
