package com.masa.pts.core.domain;

import java.io.Serializable;

public class PTSRoleAccessId implements Serializable {

	private static final long serialVersionUID = -2467284426149776459L;

	private Integer roleId;
	
	private Integer accessId;

	
	public PTSRoleAccessId(Integer roleId, Integer accessId) {
		super();
		this.roleId = roleId;
		this.accessId = accessId;
	}

	public Integer getRoleId() {
		return roleId;
	}

	public void setRoleId(Integer roleId) {
		this.roleId = roleId;
	}

	public Integer getAccessId() {
		return accessId;
	}

	public void setAccessId(Integer accessId) {
		this.accessId = accessId;
	}

	public PTSRoleAccessId() {
		super();
	}

	@Override
	public int hashCode() {
		return super.hashCode();
	}

	@Override
	public boolean equals(Object obj) {
		return super.equals(obj);
	}
}
