package com.masa.pts.core.domain;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonView;
import com.masa.pts.core.model.TPAPortalView;


@Entity
@Table(name="TLST_STATE2",schema = "dbo")
public class State implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -5527213785179922733L;

	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="state_id")
	private Integer stateId;
	
	@JsonView({MobileView.Member.class,TPAPortalView.Group.class})
	private String symbol;
	@JsonView({MobileView.Member.class,TPAPortalView.Group.class})
	@Column(name="state")
	private String name;
	
	@JsonIgnore(value=true)
	private String iso;
		
	@JsonIgnore(value=true)
	private Boolean allowAsBenefit;
	
	@Column(name="country_id")
	private Integer countryId;
	
	public State() {
		super();
	}
		public Integer getStateId() {
		return stateId;
	}
	public void setStateId(Integer stateId) {
		this.stateId = stateId;
	}
	public String getSymbol() {
		return symbol;
	}
	public void setSymbol(String symbol) {
		this.symbol = symbol;
	}	
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getIso() {
		return iso;
	}
	public void setIso(String iso) {
		this.iso = iso;
	}
	public Boolean getAllowAsBenefit() {
		return allowAsBenefit;
	}
	public void setAllowAsBenefit(Boolean allowAsBenefit) {
		this.allowAsBenefit = allowAsBenefit;
	}
	public Integer getCountryId() {
		return countryId;
	}
	public void setCountryId(Integer countryId) {
		this.countryId = countryId;
	}
	
}
