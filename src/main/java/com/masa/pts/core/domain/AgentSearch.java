package com.masa.pts.core.domain;

import java.io.Serializable;
import java.util.StringJoiner;

import javax.annotation.concurrent.Immutable;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;

@Entity
@Immutable
@Table(name="TDAT_AGENT",schema = "dbo")
public class AgentSearch implements Serializable {

	private static final long serialVersionUID = -3856076341032522276L;
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="agent_id")
	private Integer agentId;
	@JsonIgnore
	private String agentNum;
	@JsonIgnore
	private String agentFirst;
	@JsonIgnore
	private String agentLast;
	
	public AgentSearch() {
		super();
	}
	public AgentSearch(Integer agentId, String agentNum, String agentFirst, String agentLast) {
		super();
		this.agentId = agentId;
		this.agentNum = agentNum;
		this.agentFirst = agentFirst;
		this.agentLast = agentLast;
	}
	public Integer getAgentId() {
		return agentId;
	}
	public void setAgentId(Integer agentId) {
		this.agentId = agentId;
	}
	public String getAgentNum() {
		return agentNum;
	}
	public void setAgentNum(String agentNum) {
		this.agentNum = agentNum;
	}
	public String getAgentFirst() {
		return agentFirst;
	}
	public void setAgentFirst(String agentFirst) {
		this.agentFirst = agentFirst;
	}
	public String getAgentLast() {
		return agentLast;
	}
	public void setAgentLast(String agentLast) {
		this.agentLast = agentLast;
	}
	public String getAgentName() {
		StringJoiner joiner = new StringJoiner("-");
		joiner.setEmptyValue(" ");
		joiner = joiner.add(getAgentFirst() !=null ? getAgentFirst() : "")
					.add(getAgentLast() !=null ? getAgentLast(): "")
					.add(getAgentNum() !=null ? getAgentNum() : "");
		return joiner.toString();
		
	}
	@Override
	public String toString() {
		return "AgentSearch [agentId=" + agentId + ", agentNum=" + agentNum + ", agentFirst=" + agentFirst + ", agentLast=" + agentLast + "]";
	}
}
