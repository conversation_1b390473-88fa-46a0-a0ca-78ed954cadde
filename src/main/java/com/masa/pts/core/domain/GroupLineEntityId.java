package com.masa.pts.core.domain;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import java.io.Serializable;
import java.util.Objects;

@Embeddable
public class GroupLineEntityId implements Serializable {

	private static final long serialVersionUID = 793739070216085166L;

	@Column(name = "IDGROUP")
	private Integer groupId;

	@Column(name = "IDLINE")
	private Integer lineId;

	public Integer getGroupId() {
		return groupId;
	}

	public void setGroupId(Integer groupId) {
		this.groupId = groupId;
	}

	public Integer getLineId() {
		return lineId;
	}

	public void setLineId(Integer lineId) {
		this.lineId = lineId;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) return true;
		if (o == null || getClass() != o.getClass()) return false;
		GroupLineEntityId that = (GroupLineEntityId) o;
		return Objects.equals(groupId, that.groupId) &&
				Objects.equals(lineId, that.lineId);
	}

	@Override
	public int hashCode() {
		return Objects.hash(groupId, lineId);
	}
}
