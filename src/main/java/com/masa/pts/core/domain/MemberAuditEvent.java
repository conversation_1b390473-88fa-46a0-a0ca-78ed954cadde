package com.masa.pts.core.domain;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;

import com.masa.pts.core.constant.AuditEventType;

@Entity
@Table(name="TDAT_MEMBER_AUDIT_EVENT",schema = "dbo")
public class MemberAuditEvent implements Serializable {

	private static final long serialVersionUID = -1229940240692875829L;

	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="id")
	private Integer id;
	
	@OneToOne
	@JoinColumn(name = "member_id")
	private Member member;
	
	private AuditEventType auditEventType;
	
	private Integer applicationSource;
	
	private Date effectiveDate;
	
	private Date renewDate;
	
	private Integer groupId;
	
	private String comments;
	
	private Date processedDate;
	
	private Integer processedBy;

	public MemberAuditEvent() {
		super();
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Member getMember() {
		return member;
	}

	public void setMember(Member member) {
		this.member = member;
	}

	public AuditEventType getAuditEventType() {
		return auditEventType;
	}

	public void setAuditEventType(AuditEventType auditEventType) {
		this.auditEventType = auditEventType;
	}

	public Integer getApplicationSource() {
		return applicationSource;
	}

	public void setApplicationSource(Integer applicationSource) {
		this.applicationSource = applicationSource;
	}

	public Date getEffectiveDate() {
		return effectiveDate;
	}

	public void setEffectiveDate(Date effectiveDate) {
		this.effectiveDate = effectiveDate;
	}

	public Date getRenewDate() {
		return renewDate;
	}

	public void setRenewDate(Date renewDate) {
		this.renewDate = renewDate;
	}

	public Integer getGroupId() {
		return groupId;
	}

	public void setGroupId(Integer groupId) {
		this.groupId = groupId;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	public Date getProcessedDate() {
		return processedDate;
	}

	public void setProcessedDate(Date processedDate) {
		this.processedDate = processedDate;
	}

	public Integer getProcessedBy() {
		return processedBy;
	}

	public void setProcessedBy(Integer processedBy) {
		this.processedBy = processedBy;
	}

	@Override
	public String toString() {
		return "MemberAuditEvent [id=" + id + ", member=" + member + ", auditEventType=" + auditEventType
				+ ", applicationSource=" + applicationSource + ", effectiveDate=" + effectiveDate + ", renewDate="
				+ renewDate + ", groupId=" + groupId + ", comments=" + comments + ", processedDate=" + processedDate
				+ ", processedBy=" + processedBy + "]";
	}	
}
