package com.masa.pts.core.domain;

import javax.persistence.Embeddable;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import java.io.Serializable;

@Embeddable
public class PTSUserRoleId implements Serializable {

	private static final long serialVersionUID = -2467284426149776459L;
	
	@ManyToOne
	@JoinColumn(name="employee_id")
	private PTSUser user;
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name="role_id")
	private Role role;

	public PTSUserRoleId() {
		super();
	}
	
	public PTSUserRoleId(PTSUser user, Role role) {
		super();
		this.user = user;
		this.role = role;
	}
	public Role getRole() {
		return role;
	}

	public void setRole(Role role) {
		this.role = role;
	}

	@Override
	public int hashCode() {
		return super.hashCode();
	}

	@Override
	public boolean equals(Object obj) {
		return super.equals(obj);
	}

	public PTSUser getUser() {
		return user;
	}
	 
	public void setUser(PTSUser user) {
		this.user = user;
	}
}
