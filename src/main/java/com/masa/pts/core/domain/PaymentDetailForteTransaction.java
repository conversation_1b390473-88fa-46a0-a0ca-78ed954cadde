package com.masa.pts.core.domain;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name="TLNK_PAYMENT_DETAIL_FORTE_TRANSACTION",schema = "dbo")
public class PaymentDetailForteTransaction implements Serializable {

	private static final long serialVersionUID = -6825568603876753215L;

	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="id")
	private Integer id;
	
	@Column(name="paydetail_id")
	private Integer paymentDetailId;
	
	/*@OneToOne	
	@JoinColumn(name="paydetail_id") 
	private PaymentDetail paymentDetail;*/
	
	private String transactionId;
	private Integer memberId;
	private Date createdDate;
	private Integer createdBy;
	private Date modifiedDate;
	private Integer modifiedBy;
	
	public PaymentDetailForteTransaction() {
		super();
	}
	public PaymentDetailForteTransaction(String transactionId,Integer memberId,Date createdDate, Integer createdBy, Date modifiedDate, Integer modifiedBy) {
		super();	
		this.transactionId = transactionId;
		this.memberId = memberId;
		this.createdDate = createdDate;
		this.createdBy = createdBy;
		this.modifiedDate = modifiedDate;
		this.modifiedBy = modifiedBy;
	}
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public Integer getPaymentDetailId() {
		return paymentDetailId;
	}
	public void setPaymentDetailId(Integer paymentDetailId) {
		this.paymentDetailId = paymentDetailId;
	}
	public String getTransactionId() {
		return transactionId;
	}
	public void setTransactionId(String transactionId) {
		this.transactionId = transactionId;
	}
	public Date getCreatedDate() {
		return createdDate;
	}
	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}
	public Integer getCreatedBy() {
		return createdBy;
	}
	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}
	public Date getModifiedDate() {
		return modifiedDate;
	}
	public void setModifiedDate(Date modifiedDate) {
		this.modifiedDate = modifiedDate;
	}
	public Integer getModifiedBy() {
		return modifiedBy;
	}
	public void setModifiedBy(Integer modifiedBy) {
		this.modifiedBy = modifiedBy;
	}
	/*public void setPaymentDetail(PaymentDetail paymentDetail) {
		this.paymentDetail = paymentDetail;
	}*/
	public Integer getMemberId() {
		return memberId;
	}
	public void setMemberId(Integer memberId) {
		this.memberId = memberId;
	}
	@Override
	public String toString() {
		return "PaymentDetailForteTransaction [id=" + id +  ", transactionId=" + transactionId + ", createdDate=" + createdDate + ", createdBy=" + createdBy
				+ ", modifiedDate=" + modifiedDate + ", modifiedBy=" + modifiedBy + "]";
	}	
}
