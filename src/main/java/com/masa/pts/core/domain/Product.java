package com.masa.pts.core.domain;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonView;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

@Entity
@Table(name="TDAT_PRODUCT",schema = "dbo")
public class Product implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2220765492001952610L;

	
	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "product_seq")
	@SequenceGenerator(name = "product_seq", sequenceName = "TDAT_PRODUCTSEQ", allocationSize = 1, schema = "dbo")
	@Column(name="product_id")
	private Integer productId;
	
	@JsonView(MobileView.Member.class)
	private String name;
	
	@JsonView(MobileView.Member.class)
	private String description;
	
	//1-active,0-not active
	private Boolean active;
	
	@JsonView(MobileView.Member.class)
	//1-single 2-family
	private Integer type;
	
	@JsonView(MobileView.Member.class)
	//0-not upgrade, 1-upgrade
	private Integer upgradeProduct;
	
	@Column(name="is_passport",nullable = true)
	private Boolean passport;
	
	private Integer companyId;
	
	@OneToOne(fetch = FetchType.LAZY, cascade = {CascadeType.ALL})
	@JoinTable(schema = "dbo",
			name = "tlnk_Product_PRODUCTSET",
			joinColumns = @JoinColumn(name = "IDPRODUCT"),
			inverseJoinColumns = @JoinColumn(name = "IDPRODUCTSET"))
	@JsonSerialize(as = ProductSet.class)
	private ProductSet productSet= new ProductSet();
	
	@OneToOne(fetch = FetchType.LAZY, cascade = {CascadeType.ALL})
	@JoinTable(schema = "dbo",
			name = "tlnk_Product_PRODUCTCATEGORY",
			joinColumns = @JoinColumn(name = "IDPRODUCT"),
			inverseJoinColumns = @JoinColumn(name = "IDPRODUCTCATEGORY"))
	@JsonSerialize(as = ProductCategoryEntity.class)
	private ProductCategoryEntity productCategory = new ProductCategoryEntity();
		
	@OneToMany(fetch=FetchType.LAZY,cascade = CascadeType.ALL,orphanRemoval = true)
	@JoinColumn(name = "product_id")
	private Set<ProductFee> fees = new HashSet<>();
	
	
	public Product() {
		super();
	}

	public Integer getProductId() {
		return productId;
	}

	public void setProductId(Integer productId) {
		this.productId = productId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Integer getUpgradeProduct() {
		return upgradeProduct;
	}

	public void setUpgradeProduct(Integer upgradeProduct) {
		this.upgradeProduct = upgradeProduct;
	}



	public Integer getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Integer companyId) {
		this.companyId = companyId;
	}
	
	public Boolean getActive() {
		return active;
	}

	public void setActive(Boolean active) {
		this.active = active;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public ProductSet getProductSet() {
		return productSet;
	}

	public void setProductSet(ProductSet productSet) {
		this.productSet = productSet;
	}

	public interface ProductSummary {
		Integer getProductId();
		String getName();
		String getDescription();
		String getProductCategoryName();
		String getProductSetName();
	}
	
	public ProductCategoryEntity getProductCategory() {
		return productCategory;
	}

	public void setProductCategory(ProductCategoryEntity productCategory) {
		this.productCategory = productCategory;
	}

	public Set<ProductFee> getFees() {
		return fees;
	}

	public void setFees(Set<ProductFee> fees) {
		this.fees = fees;
	}

	public Boolean getPassport() {
		return passport;
	}

	public void setPassport(Boolean passport) {
		this.passport = passport;
	}		
}
