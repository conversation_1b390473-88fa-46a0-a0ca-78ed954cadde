package com.masa.pts.core.domain;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonView;
import com.masa.pts.core.model.TPAPortalView;
import org.hibernate.annotations.Where;

@Entity
@Table(name="TLST_FIELD_VALUE",schema = "dbo")
@Where(clause = "is_obsolete = 0")
public class FieldValueEntity implements Serializable {

	private static final long serialVersionUID = -1204609771142217341L;
	
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="id")
	@JsonView({TPAPortalView.Group.class})
	private Integer id;

	@JsonView({TPAPortalView.Group.class})
	private String name;

	@JsonView({TPAPortalView.Group.class})
	private String field;

	public FieldValueEntity() {
		super();
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getField() {
		return field;
	}

	public void setField(String field) {
		this.field = field;
	}	
}
