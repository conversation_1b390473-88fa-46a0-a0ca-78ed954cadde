package com.masa.pts.core.domain;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name="TLNK_AGENT_MANAGER",schema = "dbo")
public class AgentManager implements Serializable {

	private static final long serialVersionUID = 7623266452704418364L;
	
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="id")
	private Integer id;
	
	private Integer agentId;
	
	private Integer managerId;
	
	private Integer managerPosition;

	public AgentManager() {
		super();
	}

	public AgentManager(Integer id, Integer agentId, Integer managerId, Integer managerPosition) {
		super();
		this.id = id;
		this.agentId = agentId;
		this.managerId = managerId;
		this.managerPosition = managerPosition;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getAgentId() {
		return agentId;
	}

	public void setAgentId(Integer agentId) {
		this.agentId = agentId;
	}

	public Integer getManagerId() {
		return managerId;
	}

	public void setManagerId(Integer managerId) {
		this.managerId = managerId;
	}

	public Integer getManagerPosition() {
		return managerPosition;
	}

	public void setManagerPosition(Integer managerPosition) {
		this.managerPosition = managerPosition;
	}

	@Override
	public String toString() {
		return "AgentManager [id=" + id + ", agentId=" + agentId + ", managerId=" + managerId + ", managerPosition="
				+ managerPosition + "]";
	}		
}
