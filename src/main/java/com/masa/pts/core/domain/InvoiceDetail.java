package com.masa.pts.core.domain;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

@Entity
@Table(name="TDAT_INVOICE_DETAIL", schema = "dbo")
public class InvoiceDetail implements Serializable {

	private static final long serialVersionUID = -7676778578531623545L;

	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="id")
	private Integer id;
	
	@Column(name="amount_due")
	private Double amountDue;
	
	@ManyToOne	
	@JoinColumn(name="invoice_id") 
	private Invoice invoice;
	
	@OneToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "member_id")
	private Member member;
		
	/*
	@Formula("(SELECT TDAT_MEMBER.FIRST_NAME + ' ' + TDAT_MEMBER.MI + '' + TDAT_MEMBER.LAST_NAME FROM TDAT_MEMBER WHERE TDAT_MEMBER.MEMBER_ID = MEMBER_ID)")
	private String memberFullName;
	
	@Formula("(SELECT TDAT_MEMBER.EFFECTIVE_DATE FROM TDAT_MEMBER WHERE TDAT_MEMBER.MEMBER_ID = MEMBER_ID)")
	private Date memberEffectiveDate;
	
	@Formula("(SELECT TDAT_MEMBER.EMPLOYEE_ID FROM TDAT_MEMBER WHERE TDAT_MEMBER.MEMBER_ID = MEMBER_ID)")
	private String employeeId;

	@Formula("(SELECT TDAT_MEMBER.RENEW FROM TDAT_MEMBER WHERE TDAT_MEMBER.MEMBER_ID = MEMBER_ID)")
	private Integer renew;
	*/
	
	private boolean removed;
	
	@Column(name="remove_date")
	private Date removedDate;

	@Temporal(TemporalType.DATE)
	@Column(name="invoice_date")
	private Date invoiceDate;
	
	@OneToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "product_id")
	private Product product;
	
	/*
	@Transient
	private String productName;
	
	@Formula("(SELECT TPC.name " + 
			"FROM dbo.tlnk_Product_ProductCategory AS TPPC " + 
			"JOIN dbo.tlst_Product_Category AS TPC ON tpc.id = tppc.idProductCategory  " + 
			"WHERE TPPC.idProduct = PRODUCT_ID" +	")")
	private String productCategoryName;
	
	@Formula("(SELECT CASE WHEN TP.type = 1 THEN 'Single' ELSE 'Family' END " + 
			"FROM dbo.tdat_Product AS TP " + 
			"WHERE TP.product_id = PRODUCT_ID)")
	private String prdSingleFamilyType;
	*/
	
	@Column(name="period_fee")
	private Double periodFee;
	
	@Column(name="init_fee")
	private Double initFee;
	
	@Column(name="has_tax")
	private Boolean hasTax;
	
	@Column(name="tax_rate")
	private Double taxRate;
	
	@Transient
	private Date renewDate;
	
	private Date createdDate;
	private Date modifiedDate;
	private Integer createdBy;
	private Integer modifiedBy;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Double getAmountDue() {
		return amountDue;
	}

	public void setAmountDue(Double amountDue) {
		this.amountDue = amountDue;
	}
	public boolean getRemoved() {
		return removed;
	}

	public void setRemoved(boolean removed) {
		this.removed = removed;
	}

	public Date getRemovedDate() {
		return removedDate;
	}

	public void setRemovedDate(Date removedDate) {
		this.removedDate = removedDate;
	}

	public Double getPeriodFee() {
		return periodFee;
	}

	public void setPeriodFee(Double periodFee) {
		this.periodFee = periodFee;
	}

	public Double getInitFee() {
		return initFee;
	}

	public void setInitFee(Double initFee) {
		this.initFee = initFee;
	}

	public Boolean getHasTax() {
		return hasTax;
	}

	public void setHasTax(Boolean hasTax) {
		this.hasTax = hasTax;
	}

	public Double getTaxRate() {
		return taxRate;
	}

	public void setTaxRate(Double taxRate) {
		this.taxRate = taxRate;
	}
	/*
	public String getProductName() {
		if(StringUtils.hasText(productCategoryName))
		{
			return String.join(" ", productCategoryName,prdSingleFamilyType);
		}
		else
			return " ";
	}*/
	/*
	 * public Invoice getInvoice() { return invoice; }
	 */
	public void setInvoice(Invoice invoice) {
		this.invoice = invoice;
	}
	public Date getInvoiceDate() {
		return invoiceDate;
	}
	public void setInvoiceDate(Date invoiceDate) {
		this.invoiceDate = invoiceDate;
	}
	public Date getRenewDate() {
		return renewDate;
	}
	public void setRenewDate(Date renewDate) {
		this.renewDate = renewDate;
	}
	public Date getCreatedDate() {
		return createdDate;
	}
	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}
	public Date getModifiedDate() {
		return modifiedDate;
	}
	public void setModifiedDate(Date modifiedDate) {
		this.modifiedDate = modifiedDate;
	}
	public Integer getCreatedBy() {
		return createdBy;
	}
	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}
	public Integer getModifiedBy() {
		return modifiedBy;
	}
	public void setModifiedBy(Integer modifiedBy) {
		this.modifiedBy = modifiedBy;
	}

	public Member getMember() {
		return member;
	}

	public void setMember(Member member) {
		this.member = member;
	}

	public Product getProduct() {
		return product;
	}

	public void setProduct(Product product) {
		this.product = product;
	}	
}
