package com.masa.pts.core.domain;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import org.hibernate.annotations.Formula;

@Entity
@Table(name="TDAT_CLAIM_PAYMENT",schema = "dbo")
public class ClaimPayment implements Serializable {

	private static final long serialVersionUID = -6981793261551333110L;
	
	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "claim_payment_seq")
	@SequenceGenerator(name = "claim_payment_seq", sequenceName = "TDAT_CLAIM_PAYMENTSEQ", allocationSize = 1, schema = "dbo")
	@Column(name="id")
	private Integer id;
	
	@ManyToOne	
	@JoinColumn(name="claim_id")
	private Claim claim;
	
	private Integer benefitId;
	
	@Formula("(SELECT TDAT_BENEFIT.NAME FROM TDAT_BENEFIT WHERE TDAT_BENEFIT.BENEFIT_ID = BENEFIT_ID)")
	private String benefitName;
	
	private Double amount;
	
	private Date claimDate;
	
	private Date payDate;
	
	private String checkNum;
	
	private String note;
	
	private Date createDate;
	
	private Double grossAmount;
	
	private Boolean isDelete;

	public ClaimPayment() {
		super();
	}

	public ClaimPayment(Integer benefitId, Double amount, Date claimDate, Date payDate, String checkNum, String note, Date createDate, Double grossAmount, Boolean isDelete) {
		super();
		this.benefitId = benefitId;
		this.amount = amount;
		this.claimDate = claimDate;
		this.payDate = payDate;
		this.checkNum = checkNum;
		this.note = note;
		this.createDate = createDate;
		this.grossAmount = grossAmount;
		this.isDelete = isDelete;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	/*public Claim getClaim() {
		return claim;
	}*/

	public void setClaim(Claim claim) {
		this.claim = claim;
	}

	public Integer getBenefitId() {
		return benefitId;
	}

	public void setBenefitId(Integer benefitId) {
		this.benefitId = benefitId;
	}

	public String getBenefitName() {
		return benefitName;
	}

	public void setBenefitName(String benefitName) {
		this.benefitName = benefitName;
	}

	public Double getAmount() {
		return amount;
	}

	public void setAmount(Double amount) {
		this.amount = amount;
	}

	public Date getClaimDate() {
		return claimDate;
	}

	public void setClaimDate(Date claimDate) {
		this.claimDate = claimDate;
	}

	public Date getPayDate() {
		return payDate;
	}

	public void setPayDate(Date payDate) {
		this.payDate = payDate;
	}

	public String getCheckNum() {
		return checkNum;
	}

	public void setCheckNum(String checkNum) {
		this.checkNum = checkNum;
	}

	public String getNote() {
		return note;
	}

	public void setNote(String note) {
		this.note = note;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Double getGrossAmount() {
		return grossAmount;
	}

	public void setGrossAmount(Double grossAmount) {
		this.grossAmount = grossAmount;
	}

	public Boolean getIsDelete() {
		return isDelete;
	}

	public void setIsDelete(Boolean isDelete) {
		this.isDelete = isDelete;
	}

	@Override
	public String toString() {
		return "ClaimPayment [id=" + id + ", claim=" + claim + ", benefitId=" + benefitId + ", benefitName=" + benefitName + ", amount=" + amount + ", claimDate=" + claimDate + ", payDate=" + payDate
				+ ", checkNum=" + checkNum + ", note=" + note + ", createDate=" + createDate + ", grossAmount=" + grossAmount + ", isDelete=" + isDelete + "]";
	}
}
