package com.masa.pts.core.domain;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.masa.pts.core.constant.ProductFamilyType;

@Entity
@Table(name="EDI_FILE_PRODUCT_MAPPING",schema = "dbo")
public class ProductTypeMapping implements Serializable {
	private static final long serialVersionUID = -122008726206256756L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "ID")
	private Integer id;
	
	private String productType;
	private String paymentType;
	
	@Column(name = "single_family")
	private ProductFamilyType prdFamilyType;
	
	@Column(name = "SOLD_DATE_START")
	@Temporal(TemporalType.DATE)
	private Date soldDateStart;

	@Column(name = "SOLD_DATE_END")
	@Temporal(TemporalType.DATE)
	private Date soldDateEnd;
	
	private Integer productId;
	
	private Integer createdBy;
	private Integer modifiedBy;
	
	@Temporal(TemporalType.TIMESTAMP)
	private Date createdDate;
	
	@Temporal(TemporalType.TIMESTAMP)
	private Date modifiedDate;

	public ProductTypeMapping() {
		super();
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getProductType() {
		return productType;
	}

	public void setProductType(String productType) {
		this.productType = productType;
	}

	public String getPaymentType() {
		return paymentType;
	}

	public void setPaymentType(String paymentType) {
		this.paymentType = paymentType;
	}

	public Date getSoldDateStart() {
		return soldDateStart;
	}

	public void setSoldDateStart(Date soldDateStart) {
		this.soldDateStart = soldDateStart;
	}

	public Date getSoldDateEnd() {
		return soldDateEnd;
	}

	public void setSoldDateEnd(Date soldDateEnd) {
		this.soldDateEnd = soldDateEnd;
	}

	public Integer getProductId() {
		return productId;
	}

	public void setProductId(Integer productId) {
		this.productId = productId;
	}

	public Integer getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}

	public Integer getModifiedBy() {
		return modifiedBy;
	}

	public void setModifiedBy(Integer modifiedBy) {
		this.modifiedBy = modifiedBy;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public Date getModifiedDate() {
		return modifiedDate;
	}

	public void setModifiedDate(Date modifiedDate) {
		this.modifiedDate = modifiedDate;
	}

	public ProductFamilyType getPrdFamilyType() {
		return prdFamilyType;
	}

	public void setPrdFamilyType(ProductFamilyType prdFamilyType) {
		this.prdFamilyType = prdFamilyType;
	}	
}
