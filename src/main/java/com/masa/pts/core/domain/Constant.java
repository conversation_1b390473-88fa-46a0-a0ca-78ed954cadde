/**
 * 
 */
package com.masa.pts.core.domain;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class Constant {

	public static final int			FREQ_WEEK						= 1;
	public static final int			FREQ_BIWEEK						= 2;
	public static final int			FREQ_MONTH						= 3;
	public static final int			FREQ_YEAR						= 4;
	public static final int			FREQ_ONCE						= 5;
	public static final int			FREQ_LIFETIME					= 6;
	public static final int			FREQ_SEMI_MONTHLY				= 7;						
	public static final int			FREQ_FIVE_YEAR					= 8;
	public static final int			FREQ_BIENNIAL					= 12;
	public static final int			FREQ_TRIENNIAL					= 13;
	public static final int			FREQ_QUADRENNIAL				= 14;
	public static final int			FREQ_DECENNIAL					= 20;
	
	public static final Date 		DEFULT_DATE_1900 =  Date.from(LocalDate.of(1900, 1, 1).atStartOfDay(java.time.ZoneId.systemDefault()).toInstant());
	public static final long 		DEFULT_MILLISECONDS_1900 = -2208970800000L; 
	
	public static final Date 		DEFULT_DATE_1901 =  Date.from(LocalDate.of(1901, 1, 1).atStartOfDay(java.time.ZoneId.systemDefault()).toInstant());
	
	public static final Date 		DEFAULT_PRODUCT_END_DATE =  Date.from(LocalDate.of(2099, 12, 31).atStartOfDay(java.time.ZoneId.systemDefault()).toInstant());
	
	public static final LocalDate 		DEFAULT_PRODUCT_END_LOCAL_DATE =  LocalDate.of(2099, 12, 31);
	
	public static final int 		DEFAULT_USER_ID         = 322;
	
	public static final int 		DEFAULT_WEBAPP_USER_ID         = 300;
	
	public static final String      DEFAULT_PAYMENT_USERNAME = "paymentapp";
	
	public static final String      DEFAULT_PTS_USERNAME = "ptsapp";
	
	public static final String NULL_STRING = new String();

	public static final boolean NULL_BOOL = false;
	
	public static final List<Integer> SETUP_FEE_IDS = Arrays.asList(1);
	
	public static final List<Integer> INSTALLMENT_FEE_IDS = Arrays.asList(8,9);
	
	public static final List<Integer> INSTALLMENT_ADMIN_PRODUCT_IDS = Arrays.asList(182,268);

	public static final int BIG_DECIMAL_DEFAULT_SCALE = 4;
	public static final int BIG_DECIMAL_DEFAULT_ROUNGDING_MODE = BigDecimal.ROUND_HALF_EVEN;
	
	public static final List<String> PRECOA_GROUP_CODES = Arrays.asList("PRECM");
	
	public static SimpleDateFormat	dateFormat	= (SimpleDateFormat) DateFormat.getInstance();

	public static String DEFAULT_DATE_PATTERN = "MM/dd/yyyy";
	
	public static long THREASHOLD_TO_LOG = 1000;
	
	public static final int DEFAULT_SHIPPING_METHOD_ID = 1;
	
	//Allow new membership to be created in different sales channel if member already have membership in listed sales channel. 
	public static final List<String> ALLOW_NEW_WITH_EXISTING_MEMBERSHIP_IN_GRP = Arrays.asList("");
	
	public static final List<String> EMERGENT_PRD_CODES = Arrays.asList("EM","EMG","EMP");
	
	public static final List<String> PRODUCT_CHANGE_BY_GRORUP_DIVISION = Arrays.asList("B2B","International","MTS","B2C","Other","BCBS/Elan/GTM","TRS","IN_HOUSE","Retention");
	public static final List<String> PRODUCT_CHANGE_BY_GRORUP_SC = Arrays.asList("");
	public static final List<String> PRODUCT_CHANGE_BY_TABLE_SC = Arrays.asList("AMBA - SLS","AMBA","AMBA - Florida","AMBA – Wyoming","Wyoming","AMBA – Wyoming","B2C-DTC WY","B2C-Brokers WY","Ecommerce - Corporate - WY","Ecommerce - Digital - WY","Ecommerce - Direct Mail - WY","Ecommerce - TV/Radio - WY");
	public static final List<String> PRODUCT_CHANGE_BY_TABLE_DIVISION = Arrays.asList("");
	
	public static final int B2B_DIVISION_ID = 3;
	public static final int B2C_DIVISION_ID = 4;
	public static final int INTERNATIONAL_DIVISION_ID = 1;
	public static final int RETENTION_DIVISION_ID = 9;
	public static final int MONTHS_RANGE_FOR_ENROLL = 3;

	public static final List<String> MERCHANT_IDS = Arrays.asList("107625", "108443", "138483", "168555", "253748");
}
