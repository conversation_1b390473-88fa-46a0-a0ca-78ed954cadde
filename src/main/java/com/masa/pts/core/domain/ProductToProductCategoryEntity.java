package com.masa.pts.core.domain;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Objects;

@Entity
// need to use it in uppercase because hibernate parse camel-case to under_scores case, same for fields
@Table(name = "TLNK_PRODUCT_PRODUCTCATEGORY", schema = "dbo")
public class ProductToProductCategoryEntity implements Serializable {

	private static final long serialVersionUID = -4509026054975645688L;

	@Id
	@Column(name = "IDPRODUCT")
	private Integer productId;

	@Column(name = "IDPRODUCTCATEGORY")
	private Integer productCategoryId;

	public Integer getProductId() {
		return productId;
	}

	public void setProductId(Integer productId) {
		this.productId = productId;
	}

	public Integer getProductCategoryId() {
		return productCategoryId;
	}

	public void setProductCategoryId(Integer productCategoryId) {
		this.productCategoryId = productCategoryId;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) return true;
		if (o == null || getClass() != o.getClass()) return false;
		ProductToProductCategoryEntity that = (ProductToProductCategoryEntity) o;
		return Objects.equals(productId, that.productId) &&
				Objects.equals(productCategoryId, that.productCategoryId);
	}

	@Override
	public int hashCode() {
		return Objects.hash(productId, productCategoryId);
	}
}
