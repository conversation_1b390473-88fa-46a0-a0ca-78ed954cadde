package com.masa.pts.core.domain;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import com.masa.pts.core.constant.MemberContactEventStatus;
import com.masa.pts.core.constant.MemberContactEventType;

@Entity
@Table(name="TDAT_MEMBER_EVENT",schema = "dbo")
public class MemberEvent implements Serializable {

	private static final long serialVersionUID = -6704884771951708696L;
	
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="id")
	private Integer id;
	
	@Column(name="member_id")
	private Integer memberId;
	
	@Column(name="event_type")
	private Integer eventType;

	@Column(name="status")
	private Integer status;
	
	private Date createdDate;
	
	private Integer createdBy;
	
	private Date modifiedDate;
	
	private Integer modifiedBy;

	public MemberEvent() {
		super();
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getMemberId() {
		return memberId;
	}

	public void setMemberId(Integer memberId) {
		this.memberId = memberId;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public Integer getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}

	public Date getModifiedDate() {
		return modifiedDate;
	}

	public void setModifiedDate(Date modifiedDate) {
		this.modifiedDate = modifiedDate;
	}

	public Integer getModifiedBy() {
		return modifiedBy;
	}

	public void setModifiedBy(Integer modifiedBy) {
		this.modifiedBy = modifiedBy;
	}
	
	public MemberContactEventType getEventType() {
		return MemberContactEventType.getType(eventType);
	}

	public void setEventType(MemberContactEventType eventType) {
		if(null == eventType)
			this.eventType = 0;
		else
			this.eventType = eventType.getType();
	}

	public MemberContactEventStatus getStatus() {
		return MemberContactEventStatus.getStatus(status);
	}

	public void setStatus(MemberContactEventStatus status) {
		if(null == status)
			this.status = MemberContactEventStatus.CREATED.getStatus();
		else
			this.status = status.getStatus();
	}
}
