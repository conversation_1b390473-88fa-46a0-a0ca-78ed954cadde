package com.masa.pts.core.domain;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.hibernate.annotations.Formula;
import org.hibernate.annotations.Immutable;

@Entity
@Table(name="TDAT_CLAIM",schema = "dbo")
@Immutable
public class ClaimSearch implements Serializable {

	private static final long serialVersionUID = -4599551038123865844L;

	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="claim_id")
	private Integer claimID;
	
	private Integer status;
	
	@Transient
	private String statusName;
	
	@Formula("(SELECT TDAT_MEMBER.FIRST_NAME + ' ' + TDAT_MEMBER.MI + '' + TDAT_MEMBER.LAST_NAME FROM TDAT_MEMBER WHERE TDAT_MEMBER.MEMBER_ID = MEMBER_ID)")
	private String memberFullName;
	
	/*@ManyToOne
	@JoinColumn(name="member_id") 
	private Member member;*/
	
	@Column(name="member_id")
	private Integer memberId;
	
	private Double total;
	
	@Column(name="close_date")
	private Date closeDate;
	
	private Boolean active;
	
	@Column(name="create_date")
	private Date createdDate;
	
	@Column(name="original_amount")
	private Double originalAmount;
	
	@Column(name="insurance_paid_amount")
	private Double insurancePaidAmount;
	
	@Column(name="is_delete",nullable = true)
	private Boolean isDelete;
	
	@Column(name="patient_name")
	private String patientName;

	public ClaimSearch() {
		super();
	}

	public ClaimSearch(Integer claimID, Integer status, String statusName, Double total, Date closeDate, Boolean active, Date createdDate, Double originalAmount, Double insurancePaidAmount,
			Boolean isDelete) {
		super();
		this.claimID = claimID;
		this.status = status;
		this.statusName = statusName;
		this.total = total;
		this.closeDate = closeDate;
		this.active = active;
		this.createdDate = createdDate;
		this.originalAmount = originalAmount;
		this.insurancePaidAmount = insurancePaidAmount;
		this.isDelete = isDelete;
	}

	public Integer getClaimID() {
		return claimID;
	}

	public void setClaimID(Integer claimID) {
		this.claimID = claimID;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getStatusName() {
		switch(getStatus()) {
			case 1: return "Pending"; 
			case 2: return "Approved";
			case 3: return "Declined";		
			case 4: return "Inquiry"; 
			default : return "Invalid";
			}
	}

	public void setStatusName(String statusName) {
		this.statusName = statusName;
	}

	/*public Member getMember() {
		return member;
	}

	public void setMember(Member member) {
		this.member = member;
	}*/

	public Double getTotal() {
		return total;
	}

	public void setTotal(Double total) {
		this.total = total;
	}

	public Date getCloseDate() {
		return closeDate;
	}

	public void setCloseDate(Date closeDate) {
		this.closeDate = closeDate;
	}

	public Boolean getActive() {
		return active;
	}

	public void setActive(Boolean active) {
		this.active = active;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public Double getOriginalAmount() {
		return originalAmount;
	}

	public void setOriginalAmount(Double originalAmount) {
		this.originalAmount = originalAmount;
	}

	public Double getInsurancePaidAmount() {
		return insurancePaidAmount;
	}

	public void setInsurancePaidAmount(Double insurancePaidAmount) {
		this.insurancePaidAmount = insurancePaidAmount;
	}

	public Boolean getIsDelete() {
		return isDelete;
	}

	public void setIsDelete(Boolean isDelete) {
		this.isDelete = isDelete;
	}
	public String getMemberFullName() {
		return memberFullName;
	}

	public void setMemberFullName(String memberFullName) {
		this.memberFullName = memberFullName;
	}

	public Integer getMemberId() {
		return memberId;
	}

	public void setMemberId(Integer memberId) {
		this.memberId = memberId;
	}

	public String getPatientName() {
		return patientName;
	}

	public void setPatientName(String patientName) {
		this.patientName = patientName;
	}		

}
