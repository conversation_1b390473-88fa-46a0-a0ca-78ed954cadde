package com.masa.pts.core.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.OrderBy;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonView;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.masa.pts.core.model.ProductSale;
import com.masa.pts.core.model.TPAPortalView;

@Entity
@Table(name="TDAT_GROUP",schema = "dbo")
public class GroupEntity implements Serializable {

	private static final long serialVersionUID = 5378690379627138050L;

	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="group_id")
	private Integer groupId;

	@OneToOne(fetch = FetchType.LAZY, cascade=CascadeType.ALL, orphanRemoval = true)
	@JoinColumn(name = "mailing_address", referencedColumnName = "address_id")
	@JsonView(TPAPortalView.Group.class)
	@JsonSerialize(as = Address.class)
	private Address mailingAddressDetails;

	@OneToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "company_id")
	@JsonSerialize(as = Company.class)
	private Company company;

	// todo need this connection to save the salesChannels, need to refactor it after discussion with other teams
	//  about salesChannel database structure
	@OneToOne(fetch = FetchType.LAZY, cascade = {CascadeType.ALL})
	@JoinTable(
			name = "tlnk_Group_Line",
			joinColumns = @JoinColumn(name = "IDGROUP"),
			inverseJoinColumns = @JoinColumn(name = "IDLINE"))
	@JsonSerialize(as = BusinessLineEntity.class)
	private BusinessLineEntity businessLineEntity = new BusinessLineEntity();

	@OneToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "broker_agent_id")
	@JsonSerialize(as = Agent.class)
	@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
	@NotFound(action = NotFoundAction.IGNORE)
	private Agent brokerAgent;

	@OneToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "agent_id_mbr_import")
	@JsonSerialize(as = Agent.class)
	@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
	@NotFound(action = NotFoundAction.IGNORE)
	private Agent agent;

	@JsonView({TPAPortalView.Group.class})
	@OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, mappedBy = "group", orphanRemoval = true)
	private Set<GroupContactEntity> contacts = new HashSet<>();

	@OneToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "modified_by")
	@JsonSerialize(as = PTSUser.class)
	@NotFound(action = NotFoundAction.IGNORE)
	private PTSUser userWhoModified;

	@OneToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "created_by")
	@JsonSerialize(as = PTSUser.class)
	@NotFound(action = NotFoundAction.IGNORE)
	private PTSUser userWhoCreated;

	@OneToMany(cascade = CascadeType.ALL, mappedBy = "group", orphanRemoval = true, fetch=FetchType.LAZY)
	@OrderBy(value="product_id, fee_id")
	private Set<GroupProductFee> grpPrdList = new HashSet<>();

	@JsonView({TPAPortalView.Group.class,TPAPortalView.Member.class})
	@Column(name = "group_code")
	private String groupCode;
	
	@JsonView({TPAPortalView.Group.class,TPAPortalView.Member.class})
	@Column(name = "group_name")
	private String groupName;

	@Column(name = "bill_type")
	private Integer billType;

	@Column(name="pastdue_inactive")
	private Integer pastDueInactive;

	@Column(name = "print_card")
	private boolean printCard;

	@Column(name = "print_renewal")
	private boolean printRenewal;

	@Column(name = "note")
	private String note;
	
	@JsonView(TPAPortalView.Group.class)
	@Column(name = "active")
	private boolean active;

	@Column(name = "label")
	private boolean label;

	@Column(name = "bill_company")
	private boolean billCompany;
	
	@Column(name="round_monthlyfee")
	private Integer roundMonthlyFee;
	
	@Column(name="payment_months_behind")
	private Integer paymentMonthsBehind;
	
	@Column(name="receives_email_invoice")
	private boolean receivesEmailInvoice;

	@Column(name="group_effective_date")
	private Date groupEffectiveDate;

	@Column(name = "sold_date")
	private Date soldDate;

	@Column(name = "plan_type")
	private Integer planType;
	
	@Column(name="auto_pay")
	private Boolean autoPay;

	@Column(name="created_date")
	private Date createdDate;

	@Column(name = "modified_date")
	private Date modifiedDate;
	
	@Column(name="down_payment_grp")
	private Boolean downPaymentGrp=Boolean.FALSE;
	
	@Column(name="lifetime_grp")
	private Boolean lifetimeGrp = Boolean.FALSE;
	
	@Column(name="past_due_effective_date")
	private Date pastDueEffectiveDate;
	
	@Column(name="credit_debit_amount")
	private BigDecimal creditDebitAmount=BigDecimal.ZERO;
	
	@Column(name="parent_group_id")
	private Integer parentGroupId;	
	
	@Column(name="is_net_comm")
	private Boolean isNetComm = Boolean.FALSE;
	
	@Column(name="no_adv_comm")
	private Boolean isAdvComm = Boolean.FALSE;

	@Transient
	@JsonView(TPAPortalView.Group.class)
	private List<ProductSale> saleProducts = new ArrayList<>();
	
	@Transient
	@JsonView({TPAPortalView.Group.class,TPAPortalView.Member.class})
	private String logoPath ;
	
	private Integer membershipSource;
	private Integer brokerPlatforms;
	private Integer paymentTerms;
	private boolean autoSuspendCancelMember;

	public GroupEntity() { }

	public Integer getGroupId() {
		return groupId;
	}

	public Company getCompany() {
		return company;
	}

	public void setCompany(Company company) {
		this.company = company;
	}

	public PTSUser getUserWhoModified() {
		return userWhoModified;
	}

	public void setUserWhoModified(PTSUser userWhoModified) {
		this.userWhoModified = userWhoModified;
	}

	public Agent getBrokerAgent() {
		return brokerAgent;
	}

	public void setBrokerAgent(Agent brokerAgent) {
		this.brokerAgent = brokerAgent;
	}

	public Agent getAgent() {
		return agent;
	}

	public void setAgent(Agent agent) {
		this.agent = agent;
	}

	public Set<GroupContactEntity> getContacts() {
		return contacts;
	}

	// need to set this collection in that way because Hibernate will failed if the link to collection will be changed
	public void setContacts(Set<GroupContactEntity> contacts) {
		this.contacts.clear();
		this.contacts.addAll(contacts);
	}

	public BusinessLineEntity getBusinessLineEntity() {
		return businessLineEntity;
	}

	public void setBusinessLineEntity(BusinessLineEntity businessLineEntity) {
		this.businessLineEntity = businessLineEntity;
	}

	public void setGroupId(Integer groupId) {
		this.groupId = groupId;
	}

	public String getGroupCode() {
		return groupCode;
	}

	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}

	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

	public Integer getBillType() {
		return billType;
	}

	public void setBillType(Integer billType) {
		this.billType = billType;
	}

	public Integer getPastDueInactive() {
		return pastDueInactive;
	}

	public void setPastDueInactive(Integer pastDueInactive) {
		this.pastDueInactive = pastDueInactive;
	}

	public boolean getPrintCard() {
		return printCard;
	}

	public void setPrintCard(boolean printCard) {
		this.printCard = printCard;
	}

	public boolean getPrintRenewal() {
		return printRenewal;
	}

	public void setPrintRenewal(boolean printRenewal) {
		this.printRenewal = printRenewal;
	}

	public String getNote() {
		return note;
	}

	public void setNote(String note) {
		this.note = note;
	}

	public boolean isActive() {
		return active;
	}

	public void setActive(boolean active) {
		this.active = active;
	}

	public boolean getLabel() {
		return label;
	}

	public void setLabel(boolean label) {
		this.label = label;
	}
	
	public boolean isBillCompany() {
		return billCompany;
	}

	public void setBillCompany(boolean billCompany) {
		this.billCompany = billCompany;
	}

	public Integer getRoundMonthlyFee() {
		return roundMonthlyFee;
	}

	public void setRoundMonthlyFee(Integer roundMonthlyFee) {
		this.roundMonthlyFee = roundMonthlyFee;
	}

	public Integer getPaymentMonthsBehind() {
		return paymentMonthsBehind;
	}

	public void setPaymentMonthsBehind(Integer paymentMonthsBehind) {
		this.paymentMonthsBehind = paymentMonthsBehind;
	}

	public boolean isReceivesEmailInvoice() {
		return receivesEmailInvoice;
	}

	public void setReceivesEmailInvoice(boolean receivesEmailInvoice) {
		this.receivesEmailInvoice = receivesEmailInvoice;
	}

	public Address getMailingAddressDetails() {
		return mailingAddressDetails;
	}

	public void setMailingAddressDetails(Address mailingAddressDetails) {
		this.mailingAddressDetails = mailingAddressDetails;
	}

	public Set<GroupProductFee> getGrpPrdList() {
		return grpPrdList;
	}

	public void setGrpPrdList(Set<GroupProductFee> grpPrdList) {
		this.grpPrdList.clear();
		this.grpPrdList.addAll(grpPrdList);
	}

	public Date getGroupEffectiveDate() {
		return groupEffectiveDate;
	}

	public void setGroupEffectiveDate(Date groupEffectiveDate) {
		this.groupEffectiveDate = groupEffectiveDate;
	}

	public Date getSoldDate() {
		return soldDate;
	}

	public void setSoldDate(Date soldDate) {
		this.soldDate = soldDate;
	}

	public Integer getPlanType() {
		return planType;
	}

	public void setPlanType(Integer planType) {
		this.planType = planType;
	}
	
	public Boolean getAutoPay() {
		return autoPay;
	}

	public void setAutoPay(Boolean autoPay) {
		this.autoPay = autoPay;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public PTSUser getUserWhoCreated() {
		return userWhoCreated;
	}

	public void setUserWhoCreated(PTSUser userWhoCreated) {
		this.userWhoCreated = userWhoCreated;
	}

	public Date getModifiedDate() {
		return modifiedDate;
	}

	public void setModifiedDate(Date modifiedDate) {
		this.modifiedDate = modifiedDate;
	}

	public Boolean getDownPaymentGrp() {
		return downPaymentGrp;
	}

	public void setDownPaymentGrp(Boolean downPaymentGrp) {
		this.downPaymentGrp = downPaymentGrp;
	}

	public Boolean getLifetimeGrp() {
		return lifetimeGrp;
	}

	public void setLifetimeGrp(Boolean lifetimeGrp) {
		this.lifetimeGrp = lifetimeGrp;
	}

	public Date getPastDueEffectiveDate() {
		return pastDueEffectiveDate;
	}

	public void setPastDueEffectiveDate(Date pastDueEffectiveDate) {
		this.pastDueEffectiveDate = pastDueEffectiveDate;
	}

	public BigDecimal getCreditDebitAmount() {
		return creditDebitAmount;
	}

	public void setCreditDebitAmount(BigDecimal creditDebitAmount) {
		this.creditDebitAmount = creditDebitAmount;
	}

	public Integer getParentGroupId() {
		return parentGroupId;
	}

	public void setParentGroupId(Integer parentGroupId) {
		this.parentGroupId = parentGroupId;
	}

	public List<ProductSale> getSaleProducts() {
		return saleProducts;
	}

	public void setSaleProducts(List<ProductSale> saleProducts) {
		this.saleProducts = saleProducts;
	}

	public Boolean getIsNetComm() {
		return isNetComm;
	}

	public void setIsNetComm(Boolean isNetComm) {
		this.isNetComm = isNetComm;
	}

	public Boolean getIsAdvComm() {
		return isAdvComm;
	}

	public void setIsAdvComm(Boolean isAdvComm) {
		this.isAdvComm = isAdvComm;
	}

	public String getLogoPath() {
		return logoPath;
	}

	public void setLogoPath(String logoPath) {
		this.logoPath = logoPath;
	}

	public Integer getMembershipSource() {
		return membershipSource;
	}

	public void setMembershipSource(Integer membershipSource) {
		this.membershipSource = membershipSource;
	}

	public Integer getBrokerPlatforms() {
		return brokerPlatforms;
	}

	public void setBrokerPlatforms(Integer brokerPlatform) {
		this.brokerPlatforms = brokerPlatform;
	}

	public Integer getPaymentTerms() {
		return paymentTerms;
	}

	public void setPaymentTerms(Integer paymentTerm) {
		this.paymentTerms = paymentTerm;
	}

	public boolean isAutoSuspendCancelMember() {
		return autoSuspendCancelMember;
	}

	public void setAutoSuspendCancelMember(boolean autoSuspendCancelMember) {
		this.autoSuspendCancelMember = autoSuspendCancelMember;
	}

	public interface GroupDTO {
		String getBusinessLineEntitySalesChannelDivisionName();
		Integer getBusinessLineEntitySalesChannelDivisionId();
		
		String getBusinessLineEntitySalesChannelName();
		Integer getBusinessLineEntitySalesChannelId();
		
		String getGroupCode();
		String getGroupName();
		Integer getBillType();
		Integer getPastDueInactive();
		boolean getPrintCard();
		boolean getPrintRenewal();
		String getNote();
		boolean isActive();
		boolean getLabel();
		boolean isBillCompany();
		Integer getRoundMonthlyFee();
		Integer getPaymentMonthsBehind();
		boolean isReceivesEmailInvoice();
		Date getGroupEffectiveDate();
		Date getSoldDate();
		Integer getPlanType();
		Boolean getAutoPay();
		Date getCreatedDate();
		Date getModifiedDate();
		Boolean getDownPaymentGrp();
		Boolean getLifetimeGrp();
		Date getPastDueEffectiveDate();
		BigDecimal getCreditDebitAmount();
		Integer getParentGroupId();
		Boolean getIsNetComm();
		Boolean getIsAdvComm();
		
		Integer getAgentAgentId();
		String getAgentAgentFullName();
		Integer getBrokerAgentAgentId();
		String getBrokerAgentAgentFullName();
		
		String getCompanyCompanyName();
		String getUserWhoModifiedUsername();
		
		//this works
		MailingAddress getMailingAddressDetails();
		interface MailingAddress {
			Integer getAddressId();
			String getAddress1();
			String getAddress2();
			String getAddress3();
			String getCity();
			String getState();
		 }
	}
	
	
	public interface GroupSummary {
		Integer getGroupId();
		String getGroupCode();
		String getGroupName();
		boolean isActive();
		Integer getPaymentMonthsBehind();
		Integer getBillType();
		String getCompanyCompanyName();
		//String getContactFirst();
		//String getContactLast();
		//String getPhone();
		/*
		String getBusinessLineEntitySalesChannelDivisionName();
		Integer getBusinessLineEntitySalesChannelDivisionId();
		
		String getBusinessLineEntitySalesChannelName();
		Integer getBusinessLineEntitySalesChannelId();
		*/
		Date getPastDueEffectiveDate();
	}
	
	public interface GroupID {
		Integer getGroupId();
	}

	public interface GroupCodeSmry {
		String getGroupCode();
	}
	public interface GroupIDCodeSmry{
		Integer getGroupId();
		String getGroupCode();
	}
	//TODO test
	/*public interface GroupDetailWithSalesChannelDivision extends GroupCodeSmry {
		String getBusinessLineEntitySalesChannelDivisionName();
		Integer getBusinessLineEntitySalesChannelDivisionId();
		
		String getBusinessLineEntitySalesChannelName();
		Integer getBusinessLineEntitySalesChannelId();
	}
	*/

	@Override
	public String toString() {
		return "GroupEntity{" +
				"groupId=" + groupId +
				", mailingAddressDetails=" + mailingAddressDetails +
				", company=" + company +
				", businessLineEntity=" + businessLineEntity +
				", brokerAgent=" + brokerAgent +
				", agent=" + agent +
				", contacts=" + contacts +
				", userWhoModified=" + userWhoModified +
				", userWhoCreated=" + userWhoCreated +
				", grpPrdList=" + grpPrdList +
				", groupCode='" + groupCode + '\'' +
				", groupName='" + groupName + '\'' +
				", billType=" + billType +
				", pastDueInactive=" + pastDueInactive +
				", printCard=" + printCard +
				", printRenewal=" + printRenewal +
				", note='" + note + '\'' +
				", active=" + active +
				", label=" + label +
				", billCompany=" + billCompany +
				", roundMonthlyFee=" + roundMonthlyFee +
				", paymentMonthsBehind=" + paymentMonthsBehind +
				", receivesEmailInvoice=" + receivesEmailInvoice +
				", groupEffectiveDate=" + groupEffectiveDate +
				", soldDate=" + soldDate +
				", planType=" + planType +
				", autoPay=" + autoPay +
				", createdDate=" + createdDate +
				", modifiedDate=" + modifiedDate +
				", downPaymentGrp=" + downPaymentGrp +
				", lifetimeGrp=" + lifetimeGrp +
				", pastDueEffectiveDate=" + pastDueEffectiveDate +
				", creditDebitAmount=" + creditDebitAmount +
				", parentGroupId=" + parentGroupId +
				", isNetComm=" + isNetComm +
				", isAdvComm=" + isAdvComm +
				", saleProducts=" + saleProducts +
				", logoPath='" + logoPath + '\'' +
				", membershipSource='" + membershipSource + '\'' +
				", brokerPlatform='" + brokerPlatforms + '\'' +
				", paymentTerm='" + paymentTerms + '\'' +
				", autoSuspendCancelMember=" + autoSuspendCancelMember +
				'}';
	}
}
