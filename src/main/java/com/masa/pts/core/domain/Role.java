package com.masa.pts.core.domain;

import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import org.hibernate.annotations.Where;

import com.fasterxml.jackson.annotation.JsonIgnore;


@Entity
@Table(name="TLST_ROLE",schema = "dbo")
@Where(clause = "is_obsolete = 0")
public class Role implements Serializable {

	private static final long serialVersionUID = -8368845024357448756L;
	
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="id")
	private Integer id;
	
	@Column(name="code")
	private String code;
	
	@Column(name="description")
	private String description;
	
	@JsonIgnore
	private boolean isObsolete;
	
	@JsonIgnore
	private Date obsoleteDate;
	
	@OneToMany(fetch = FetchType.LAZY)
	@JoinTable(
			name="tlnk_role_access",
			joinColumns = @JoinColumn(name="role_id"),
			inverseJoinColumns = @JoinColumn(name="access_id")
			)
	private Set<UserAccess> accessDetails = new HashSet<>();

	public Role() {
		super();
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public boolean isObsolete() {
		return isObsolete;
	}

	public Role setObsolete(boolean isObsolete) {
		this.isObsolete = isObsolete;
		return this;
	}

	public Date getObsoleteDate() {
		return obsoleteDate;
	}

	public void setObsoleteDate(Date obsoleteDate) {
		this.obsoleteDate = obsoleteDate;
	}

	public Set<UserAccess> getAccessDetails() {
		return accessDetails;
	}
	
	public void addUserAccess(UserAccess userAccess) {
		this.accessDetails.add(userAccess);
	}

	public void setAccessDetails(Set<UserAccess> accessDetails) {
		this.accessDetails = accessDetails;
	}	
}
