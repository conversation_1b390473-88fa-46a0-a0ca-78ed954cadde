package com.masa.pts.core.domain;

import java.util.Arrays;
import java.util.Optional;

public enum BillCompanyType {
	MONTHLY(0),
	ANNUAL(1);

	private final int type;

	BillCompanyType(int type) {
		this.type = type;
	}

	public Integer getType() {
		return type;
	}

	public static Optional<BillCompanyType> getGroupBillCompanyTypeByType(Integer type) {
		return Arrays.stream(values()).filter(value -> value.getType().equals(type)).findFirst();
	}
	
	public static Integer getBillCompanyTypeByValue(boolean value) {
		return value ? ANNUAL.getType() : MONTHLY.getType();
	}
}
