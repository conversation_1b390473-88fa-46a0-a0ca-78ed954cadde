package com.masa.pts.core.domain;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;

@Entity(name="Company")
@Table(name="TDAT_COMPANY",schema = "dbo")             
public class Company implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1956851869274222459L;

	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="company_id")	
	private Integer companyId;
	
	@Column(name="companyname")
	private String companyName;
	
	private Integer address;
	
	@OneToOne(fetch=FetchType.LAZY)
	@JoinColumn(name = "address", insertable=false, updatable=false)
	@JsonSerialize(as = Address.class)
	private Address addressDetails;
	
	private String phone;
	
	private String fax;
	
	private boolean active;
	
	private String contact;

	@OneToMany(fetch=FetchType.LAZY)
	@JoinColumn(name="companyId",insertable=false,updatable=false)	
	private List<Product> products = new ArrayList<>();
	
	public Company() {
		super();
	}

	public Company(Integer companyId) {
		this.companyId = companyId;
	}
	
	public interface CompanyInfo{
		Integer getCompanyId();
		String getCompanyName();
	}
	
	public Company(String companyName, Integer address,  String phone, String fax,
			boolean active, String contact) {
		super();
		this.companyName = companyName;
		this.address = address;		
		this.phone = phone;
		this.fax = fax;
		this.active = active;
		this.contact = contact;
	}

	public Integer getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Integer companyId) {
		this.companyId = companyId;
	}

	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	public Integer getAddress() {
		return address;
	}

	public void setAddress(Integer address) {
		this.address = address;
	}

	public Address getAddressDetails() {
		return addressDetails;
	}

	public void setAddressDetails(Address addressDetails) {
		this.addressDetails = addressDetails;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getFax() {
		return fax;
	}

	public void setFax(String fax) {
		this.fax = fax;
	}

	public boolean isActive() {
		return active;
	}

	public void setActive(boolean active) {
		this.active = active;
	}

	public String getContact() {
		return contact;
	}

	public void setContact(String contact) {
		this.contact = contact;
	}

	public List<Product> getProducts() {
		return products;
	}

	public void setProducts(List<Product> products) {
		this.products = products;
	}

	public interface CompanySummary {
		Integer getCompanyId();
		String getCompanyName();
	}
}
