package com.masa.pts.core.domain;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;

import org.hibernate.annotations.Formula;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonView;
import com.masa.pts.core.model.TPAPortalView;

@Entity
@Table(name="TDAT_ADDRESS",schema = "dbo")
public class Address implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4263445758085503337L;

	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="address_id")
	@JsonView({MobileView.Member.class,TPAPortalView.Group.class,TPAPortalView.Member.class})
	private Integer addressId;
	
	@JsonView({MobileView.Member.class,TPAPortalView.Group.class,TPAPortalView.Member.class})
	private String address1;
	
	@JsonView({MobileView.Member.class,TPAPortalView.Group.class,TPAPortalView.Member.class})
	private String address2;
	
	@JsonView({MobileView.Member.class,TPAPortalView.Group.class,TPAPortalView.Member.class})
	private String address3;
	
	@JsonView({MobileView.Member.class,TPAPortalView.Group.class,TPAPortalView.Member.class})
	private String city;
	
	@JsonView({MobileView.Member.class,TPAPortalView.Group.class,TPAPortalView.Member.class})
	@JsonIgnore(value=true)
	private Integer state;
	
	@JsonView({MobileView.Member.class,TPAPortalView.Group.class,TPAPortalView.Member.class})
	@JsonIgnore(value=true)
	private Integer country;
	
	@JsonView({MobileView.Member.class,TPAPortalView.Group.class,TPAPortalView.Member.class})
	private String zip;
	
	@JsonView({MobileView.Member.class,TPAPortalView.Group.class,TPAPortalView.Member.class})
	private String zip4;
	
	@Column(name="last_modified")
	@JsonIgnore(value=true)
	private Date lastModifiedDate;

	@JsonIgnore(value=true)
	private String otherInfo;
	
	@JsonIgnore(value=true)
	private Integer otherId;
	
	@JsonView({MobileView.Member.class,TPAPortalView.Group.class,TPAPortalView.Member.class})
	@OneToOne(fetch = FetchType.LAZY)
	@NotFound(action=NotFoundAction.IGNORE)
	@JoinColumn(name = "state", referencedColumnName = "state_id",insertable=false,updatable=false)
	private State stateDetails;
	
	@JsonView({MobileView.Member.class,TPAPortalView.Group.class,TPAPortalView.Member.class})
	@OneToOne(fetch = FetchType.LAZY)
	@NotFound(action=NotFoundAction.IGNORE)
	@JoinColumn(name = "country", referencedColumnName = "country_id",insertable=false,updatable=false)
	private Country countryDetails;

	public Address() { }

	public Integer getAddressId() {
		return addressId;
	}

	public void setAddressId(Integer addressId) {
		this.addressId = addressId;
	}

	public String getAddress1() {
		return address1;
	}

	public void setAddress1(String address1) {
		this.address1 = address1;
	}

	public String getAddress2() {
		return address2;
	}

	public void setAddress2(String address2) {
		this.address2 = address2;
	}

	public String getAddress3() {
		return address3;
	}

	public void setAddress3(String address3) {
		this.address3 = address3;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public Integer getCountry() {
		return country;
	}

	public void setCountry(Integer country) {
		this.country = country;
	}

	public String getZip() {
		return zip;
	}

	public void setZip(String zip) {
		this.zip = zip;
	}

	public String getZip4() {
		return zip4;
	}

	public void setZip4(String zip4) {
		this.zip4 = zip4;
	}

	public Date getLastModifiedDate() {
		return lastModifiedDate;
	}

	public void setLastModifiedDate(Date lastModifiedDate) {
		this.lastModifiedDate = lastModifiedDate;
	}

	public String getOtherInfo() {
		return otherInfo;
	}

	public void setOtherInfo(String otherInfo) {
		this.otherInfo = otherInfo;
	}

	public Integer getOtherId() {
		return otherId;
	}

	public void setOtherId(Integer otherId) {
		this.otherId = otherId;
	}

	public String formatAddress()
	{
		StringBuilder sb = new StringBuilder();
		sb.append(this.address1).append("  ").append(this.address2 == null ? "" : this.address2).append("  ").append(this.city).append("  ").append(this.stateDetails == null ? "" : this.stateDetails.getSymbol()).append("  ").append(this.zip).append("  ").append(this.zip4).append("  ");
		sb.append(this.countryDetails !=null ? this.countryDetails.getName() : "");
		return sb.toString();
	}

	public State getStateDetails() {
		return stateDetails;
	}

	public void setStateDetails(State stateDetails) {
		this.stateDetails = stateDetails;
	}

	public Country getCountryDetails() {
		return countryDetails;
	}

	public void setCountryDetails(Country countryDetails) {
		this.countryDetails = countryDetails;
	}	
	/*public void setGroup(GroupEntity group) {
		this.group = group;
	}*/
}
