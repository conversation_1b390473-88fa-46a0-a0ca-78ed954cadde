package com.masa.pts.core.domain;

import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.OrderColumn;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import javax.validation.constraints.Size;

@Entity
@Table(name="EDI_FILE_MEMBER",schema = "dbo")
public class MemberFile implements Serializable {
	
	private static final long serialVersionUID = -9159155746681149427L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "ID")
	private Integer id;

	@Size(max=20,message="Contract Number/Employee ID exceeds maximum length allowed.")
	private String contractNumber;
	
	@Size(max=10,message="Modification field data exceeds maximum length allowed.")
	@Column(name = "action")
	private String modification;
		
	@Column(name = "product")
	private String productType;
	
	private String relationship;
		
	@Size(max=50,message="First name exceeds maximum length allowed.")
	private String firstName;		
	
	@Size(max=50,message="Middle name exceeds maximum length allowed.")
	private String middleName;
	
	@Size(max=50,message="Last name exceeds maximum length allowed.")
	private String lastName;
	
	@Temporal(TemporalType.DATE)
	private Date birthDate;
	
	@Transient
	private String birthDateStr;
	
	private String benefitAddress;
	private String benefitAddressLine2;
	private String benefitCity;
	private String benefitState;
	private String benefitZip;
	private String benefitCountry;
	
	private String mailingAddress;
	private String mailingAddressLine2;
	private String mailingCity;	
	private String mailingState;
	private String mailingZip;
	private String mailingCountry;
	
	private String email;
	
	private String phone;
	private String cell;

	@Temporal(TemporalType.DATE)
	private Date cancelDate;
	
	private String cancelCode;

	private String thirdPartyId;
	private String employer;
	private String employeeId;
	private String sourceFileName;

	private String paymentCode;
	
	private Integer masaMemberId;
	
	private Integer agentId;
	private String agentCode;

	private Integer productId;
	private String productName;

	@Temporal(TemporalType.DATE)
	private Date soldDate;
	
	private String groupCode;
	
	@Temporal(TemporalType.DATE)
	private Date effectiveDate;
	
	@Temporal(TemporalType.DATE)
	private Date renewDate;

	@Temporal(TemporalType.TIMESTAMP)
	private Date createdDate;

	@Column(name="MEMBER_PROCESSED_DATE")
	private Date memberProcessedDate;

	//private String processErrors;
	private String processComments;
	
	private Boolean processed;
	
	@Enumerated(EnumType.STRING)
	@Column(name="STATUS")
	private UploadStatus status = UploadStatus.UNPROCESSED;
	
	@Column(name="MEMBER_ACTION")
	@Enumerated(EnumType.STRING)
	private MemberAction memberAction;
	
	private Long jobExecutionId;
	
	@Column(name="REVIEW_STATUS")
	@Enumerated(EnumType.STRING)
	private ReviewStatus reviewStatus = ReviewStatus.OPEN;
	
	@Column(name="STATUS_UPDATED_BY")
	private String statusUpdatedBy = Constant.DEFAULT_PTS_USERNAME;

	@Column(name="PRD_CATEGORY_CODE")
	private String productCategoryCode;
	
	@Column(name="USPS_ADDR_ERROR")
	private String uspsAddrError;
	
	@OneToMany(mappedBy = "memberFile", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	@OrderColumn(name="new_status")
	private Set<MemberFileReviewStatusEntity> reviewStatusSet = new HashSet<>();
	
	@OneToMany(mappedBy = "memberFile", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
	private Set<MemberFileRecordErrorEntity> recordErrorSet = new HashSet<>();
	
	@Column(name="DIVISION")
	private String division;
	
	@Column(name="SCI_FILE")
	private boolean sciFile;
	
	@Column(name="PAY_AMOUNT")
	private Double paymentAmount;
	
	@Column(name="FULFILLMENT_PENDING")
	private Boolean fulfillmentPending = false;
	
	//added for skipfile writer bean access
	@Transient
	private String skipMessages;
	
	private String fileOwner;
	
	public MemberFile() {
		super();
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getContractNumber() {
		return contractNumber;
	}

	public void setContractNumber(String contractNumber) {
		this.contractNumber = contractNumber;
	}

	
	public String getRelationship() {
		return relationship;
	}

	public void setRelationship(String relationship) {
		this.relationship = relationship;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getMiddleName() {
		return middleName;
	}

	public void setMiddleName(String middleName) {
		this.middleName = middleName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public Date getBirthDate() {
		return birthDate;
	}

	public void setBirthDate(Date birthDate) {
		this.birthDate = birthDate;
	}

	public String getBenefitAddress() {
		return benefitAddress;
	}

	public void setBenefitAddress(String benefitAddress) {
		this.benefitAddress = benefitAddress;
	}

	public String getBenefitAddressLine2() {
		return benefitAddressLine2;
	}

	public void setBenefitAddressLine2(String benefitAddressLine2) {
		this.benefitAddressLine2 = benefitAddressLine2;
	}

	public String getBenefitCity() {
		return benefitCity;
	}

	public void setBenefitCity(String benefitCity) {
		this.benefitCity = benefitCity;
	}

	public String getBenefitState() {
		return benefitState;
	}

	public void setBenefitState(String benefitState) {
		this.benefitState = benefitState;
	}

	public String getBenefitZip() {
		return benefitZip;
	}

	public void setBenefitZip(String benefitZip) {
		this.benefitZip = benefitZip;
	}

	public String getBenefitCountry() {
		return benefitCountry;
	}

	public void setBenefitCountry(String benefitCountry) {
		this.benefitCountry = benefitCountry;
	}

	public String getMailingAddress() {
		return mailingAddress;
	}

	public void setMailingAddress(String mailingAddress) {
		this.mailingAddress = mailingAddress;
	}

	public String getMailingAddressLine2() {
		return mailingAddressLine2;
	}

	public void setMailingAddressLine2(String mailingAddressLine2) {
		this.mailingAddressLine2 = mailingAddressLine2;
	}

	public String getMailingCity() {
		return mailingCity;
	}

	public void setMailingCity(String mailingCity) {
		this.mailingCity = mailingCity;
	}

	public String getMailingState() {
		return mailingState;
	}

	public void setMailingState(String mailingState) {
		this.mailingState = mailingState;
	}

	public String getMailingZip() {
		return mailingZip;
	}

	public void setMailingZip(String mailingZip) {
		this.mailingZip = mailingZip;
	}

	public String getMailingCountry() {
		return mailingCountry;
	}

	public void setMailingCountry(String mailingCountry) {
		this.mailingCountry = mailingCountry;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getCell() {
		return cell;
	}

	public void setCell(String cell) {
		this.cell = cell;
	}

	public Date getCancelDate() {
		return cancelDate;
	}

	public void setCancelDate(Date cancelDate) {
		this.cancelDate = cancelDate;
	}

	public String getCancelCode() {
		return cancelCode;
	}

	public void setCancelCode(String cancelCode) {
		this.cancelCode = cancelCode;
	}

	public String getThirdPartyId() {
		return thirdPartyId;
	}

	public void setThirdPartyId(String thirdPartyId) {
		this.thirdPartyId = thirdPartyId;
	}

	public String getEmployer() {
		return employer;
	}

	public void setEmployer(String employer) {
		this.employer = employer;
	}

	public String getEmployeeId() {
		return employeeId;
	}

	public void setEmployeeId(String employeeId) {
		this.employeeId = employeeId;
	}

	public String getSourceFileName() {
		return sourceFileName;
	}

	public void setSourceFileName(String sourceFileName) {
		this.sourceFileName = sourceFileName;
	}

	public String getPaymentCode() {
		return paymentCode;
	}

	public void setPaymentCode(String paymentCode) {
		this.paymentCode = paymentCode;
	}

	public Integer getMasaMemberId() {
		return masaMemberId;
	}

	public void setMasaMemberId(Integer masaMemberId) {
		this.masaMemberId = masaMemberId;
	}

	public String getAgentCode() {
		return agentCode;
	}

	public void setAgentCode(String agentCode) {
		this.agentCode = agentCode;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public Date getSoldDate() {
		return soldDate;
	}

	public void setSoldDate(Date soldDate) {
		this.soldDate = soldDate;
	}

	public String getGroupCode() {
		return groupCode;
	}

	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}

	public Date getEffectiveDate() {
		return effectiveDate;
	}

	public void setEffectiveDate(Date effectiveDate) {
		this.effectiveDate = effectiveDate;
	}

	public Date getRenewDate() {
		return renewDate;
	}

	public void setRenewDate(Date renewDate) {
		this.renewDate = renewDate;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	/*
	public String getProcessErrors() {
		return processErrors;
	}

	public void setProcessErrors(String processErrors) {
		this.processErrors = processErrors;
	}
	*/

	public Boolean getProcessed() {
		return processed;
	}

	public void setProcessed(Boolean processed) {
		this.processed = processed;
	}

	public String getModification() {
		return modification;
	}

	public void setModification(String modification) {
		this.modification = modification;
	}

	public String getProductType() {
		return productType;
	}

	public void setProductType(String productType) {
		this.productType = productType;
	}
	
	public Integer getProductId() {
		return productId;
	}

	public void setProductId(Integer productId) {
		this.productId = productId;
	}

	public Integer getAgentId() {
		return agentId;
	}

	public void setAgentId(Integer agentId) {
		this.agentId = agentId;
	}

	public Date getMemberProcessedDate() {
		return memberProcessedDate;
	}

	public void setMemberProcessedDate(Date memberProcessedDate) {
		this.memberProcessedDate = memberProcessedDate;
	}

	public String getProcessComments() {
		return processComments;
	}

	public void setProcessComments(String processComments) {
		this.processComments = processComments;
	}

	public String getBirthDateStr() {
		return birthDateStr;
	}

	public void setBirthDateStr(String birthDateStr) {
		this.birthDateStr = birthDateStr;
	}

	public UploadStatus getStatus() {
		return status;
	}

	public void setStatus(UploadStatus status) {
		this.status = status;
	}	
	public MemberAction getMemberAction() {
		return memberAction;
	}

	public void setMemberAction(MemberAction memberAction) {
		this.memberAction = memberAction;
	}
	
	public Long getJobExecutionId() {
		return jobExecutionId;
	}

	public void setJobExecutionId(Long jobExecutionId) {
		this.jobExecutionId = jobExecutionId;
	}

	public ReviewStatus getReviewStatus() {
		return reviewStatus;
	}

	public void setReviewStatus(ReviewStatus reviewStatus) {
		this.reviewStatus = reviewStatus;
	}

	public String getStatusUpdatedBy() {
		return statusUpdatedBy;
	}

	public void setStatusUpdatedBy(String statusUpdatedBy) {
		this.statusUpdatedBy = statusUpdatedBy;
	}

	/*
	public String getErrorCode() {
		return errorCode;
	}

	public void setErrorCode(String errorCode) {
		this.errorCode = errorCode;
	}
	*/

	public String getProductCategoryCode() {
		return productCategoryCode;
	}

	public void setProductCategoryCode(String productCategoryCode) {
		this.productCategoryCode = productCategoryCode;
	}

	public String getUspsAddrError() {
		return uspsAddrError;
	}

	public void setUspsAddrError(String uspsAddrError) {
		this.uspsAddrError = uspsAddrError;
	}

	
	public Set<MemberFileReviewStatusEntity> getReviewStatusSet() {
		return reviewStatusSet;
	}

	public void setReviewStatusSet(Set<MemberFileReviewStatusEntity> reviewStatusSet) {
		this.reviewStatusSet.clear();
		this.reviewStatusSet.addAll(reviewStatusSet);
	}
	
	public void addReviewStatus(MemberFileReviewStatusEntity reviewStatus) {
		this.reviewStatusSet.add(reviewStatus);
	}

	public Set<MemberFileRecordErrorEntity> getRecordErrorSet() {
		return recordErrorSet;
	}

	public void setRecordErrorSet(Set<MemberFileRecordErrorEntity> recordErrorSet) {
		this.recordErrorSet = recordErrorSet;
	}
	
	public void addRecordErrorSet(MemberFileRecordErrorEntity recordError) {
		this.recordErrorSet.add(recordError);
	}

	@Override
	public String toString() {
		return "MemberFile [id=" + id + ", contractNumber=" + contractNumber + ", modification=" + modification
				+ ", productType=" + productType + ", relationship=" + relationship + ", firstName=" + firstName
				+ ", middleName=" + middleName + ", lastName=" + lastName + ", birthDate=" + birthDate
				+ ", birthDateStr=" + birthDateStr + ", benefitAddress=" + benefitAddress + ", benefitAddressLine2="
				+ benefitAddressLine2 + ", benefitCity=" + benefitCity + ", benefitState=" + benefitState
				+ ", benefitZip=" + benefitZip + ", benefitCountry=" + benefitCountry + ", mailingAddress="
				+ mailingAddress + ", mailingAddressLine2=" + mailingAddressLine2 + ", mailingCity=" + mailingCity
				+ ", mailingState=" + mailingState + ", mailingZip=" + mailingZip + ", mailingCountry=" + mailingCountry
				+ ", email=" + email + ", phone=" + phone + ", cell=" + cell + ", cancelDate=" + cancelDate
				+ ", cancelCode=" + cancelCode + ", thirdPartyId=" + thirdPartyId + ", employer=" + employer
				+ ", employeeId=" + employeeId + ", sourceFileName=" + sourceFileName + ", paymentCode=" + paymentCode
				+ ", masaMemberId=" + masaMemberId + ", agentId=" + agentId + ", agentCode=" + agentCode
				+ ", productId=" + productId + ", productName=" + productName + ", soldDate=" + soldDate
				+ ", groupCode=" + groupCode + ", effectiveDate=" + effectiveDate + ", renewDate=" + renewDate
				+ ", createdDate=" + createdDate + ", memberProcessedDate=" + memberProcessedDate + ", processComments="
				+ processComments + ", processed=" + processed + ", status=" + status + ", memberAction=" + memberAction
				+ ", jobExecutionId=" + jobExecutionId + ", reviewStatus=" + reviewStatus + ", statusUpdatedBy="
				+ statusUpdatedBy + ", productCategoryCode=" + productCategoryCode + ", uspsAddrError=" + uspsAddrError
				+ ", reviewStatusSet=" + reviewStatusSet + ", recordErrorSet=" + recordErrorSet + ", division="
				+ division + ", sciFile=" + sciFile + ", paymentAmount=" + paymentAmount + ", fulfillmentPending="
				+ fulfillmentPending + ", skipMessages=" + skipMessages + "]";
	}

	public MemberFile(Integer id, String groupCode, Integer masaMemberId,
			String contractNumber, UploadStatus status,ReviewStatus reviewStatus,
			String statusUpdatedBy, Date createdDate,String sourceFileName,
			String modification, String productType, String relationship,
			String firstName, String middleName, String lastName, Date birthDate, 
			String benefitAddress, String benefitCity, String benefitState,
			String benefitZip, String mailingAddress, String mailingCity, String mailingState,
			String mailingZip, String email, String phone, String cell, Date cancelDate, 
			String paymentCode, String agentCode, Integer productId, String productName, Date soldDate,
			Date effectiveDate,String processComments , MemberAction memberAction, String uspsAddrError) {
		super();
		this.id = id;
		this.contractNumber = contractNumber;
		this.modification = modification;
		this.productType = productType;
		this.relationship = relationship;
		this.firstName = firstName;
		this.middleName = middleName;
		this.lastName = lastName;
		this.birthDate = birthDate;
		this.benefitAddress = benefitAddress;
		this.benefitCity = benefitCity;
		this.benefitState = benefitState;
		this.benefitZip = benefitZip;
		this.mailingAddress = mailingAddress;
		this.mailingCity = mailingCity;
		this.mailingState = mailingState;
		this.mailingZip = mailingZip;
		this.email = email;
		this.phone = phone;
		this.cell = cell;
		this.cancelDate = cancelDate;
		this.sourceFileName = sourceFileName;
		this.paymentCode = paymentCode;
		this.masaMemberId = masaMemberId;
		this.agentCode = agentCode;
		this.productId = productId;
		this.productName = productName;
		this.soldDate = soldDate;
		this.groupCode = groupCode;
		this.effectiveDate = effectiveDate;
		this.createdDate = createdDate;
		this.processComments = processComments;
		this.status = status;
		this.memberAction = memberAction;
		this.reviewStatus = reviewStatus;
		this.statusUpdatedBy = statusUpdatedBy;
		this.uspsAddrError = uspsAddrError;
	}
	public String concatenateErrorMessages() {
		return this.recordErrorSet.stream().map(MemberFileRecordErrorEntity::getErrorMessage).collect(Collectors.joining(";"));
	}

	public String getSkipMessages() {
		return this.recordErrorSet.stream().map(MemberFileRecordErrorEntity::getErrorMessage).collect(Collectors.joining(";"));
	}

	public void setSkipMessages(String skipMessages) {
		this.skipMessages = skipMessages;
	}

	public String getDivision() {
		return division;
	}

	public void setDivision(String division) {
		this.division = division;
	}

	public boolean isSciFile() {
		return sciFile;
	}

	public void setSciFile(boolean sciFile) {
		this.sciFile = sciFile;
	}

	public Double getPaymentAmount() {
		return paymentAmount;
	}

	public void setPaymentAmount(Double paymentAmount) {
		this.paymentAmount = paymentAmount;
	}

	public Boolean isFulfillmentPending() {
		return fulfillmentPending;
	}

	public void setFulfillmentPending(Boolean fulfillmentPending) {
		this.fulfillmentPending = fulfillmentPending;
	}

	public String getFileOwner() {
		return fileOwner;
	}

	public void setFileOwner(String fileOwner) {
		this.fileOwner = fileOwner;
	}	
	
	
}
