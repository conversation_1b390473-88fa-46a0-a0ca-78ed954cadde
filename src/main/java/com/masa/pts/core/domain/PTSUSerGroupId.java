package com.masa.pts.core.domain;

import java.io.Serializable;

import javax.persistence.Embeddable;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;

@Embeddable
public class PTSUSerGroupId implements Serializable {
	
	private static final long serialVersionUID = -175408374757722415L;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name="employee_id")
	private PTSUser user;
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name="group_id")
	private GroupEntity group;
	
	public PTSUSerGroupId() {
		super();
	}	

	@Override
	public int hashCode() {
		return super.hashCode();
	}

	@Override
	public boolean equals(Object obj) {
		return super.equals(obj);
	}

	public PTSUSerGroupId(PTSUser user, GroupEntity group) {
		super();
		this.user = user;
		this.group = group;
	}

	/*
	 * public PTSUser getUser() { return user; }
	 */

	public void setUser(PTSUser user) {
		this.user = user;
	}

	public GroupEntity getGroup() {
		return group;
	}

	public void setGroup(GroupEntity group) {
		this.group = group;
	}	
}
