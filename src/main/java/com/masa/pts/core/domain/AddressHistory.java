package com.masa.pts.core.domain;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;

import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonView;
import com.masa.pts.core.constant.AddressType;

@Entity
@Table(name = "TDAT_HISTORY_ADDRESS", schema = "dbo")
public class AddressHistory implements Serializable {

	private static final long serialVersionUID = 36783426845296212L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "address_id")
	private Integer addressId;

	//1,2,3->Benefits,Mailing,Burial
	@Enumerated(EnumType.ORDINAL)
	private AddressType type;

	@JsonView(MobileView.Member.class)
	private String address1;

	@JsonView(MobileView.Member.class)
	private String address2;

	@JsonView(MobileView.Member.class)
	private String address3;

	@JsonView(MobileView.Member.class)
	private String city;

	@JsonView(MobileView.Member.class)
	@JsonIgnore(value = true)
	private Integer state;

	@JsonView(MobileView.Member.class)
	@JsonIgnore(value = true)
	private Integer country;

	@JsonView(MobileView.Member.class)
	private String zip;

	@JsonView(MobileView.Member.class)
	private String zip4;

	@Column(name = "last_modified")
	@JsonIgnore(value = true)
	private Date lastModifiedDate;

	@JsonView(MobileView.Member.class)
	@OneToOne(fetch = FetchType.EAGER, targetEntity = State.class, optional = true)
	@NotFound(action = NotFoundAction.IGNORE)
	@JoinColumn(name = "state", referencedColumnName = "state_id", insertable = false, updatable = false)
	private State stateDetails;

	@JsonView(MobileView.Member.class)
	@OneToOne(fetch = FetchType.EAGER, targetEntity = Country.class, optional = true)
	@NotFound(action = NotFoundAction.IGNORE)
	@JoinColumn(name = "country", referencedColumnName = "country_id", insertable = false, updatable = false)
	private Country countryDetails;

	@Column(name = "member_id")
	private Integer memberId;

	public AddressHistory() {
		super();
	}

	public Integer getAddressId() {
		return addressId;
	}

	public void setAddressId(Integer addressId) {
		this.addressId = addressId;
	}

	public AddressType getType() {
		return type;
	}

	public void setType(AddressType type) {
		this.type = type;
	}

	public String getAddress1() {
		return address1;
	}

	public void setAddress1(String address1) {
		this.address1 = address1;
	}

	public String getAddress2() {
		return address2;
	}

	public void setAddress2(String address2) {
		this.address2 = address2;
	}

	public String getAddress3() {
		return address3;
	}

	public void setAddress3(String address3) {
		this.address3 = address3;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public Integer getCountry() {
		return country;
	}

	public void setCountry(Integer country) {
		this.country = country;
	}

	public String getZip() {
		return zip;
	}

	public void setZip(String zip) {
		this.zip = zip;
	}

	public String getZip4() {
		return zip4;
	}

	public void setZip4(String zip4) {
		this.zip4 = zip4;
	}

	public Date getLastModifiedDate() {
		return lastModifiedDate;
	}

	public void setLastModifiedDate(Date lastModifiedDate) {
		this.lastModifiedDate = lastModifiedDate;
	}

	public State getStateDetails() {
		return stateDetails;
	}

	public void setStateDetails(State stateDetails) {
		this.stateDetails = stateDetails;
	}

	public Country getCountryDetails() {
		return countryDetails;
	}

	public void setCountryDetails(Country countryDetails) {
		this.countryDetails = countryDetails;
	}

	public Integer getMemberId() {
		return memberId;
	}

	public void setMemberId(Integer memberId) {
		this.memberId = memberId;
	}
}
