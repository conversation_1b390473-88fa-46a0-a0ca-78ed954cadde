package com.masa.pts.core.domain;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

@Entity
@Table(name = "tlst_Product_Category", schema = "dbo")
public class ProductCategoryEntity implements Serializable {

	private static final long serialVersionUID = -4662493805129297129L;

	@Id
	@GeneratedValue(strategy= GenerationType.IDENTITY)
	@Column(name="id")
	private Integer id;

	@Column(name="code")
	private String code;

	@Column(name="name")
	private String name;

	@Column(name="create_date")
	private Date createDate;

	@Column(name="seq")
	private Integer sequence;

	@Column(name="is_obsolete")
	private boolean isObsolete;

	@Column(name="obsolete_date")
	private Date obsoleteDate;

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Integer getSequence() {
		return sequence;
	}

	public void setSequence(Integer sequence) {
		this.sequence = sequence;
	}

	public boolean isObsolete() {
		return isObsolete;
	}

	public void setObsolete(boolean obsolete) {
		isObsolete = obsolete;
	}

	public Date getObsoleteDate() {
		return obsoleteDate;
	}

	public void setObsoleteDate(Date obsoleteDate) {
		this.obsoleteDate = obsoleteDate;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) return true;
		if (o == null || getClass() != o.getClass()) return false;
		ProductCategoryEntity that = (ProductCategoryEntity) o;
		return isObsolete == that.isObsolete &&
				Objects.equals(id, that.id) &&
				Objects.equals(code, that.code) &&
				Objects.equals(name, that.name) &&
				Objects.equals(createDate, that.createDate) &&
				Objects.equals(sequence, that.sequence) &&
				Objects.equals(obsoleteDate, that.obsoleteDate);
	}

	@Override
	public int hashCode() {
		return Objects.hash(id, code, name, createDate, sequence, isObsolete, obsoleteDate);
	}
}
