package com.masa.pts.core.domain;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.springframework.lang.Nullable;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonView;

@Entity
@Table(name="TDAT_PAYMENT_DETAIL",schema = "dbo")
public class PaymentDetail implements Serializable {

	private static final long serialVersionUID = 2308399202855802061L;

	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="id")
	private Integer id;
	
	//payid 
	@ManyToOne	
	@JoinColumn(name="pay_id") 
	private Payment payment;
	
	//member id
	private Integer memberId;
	
	@JsonView(MobileView.Member.class)
	private Double amountPaid;
	private Boolean isNew;
	private Integer isCommission;
	private Double periodFee;
	@Column(name="is_chargeback")	
	private Boolean isChargeBack=Boolean.FALSE;
	private Double commPeriod=0.0;
	private Integer productId;
	
	@JsonView(MobileView.Member.class)
	@JsonFormat(pattern="yyyy-MM-dd")
	private Date transactionDate;
	@Nullable()
	private Boolean hasTax;
	private Double taxRate;
	
	private String note;
	private Date createdDate;
	private Date modifiedDate;
	private Integer createdBy;
	private Integer modifiedBy;
	
	@Column(name="last_chargeback_date")
	private Date lastChargebackDate;
	
	@Column(name="last_unchargeback_date")
	private Date lastUnChargebackDate;
	
	@Column(name="is_void")
	private Boolean voidPayment=Boolean.FALSE;
	
	@Transient
	private Integer alternatePayorId;
	
	/*@OneToOne(fetch=FetchType.LAZY,cascade = CascadeType.ALL,mappedBy = "paymentDetail", orphanRemoval = true,optional=true)
	private PaymentDetailForteTransaction paymentDetailFortrTransaction;*/
	
	/*@Transient
	private boolean paymentMatchWithPremimum=false;
	*/
	
	@Transient
	private Date renewDate;
	
	@Transient
	private Date oldRenewDate;
	
	@Transient
	private String forteTransactionId;
	
	@Transient
	private Date postDate;
	
	public PaymentDetail() {
		super();
	}
	public PaymentDetail(Integer id, Integer memberId, Double amountPaid, Boolean isNew, Integer isCommission, Double periodFee,
			Boolean isChargeBack, Double commPeriod, Integer productId, Date transactionDate, Boolean hasTax, Double taxRate) {
		super();
		this.id = id;
		this.memberId = memberId;
		this.amountPaid = amountPaid;
		this.isNew = isNew;
		this.isCommission = isCommission;
		this.periodFee = periodFee;
		this.isChargeBack = isChargeBack;
		this.commPeriod = commPeriod;
		this.productId = productId;
		this.transactionDate = transactionDate;
		this.hasTax = hasTax;
		this.taxRate = taxRate;
	}
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	/*public Payment getPayment() {
		return payment;
	}*/
	public void setPayment(Payment payment) {
		this.payment = payment;
	}
	public Integer getMemberId() {
		return memberId;
	}
	public void setMemberId(Integer memberId) {
		this.memberId = memberId;
	}
	public Double getAmountPaid() {
		return amountPaid;
	}
	public void setAmountPaid(Double amountPaid) {
		this.amountPaid = amountPaid;
	}
	public Boolean getIsNew() {
		return isNew;
	}
	public void setIsNew(Boolean isNew) {
		this.isNew = isNew;
	}
	public Integer getIsCommission() {
		return isCommission;
	}
	public void setIsCommission(Integer isCommission) {
		this.isCommission = isCommission;
	}
	public Double getPeriodFee() {
		return periodFee;
	}
	public void setPeriodFee(Double periodFee) {
		this.periodFee = periodFee;
	}
	public Boolean getIsChargeBack() {
		return isChargeBack;
	}
	public void setIsChargeBack(Boolean isChargeBack) {
		this.isChargeBack = isChargeBack;
	}
	public Double getCommPeriod() {
		return commPeriod;
	}
	public void setCommPeriod(Double commPeriod) {
		this.commPeriod = commPeriod;
	}
	public Integer getProductId() {
		return productId;
	}
	public void setProductId(Integer productId) {
		this.productId = productId;
	}
	public Date getTransactionDate() {
		return transactionDate;
	}
	public void setTransactionDate(Date transactionDate) {
		this.transactionDate = transactionDate;
	}
	
	public Boolean getHasTax() {
		return hasTax;
	}
	public void setHasTax(Boolean hasTax) {
		this.hasTax = hasTax;
	}
	public Double getTaxRate() {
		return (null==taxRate) ? 0 : taxRate;
	}
	public void setTaxRate(Double taxRate) {
		this.taxRate = taxRate;
	}	
	public Date getRenewDate() {
		return renewDate;
	}
	public void setRenewDate(Date renewDate) {
		this.renewDate = renewDate;
	}
	public String getForteTransactionId() {
		return forteTransactionId;
	}
	public void setForteTransactionId(String forteTransactionId) {
		this.forteTransactionId = forteTransactionId;
	}
	public Integer getAlternatePayorId() {
		return alternatePayorId;
	}
	public void setAlternatePayorId(Integer alternatePayorId) {
		this.alternatePayorId = alternatePayorId;
	}
	
	public String getNote() {
		return note;
	}
	public void setNote(String note) {
		this.note = note;
	}
	public Date getCreatedDate() {
		return createdDate;
	}
	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}
	public Date getModifiedDate() {
		return modifiedDate;
	}
	public void setModifiedDate(Date modifiedDate) {
		this.modifiedDate = modifiedDate;
	}
	public Integer getCreatedBy() {
		return createdBy;
	}
	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}
	public Integer getModifiedBy() {
		return modifiedBy;
	}
	public void setModifiedBy(Integer modifiedBy) {
		this.modifiedBy = modifiedBy;
	}
	public Date getOldRenewDate() {
		return oldRenewDate;
	}
	public void setOldRenewDate(Date oldRenewDate) {
		this.oldRenewDate = oldRenewDate;
	}
	/*public PaymentDetailForteTransaction getPaymentDetailFortrTransaction() {
		return paymentDetailFortrTransaction;
	}
	public void setPaymentDetailFortrTransaction(PaymentDetailForteTransaction paymentDetailFortrTransaction) {
		this.paymentDetailFortrTransaction = paymentDetailFortrTransaction;
	}*/
	/*public boolean isPaymentMatchWithPremimum() {
		return paymentMatchWithPremimum;
	}
	public void setPaymentMatchWithPremimum(boolean paymentMatchWithPremimum) {
		this.paymentMatchWithPremimum = paymentMatchWithPremimum;
	}*/
	@Override
	public String toString() {
		return "PaymentDetail [id=" + id + ", memberId=" + memberId + ", amountPaid=" + amountPaid + ", isNew=" + isNew
				+ ", isCommission=" + isCommission + ", periodFee=" + periodFee + ", isChargeBack=" + isChargeBack + ", commPeriod=" + commPeriod
				+ ", productId=" + productId + ", transactionDate=" + transactionDate + ", hasTax=" + hasTax + ", taxRate=" + taxRate + "]";
	}	
	
	public Date getPostDate() {
		return postDate;
	}
	public void setPostDate(Date postDate) {
		this.postDate = postDate;
	}

	public Date getLastChargebackDate() {
		return lastChargebackDate;
	}
	public void setLastChargebackDate(Date lastChargebackDate) {
		this.lastChargebackDate = lastChargebackDate;
	}

	public Date getLastUnChargebackDate() {
		return lastUnChargebackDate;
	}
	public void setLastUnChargebackDate(Date lastUnChargebackDate) {
		this.lastUnChargebackDate = lastUnChargebackDate;
	}
	public Boolean getVoidPayment() {
		return voidPayment;
	}
	public void setVoidPayment(Boolean voidPayment) {
		this.voidPayment = voidPayment;
	}

	public interface PaymentDetailSummary{
		Integer getId();
		Date getTransactionDate();		
		Double getAmountPaid();
		Double getCommPeriod();
		Integer getMemberId();
		Double getTaxRate();
		Integer getCreatedBy();
		Date getCreatedDate();
		Boolean getHasTax();
		Boolean getIsChargeBack();
		Integer getIsCommission();
		Boolean getIsNew();
		Integer getModifiedBy();
		Date getModifiedDate();
		String getNote();
		Double getPeriodFee();
		Integer getProductId();
		Boolean getVoidPayment();
	}
}
