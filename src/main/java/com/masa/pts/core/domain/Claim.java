package com.masa.pts.core.domain;

import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.hibernate.annotations.Where;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonView;

@Entity
@Table(name="TDAT_CLAIM",schema = "dbo")
public class Claim implements Serializable{

	private static final long serialVersionUID = -4670805137366082349L;

	@JsonView(MobileView.Member.class)
	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "claim_seq")
	@SequenceGenerator(name = "claim_seq", sequenceName = "TDAT_CLAIMSEQ", allocationSize = 1, schema = "dbo")
	@Column(name="claim_id")
	private Integer claimID;
	
	@JsonView(MobileView.Member.class)
	private Integer status;
	
	//@JsonInclude()
	@JsonView(MobileView.Member.class)
	@Transient
	private String statusName;
	
	//@Column(name="member_id")
	//private Integer memberId;
	
	@ManyToOne
	@JoinColumn(name="member_id") 
	private ClaimMember member;
	
/*	@Formula("(SELECT TDAT_MEMBER.FIRST_NAME + ' ' + TDAT_MEMBER.MI + '' + TDAT_MEMBER.LAST_NAME FROM TDAT_MEMBER WHERE TDAT_MEMBER.MEMBER_ID = MEMBER_ID)")
	private String memberFullName;
*/
	@Column(name="patient_type")
	private Integer patientType;
	
	private String relation;
	
	@JsonView(MobileView.Member.class)
	private Double total;
	
	private String handler;
	
	@Column(name="close_date")
	private Date closedDate;
	
	private String departure;
	
	private String destination;
	
	private boolean active;
	
	@JsonView(MobileView.Member.class)
	@Column(name="patient_name")
	private String patientName;
	
	@JsonView(MobileView.Member.class)
	@Column(name="create_date")
	private Date createdDate;
	
	private String provider;
	
	@Column(name="original_amount")
	private Double originalAmount;
	
	//@JsonView(MobileView.Member.class)
	@Column(name="insurance_paid_amount")
	private Double insurancePaidAmount;
	
	@Column(name="is_delete",nullable = true)
	private Boolean isDelete;

	@JsonView(MobileView.Member.class)
	@OneToMany(fetch=FetchType.LAZY,cascade = CascadeType.ALL,mappedBy = "claim", orphanRemoval = true)
	@Where(clause="is_delete=0 and type=2")
	@OrderBy(value="note_id desc")
	@JsonProperty("MemberClaimNotes")
	private Set<ClaimNote> customerNotes = new HashSet<>();
	
	
	@OneToMany(fetch=FetchType.LAZY,cascade = CascadeType.ALL,mappedBy = "claim", orphanRemoval = true)
	private Set<ClaimPayment> payment = new HashSet<>();
	
	@JsonView(MobileView.Member.class)
	@Transient
	@JsonFormat(pattern="yyyy-MM-dd")
	public Date claimDate;
	
	@JsonView(MobileView.Member.class)
	@JsonFormat(pattern="yyyy-MM-dd")
	@Transient
	public Date claimPaidDate;
	
	public Claim() {
		super();
	}

	
	public Claim(Integer status, Integer patientType, String relation, Double total, String handler,
			Date closedDate, String departure, String destination, boolean active, String patientName, Date createdDate,
			String provider, Double originalAmount, Double insurancePaidAmount, Boolean isDelete) { 
		super();
		this.status = status;
		//this.memberId = memberId;
		this.patientType = patientType;
		this.relation = relation;
		this.total = total;
		this.handler = handler;
		this.closedDate = closedDate;
		this.departure = departure;
		this.destination = destination;
		this.active = active;
		this.patientName = patientName;
		this.createdDate = createdDate;
		this.provider = provider;
		this.originalAmount = originalAmount;
		this.insurancePaidAmount = insurancePaidAmount;
		this.isDelete = isDelete;
	}


	public Integer getClaimId() {
		return claimID;
	}


	public void setClaimId(Integer claimId) {
		this.claimID = claimId;
	}


	public Integer getStatus() {
		return status;
	}


	public void setStatus(Integer status) {
		this.status = status;
	}


	/*public Integer getMemberId() {
		return memberId;
	}


	public void setMemberId(Integer memberId) {
		this.memberId = memberId;
	}*/


	public Integer getPatientType() {
		return patientType;
	}


	public void setPatientType(Integer patientType) {
		this.patientType = patientType;
	}


	public String getRelation() {
		return relation;
	}


	public void setRelation(String relation) {
		this.relation = relation;
	}


	public Double getTotal() {
		return total;
	}


	public void setTotal(Double total) {
		this.total = total;
	}


	public String getHandler() {
		return handler;
	}


	public void setHandler(String handler) {
		this.handler = handler;
	}


	public Date getClosedDate() {
		return closedDate;
	}


	public void setClosedDate(Date closedDate) {
		this.closedDate = closedDate;
	}


	public String getDeparture() {
		return departure;
	}


	public void setDeparture(String departure) {
		this.departure = departure;
	}


	public String getDestination() {
		return destination;
	}


	public void setDestination(String destination) {
		this.destination = destination;
	}


	public boolean isActive() {
		return active;
	}


	public void setActive(boolean active) {
		this.active = active;
	}


	public String getPatientName() {
		return patientName;
	}


	public void setPatientName(String patientName) {
		this.patientName = patientName;
	}


	public Date getCreatedDate() {
		return createdDate;
	}


	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}


	public String getProvider() {
		return provider;
	}


	public void setProvider(String provider) {
		this.provider = provider;
	}


	public Double getOriginalAmount() {
		return originalAmount;
	}


	public void setOriginalAmount(Double originalAmount) {
		this.originalAmount = originalAmount;
	}


	public Double getInsurancePaidAmount() {
		return insurancePaidAmount;
	}


	public void setInsurancePaidAmount(Double insurancePaidAmount) {
		this.insurancePaidAmount = insurancePaidAmount;
	}


	public Boolean isDelete() {
		return isDelete;
	}


	public void setDelete(Boolean isDelete) {
		this.isDelete = isDelete;
	}


	public String getStatusName() {
		switch(getStatus()) {
		case 1: return "Pending"; 
		case 2: return "Approved";
		case 3: return "Declined";		
		case 4: return "Inquiry"; 
		default : return "Invalid";
		}		
	}

	public void setStatusName(String statusName) {
		this.statusName = statusName;
	}
	
	public void setMember(ClaimMember member) {
		this.member = member;
	}	
	
	public ClaimMember getMember() {
		return member;
	}
			
	public Set<ClaimNote> getCustomerNotes() {
		return customerNotes;
	}

	public void setCustomerNotes(Set<ClaimNote> customerNotes) {
		this.customerNotes = customerNotes;
	}
	
	public void addCustomerNote(ClaimNote customerNote) {
		this.customerNotes.add(customerNote);
		customerNote.setClaim(this);
	}
	
	public void removeCustomerNote(ClaimNote customerNote) {
		this.customerNotes.remove(customerNote);
		customerNote.setClaim(null);
	}


	public Set<ClaimPayment> getPayment() {
		return payment;
	}

	public void setPayment(Set<ClaimPayment> payment) {
		this.payment = payment;
	}

	public Date getClaimDate() {
		return claimDate;
	}


	public void setClaimDate(Date claimDate) {
		this.claimDate = claimDate;
	}


	public Date getClaimPaidDate() {
		return claimPaidDate;
	}


	public void setClaimPaidDate(Date claimPaidDate) {
		this.claimPaidDate = claimPaidDate;
	}

	public interface ClaimSummary {
		String getClaimId();
		String getMemberId();
		String getMemberFullName();
		String getStatusName();
	}
}
