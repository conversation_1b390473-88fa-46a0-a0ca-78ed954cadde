package com.masa.pts.core.domain;

import java.io.Serializable;

import javax.persistence.Embeddable;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;

@Embeddable
public class PTSUserSalesChannelId implements Serializable {

	private static final long serialVersionUID = 8846704350594347737L;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name="employee_id")
	private PTSUser user;
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name="sales_channel_id")
	private SalesChannel salesChannel;

	public PTSUserSalesChannelId() {
		super();
	}

	/*commented as json including complete user - chain
	 * public PTSUser getUser() { return user; }
	 */

	public void setUser(PTSUser user) {
		this.user = user;
	}
	
	public SalesChannel getSalesChannel() {
		return salesChannel;
	}

	public void setSalesChannel(SalesChannel salesChannel) {
		this.salesChannel = salesChannel;
	}

	@Override
	public int hashCode() {
		return super.hashCode();
	}

	@Override
	public boolean equals(Object obj) {
		return super.equals(obj);
	}
}
