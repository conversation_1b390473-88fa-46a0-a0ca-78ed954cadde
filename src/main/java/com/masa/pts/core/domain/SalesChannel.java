package com.masa.pts.core.domain;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import lombok.ToString;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

@Entity
@Table(name = "TDAT_SALES_CHANNEL", schema = "dbo")
public class SalesChannel implements Serializable {

	private static final long serialVersionUID = -8134645811987110391L;
	
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "id")
	private Integer id;

	@Column(name = "name")
	private String name;

	@OneToOne(optional = false, fetch = FetchType.LAZY)
	@JoinColumn(name = "division", referencedColumnName = "id")
	@JsonSerialize(as = Division.class)
	private Division division;
	
	@JsonIgnore
	@Column(name = "isObsolete")
	private Boolean isObsolete;

	@Column(name="created_by")
	private Integer createdBy;

	@CreationTimestamp
	@Column(name="created_date")
	private Date createdDate;

	@UpdateTimestamp
	@Column(name="modified_date")
	private Date modifiedDate;

	@Column(name="modified_by")
	private Integer modifiedBy;

	@ToString.Exclude
	@OneToOne(optional = false, mappedBy = "salesChannel", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
	private SalesChannelForteMerchant forteMerchant;
	
	public SalesChannel() { }

	public SalesChannel(Integer id) {
		this.id = id;
	}

	public SalesChannel(Integer id, String name) {
		this.id = id;
		this.name = name;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Division getDivision() {
		return division;
	}

	public void setDivision(Division division) {
		this.division = division;
	}

	public Boolean getIsObsolete() {
		return isObsolete;
	}

	public void setIsObsolete(Boolean isObsolete) {
		this.isObsolete = isObsolete;
	}

	public SalesChannelForteMerchant getForteMerchant() {
		return forteMerchant;
	}

	public void setForteMerchant(SalesChannelForteMerchant forteMerchant) {
		this.forteMerchant = forteMerchant;
	}

	public Integer getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public Date getModifiedDate() {
		return modifiedDate;
	}

	public void setModifiedDate(Date modifiedDate) {
		this.modifiedDate = modifiedDate;
	}

	public Integer getModifiedBy() {
		return modifiedBy;
	}

	public void setModifiedBy(Integer modifiedBy) {
		this.modifiedBy = modifiedBy;
	}

	@Override
	public String toString() {
		return "SalesChannel{" +
				"id=" + id +
				", name='" + name + '\'' +
				", division=" + division +
				", isObsolete=" + isObsolete +
				'}';
	}
}
