package com.masa.pts.core.domain;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.Table;

@Entity
@Table(name="TDAT_MEMBER_COMMISSION",schema = "dbo")
public class MemberCommission implements Serializable {

	private static final long serialVersionUID = 2290681710863845142L;
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="commission_id")
	private Integer commissionId;

	@ManyToOne	
	@JoinColumn(name="member_id") 
	private Member member;

	private Integer agentId;
	private Integer productId;
	
	private Double newSalePercent;
	private Double renewPercent;
	private Integer agentPosition;
	private Boolean useFlatRate;
	
	private Date createdDate;
	private Integer createdBy;
	private Date modifiedDate;
	private Integer modifiedBy;
	
	@OneToOne
	@JoinColumn(name="fee_id")
	private MemberFee memberFee;	
	
	public MemberCommission() {
		super();
	}
	
	public Integer getCommissionId() {
		return commissionId;
	}
	public void setCommissionId(Integer commissionId) {
		this.commissionId = commissionId;
	}
	/*public Integer getMemberId() {
		return memberId;
	}
	public void setMemberId(Integer memberId) {
		this.memberId = memberId;
	}*/
	public Integer getAgentId() {
		return agentId;
	}
	public void setAgentId(Integer agentId) {
		this.agentId = agentId;
	}
	public Integer getProductId() {
		return productId;
	}
	public void setProductId(Integer productId) {
		this.productId = productId;
	}
	public Double getNewSalePercent() {
		return newSalePercent;
	}
	public void setNewSalePercent(Double newSalePercent) {
		this.newSalePercent = newSalePercent;
	}
	public Double getRenewPercent() {
		return renewPercent;
	}
	public void setRenewPercent(Double renewPercent) {
		this.renewPercent = renewPercent;
	}
	public Integer getAgentPosition() {
		return agentPosition;
	}
	public void setAgentPosition(Integer agentPosition) {
		this.agentPosition = agentPosition;
	}
	public Boolean getUseFlatRate() {
		return useFlatRate;
	}
	public void setUseFlatRate(Boolean useFlatRate) {
		this.useFlatRate = useFlatRate;
	}
	/*public Member getMember() {
		return member;
	}*/
	public void setMember(Member member) {
		this.member = member;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public Integer getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}

	public Date getModifiedDate() {
		return modifiedDate;
	}

	public void setModifiedDate(Date modifiedDate) {
		this.modifiedDate = modifiedDate;
	}

	public Integer getModifiedBy() {
		return modifiedBy;
	}

	public void setModifiedBy(Integer modifiedBy) {
		this.modifiedBy = modifiedBy;
	}

	public void setMemberFee(MemberFee memberFee) {
		this.memberFee = memberFee;
	}
}
