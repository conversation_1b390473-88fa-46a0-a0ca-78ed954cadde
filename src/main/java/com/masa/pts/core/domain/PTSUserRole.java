package com.masa.pts.core.domain;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name="TLNK_EMPLOYEE_ROLE",schema = "dbo")
public class PTSUserRole implements Serializable {

	private static final long serialVersionUID = 5453304027677325648L;

	@EmbeddedId
	private PTSUserRoleId userRoleId;
	
	@Column(name="role_code")
	private String roleCode;
	
	/*
	@Formula("(SELECT TLST_ROLE.DESCRIPTION FROM TLST_ROLE WHERE TLST_ROLE.ID = ROLE_ID)")
	private String description;
	*/
	
	public PTSUserRole() {
		super();
	}
	public String getRoleCode() {
		return roleCode;
	}
	public void setRoleCode(String roleCode) {
		this.roleCode = roleCode;
	}
	public PTSUserRoleId getUserRoleId() {
		return userRoleId;
	}
	public void setUserRoleId(PTSUserRoleId userRoleId) {
		this.userRoleId = userRoleId;
	}
}
