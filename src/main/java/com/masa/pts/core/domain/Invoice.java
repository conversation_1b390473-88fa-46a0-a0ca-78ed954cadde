package com.masa.pts.core.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

@Entity
@Table(name="TDAT_INVOICE",schema = "dbo")
public class Invoice implements Serializable {

	private static final long serialVersionUID = 687045718482266073L;

	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="id")
	private Integer invoiceId;
	
	@ManyToOne
	@JoinColumn(name="group_id")
	private GroupEntity group;

	@Column(name="invoice_type")
	private Integer invoiceType;
	
	@Column(name="total_due")
	private double totalDue;
	
	@Temporal(TemporalType.DATE)
	@Column(name="inv_date")
	private Date invoiceDate;
	
	@OneToOne(optional=true,targetEntity=Payment.class)	
    @JoinColumn(name = "pay_id")
	@NotFound(action = NotFoundAction.IGNORE)
	private Payment payment;
	
	//@Column(name="pay_id",nullable=true)
	//private Integer paymentId;
	
	@Column(name="is_paid")
	private Boolean isPaid;
	
	private Date createdDate;
	private Date modifiedDate;
	private Integer createdBy;
	private Integer modifiedBy;

	@Column(name="group_credit_amount")
	private BigDecimal groupCreditAmount = BigDecimal.ZERO;
	
	@OneToMany(fetch=FetchType.EAGER,cascade = CascadeType.ALL,mappedBy = "invoice", orphanRemoval = true)
	private Set<InvoiceDetail> invoiceDetail = new HashSet<>();

	public Integer getInvoiceId() {
		return invoiceId;
	}

	public void setInvoiceId(Integer invoiceId) {
		this.invoiceId = invoiceId;
	}

	public Integer getInvoiceType() {
		return invoiceType;
	}

	public void setInvoiceType(Integer invoiceType) {
		this.invoiceType = invoiceType;
	}

	public double getTotalDue() {
		return totalDue;
	}

	public void setTotalDue(double totalDue) {
		this.totalDue = totalDue;
	}

	public Date getInvoiceDate() {
		return invoiceDate;
	}

	public void setInvoiceDate(Date invoiceDate) {
		this.invoiceDate = invoiceDate;
	}

	public Boolean getIsPaid() {
		return isPaid;
	}

	public void setIsPaid(Boolean isPaid) {
		this.isPaid = isPaid;
	}

	public Invoice() {
		super();
	}
	
	public Set<InvoiceDetail> getInvoiceDetail() {
		return invoiceDetail;
	}

	public void setInvoiceDetail(Set<InvoiceDetail> invoiceDetail) {
		this.invoiceDetail = invoiceDetail;
	}

	public void addInvoiceDetail(InvoiceDetail invDetail) {
		this.invoiceDetail.add(invDetail);		
	}
	public void removeInvoiceDetail(InvoiceDetail invDetail) {
		this.invoiceDetail.remove(invDetail);
	}
	

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public Date getModifiedDate() {
		return modifiedDate;
	}

	public void setModifiedDate(Date modifiedDate) {
		this.modifiedDate = modifiedDate;
	}

	public Integer getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}

	public Integer getModifiedBy() {
		return modifiedBy;
	}

	public void setModifiedBy(Integer modifiedBy) {
		this.modifiedBy = modifiedBy;
	}
	
	public Payment getPayment() {
		return payment;
	}

	public void setPayment(Payment payment) {
		this.payment = payment;
	}

	public BigDecimal getGroupCreditAmount() {
		return groupCreditAmount;
	}

	public void setGroupCreditAmount(BigDecimal groupCreditAmount) {
		this.groupCreditAmount = groupCreditAmount;
	}

	public interface GroupInvoiceDates {
		Date getInvoiceDate(); 
	}
	
	public GroupEntity getGroup() {
		return group;
	}

	public void setGroup(GroupEntity group) {
		this.group = group;
	}
	public interface InvoiceSummary {
		Integer getInvoiceId();
		Integer getGroupId();
		Date getInvoiceDate();	
		Integer getInvoiceType();
		double getTotalDue();
		Boolean getIsPaid();
		String getGroupCode();
		Date getCreatedDate();
		Date getModifiedDate();
		Integer getCreatedBy();
		Integer getModifiedBy();
		Integer getPaymentId();
		BigDecimal getGroupCreditAmount();
		String getGroupName();
	}
}
