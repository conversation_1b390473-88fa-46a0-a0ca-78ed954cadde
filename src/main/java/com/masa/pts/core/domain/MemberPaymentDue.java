package com.masa.pts.core.domain;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

public class MemberPaymentDue implements Serializable {

	private static final long serialVersionUID = 4849001205477430399L;
	
	private Double totalAmountDue;
	
	private Double taxRate;
	
	private Double totalOutstandingBalance;
	
	Set<MemberFee> productFees = new HashSet<MemberFee>();

	public MemberPaymentDue() {
		super();
	}
	public MemberPaymentDue(Double totalAmountDue, Double taxRate, Double totalOutstandingBalance, Set<MemberFee> productFees) {
		super();
		this.totalAmountDue = totalAmountDue;
		this.taxRate = taxRate;
		this.totalOutstandingBalance = totalOutstandingBalance;
		this.productFees = productFees;
	}

	public Double getTotalAmountDue() {
		return totalAmountDue;
	}

	public void setTotalAmountDue(Double totalAmountDue) {
		this.totalAmountDue = totalAmountDue;
	}

	public Double getTaxRate() {
		return taxRate;
	}

	public void setTaxRate(Double taxRate) {
		this.taxRate = taxRate;
	}

	public Double getTotalOutstandingBalance() {
		return totalOutstandingBalance;
	}

	public void setTotalOutstandingBalance(Double totalOutstandingBalance) {
		this.totalOutstandingBalance = totalOutstandingBalance;
	}
	public Set<MemberFee> getProductFees() {
		return productFees;
	}
	public void setProductFees(Set<MemberFee> productFees) {
		this.productFees = productFees;
	}
	@Override
	public String toString() {
		return "MemberPaymentDue [totalAmountDue=" + totalAmountDue + ", taxRate=" + taxRate + ", totalOutstandingBalance=" + totalOutstandingBalance + ", productFees=" + productFees + "]";
	}	
}
