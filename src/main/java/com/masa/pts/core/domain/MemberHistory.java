package com.masa.pts.core.domain;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name="TDAT_MEMBER_HISTORY",schema = "dbo")
public class MemberHistory implements Serializable {

	private static final long serialVersionUID = 7831107381082087545L;

	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="id")
	private Integer id;
	
	@Column(name="member_id")
	private Integer memberId;
	
	private Integer active;
	
	private String groupCode;
	
	private Integer salesChannel;
	
	private Integer division;
	
	@Column(name="product_id_primary")
	private Integer productIdPrimary;
	
	@Column(name="event_type")
	private Integer eventType;
	
	private Date createdDate;
	
	private Integer createdBy;
	
	private Integer eventId; 

	public MemberHistory() {
		super();
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getMemberId() {
		return memberId;
	}

	public void setMemberId(Integer memberId) {
		this.memberId = memberId;
	}

	public Integer getActive() {
		return active;
	}

	public void setActive(Integer active) {
		this.active = active;
	}

	public String getGroupCode() {
		return groupCode;
	}

	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}

	public Integer getSalesChannel() {
		return salesChannel;
	}

	public void setSalesChannel(Integer salesChannel) {
		this.salesChannel = salesChannel;
	}

	public Integer getDivision() {
		return division;
	}

	public void setDivision(Integer division) {
		this.division = division;
	}

	public Integer getProductIdPrimary() {
		return productIdPrimary;
	}

	public void setProductIdPrimary(Integer productIdPrimary) {
		this.productIdPrimary = productIdPrimary;
	}

	public Integer getEventType() {
		return eventType;
	}

	public void setEventType(Integer eventType) {
		this.eventType = eventType;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public Integer getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}

	public Integer getEventId() {
		return eventId;
	}

	public void setEventId(Integer eventId) {
		this.eventId = eventId;
	}
}
