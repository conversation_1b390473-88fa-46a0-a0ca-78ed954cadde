package com.masa.pts.core.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.OrderBy;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

import org.hibernate.annotations.LazyToOne;
import org.hibernate.annotations.LazyToOneOption;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import org.hibernate.annotations.Where;

import com.masa.pts.core.constant.MemberActiveStatus;
import com.masa.pts.core.model.MemberProduct;
import com.masa.pts.core.model.ProductSale;

@Entity
@Table(name="TDAT_MEMBER",schema = "dbo")
public class Member implements Serializable {

	private static final long serialVersionUID = 8647553210220520764L;
	private static final Date DEFULT_DATE_1900 =  Date.from(LocalDate.of(1900, 1, 1).atStartOfDay(java.time.ZoneId.systemDefault()).toInstant());
	
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="member_id")
	private Integer memberId;
	
	@Column(name="employee_id")
	private String employeeId;
	
	@Column(name="first_name")
	private String firstName;
	
	@Column(name="last_name")
	private String lastName;
	
	private String mi;
	
	private String title;
	
	@Temporal(TemporalType.DATE	)
	@Column(name="birth_date")
	private Date birthDate;
	
	@Column(name="spouse_first")
	private String spouseFirst;
	
	@Column(name="spouse_last")
	private String spouseLast;
	
	@Column(name="spouse_mi")
	private String spouseMi;
	
	@Column(name="spouse_title")
	private String spouseTitle;
	
	@Temporal(TemporalType.DATE	)
	@Column(name="spouse_birth_date")
	private Date spouseBirthDate;
	
	private String phone;
	
	@Column(name="cell_phone")
	private String cellPhone;
	
	private String email;
	
	@Column(name="receives_newsletter")
	private Integer receivesNewsLetter=0;
	
	@Temporal(TemporalType.DATE	)
	@Column(name="cancel_date")
	private Date cancelDate=DEFULT_DATE_1900;
	
	@Temporal(TemporalType.DATE	)
	@Column(name="effective_date")
	private Date effectiveDate;
	
	@Temporal(TemporalType.DATE	)
	@Column(name="renew_date")
	private Date renewDate;
	
	@Temporal(TemporalType.DATE	)
	@Column(name="reinstate_date")
	private Date reinstateDate=DEFULT_DATE_1900;
	
	@Temporal(TemporalType.DATE	)
	@Column(name="last_welcome_package")
	private Date lastWelcomePackageDate=DEFULT_DATE_1900;
	
	@Temporal(TemporalType.DATE	)
	@Column(name="last_notice")
	private Date lastNoticeDate=DEFULT_DATE_1900;
	
	private Integer renew=0;
	
	@Column(name="flash_note")
	private String flashNote;
	
	
	@ManyToOne(fetch=FetchType.LAZY,optional=true)
	@JoinColumn(name="agent_id",nullable=true)
	@NotFound(action = NotFoundAction.IGNORE)
	private Agent agent;
	
	
	@Temporal(TemporalType.DATE	)
	@Column(name="expire_date")
	private Date expireDate=DEFULT_DATE_1900;
	
	@Column(name="pay_type")
	private Integer payType=0;
	
	@Column(name="cc_type")
	private Integer ccType=0;
	
	@Temporal(TemporalType.DATE	)
	@Column(name="cc_expire")
	private Date ccExpirationDate;
	
	@Column(name="ach_bank")	
	private String achBank;
	
	@Column(name="ach_routing")
	private String achRouting;
	
	@Column(name="ach_num")
	private String achNumber;
	
	@Column(name="alternate_payer") 
	private Integer alternatePayer=0;
	
	//@ManyToOne()
	//@Column(name="alternate_payer")
	//private Member alternatePayer;
	
	@JoinColumn(name="alternate_payer")
	@OneToMany(fetch=FetchType.LAZY)
	private Set<Member> subMembers = new HashSet<>(); 
	
	@Column(name="is_advance")
	private Integer isAdvance=0;
	
	@Column(name="advance_comm")
	private BigDecimal adanceComm=BigDecimal.ZERO;
	
	@Column(name="modified_by")
	private Integer modifiedBy;
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name="modified_on")
	private Date modifiedDate;
	
	@Temporal(TemporalType.DATE)
	@Column(name="last_invoice")
	private Date lastInvoiceDate=DEFULT_DATE_1900;
	
	private Integer active;
	
	@Column(name="is_renew")
	private Boolean isRenew=false;
		
	/*@Column(name="finderFee")
	private Double finderFee;*/
	
	@Column(name="is_ff_chargedback")
	private Boolean isFFChargeBack=false;
	
	@Column(name="is_reinstatement")
	private Boolean isReinstatement=false;
	
	private String ssn;
	
	@Column(name="cc_num")
	private String ccNum;
	
	@Column(name="str_cc_num")
	private String ccNumStr;
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name="created_on",updatable=false)
	private Date createdDate;
	
	@Column(name="created_by")
	private Integer createdBy;
	
	@Column(name="sold_region")
	private Integer soldRegion=0;
	
	@Column(name="alter_id")
	private String alterId;
	
	@Temporal(TemporalType.DATE	)
	@Column(name="lastservice_date")
	private Date lastServiceDate=DEFULT_DATE_1900;
	
	@Temporal(TemporalType.DATE	)
	@Column(name="updateproduct_date")
	private Date updateProductDate=DEFULT_DATE_1900;
	
	@Temporal(TemporalType.DATE	)
	@Column(name="draft_date")
	private Date draftDate=DEFULT_DATE_1900;
	
	@Temporal(TemporalType.DATE	)
	@Column(name="passport_date")
	private Date passportDate=DEFULT_DATE_1900;
	
	@Column(name="physical_newsletter")
	private Boolean physicalNewsLetter=false;
	
	@Temporal(TemporalType.DATE	)
	@Column(name="updateproduct2_date")
	private Date updateProduct2Date=DEFULT_DATE_1900;
	
	@Column(name="round_monthlyfee")
	private Boolean roundMonthlyFee=false;
	
	@Temporal(TemporalType.DATE	)
	@Column(name="receive_letter")
	private Date receiveLetterDate;
	
	@Column(name="onetime_init")
	private Boolean oneTimeInit=false;
	
	private String employer;
	
	@Column(name="cancel_code")
	private Integer cancelCode=0;
	
	@Column(name="lead_id")
	private String leadId;
	
	@Column(name="census_headcount")
	private Integer censusHeadCount=0;
	
	@Column(name="issued_membership_refund")
	private Boolean issuedMembershipRefund=false;
	
	@Temporal(TemporalType.DATE	)
	@Column(name="last_dep_cards")
	private Date lastDepCardsDate=DEFULT_DATE_1900;
	
	@Column(name="spouse_email")
	private String spouseEmail;
	
	@Column(name="spouse_phone")
	private String spousePhone;
	
	@Column(name="refund_amount")
	private  Double refundAmount=0.0;
	
	@Column(name="tax_rate")
	private Double taxRate=0.0;
	
	@Temporal(TemporalType.DATE	)
	
	@Column(name="reactive_date")
	private Date reactiveDate = Constant.DEFULT_DATE_1900;
	
	@Column(name="application_source")
	private Integer applicationSource = 0;
	
	@OneToOne(fetch = FetchType.LAZY, cascade=CascadeType.ALL, orphanRemoval = true)
	@JoinColumn(name = "address", referencedColumnName = "address_id")
	@NotFound(action = NotFoundAction.IGNORE)
	private Address addressDetails;
		
	@ManyToOne
	@JoinColumn(name="group_id")
	private GroupEntity group;
	
	
	@OneToOne(fetch = FetchType.LAZY, cascade=CascadeType.ALL, orphanRemoval = true)
	@JoinColumn(name = "alternateAddress", referencedColumnName = "address_id")
	@NotFound(action = NotFoundAction.IGNORE)
	private Address alternateAddressDetails;
	
	
	@OneToOne(fetch = FetchType.LAZY, cascade=CascadeType.ALL, orphanRemoval = true)
	@JoinColumn(name = "burialAddress" , referencedColumnName = "address_id")
	@NotFound(action = NotFoundAction.IGNORE)
	private Address burialAddressDetails ;
	
	@OneToMany(fetch=FetchType.LAZY,cascade = CascadeType.ALL,mappedBy = "member", orphanRemoval = true)
	private Set<Dependant> dependants = new HashSet<>();
	
	@OneToMany(fetch=FetchType.LAZY,cascade = CascadeType.ALL,mappedBy = "member", orphanRemoval = true)
	@OrderBy(value="note_id desc")
	private Set<Note> notes = new HashSet<>();
		
	
	@OneToMany(fetch=FetchType.LAZY,cascade = CascadeType.ALL, orphanRemoval = true)
	@JoinColumn(name="member_id")
	@OrderBy(value="product_id,fee_id")
	@Where(clause="product_id > 0")
	private Set<MemberFee> memberFee = new HashSet<>();
	
	@Transient
	private List<ProductSale> saleProducts = new ArrayList<>();
	
	
	@OneToMany(fetch=FetchType.LAZY,cascade = CascadeType.ALL,mappedBy = "member", orphanRemoval = false)
	private Set<Claim> claim = new HashSet<>();
	
	@OneToOne(mappedBy="member",fetch=FetchType.LAZY,orphanRemoval = true)
	private MemberAttributes attributes =null;
	
	@OneToMany(fetch=FetchType.LAZY,cascade = CascadeType.ALL,mappedBy = "member", orphanRemoval = true)
	@Where(clause = "is_delete=0")
	private Set<MemberCoverageLapse> coverageLapse = new HashSet<>();
	
	@Transient
	private MemberProduct memberProduct=new MemberProduct();
	
	@Transient
	private Set<PaymentDetail> memberPayments = new HashSet<>();
	
	@Transient
	private Integer paymentCountFilter=0;

	@OneToOne(mappedBy = "member",cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
	private EmergencyContactEntity emergencyContact;

	public interface MemberSummary {
		Integer getMemberId();
		String getFirstName();
		String getLastName();
		String getMi();
		Date getBirthDate();
		String getEmail();
		String getGroupGroupCode();
		Integer getActive();
		String getEmployeeId();
		Double getTaxRate();
		Boolean getOneTimeInit();
		Date getEffectiveDate();
		String getCellPhone();
		String getPhone();
		Date getCcExpirationDate();
		Date getRenewDate();
		Address getAddressDetails();
		String getAchNumber();
		String getCcNum();
		String getSpouseFirst();
		String getSpouseLast();
		String getSpouseMi();
		Date getSpouseBirthDate();
		String getSpouseEmail();
		String getSpousePhone();
		Date getReinstateDate();
		Date getCancelDate();
	}
	
	public interface MemberSummaryWithGroup extends MemberSummary {
		GroupEntity getGroup();
	}
		
	public interface MemberInfo {
		Integer getMemberId();
		String getFirstName();
		String getLastName();
		String getMi();
		Date getBirthDate();
		String getEmail();
		Integer getActive();
		String getEmployeeId();
		Double getTaxRate();
		Boolean getOneTimeInit();
		Integer getAlternatePayer();
		Date getReinstateDate();
		Date getEffectiveDate();
		Date getRenewDate();
		Date getCancelDate();
	}
	
	public interface MemberFeeDetails {
		Set<MemberFee> getMemberFee();
	}
	
	public interface MemberDetailsWithFee extends MemberFeeDetails {
		Integer getMemberId();
		Integer getActive();
		String getFirstName();
		String getLastName();
		String getMi();
	}
	
	public interface MemberSummaryForGrp {
		Integer getMemberId();
		String getFirstName();
		String getLastName();
		String getMi();
		Date getBirthDate();
		String getEmail();		
		Integer getActive();
		String getEmployeeId();
		Set<MemberFee> getMemberFee();
		Date getCancelDate();
	}
	
	public interface MemberClaimsSummary {
		Integer getMemberId();
		String getFirstName();
		String getLastName();
		String getMi();
		Date getBirthDate();
		String getEmail();
		Integer getActive();		
		Date getEffectiveDate();
		Date getRenewDate();
		String getSpouseFirst();
		String getSpouseLast();
		String getSpouseMi();
		Date getSpouseBirthDate();
		String getSpouseEmail();
		String getSpousePhone();
		Set<Claim> getClaim();
		Address getAddressDetails();
		Address getBurialAddressDetails();
		Address getAlternateAddressDetails();
	}
	

	public Integer getMemberId() {
		return memberId;
	}

	public void setMemberId(Integer memberId) {
		this.memberId = memberId;
	}

	public String getEmployeeId() {
		return employeeId;
	}

	public void setEmployeeId(String employeeId) {
		this.employeeId = employeeId;
	}

	public Address getAddressDetails() {
		return addressDetails;
	}

	public void setAddressDetails(Address addressDetails) {
		this.addressDetails = addressDetails;		
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public String getMi() {
		return mi;
	}

	public void setMi(String mi) {
		this.mi = mi;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public Date getBirthDate() {
		return birthDate;
	}

	public void setBirthDate(Date birthDate) {
		this.birthDate = birthDate;
	}

	public String getSpouseFirst() {
		return spouseFirst;
	}

	public void setSpouseFirst(String spouseFirst) {
		this.spouseFirst = spouseFirst;
	}

	public String getSpouseLast() {
		return spouseLast;
	}

	public void setSpouseLast(String spouseLast) {
		this.spouseLast = spouseLast;
	}

	public String getSpouseMi() {
		return spouseMi;
	}

	public void setSpouseMi(String spouseMi) {
		this.spouseMi = spouseMi;
	}

	public String getSpouseTitle() {
		return spouseTitle;
	}

	public void setSpouseTitle(String spouseTitle) {
		this.spouseTitle = spouseTitle;
	}

	public Date getSpouseBirthDate() {
		return spouseBirthDate;
	}

	public void setSpouseBirthDate(Date spouseBirthDate) {
		this.spouseBirthDate = spouseBirthDate;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getCellPhone() {
		return cellPhone;
	}

	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public GroupEntity getGroup() {
		return group;
	}

	public void setGroup(GroupEntity group) {
		this.group = group;
	}

	public Integer getReceivesNewsLetter() {
		return receivesNewsLetter;
	}

	public void setReceivesNewsLetter(Integer receivesNewsLetter) {
		this.receivesNewsLetter = receivesNewsLetter;
	}

	public Date getCancelDate() {
		return cancelDate;
	}

	public void setCancelDate(Date cancelDate) {
		this.cancelDate = cancelDate;
	}

	public Date getEffectiveDate() {
		return effectiveDate;
	}

	public void setEffectiveDate(Date effectiveDate) {
		this.effectiveDate = effectiveDate;
	}

	public Date getRenewDate() {
		return renewDate;
	}

	public void setRenewDate(Date renewDate) {
		this.renewDate = renewDate;
	}

	public Date getReinstateDate() {
		return reinstateDate;
	}

	public void setReinstateDate(Date reinstateDate) {
		this.reinstateDate = reinstateDate;
	}

	public Date getLastWelcomePackageDate() {
		return lastWelcomePackageDate;
	}

	public void setLastWelcomePackageDate(Date lastWelcomePackageDate) {
		this.lastWelcomePackageDate = lastWelcomePackageDate;
	}

	public Date getLastNoticeDate() {
		return lastNoticeDate;
	}

	public void setLastNoticeDate(Date lastNoticeDate) {
		this.lastNoticeDate = lastNoticeDate;
	}

	public Integer getRenew() {
		return renew;
	}

	public void setRenew(Integer renew) {
		this.renew = renew;
	}

	public String getFlashNote() {
		return flashNote;
	}

	public void setFlashNote(String flashNote) {
		this.flashNote = flashNote;
	}
	
	public Address getAlternateAddressDetails() {
		return alternateAddressDetails;
	}

	public void setAlternateAddressDetails(Address alternateAddressDetails) {
		this.alternateAddressDetails = alternateAddressDetails;
	}
	
	public Address getBurialAddressDetails() {
		return burialAddressDetails;
	}

	public void setBurialAddressDetails(Address burialAddressDetails) {
		this.burialAddressDetails = burialAddressDetails;
	}

	public Date getExpireDate() {
		return expireDate;
	}

	public void setExpireDate(Date expireDate) {
		this.expireDate = expireDate;
	}

	public Integer getPayType() {
		return payType;
	}

	public void setPayType(Integer payType) {
		this.payType = payType;
	}

	public Integer getCcType() {
		return ccType;
	}

	public void setCcType(Integer ccType) {
		this.ccType = ccType;
	}

	public Date getCcExpirationDate() {
		return ccExpirationDate;
	}

	public void setCcExpirationDate(Date ccExpirationDate) {
		this.ccExpirationDate = ccExpirationDate;
	}

	public String getAchBank() {
		return achBank;
	}

	public void setAchBank(String achBank) {
		this.achBank = achBank;
	}

	public String getAchRouting() {
		return achRouting;
	}

	public void setAchRouting(String achRouting) {
		this.achRouting = achRouting;
	}

	public String getAchNumber() {
		return achNumber;
	}

	public void setAchNumber(String achNumber) {
		this.achNumber = achNumber;
	}

	public Integer getAlternatePayer() {
		return alternatePayer;
	}

	public void setAlternatePayer(Integer alternatePayer) {
		this.alternatePayer = alternatePayer;
	}

	public Integer getIsAdvance() {
		return isAdvance;
	}

	public void setIsAdvance(Integer isAdvance) {
		this.isAdvance = isAdvance;
	}

	public BigDecimal getAdanceComm() {
		return adanceComm;
	}

	public void setAdanceComm(BigDecimal adanceComm) {
		this.adanceComm = adanceComm;
	}

	public Integer getModifiedBy() {
		return modifiedBy;
	}

	public void setModifiedBy(Integer modifiedBy) {
		this.modifiedBy = modifiedBy;
	}

	public Date getModifiedDate() {
		return modifiedDate;
	}

	public void setModifiedDate(Date modifiedDate) {
		this.modifiedDate = modifiedDate;
	}

	public Integer getActive() {
		return active;
	}

	public void setActive(Integer active) {
		this.active = active;
	}
	
	public Boolean isStatusActive() {		
		return active.compareTo(MemberActiveStatus.ACTIVE.getStatus())==0;
	}
	
	public Boolean isStatusCancelled() {		
		return active.compareTo(MemberActiveStatus.CANCELLED.getStatus())==0;
	}
	
	public Boolean isStatusSuspended() {		
		return active.compareTo(MemberActiveStatus.SUSPENDED.getStatus())==0;
	}

	public Boolean getIsRenew() {
		return isRenew;
	}

	public void setIsRenew(Boolean isRenew) {
		this.isRenew = isRenew;
	}

	public Boolean getIsFFChargeBack() {
		return isFFChargeBack;
	}

	public void setIsFFChargeBack(Boolean isFFChargeBack) {
		this.isFFChargeBack = isFFChargeBack;
	}

	public Boolean getIsReinstatement() {
		return isReinstatement;
	}

	public void setIsReinstatement(Boolean isReinstatement) {
		this.isReinstatement = isReinstatement;
	}

	public String getSsn() {
		return ssn;
	}

	public void setSsn(String ssn) {
		this.ssn = ssn;
	}

	public String getCcNum() {
		return ccNum;
	}

	public void setCcNum(String ccNum) {
		this.ccNum = ccNum;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public Integer getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}

	public Integer getSoldRegion() {
		return soldRegion;
	}

	public void setSoldRegion(Integer soldRegion) {
		this.soldRegion = soldRegion;
	}

	public String getAlterId() {
		return alterId;
	}

	public void setAlterId(String alterId) {
		this.alterId = alterId;
	}

	public Date getLastServiceDate() {
		return lastServiceDate;
	}

	public void setLastServiceDate(Date lastServiceDate) {
		this.lastServiceDate = lastServiceDate;
	}

	public Date getUpdateProductDate() {
		return updateProductDate;
	}

	public void setUpdateProductDate(Date updateProductDate) {
		this.updateProductDate = updateProductDate;
	}

	public Date getDraftDate() {
		return draftDate;
	}

	public void setDraftDate(Date draftDate) {
		this.draftDate = draftDate;
	}

	public Date getPassportDate() {
		return passportDate;
	}

	public void setPassportDate(Date passportDate) {
		this.passportDate = passportDate;
	}

	public Boolean getPhysicalNewsLetter() {
		return physicalNewsLetter;
	}

	public void setPhysicalNewsLetter(Boolean physicalNewsLetter) {
		this.physicalNewsLetter = physicalNewsLetter;
	}

	public Date getUpdateProduct2Date() {
		return updateProduct2Date;
	}

	public void setUpdateProduct2Date(Date updateProduct2Date) {
		this.updateProduct2Date = updateProduct2Date;
	}

	public Boolean getRoundMonthlyFee() {
		return roundMonthlyFee;
	}

	public void setRoundMonthlyFee(Boolean roundMonthlyFee) {
		this.roundMonthlyFee = roundMonthlyFee;
	}

	public Date getReceiveLetterDate() {
		return receiveLetterDate;
	}

	public void setReceiveLetterDate(Date receiveLetterDate) {
		this.receiveLetterDate = receiveLetterDate;
	}

	public Boolean getOneTimeInit() {
		return (oneTimeInit==null ? Boolean.FALSE : oneTimeInit);
	}

	public void setOneTimeInit(Boolean oneTimeInit) {
		this.oneTimeInit = oneTimeInit;
	}

	public String getEmployer() {
		return employer;
	}

	public void setEmployer(String employer) {
		this.employer = employer;
	}

	public Integer getCancelCode() {
		return cancelCode;
	}

	public void setCancelCode(Integer cancelCode) {
		this.cancelCode = cancelCode;
	}

	public String getLeadId() {
		return leadId;
	}

	public void setLeadId(String leadId) {
		this.leadId = leadId;
	}

	public Integer getCensusHeadCount() {
		return censusHeadCount;
	}

	public void setCensusHeadCount(Integer censusHeadCount) {
		this.censusHeadCount = censusHeadCount;
	}

	public Boolean getIssuedMembershipRefund() {
		return issuedMembershipRefund;
	}

	public void setIssuedMembershipRefund(Boolean issuedMembershipRefund) {
		this.issuedMembershipRefund = issuedMembershipRefund;
	}

	public Date getLastDepCardsDate() {
		return lastDepCardsDate;
	}

	public void setLastDepCardsDate(Date lastDepCardsDate) {
		this.lastDepCardsDate = lastDepCardsDate;
	}

	public String getSpouseEmail() {
		return spouseEmail;
	}

	public void setSpouseEmail(String spouseEmail) {
		this.spouseEmail = spouseEmail;
	}

	public Double getRefundAmount() {
		return refundAmount;
	}

	public void setRefundAmount(Double refundAmount) {
		this.refundAmount = refundAmount;
	}

	public Double getTaxRate() {
		return (null == taxRate) ? 0.0 : taxRate;
	}

	public void setTaxRate(Double taxRate) {
		this.taxRate = taxRate;
	}

	public Set<Dependant> getDependants() {
		return dependants;
	}

	// need to set this collection in that way because Hibernate will failed if the link to collection will be changed
	public void setDependants(Set<Dependant> dependants) {
		this.dependants.clear();
		this.dependants.addAll(dependants);
	}
	
	public void addDependant(Dependant dependant) {
		this.dependants.add(dependant);
		dependant.setMember(this);
	}
	
	public void removeDependant(Dependant dependant) {
		this.dependants.remove(dependant);
		dependant.setMember(null);		
	}

	public Set<Note> getNotes() {
		return notes;
	}

	public void setNotes(Set<Note> notes) {
		this.notes = notes;
	}
	
	public void addNote(Note note) {
		this.notes.add(note);
		note.setMember(this);
	}
	public void removeNote(Note note) {
		this.notes.remove(note);
		note.setMember(null);
	}

	public Set<MemberFee> getMemberFee() {
		return memberFee;
	}

	public void setMemberFee(Set<MemberFee> memberFee) {
		this.memberFee.clear();
		if(null !=memberFee)
			this.memberFee.addAll(memberFee);		
	}
	
	public void addMemberFee(MemberFee memberFee) {
		this.memberFee.add(memberFee);
		//memberFee.setMember(this);
	}
	
	public void removeMemberFee(MemberFee memberFee) {
		this.memberFee.remove(memberFee);
		//memberFee.setMember(null);
	}

	/*
	public Set<MemberCommission> getCommission() {
		return commission;
	}

	public void setCommission(Set<MemberCommission> commission) {
		this.commission.clear();
		if(null != commission)
			this.commission.addAll(commission);
	}
	
	public void addCommission(MemberCommission memCommission) {
		commission.add(memCommission);
		memCommission.setMember(this);
	}
	public void removeCommission(MemberCommission memCommission) {
		memCommission.setMember(null); 
		this.commission.remove(memCommission);		 
	}
	*/

	
	public Date getLastInvoiceDate() {
		return lastInvoiceDate;
	}

	public void setLastInvoiceDate(Date lastInvoiceDate) {
		this.lastInvoiceDate = lastInvoiceDate;
	}

	public Set<Member> getSubMembers() {
		return subMembers;
	}

	public void setSubMembers(Set<Member> subMembers) {
		this.subMembers = subMembers;
	}

	public void addSumMember(Member member) {
		this.subMembers.add(member);
	}
	
	public Set<Claim> getClaim() {
		return claim;
	}

	public void setClaim(Set<Claim> claim) {
		this.claim = claim;
	}

	
	public Agent getAgent() {
		return agent;
	}

	public void setAgent(Agent agent) {
		this.agent = agent;
	}
	

	public MemberProduct getMemberProduct() {
		return memberProduct;
	}

	public void setMemberProduct(MemberProduct memberProduct) {
		this.memberProduct = memberProduct;
	}

	public Set<PaymentDetail> getMemberPayments() {
		return memberPayments;
	}

	public void setMemberPayments(Set<PaymentDetail> memberPayments) {
		this.memberPayments = memberPayments;
	}

	public MemberAttributes getAttributes() {
		return attributes;
	}

	public void setAttributes(MemberAttributes attributes) {
		this.attributes = attributes;
	}

	public Integer getPaymentCountFilter() {
		return paymentCountFilter;
	}

	public void setPaymentCountFilter(Integer paymentCountFilter) {
		this.paymentCountFilter = paymentCountFilter;
	}

	public String getCcNumStr() {
		return ccNumStr;
	}

	public void setCcNumStr(String ccNumStr) {
		this.ccNumStr = ccNumStr;
	}

	public List<ProductSale> getSaleProducts() {
		return saleProducts;
	}

	public void setSaleProducts(List<ProductSale> saleProducts) {
		this.saleProducts = saleProducts;
	}

	public Date getReactiveDate(){
		return reactiveDate;
	}

	public void setReactiveDate(Date reactiveDate) {
		this.reactiveDate = reactiveDate;
	}

	public Set<MemberCoverageLapse> getCoverageLapse() {
		return coverageLapse;
	}

	public void setCoverageLapse(Set<MemberCoverageLapse> coverageLapse) {
		this.coverageLapse = coverageLapse;
	}

	public void addCoverageLapse(MemberCoverageLapse coverageLapse) {
		this.coverageLapse.add(coverageLapse);
	}

	public Member() { }

	public Integer getApplicationSource() {
		return applicationSource;
	}

	public void setApplicationSource(Integer applicationSource) {
		this.applicationSource = applicationSource;
	}

	public String getSpousePhone() {
		return spousePhone;
	}

	public void setSpousePhone(String spousePhone) {
		this.spousePhone = spousePhone;
	}

	public EmergencyContactEntity getEmergencyContact() {
		return emergencyContact;
	}

	public void setEmergencyContact(EmergencyContactEntity emergencyContactEntity) {
		this.emergencyContact = emergencyContactEntity;
	}

}
