package com.masa.pts.core.domain;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.IdClass;
import javax.persistence.Table;

@Entity
@IdClass(PTSUserAccessId.class)
@Table(name="TLNK_EMPLOYEE_ACCESS",schema = "dbo")
public class PTSUserAccess implements Serializable {
	
	private static final long serialVersionUID = 5180608347544629259L;

	@Id
	@Column(name="employee_id")
	private Integer employeeId;
	
	@Id
	@Column(name="access_id")
	private Integer accessId;
	
	@Column(name="access_code")
	private String accessCode;

	public PTSUserAccess() {
		super();
	}
	
	public Integer getEmployeeId() {
		return employeeId;
	}

	public void setEmployeeId(Integer employeeId) {
		this.employeeId = employeeId;
	}

	public Integer getAccessId() {
		return accessId;
	}

	public void setAccessId(Integer accessId) {
		this.accessId = accessId;
	}

	public String getAccessCode() {
		return accessCode;
	}

	public void setAccessCode(String accessCode) {
		this.accessCode = accessCode;
	}
}
