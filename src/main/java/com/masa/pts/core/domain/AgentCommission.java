package com.masa.pts.core.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Entity
@Table(name="TDAT_AGENT_COMMISSION",schema = "dbo")
public class AgentCommission implements Serializable {

	private static final long serialVersionUID = -1240687097883738923L;
	
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="commission_id")
	private Integer commissionId;
	
	//private Integer agentId;
	@ManyToOne	
	@JoinColumn(name="agent_id")
	private Agent agent;
	private Integer productId;
	
	private BigDecimal newSalePercent;
	private BigDecimal renewPercent;
	
	private Date createDate;
	private Boolean isManagerOverride;
	
	public AgentCommission() {
		super();
	}

	public AgentCommission(Integer commissionId, Integer productId, BigDecimal newSalePercent,
			BigDecimal renewPercent, Date createDate, Boolean isManagerOverride) {
		this.commissionId = commissionId;
		//this.agentId = agentId;
		this.productId = productId;
		this.newSalePercent = newSalePercent;
		this.renewPercent = renewPercent;
		this.createDate = createDate;
		this.isManagerOverride = isManagerOverride;
	}

	public Integer getCommissionId() {
		return commissionId;
	}

	public void setCommissionId(Integer commissionId) {
		this.commissionId = commissionId;
	}

	public Integer getProductId() {
		return productId;
	}

	public void setProductId(Integer productId) {
		this.productId = productId;
	}

	public BigDecimal getNewSalePercent() {
		return newSalePercent;
	}

	public void setNewSalePercent(BigDecimal newSalePercent) {
		this.newSalePercent = newSalePercent;
	}

	public BigDecimal getRenewPercent() {
		return renewPercent;
	}

	public void setRenewPercent(BigDecimal renewPercent) {
		this.renewPercent = renewPercent;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Boolean getIsManagerOverride() {
		return isManagerOverride;
	}

	public void setIsManagerOverride(Boolean isManagerOverride) {
		this.isManagerOverride = isManagerOverride;
	}

	public Agent getAgent() {
		return agent;
	}

	public void setAgent(Agent agent) {
		this.agent = agent;
	}
}
