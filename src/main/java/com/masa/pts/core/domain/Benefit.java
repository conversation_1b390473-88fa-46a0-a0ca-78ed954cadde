package com.masa.pts.core.domain;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonView;

@Entity
@Table(name="TDAT_BENEFIT",schema = "dbo")
public class Benefit implements Serializable {

	private static final long serialVersionUID = 3407349812935995584L;
	
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="benefit_id")
	private Integer benefitId;

	@JsonView(MobileView.Member.class)
	private String name;
	
	@JsonView(MobileView.Member.class)
	private String description;
	
	@JsonView(MobileView.Member.class)
	private Boolean active;

	public Benefit() {
		super();
	}

	public Benefit(Integer benefitId, String name, String description, Boolean active) {
		super();
		this.benefitId = benefitId;
		this.name = name;
		this.description = description;
		this.active = active;
	}

	public Integer getBenefitId() {
		return benefitId;
	}

	public void setBenefitId(Integer benefitId) {
		this.benefitId = benefitId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Boolean getActive() {
		return active;
	}

	public void setActive(Boolean active) {
		this.active = active;
	}

	@Override
	public String toString() {
		return "Benefit [benefitId=" + benefitId + ", name=" + name + ", description=" + description + ", active=" + active + "]";
	}
	
}
