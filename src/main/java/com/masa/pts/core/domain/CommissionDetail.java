package com.masa.pts.core.domain;

import java.io.Serializable;
import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Entity
@Table(name="TDAT_COMMISSION_DETAIL",schema = "dbo")
public class CommissionDetail implements Serializable {

	private static final long serialVersionUID = 5861105670649241537L;
	
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="id")
	private Integer id;
	
	@ManyToOne
	@JoinColumn(name="comm_id")
	private Commission commission;
	
	@Column(name="paydetail_id")
	private Integer payDetailId;
	
	private Integer memberId;
	
	@Column(name="is_advance")
	private Integer advance;
	
	private BigDecimal amount;
	
	@Column(name="is_chargeback")
	private Boolean chargeback;
	
	@Column(name="left_comm")
	private BigDecimal leftCommAmount;
	
	@Column(name="product_id")
	private Integer productId;
	
	private String note;

	public CommissionDetail() {
		super();
	}

	public CommissionDetail(Integer id, Commission commission, Integer payDetailId, Integer memberId, Integer advance, BigDecimal amount, Boolean chargeback, BigDecimal leftCommAmount,
			Integer productId, String note) {
		super();
		this.id = id;
		this.commission = commission;
		this.payDetailId = payDetailId;
		this.memberId = memberId;
		this.advance = advance;
		this.amount = amount;
		this.chargeback = chargeback;
		this.leftCommAmount = leftCommAmount;
		this.productId = productId;
		this.note = note;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Commission getCommission() {
		return commission;
	}

	public void setCommission(Commission commission) {
		this.commission = commission;
	}

	public Integer getPayDetailId() {
		if(payDetailId == null)
			return 0;
		return payDetailId;
	}

	public void setPayDetailId(Integer payDetailId) {
		this.payDetailId = payDetailId;
	}

	public Integer getMemberId() {
		return memberId;
	}

	public void setMemberId(Integer memberId) {
		this.memberId = memberId;
	}

	public Integer getAdvance() {
		return advance;
	}

	public void setAdvance(Integer advance) {
		this.advance = advance;
	}

	public BigDecimal getAmount() {
		return amount;
	}

	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}

	public Boolean getChargeback() {
		return chargeback;
	}

	public void setChargeback(Boolean chargeback) {
		this.chargeback = chargeback;
	}

	public BigDecimal getLeftCommAmount() {
		return leftCommAmount;
	}

	public void setLeftCommAmount(BigDecimal leftCommAmount) {
		this.leftCommAmount = leftCommAmount;
	}

	public Integer getProductId() {
		return productId;
	}

	public void setProductId(Integer productId) {
		this.productId = productId;
	}

	public String getNote() {
		return note;
	}

	public void setNote(String note) {
		this.note = note;
	}

	@Override
	public String toString() {
		return "CommissionDetail [id=" + id + ", payDetailId=" + payDetailId + ", memberId=" + memberId + ", advance=" + advance + ", amount=" + amount + ", chargeback="
				+ chargeback + ", leftCommAmount=" + leftCommAmount + ", productId=" + productId + ", note=" + note + "]";
	}

	
}
