package com.masa.pts.core.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.OrderBy;
import javax.persistence.Table;

@Entity
@Table(name="TDAT_COMMISSION",schema = "dbo")
public class Commission implements Serializable {

	private static final long serialVersionUID = -5466991498639439712L;
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="id")
	private Integer id;
	
	//@OneToOne(targetEntity=Agent.class)
	@OneToOne()
	@JoinColumn(name="agent_id")
	private Agent agent;
	
	@Column(name="comm_date")
	private Date commissionDate;
	
	@Column(name="amount")
	private BigDecimal commTotalAmount;
	
	private Integer commType;
	
	private Boolean paid;
	
	private Date payDate;
	
	@Column(name="active_comm")
	private Boolean active;
	@Column(name="is_delete")
	private Boolean deleted;
	
	@Column(name="create_date")
	private Date createdDate;
	
	@OneToMany(mappedBy="commission",cascade=CascadeType.ALL,fetch=FetchType.EAGER,orphanRemoval=true)
	@OrderBy("id")
	private Set<CommissionDetail> commDetails = new HashSet<CommissionDetail>();

	public Commission() {
		super();
	}

	public Commission(Integer id, Agent agent, Date commissionDate, BigDecimal commTotalAmount, Integer commType, Boolean paid, Date payDate, Boolean active, Boolean deleted, Date createdDate,
			Set<CommissionDetail> commDetails) {
		super();
		this.id = id;
		this.agent = agent;
		this.commissionDate = commissionDate;
		this.commTotalAmount = commTotalAmount;
		this.commType = commType;
		this.paid = paid;
		this.payDate = payDate;
		this.active = active;
		this.deleted = deleted;
		this.createdDate = createdDate;
		this.commDetails = commDetails;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Agent getAgent() {
		return agent;
	}

	public void setAgent(Agent agent) {
		this.agent = agent;
	}

	public Date getCommissionDate() {
		return commissionDate;
	}

	public void setCommissionDate(Date commissionDate) {
		this.commissionDate = commissionDate;
	}

	public BigDecimal getCommTotalAmount() {
		return commTotalAmount;
	}

	public void setCommTotalAmount(BigDecimal commTotalAmount) {
		this.commTotalAmount = commTotalAmount;
	}

	public Integer getCommType() {
		return commType;
	}

	public void setCommType(Integer commType) {
		this.commType = commType;
	}

	public Boolean getPaid() {
		return paid;
	}

	public void setPaid(Boolean paid) {
		this.paid = paid;
	}

	public Date getPayDate() {
		return payDate;
	}

	public void setPayDate(Date payDate) {
		this.payDate = payDate;
	}

	public Boolean getActive() {
		return active;
	}

	public void setActive(Boolean active) {
		this.active = active;
	}

	public Boolean getDeleted() {
		return deleted;
	}

	public void setDeleted(Boolean deleted) {
		this.deleted = deleted;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public Set<CommissionDetail> getCommDetails() {
		return commDetails;
	}

	public void setCommDetails(Set<CommissionDetail> commDetails) {
		this.commDetails = commDetails;
	}
	
	public void addCommDetails(CommissionDetail commDetail) {
		this.commDetails.add(commDetail);		
	}
	public void removeCommDetails(CommissionDetail commDetail) {
		this.commDetails.remove(commDetail);
	}

	@Override
	public String toString() {
		return "Commission [id=" + id + ", agent=" + agent + ", commissionDate=" + commissionDate + ", commTotalAmount=" + commTotalAmount + ", commType=" + commType + ", paid=" + paid + ", payDate="
				+ payDate + ", active=" + active + ", deleted=" + deleted + ", createdDate=" + createdDate +  "]";
	}	
}
