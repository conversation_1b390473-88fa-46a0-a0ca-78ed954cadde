package com.masa.pts.core.domain;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import java.io.Serializable;

@Entity(name="ProductFee")
@Table(name="TLNK_PRODUCT_FEE",schema = "dbo") 
public class ProductFee implements Serializable {

	private static final long serialVersionUID = 6239153591965920158L;

	@EmbeddedId
	private ProductFeeId productFeeId;
	
	private Double amount;
	
	private Integer frequency;

	@JsonSerialize(as = Frequency.class)
	@OneToOne(fetch=FetchType.LAZY)
	@JoinColumn(name = "frequency", insertable = false, updatable = false)
	private Frequency frequencyDetails;

	private Double maxAmountDue;

	public ProductFee() { }

	public ProductFee(Double amount, Integer frequency, Double maxAmountDue) {
		this.amount = amount;
		this.frequency = frequency;
		this.maxAmountDue = maxAmountDue;
	}

	public ProductFeeId getProductFeeId() {
		return productFeeId;
	}

	public void setProductFeeId(ProductFeeId productFeeId) {
		this.productFeeId = productFeeId;
	}

	public Double getAmount() {
		return amount;
	}

	public void setAmount(Double amount) {
		this.amount = amount;
	}

	public Integer getFrequency() {
		return frequency;
	}

	public void setFrequency(Integer frequency) {
		this.frequency = frequency;
	}

	public Double getMaxAmountDue() {
		return maxAmountDue;
	}

	public void setMaxAmountDue(Double maxAmountDue) {
		this.maxAmountDue = maxAmountDue;
	}

	public Frequency getFrequencyDetails() {
		return frequencyDetails;
	}

	public void setFrequencyDetails(Frequency frequencyDetails) {
		this.frequencyDetails = frequencyDetails;
	}

}
