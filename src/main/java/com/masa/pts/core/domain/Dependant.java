package com.masa.pts.core.domain;

import com.masa.pts.core.model.MemberDependantInput;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name="TDAT_DEPENDANT",schema = "dbo")
public class Dependant implements Serializable {

	private static final long serialVersionUID = -4637887718299170992L;

	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="depend_id")
	private Integer dependantId;
	
	@ManyToOne	
	@JoinColumn(name="member_id") 
	private Member member;
	
	private String firstName;
	
	private String lastName;
	
	private String miName;
	
	@Column(name="birthdate")
	private Date birthDate;

	private Boolean enrolledCollege;

	private Boolean active;

	private String notes;
	
	@Transient
	private long age;
	
	public Dependant() {
		super();
	}

	public Dependant(String firstName, String lastName, String miName,
			Date birthDate, Boolean enrolledCollege, Boolean active, String notes) {
		super();
		//this.memberId = memberId;
		this.firstName = firstName;
		this.lastName = lastName;
		this.miName = miName;
		this.birthDate = birthDate;
		this.enrolledCollege = enrolledCollege;
		this.active = active;
		this.notes = notes;
	}

	public Integer getDependantId() {
		return dependantId;
	}

	public void setDependantId(Integer dependantId) {
		this.dependantId = dependantId;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public String getMiName() {
		return miName;
	}

	public void setMiName(String miName) {
		this.miName = miName;
	}

	public Date getBirthDate() {
		return birthDate;
	}

	public void setBirthDate(Date birthDate) {
		this.birthDate = birthDate;
	}

	public Boolean isEnrolledCollege() {
		return enrolledCollege;
	}

	public void setEnrolledCollege(Boolean enrolledCollege) {
		this.enrolledCollege = enrolledCollege;
	}

	public Boolean isActive() {
		return active;
	}

	public void setActive(Boolean active) {
		this.active = active;
	}

	public String getNotes() {
		return notes;
	}

	public void setNotes(String notes) {
		this.notes = notes;
	}
	public void setMember(Member member) {
		this.member = member;
	}

	public long getAge() {
		return age;
	}

	public void setAge(long age) {
		this.age = age;
	}

	public MemberDependantInput toMemberDependantInput() {
		return new MemberDependantInput.Builder()
				// todo new Date is workaround, entity birthDate field is java.sql.Date in runtime.
				//  Should be fixed in future
				.birthDate(new Date(this.birthDate.getTime()))
				.enrolledCollege(this.enrolledCollege)
				.firstName(this.firstName)
				.lastName(this.lastName)
				.dependantId(this.dependantId)
				.miName(this.miName)
				.build();
	}
}
