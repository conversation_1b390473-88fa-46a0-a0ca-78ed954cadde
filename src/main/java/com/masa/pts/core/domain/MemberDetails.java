package com.masa.pts.core.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.fasterxml.jackson.annotation.JsonFormat;

@Entity
@Table(name="TDAT_MEMBER",schema = "dbo")
public class MemberDetails implements Serializable {

	private static final long serialVersionUID = -7739194777736660259L;
	
	private static final Date DEFULT_DATE_1900 =  Date.from(LocalDate.of(1900, 1, 1).atStartOfDay(java.time.ZoneId.systemDefault()).toInstant());

	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="member_id")
	private Integer memberId;
	
	@Column(name="employee_id")
	private String employeeId;
	
	@Column(name="first_name")
	private String firstName;
	
	@Column(name="last_name")
	private String lastName;
	
	private String mi;
	
	private String title;
	
	@Temporal(TemporalType.DATE	)
	@Column(name="birth_date")
	private Date birthDate;
	
	@Column(name="spouse_first")
	private String spouseFirst;
	
	@Column(name="spouse_last")
	private String spouseLast;
	
	@Column(name="spouse_mi")
	private String spouseMi;
	
	@Column(name="spouse_title")
	private String spouseTitle;
	
	@Temporal(TemporalType.DATE	)
	@Column(name="spouse_birth_date")
	private Date spouseBirthDate;
	
	private String phone;
	
	@Column(name="cell_phone")
	private String cellPhone;
	
	private String email;
	
	@Column(name="group_id")
	private Integer groupId;
	
	@Column(name="receives_newsletter")
	private Integer receivesNewsLetter=0;
	
	@Temporal(TemporalType.DATE	)
	@Column(name="cancel_date")
	private Date cancelDate=DEFULT_DATE_1900;

	@Temporal(TemporalType.DATE	)
	@Column(name="effective_date")
	private Date effectiveDate;
	
	@Temporal(TemporalType.DATE	)
	@Column(name="renew_date")
	private Date renewDate;
	
	@Temporal(TemporalType.DATE	)
	@Column(name="reinstate_date")
	private Date reinstateDate=DEFULT_DATE_1900;
	
	@Temporal(TemporalType.DATE	)
	@Column(name="last_welcome_package")
	private Date lastWelcomePackageDate=DEFULT_DATE_1900;
	
	@Temporal(TemporalType.DATE	)
	@Column(name="last_notice")
	private Date lastNoticeDate=DEFULT_DATE_1900;
	
	private Integer renew=0;
	
	@Column(name="flash_note")
	private String flashNote;
	
	@Column(name="agent_id")
	private Integer agentId=0;
		
	
	@Temporal(TemporalType.DATE	)
	@Column(name="expire_date")
	private Date expireDate=DEFULT_DATE_1900;
	
	@Column(name="pay_type")
	private Integer payType=0;
	
	@Column(name="cc_type")
	private Integer ccType=0;
	
	@Temporal(TemporalType.DATE	)
	@Column(name="cc_expire")
	@JsonFormat(shape=JsonFormat.Shape.STRING,pattern="MM/yyyy",timezone="America/New_York")
	private Date ccExpirationDate;
	
	@Column(name="ach_bank")	
	private String achBank;
	
	@Column(name="ach_routing")
	private String achRouting;
	
	@Column(name="ach_num")
	private String achNumber;
	
	@Column(name="alternate_payer") 
	private Integer alternatePayer=0;
	
	@JoinColumn(name="alternate_payer")
	@OneToMany(fetch=FetchType.EAGER)
	private Set<MemberDetails> subMembers = new HashSet<>(); 
	
	@Column(name="is_advance")
	private Integer isAdvance=0;
	
	@Column(name="advance_comm")
	private BigDecimal adanceComm=BigDecimal.ZERO;
	
	@Column(name="modified_by")
	private Integer modifiedBy;
			
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name="modified_on")
	private Date modifiedDate;
	
	
	@Temporal(TemporalType.DATE)
	@Column(name="last_invoice")
	private Date lastInvoiceDate=DEFULT_DATE_1900;
	
	private Integer active;
	
	@Column(name="is_renew")
	private Boolean isRenew=false;
			
	@Column(name="is_ff_chargedback")
	private Boolean isFFChargeBack=false;
	
	@Column(name="is_reinstatement")
	private Boolean isReinstatement=false;
	
	private String ssn;
	
	@Column(name="cc_num")
	private String ccNum;
	
	@Column(name="str_cc_num")
	private String ccNumStr;
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name="created_on",updatable=false)
	private Date createdDate;
	
	@Column(name="created_by")
	private Integer createdBy;
	
	@Column(name="sold_region")
	private Integer soldRegion=0;
	
	@Column(name="alter_id")
	private String alterId;
	
	@Temporal(TemporalType.DATE	)
	@Column(name="lastservice_date")
	private Date lastServiceDate=DEFULT_DATE_1900;
	
	@Temporal(TemporalType.DATE	)
	@Column(name="updateproduct_date")
	private Date updateProductDate=DEFULT_DATE_1900;
	
	@Temporal(TemporalType.DATE	)
	@Column(name="draft_date")
	private Date draftDate=DEFULT_DATE_1900;
	
	@Temporal(TemporalType.DATE	)
	@Column(name="passport_date")
	private Date passportDate=DEFULT_DATE_1900;
	
	@Column(name="physical_newsletter")
	private Boolean physicalNewsLetter=false;
	
	@Temporal(TemporalType.DATE	)
	@Column(name="updateproduct2_date")
	private Date updateProduct2Date=DEFULT_DATE_1900;
	
	@Column(name="round_monthlyfee")
	private Boolean roundMonthlyFee=false;
	
	@Temporal(TemporalType.DATE	)
	@Column(name="receive_letter")
	private Date receiveLetterDate;
	
	@Column(name="onetime_init")
	private Boolean oneTimeInit=false;
	
	private String employer;
	
	@Column(name="cancel_code")
	private Integer cancelCode=0;
	
	@Column(name="lead_id")
	private String leadId;
	
	@Column(name="census_headcount")
	private Integer censusHeadCount=0;
	
	@Column(name="issued_membership_refund")
	private Boolean issuedMembershipRefund=false;
	
	@Temporal(TemporalType.DATE	)
	@Column(name="last_dep_cards")
	private Date lastDepCardsDate=DEFULT_DATE_1900;
	
	@Column(name="spouse_email")
	private String spouseEmail;
	
	@Column(name="refund_amount")
	private  Double refundAmount=0.0;
	
	@Column(name="tax_rate")
	private Double taxRate=0.0;
	
	@Temporal(TemporalType.DATE	)		
	@Column(name="reactive_date")
	private Date reactiveDate = Constant.DEFULT_DATE_1900;
	
	@Column(name="application_source")
	private Integer applicationSource = 0;
	
	private Integer address;

	private Integer alternateAddress;
	
	private Integer burialAddress;

	public MemberDetails() {
		super();
	}

	public Integer getMemberId() {
		return memberId;
	}

	public void setMemberId(Integer memberId) {
		this.memberId = memberId;
	}

	public String getEmployeeId() {
		return employeeId;
	}

	public void setEmployeeId(String employeeId) {
		this.employeeId = employeeId;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public String getMi() {
		return mi;
	}

	public void setMi(String mi) {
		this.mi = mi;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public Date getBirthDate() {
		return birthDate;
	}

	public void setBirthDate(Date birthDate) {
		this.birthDate = birthDate;
	}

	public String getSpouseFirst() {
		return spouseFirst;
	}

	public void setSpouseFirst(String spouseFirst) {
		this.spouseFirst = spouseFirst;
	}

	public String getSpouseLast() {
		return spouseLast;
	}

	public void setSpouseLast(String spouseLast) {
		this.spouseLast = spouseLast;
	}

	public String getSpouseMi() {
		return spouseMi;
	}

	public void setSpouseMi(String spouseMi) {
		this.spouseMi = spouseMi;
	}

	public String getSpouseTitle() {
		return spouseTitle;
	}

	public void setSpouseTitle(String spouseTitle) {
		this.spouseTitle = spouseTitle;
	}

	public Date getSpouseBirthDate() {
		return spouseBirthDate;
	}

	public void setSpouseBirthDate(Date spouseBirthDate) {
		this.spouseBirthDate = spouseBirthDate;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getCellPhone() {
		return cellPhone;
	}

	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public Integer getReceivesNewsLetter() {
		return receivesNewsLetter;
	}

	public void setReceivesNewsLetter(Integer receivesNewsLetter) {
		this.receivesNewsLetter = receivesNewsLetter;
	}

	public Date getCancelDate() {
		return cancelDate;
	}

	public void setCancelDate(Date cancelDate) {
		this.cancelDate = cancelDate;
	}

	public Date getEffectiveDate() {
		return effectiveDate;
	}

	public void setEffectiveDate(Date effectiveDate) {
		this.effectiveDate = effectiveDate;
	}

	public Date getRenewDate() {
		return renewDate;
	}

	public void setRenewDate(Date renewDate) {
		this.renewDate = renewDate;
	}

	public Date getReinstateDate() {
		return reinstateDate;
	}

	public void setReinstateDate(Date reinstateDate) {
		this.reinstateDate = reinstateDate;
	}

	public Date getLastWelcomePackageDate() {
		return lastWelcomePackageDate;
	}

	public void setLastWelcomePackageDate(Date lastWelcomePackageDate) {
		this.lastWelcomePackageDate = lastWelcomePackageDate;
	}

	public Date getLastNoticeDate() {
		return lastNoticeDate;
	}

	public void setLastNoticeDate(Date lastNoticeDate) {
		this.lastNoticeDate = lastNoticeDate;
	}

	public Integer getRenew() {
		return renew;
	}

	public void setRenew(Integer renew) {
		this.renew = renew;
	}

	public String getFlashNote() {
		return flashNote;
	}

	public void setFlashNote(String flashNote) {
		this.flashNote = flashNote;
	}

	public Integer getAgentId() {
		return agentId;
	}

	public void setAgentId(Integer agentId) {
		this.agentId = agentId;
	}

	public Date getExpireDate() {
		return expireDate;
	}

	public void setExpireDate(Date expireDate) {
		this.expireDate = expireDate;
	}

	public Integer getPayType() {
		return payType;
	}

	public void setPayType(Integer payType) {
		this.payType = payType;
	}

	public Integer getCcType() {
		return ccType;
	}

	public void setCcType(Integer ccType) {
		this.ccType = ccType;
	}

	public Date getCcExpirationDate() {
		return ccExpirationDate;
	}

	public void setCcExpirationDate(Date ccExpirationDate) {
		this.ccExpirationDate = ccExpirationDate;
	}

	public String getAchBank() {
		return achBank;
	}

	public void setAchBank(String achBank) {
		this.achBank = achBank;
	}

	public String getAchRouting() {
		return achRouting;
	}

	public void setAchRouting(String achRouting) {
		this.achRouting = achRouting;
	}

	public String getAchNumber() {
		return achNumber;
	}

	public void setAchNumber(String achNumber) {
		this.achNumber = achNumber;
	}

	public Integer getAlternatePayer() {
		return alternatePayer;
	}

	public void setAlternatePayer(Integer alternatePayer) {
		this.alternatePayer = alternatePayer;
	}

	public Set<MemberDetails> getSubMembers() {
		return subMembers;
	}

	public void setSubMembers(Set<MemberDetails> subMembers) {
		this.subMembers = subMembers;
	}

	public Integer getIsAdvance() {
		return isAdvance;
	}

	public void setIsAdvance(Integer isAdvance) {
		this.isAdvance = isAdvance;
	}

	public BigDecimal getAdanceComm() {
		return adanceComm;
	}

	public void setAdanceComm(BigDecimal adanceComm) {
		this.adanceComm = adanceComm;
	}

	public Integer getModifiedBy() {
		return modifiedBy;
	}

	public void setModifiedBy(Integer modifiedBy) {
		this.modifiedBy = modifiedBy;
	}

	public Date getModifiedDate() {
		return modifiedDate;
	}

	public void setModifiedDate(Date modifiedDate) {
		this.modifiedDate = modifiedDate;
	}

	public Date getLastInvoiceDate() {
		return lastInvoiceDate;
	}

	public void setLastInvoiceDate(Date lastInvoiceDate) {
		this.lastInvoiceDate = lastInvoiceDate;
	}

	public Integer getActive() {
		return active;
	}

	public void setActive(Integer active) {
		this.active = active;
	}

	public Boolean getIsRenew() {
		return isRenew;
	}

	public void setIsRenew(Boolean isRenew) {
		this.isRenew = isRenew;
	}

	public Boolean getIsFFChargeBack() {
		return isFFChargeBack;
	}

	public void setIsFFChargeBack(Boolean isFFChargeBack) {
		this.isFFChargeBack = isFFChargeBack;
	}

	public Boolean getIsReinstatement() {
		return isReinstatement;
	}

	public void setIsReinstatement(Boolean isReinstatement) {
		this.isReinstatement = isReinstatement;
	}

	public String getSsn() {
		return ssn;
	}

	public void setSsn(String ssn) {
		this.ssn = ssn;
	}

	public String getCcNum() {
		return ccNum;
	}

	public void setCcNum(String ccNum) {
		this.ccNum = ccNum;
	}

	public String getCcNumStr() {
		return ccNumStr;
	}

	public void setCcNumStr(String ccNumStr) {
		this.ccNumStr = ccNumStr;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public Integer getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}

	public Integer getSoldRegion() {
		return soldRegion;
	}

	public void setSoldRegion(Integer soldRegion) {
		this.soldRegion = soldRegion;
	}

	public String getAlterId() {
		return alterId;
	}

	public void setAlterId(String alterId) {
		this.alterId = alterId;
	}

	public Date getLastServiceDate() {
		return lastServiceDate;
	}

	public void setLastServiceDate(Date lastServiceDate) {
		this.lastServiceDate = lastServiceDate;
	}

	public Date getUpdateProductDate() {
		return updateProductDate;
	}

	public void setUpdateProductDate(Date updateProductDate) {
		this.updateProductDate = updateProductDate;
	}

	public Date getDraftDate() {
		return draftDate;
	}

	public void setDraftDate(Date draftDate) {
		this.draftDate = draftDate;
	}

	public Date getPassportDate() {
		return passportDate;
	}

	public void setPassportDate(Date passportDate) {
		this.passportDate = passportDate;
	}

	public Boolean getPhysicalNewsLetter() {
		return physicalNewsLetter;
	}

	public void setPhysicalNewsLetter(Boolean physicalNewsLetter) {
		this.physicalNewsLetter = physicalNewsLetter;
	}

	public Date getUpdateProduct2Date() {
		return updateProduct2Date;
	}

	public void setUpdateProduct2Date(Date updateProduct2Date) {
		this.updateProduct2Date = updateProduct2Date;
	}

	public Boolean getRoundMonthlyFee() {
		return roundMonthlyFee;
	}

	public void setRoundMonthlyFee(Boolean roundMonthlyFee) {
		this.roundMonthlyFee = roundMonthlyFee;
	}

	public Date getReceiveLetterDate() {
		return receiveLetterDate;
	}

	public void setReceiveLetterDate(Date receiveLetterDate) {
		this.receiveLetterDate = receiveLetterDate;
	}

	public Boolean getOneTimeInit() {
		return oneTimeInit;
	}

	public void setOneTimeInit(Boolean oneTimeInit) {
		this.oneTimeInit = oneTimeInit;
	}

	public String getEmployer() {
		return employer;
	}

	public void setEmployer(String employer) {
		this.employer = employer;
	}

	public Integer getCancelCode() {
		return cancelCode;
	}

	public void setCancelCode(Integer cancelCode) {
		this.cancelCode = cancelCode;
	}

	public String getLeadId() {
		return leadId;
	}

	public void setLeadId(String leadId) {
		this.leadId = leadId;
	}

	public Integer getCensusHeadCount() {
		return censusHeadCount;
	}

	public void setCensusHeadCount(Integer censusHeadCount) {
		this.censusHeadCount = censusHeadCount;
	}

	public Boolean getIssuedMembershipRefund() {
		return issuedMembershipRefund;
	}

	public void setIssuedMembershipRefund(Boolean issuedMembershipRefund) {
		this.issuedMembershipRefund = issuedMembershipRefund;
	}

	public Date getLastDepCardsDate() {
		return lastDepCardsDate;
	}

	public void setLastDepCardsDate(Date lastDepCardsDate) {
		this.lastDepCardsDate = lastDepCardsDate;
	}

	public String getSpouseEmail() {
		return spouseEmail;
	}

	public void setSpouseEmail(String spouseEmail) {
		this.spouseEmail = spouseEmail;
	}

	public Double getRefundAmount() {
		return refundAmount;
	}

	public void setRefundAmount(Double refundAmount) {
		this.refundAmount = refundAmount;
	}

	public Double getTaxRate() {
		return taxRate;
	}

	public void setTaxRate(Double taxRate) {
		this.taxRate = taxRate;
	}

	public Date getReactiveDate() {
		return reactiveDate;
	}

	public void setReactiveDate(Date reactiveDate) {
		this.reactiveDate = reactiveDate;
	}

	public Integer getApplicationSource() {
		return applicationSource;
	}

	public void setApplicationSource(Integer applicationSource) {
		this.applicationSource = applicationSource;
	}

	public Integer getAddress() {
		return address;
	}

	public void setAddress(Integer address) {
		this.address = address;
	}

	public Integer getAlternateAddress() {
		return alternateAddress;
	}

	public void setAlternateAddress(Integer alternateAddress) {
		this.alternateAddress = alternateAddress;
	}

	public Integer getBurialAddress() {
		return burialAddress;
	}

	public void setBurialAddress(Integer burialAddress) {
		this.burialAddress = burialAddress;
	}
	
}
