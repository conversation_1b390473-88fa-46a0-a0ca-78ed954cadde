package com.masa.pts.core.domain;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name="TDAT_MEMBER_FULFILLMENT_REQUEST",schema = "dbo")
public class MemberFulfillmentRequest implements Serializable {

	private static final long serialVersionUID = -7072000522130365080L;
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="id")
	private Integer id;
	
	private Integer memberId;
	private Integer productId;
	private Integer feeId;
	private Integer upgradeType;
	private Integer fulfillmentType;
	private Date requestedDate;
	private Date processedDate;
	private String uid;
	private Boolean isDelete=Boolean.FALSE;
	private Date deletedDate;
	private Integer deletedBy=0;
	private Date createdDate;
	private Date modifiedDate;
	private Integer createdBy;
	private Integer modifiedBy;
	
	@Column(name="is_census_member")
	private boolean isCensusMember;

	@Column(name="pkg_cnt")
	private Integer pkgCnt;
	
	@Column(name="shipping_method")
	private Integer shippingMethod;
	
	@Column(name="shipping_address1")
	private String shippingAddress1;
	
	@Column(name="shipping_address2")
	private String shippingAddress2;
	
	@Column(name="shipping_address3")
	private String shippingAddress3;
	
	@Column(name="shipping_city")
	private String shippingCity;
	
	@Column(name="shipping_state")
	private String shippingState;
	
	@Column(name="shipping_country")
	private String shippingCountry;
	
	@Column(name="shipping_zip")
	private String shippingZip;
	
	@Column(name="shipping_zip4")
	private String shippingZip4;
	
	@Column(name="shipping_contact")
	private String shippingContact; 
	
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public Integer getMemberId() {
		return memberId;
	}
	public void setMemberId(Integer memberId) {
		this.memberId = memberId;
	}
	public Integer getProductId() {
		return productId;
	}
	public void setProductId(Integer productId) {
		this.productId = productId;
	}
	public Integer getFeeId() {
		return feeId;
	}
	public void setFeeId(Integer feeId) {
		this.feeId = feeId;
	}
	public Integer getUpgradeType() {
		return upgradeType;
	}
	public void setUpgradeType(Integer upgradeType) {
		this.upgradeType = upgradeType;
	}	
	public Integer getFulfillmentType() {
		return fulfillmentType;
	}
	public void setFulfillmentType(Integer fulfillmentType) {
		this.fulfillmentType = fulfillmentType;
	}
	public Date getRequestedDate() {
		return requestedDate;
	}
	public void setRequestedDate(Date requestedDate) {
		this.requestedDate = requestedDate;
	}
	public Date getProcessedDate() {
		return processedDate;
	}
	public void setProcessedDate(Date processedDate) {
		this.processedDate = processedDate;
	}
	public Date getCreatedDate() {
		return createdDate;
	}
	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}
	public Date getModifiedDate() {
		return modifiedDate;
	}
	public void setModifiedDate(Date modifiedDate) {
		this.modifiedDate = modifiedDate;
	}
	public Integer getCreatedBy() {
		return createdBy;
	}
	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}
	public Integer getModifiedBy() {
		return modifiedBy;
	}
	public void setModifiedBy(Integer modifiedBy) {
		this.modifiedBy = modifiedBy;
	}
	public String getUid() {
		return uid;
	}
	public void setUid(String uid) {
		this.uid = uid;
	}
	public Boolean getIsDelete() {
		return isDelete;
	}
	public void setIsDelete(Boolean isDelete) {
		this.isDelete = isDelete;
	}
	public Date getDeletedDate() {
		return deletedDate;
	}
	public void setDeletedDate(Date deletedDate) {
		this.deletedDate = deletedDate;
	}
	public Integer getDeletedBy() {
		return deletedBy;
	}
	public void setDeletedBy(Integer deletedBy) {
		this.deletedBy = deletedBy;
	}
	public boolean isCensusMember() {
		return isCensusMember;
	}
	public void setCensusMember(boolean isCensusMember) {
		this.isCensusMember = isCensusMember;
	}
	public Integer getPkgCnt() {
		return pkgCnt;
	}
	public void setPkgCnt(Integer pkgCnt) {
		this.pkgCnt = pkgCnt;
	}
	public Integer getShippingMethod() {
		return shippingMethod;
	}
	public void setShippingMethod(Integer shippingMethod) {
		this.shippingMethod = shippingMethod;
	}
	public String getShippingAddress1() {
		return shippingAddress1;
	}
	public void setShippingAddress1(String shippingAddress1) {
		this.shippingAddress1 = shippingAddress1;
	}
	public String getShippingAddress2() {
		return shippingAddress2;
	}
	public void setShippingAddress2(String shippingAddress2) {
		this.shippingAddress2 = shippingAddress2;
	}
	public String getShippingAddress3() {
		return shippingAddress3;
	}
	public void setShippingAddress3(String shippingAddress3) {
		this.shippingAddress3 = shippingAddress3;
	}
	public String getShippingCity() {
		return shippingCity;
	}
	public void setShippingCity(String shippingCity) {
		this.shippingCity = shippingCity;
	}
	public String getShippingState() {
		return shippingState;
	}
	public void setShippingState(String shippingState) {
		this.shippingState = shippingState;
	}
	public String getShippingCountry() {
		return shippingCountry;
	}
	public void setShippingCountry(String shippingCountry) {
		this.shippingCountry = shippingCountry;
	}
	public String getShippingZip() {
		return shippingZip;
	}
	public void setShippingZip(String shippingZip) {
		this.shippingZip = shippingZip;
	}
	public String getShippingZip4() {
		return shippingZip4;
	}
	public void setShippingZip4(String shippingZip4) {
		this.shippingZip4 = shippingZip4;
	}
	public String getShippingContact() {
		return shippingContact;
	}
	public void setShippingContact(String shippingContact) {
		this.shippingContact = shippingContact;
	}	
	public MemberFulfillmentRequest() {
		super();
	}
	@Override
	public String toString() {
		return "MemberFulfillmentRequest [id=" + id + ", memberId=" + memberId + ", productId=" + productId + ", feeId=" + feeId + ", upgradeType=" + upgradeType + ", fulfillmentType="
				+ fulfillmentType + ", requestedDate=" + requestedDate + ", processedDate=" + processedDate + ", createdDate=" + createdDate + ", modifiedDate=" + modifiedDate + ", createdBy="
				+ createdBy + ", modifiedBy=" + modifiedBy + "]";
	}	
}
