package com.masa.pts.core.domain;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

@Entity
@Table(name="TDAT_CLAIM_NOTE",schema = "dbo")
public class ClaimNote implements Serializable {

	private static final long serialVersionUID = 4485187930062088073L;
	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "claim_note_seq")
	@SequenceGenerator(name = "claim_note_seq", sequenceName = "TDAT_CLAIM_NOTESEQ", allocationSize = 1, schema = "dbo")
	@Column(name="note_id")
	private Integer noteId;
	private Integer type;
	
	@ManyToOne	
	@JoinColumn(name="claim_id") 
	private Claim claim;
	
	private Integer enteredBy;
	private Date noteDate;
	private String note;
	private Boolean isDelete;
	private Date deleteDate;
	private Integer deletedBy;
	public ClaimNote() {
		super();
	}
	public ClaimNote(Integer noteId, Integer type, Claim claim, Integer enteredBy, Date noteDate, String note, Boolean isDelete, Date deleteDate, Integer deletedBy) {
		super();
		this.noteId = noteId;
		this.type = type;
		this.claim = claim;
		this.enteredBy = enteredBy;
		this.noteDate = noteDate;
		this.note = note;
		this.isDelete = isDelete;
		this.deleteDate = deleteDate;
		this.deletedBy = deletedBy;
	}
	public Integer getNoteId() {
		return noteId;
	}
	public void setNoteId(Integer noteId) {
		this.noteId = noteId;
	}
	public Integer getType() {
		return type;
	}
	public void setType(Integer type) {
		this.type = type;
	}
	/*public Claim getClaim() {
		return claim;
	}*/
	public void setClaim(Claim claim) {
		this.claim = claim;
	}
	public Integer getEnteredBy() {
		return enteredBy;
	}
	public void setEnteredBy(Integer enteredBy) {
		this.enteredBy = enteredBy;
	}
	public Date getNoteDate() {
		return noteDate;
	}
	public void setNoteDate(Date noteDate) {
		this.noteDate = noteDate;
	}
	public String getNote() {
		return note;
	}
	public void setNote(String note) {
		this.note = note;
	}
	public Boolean getIsDelete() {
		return isDelete;
	}
	public void setIsDelete(Boolean isDelete) {
		this.isDelete = isDelete;
	}
	public Date getDeleteDate() {
		return deleteDate;
	}
	public void setDeleteDate(Date deleteDate) {
		this.deleteDate = deleteDate;
	}
	public Integer getDeletedBy() {
		return deletedBy;
	}
	public void setDeletedBy(Integer deletedBy) {
		this.deletedBy = deletedBy;
	}
	@Override
	public String toString() {
		return "ClaimNote [noteId=" + noteId + ", type=" + type + ", claim=" + claim + ", enteredBy=" + enteredBy + ", noteDate=" + noteDate + ", note=" + note + ", isDelete=" + isDelete
				+ ", deleteDate=" + deleteDate + ", deletedBy=" + deletedBy + "]";
	}	
}
