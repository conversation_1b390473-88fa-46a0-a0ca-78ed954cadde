package com.masa.pts.core.domain;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonFormat;
@Entity
@Table(name="ex_edi_record_status_log",schema = "dbo")
public class MemberFileReviewStatusEntity implements Serializable {

	private static final long serialVersionUID = -9193414685930384360L;

	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="id")
	private Integer id;
	
	@ManyToOne()
	@JoinColumn(name="record_id")
	private MemberFile memberFile;
	
	@Column(name="current_status")
	@Enumerated(EnumType.STRING)
	private ReviewStatus currentStatus;
	
	@Column(name="new_status")
	@Enumerated(EnumType.STRING)
	private ReviewStatus newStatus;
	
	@Column(name="status_date")
	private Date statusDate;

	@Column(name="status_updated_by")
	private String statusUpdatedBy;
	
	public MemberFileReviewStatusEntity() {
		super();
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	//json output - recursion
	/*
	public MemberFile getMemberFile() {
		return memberFile;
	}
	*/

	public void setMemberFile(MemberFile memberFile) {
		this.memberFile = memberFile;
	}

	public ReviewStatus getCurrentStatus() {
		return currentStatus;
	}

	public void setCurrentStatus(ReviewStatus currentStatus) {
		this.currentStatus = currentStatus;
	}

	public ReviewStatus getNewStatus() {
		return newStatus;
	}

	public void setNewStatus(ReviewStatus newStatus) {
		this.newStatus = newStatus;
	}

	public Date getStatusDate() {
		return statusDate;
	}

	public void setStatusDate(Date statusDate) {
		this.statusDate = statusDate;
	}

	public String getStatusUpdatedBy() {
		return statusUpdatedBy;
	}

	public void setStatusUpdatedBy(String statusUpdatedBy) {
		this.statusUpdatedBy = statusUpdatedBy;
	}
	
	@Override
	public int hashCode() {
		return Objects.hash(currentStatus,newStatus,statusDate);
	}

	@Override
	public boolean equals(Object o) {
		if(this == o) return true;
		if(o == null || getClass() != o.getClass()) return false;
		MemberFileReviewStatusEntity that = (MemberFileReviewStatusEntity)o;
		return Objects.equals(currentStatus, that.currentStatus) &&
			   Objects.equals(newStatus, that.newStatus) &&
			   Objects.equals(statusDate, that.statusDate)
				;
	}
}
