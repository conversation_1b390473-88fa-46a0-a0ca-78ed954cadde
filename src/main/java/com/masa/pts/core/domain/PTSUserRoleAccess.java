package com.masa.pts.core.domain;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.IdClass;
import javax.persistence.Table;


@Entity
@IdClass(PTSRoleAccessId.class)
@Table(name="TLNK_ROLE_ACCESS",schema = "dbo")
public class PTSUserRoleAccess implements Serializable {

	private static final long serialVersionUID = -534557867923508995L;

	@Id
	@Column(name="role_id")
	private Integer roleId;
	
	@Id
	@Column(name="access_id")
	private Integer accessId;
	
	

	public PTSUserRoleAccess() {
		super();
	}

	public Integer getRoleId() {
		return roleId;
	}

	public void setRoleId(Integer roleId) {
		this.roleId = roleId;
	}

	public Integer getAccessId() {
		return accessId;
	}

	public void setAccessId(Integer accessId) {
		this.accessId = accessId;
	}

	
}
