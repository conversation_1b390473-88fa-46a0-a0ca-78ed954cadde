package com.masa.pts.core.domain;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

@Entity
@Table(name="TDAT_NOTE",schema = "dbo")
public class Note implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1072771366019085630L;

	public static final int NOTE_TYPE_MEMBER = 1;
	public static final int NOTE_TYPE_CLAIM = 2;
	
	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "note_seq")
	@SequenceGenerator(name = "note_seq", sequenceName = "TDAT_NOTESEQ", allocationSize = 1, schema = "dbo")
	@Column(name="note_id")
	private Integer noteId;
	private Integer type;
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name="member_id") 
	private Member member;
	
	private Integer enteredBy;
	private Date noteDate;
	private String note;
	private Boolean isDelete;
	private Date deleteDate;
	private Integer deletedBy;
	public Note() {
		super();
	}
	public Note(Integer type,Integer enteredBy, Date noteDate, String note, Boolean isDelete,
			Date deleteDate, Integer deletedBy) {
		super();
		this.type = type;
		//this.memberId = memberId;
		this.enteredBy = enteredBy;
		this.noteDate = noteDate;
		this.note = note;
		this.isDelete = isDelete;
		this.deleteDate = deleteDate;
		this.deletedBy = deletedBy;
	}
	public Integer getNoteId() {
		return noteId;
	}
	public void setNoteId(Integer noteId) {
		this.noteId = noteId;
	}
	public Integer getType() {
		return type;
	}
	public void setType(Integer type) {
		this.type = type;
	}
	public Integer getEnteredBy() {
		return enteredBy;
	}
	public void setEnteredBy(Integer enteredBy) {
		this.enteredBy = enteredBy;
	}
	public Date getNoteDate() {
		return noteDate;
	}
	public void setNoteDate(Date noteDate) {
		this.noteDate = noteDate;
	}
	public String getNote() {
		return note;
	}
	public void setNote(String note) {
		this.note = note;
	}
	public Boolean getIsDelete() {
		return isDelete;
	}
	public void setIsDelete(Boolean isDelete) {
		this.isDelete = isDelete;
	}
	public Date getDeleteDate() {
		return deleteDate;
	}
	public void setDeleteDate(Date deleteDate) {
		this.deleteDate = deleteDate;
	}
	public Integer getDeletedBy() {
		return deletedBy;
	}
	public void setDeletedBy(Integer deletedBy) {
		this.deletedBy = deletedBy;
	}
	public void setMember(Member member) {
		this.member = member;
	}	
}
