package com.masa.pts.core.domain;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import com.google.common.collect.ImmutableList;
import com.masa.pts.core.model.MemberFileDTO;
import com.masa.pts.core.service.PTSUtilityService;
import com.masa.pts.core.utils.common.ExcelDocument;

import static com.masa.pts.core.domain.Constant.DEFAULT_DATE_PATTERN;

public class MemberFileExcelDocument implements ExcelDocument<MemberFileDTO> {

	private List<List<Object>> dataRows;
	private List<List<Object>> totalRow;
	
	public MemberFileExcelDocument(List<MemberFileDTO> detailsList) {
		setDataRow(detailsList);
	}
	
	private String formatDate(Date input)
	{
		SimpleDateFormat dateFormat = (SimpleDateFormat) DateFormat.getInstance();
		dateFormat.applyPattern(DEFAULT_DATE_PATTERN);
		
		return input == null ? "" : dateFormat.format(input);
	}
	
	private String formatString(String input)
	{
		return input == null ? "" : input;
	}

	@Override
	public void setDataRow(List<MemberFileDTO> rowData) {
		this.dataRows = rowData.stream().map(memberFile -> Arrays.<Object>asList(
				memberFile.getModification()
						+ (memberFile.getMemberAction() == null ? "" : "-" + memberFile.getMemberAction()),
				memberFile.getMasaMemberId(), memberFile.getContractNumber(),
				String.join(" ", formatString(memberFile.getFirstName()), formatString(memberFile.getMiddleName()),
						formatString(memberFile.getLastName())),
				memberFile.getRelationship(), memberFile
						.getProductType(),
				memberFile.getAgentCode(), memberFile.getGroupCode(), formatDate(memberFile.getEffectiveDate()),
				 memberFile.getStatus().toString(),
				memberFile.getSourceFileName(),
				PTSUtilityService.formatUtilDate(memberFile.getBirthDate()),
				memberFile.getCreatedDate(),
				memberFile.getFileOwner(),
				"Errors [" + formatString(memberFile.getErrorMessage()) + "] Comments ["
						+ formatString(memberFile.getProcessComments()) + "]"))
				.collect(Collectors.toList());
	}
	
	@Override
	public void setTotalRow(List<MemberFileDTO> detailsList) {
		this.totalRow = Collections.emptyList();
	}

	@Override
	public List<String> getColumnNames() {
		return ImmutableList.of("Action", "Member Id", "Employee Id", "Member Name", "Relationship", "Product Type",
				"Agent", "Group", "Effective Date", "Status", "Source file name", "Birth Date", "File Uploaded Date",
				"File Owner",
				"Comments");
	}

	@Override
	public List<List<Object>> getDataRows() {
		return this.dataRows;
	}

	@Override
	public List<List<Object>> getTotalRow() {
		return this.totalRow;
	}

}
