package com.masa.pts.core.domain;

import java.io.Serializable;
import java.util.Date;

import javax.annotation.concurrent.Immutable;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.Formula;

import com.fasterxml.jackson.annotation.JsonFormat;

@Entity
@Table(name="TDAT_INVOICE",schema = "dbo")
@Immutable
public class InvoiceSearch implements Serializable {

	private static final long serialVersionUID = 1725873616642810888L;

	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="id")
	private Integer invoiceId;
	
	@Column(name="group_id")
	private Integer groupId;
	
	@Column(name="invoice_type")
	private Integer invoiceType;
	
	@Column(name="total_due")
	private double totalDue;
	
	@Temporal(TemporalType.DATE)
	@Column(name="inv_date")
	private Date invoiceDate;
	
	@Column(name="is_paid")
	private Boolean isPaid;
	
	private Date createdDate;
	private Date modifiedDate;
	private Integer createdBy;
	private Integer modifiedBy;
	
	@Formula("(SELECT TDAT_GROUP.GROUP_CODE FROM TDAT_GROUP WHERE TDAT_GROUP.GROUP_ID = GROUP_ID)")
	private String groupCode;
	
	@Formula("(SELECT TDAT_GROUP.GROUP_NAME FROM TDAT_GROUP WHERE TDAT_GROUP.GROUP_ID = GROUP_ID)")
	private String groupName;

	@Formula("(SELECT TDAT_SALES_CHANNEL.id " +
			"FROM TDAT_INVOICE " +
			"LEFT JOIN TDAT_GROUP ON TDAT_INVOICE.GROUP_ID = TDAT_GROUP.GROUP_ID " +
			"LEFT JOIN tlnk_Group_Line ON tdat_Group.group_id = tlnk_Group_Line.idGroup " +
			"LEFT JOIN tdat_Business_Line ON tlnk_Group_Line.idLine = tdat_Business_Line.id " +
			"left join tdat_sales_channel on tdat_sales_channel.id = tdat_business_line.channel " +
			"WHERE TDAT_INVOICE.id = id )")
	private Integer salesChannelId;
	
	public InvoiceSearch() {
		super();
	}

	public Integer getInvoiceId() {
		return invoiceId;
	}

	public void setInvoiceId(Integer invoiceId) {
		this.invoiceId = invoiceId;
	}

	public Integer getGroupId() {
		return groupId;
	}

	public void setGroupId(Integer groupId) {
		this.groupId = groupId;
	}

	public Integer getInvoiceType() {
		return invoiceType;
	}

	public void setInvoiceType(Integer invoiceType) {
		this.invoiceType = invoiceType;
	}

	public double getTotalDue() {
		return totalDue;
	}

	public void setTotalDue(double totalDue) {
		this.totalDue = totalDue;
	}

	public Date getInvoiceDate() {
		return invoiceDate;
	}

	public void setInvoiceDate(Date invoiceDate) {
		this.invoiceDate = invoiceDate;
	}

	public Boolean getIsPaid() {
		return isPaid;
	}

	public void setIsPaid(Boolean isPaid) {
		this.isPaid = isPaid;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public Date getModifiedDate() {
		return modifiedDate;
	}

	public void setModifiedDate(Date modifiedDate) {
		this.modifiedDate = modifiedDate;
	}

	public Integer getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}

	public Integer getModifiedBy() {
		return modifiedBy;
	}

	public void setModifiedBy(Integer modifiedBy) {
		this.modifiedBy = modifiedBy;
	}

	public String getGroupCode() {
		return groupCode;
	}

	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}

	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

	public Integer getSalesChannelId() {
		return salesChannelId;
	}

	public void setSalesChannelId(Integer salesChannelId) {
		this.salesChannelId = salesChannelId;
	}
}
