package com.masa.pts.core.domain;

import java.io.Serializable;

public class PTSUserAccessId implements Serializable {
	
	private static final long serialVersionUID = 2927344329611497404L;
	
	private Integer employeeId;
	
	private Integer accessId;

	public PTSUserAccessId(Integer employeeId, Integer accessId) {
		super();
		this.employeeId = employeeId;
		this.accessId = accessId;
	}

	public PTSUserAccessId() {
		super();
	}

	public Integer getEmployeeId() {
		return employeeId;
	}

	public void setEmployeeId(Integer employeeId) {
		this.employeeId = employeeId;
	}

	public Integer getAccessId() {
		return accessId;
	}

	public void setAccessId(Integer accessId) {
		this.accessId = accessId;
	}

	@Override
	public int hashCode() {
		return super.hashCode();
	}

	@Override
	public boolean equals(Object obj) {
		return super.equals(obj);
	}
	
}
