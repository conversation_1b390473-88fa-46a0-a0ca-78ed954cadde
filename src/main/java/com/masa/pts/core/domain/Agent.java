package com.masa.pts.core.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;

import org.hibernate.annotations.Formula;

import com.fasterxml.jackson.annotation.JsonView;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

@Entity
@Table(name="TDAT_AGENT",schema = "dbo")
public class Agent implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 3663438085617679324L;

	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="agent_id")
	private Integer agentId;
	
	private String agentNum;
	private Integer manager;
	private Boolean isManualManager;
	private String agentAccount;
	
	private Integer address;
	
	@JsonView(MobileView.Member.class)
	private String agentFirst;
	@JsonView(MobileView.Member.class)
	private String agentLast;
	@JsonView(MobileView.Member.class)
	private String agentMi;
	@JsonView(MobileView.Member.class)
	private String homePhone;
	@JsonView(MobileView.Member.class)
	private String cellPhone;
	@JsonView(MobileView.Member.class)
	private String workPhone;
	@JsonView(MobileView.Member.class)
	private String email;
	private String region;
	private String ssn;
	private Date dateOfBirth;
	private Boolean isAdvance;
	private BigDecimal advanceComm;
	private Date startDate;
	private Date endDate;
	private Date restartDate;
	private String comments;
	private Boolean active;
	private BigDecimal fixedNew;
	private BigDecimal fixedRenew;
	private String note;
	@JsonView(MobileView.Member.class)
	private String dba;
	private Boolean isBroker;
	private BigDecimal installmentComm;
	private Date vestedDate;
	private String adpIdentifier;
	private Boolean useFlatRate;

	@Formula(" (agent_first + ' ' +agent_last+ ' '+agent_num)")
	private String agentFullName;
	
	@OneToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "sales_channel_id")
	@JsonSerialize(as = SalesChannel.class)
	private SalesChannel salesChannel;
	
	@OneToMany(fetch=FetchType.LAZY, cascade = CascadeType.ALL,mappedBy = "agent", orphanRemoval = true)
	private Set<AgentCommission> commissions = new HashSet<>();
	
	public Agent() {
		super();
	}

	public Agent(Integer agentId) {
		this.agentId = agentId;
	}

	public Agent(String agentNum, Integer manager, Boolean isManualManager, String agentAccount, Integer address,
			String agentFirst, String agentLast, String agentMi, String homePhone, String cellPhone, String workPhone,
			String email, String region, String ssn, Date dateOfBirth, Boolean isAdvance, BigDecimal advanceComm,
			Date startDate, Date endDate, Date restartDate, String comments, Boolean active, BigDecimal fixedNew,
			BigDecimal fixedRenew, String note, String dba, Boolean isBroker,
			BigDecimal installmentComm, Date vestedDate, String adpIdentifier, Boolean useFlatRate) {
		super();
		this.agentNum = agentNum;
		this.manager = manager;
		this.isManualManager = isManualManager;
		this.agentAccount = agentAccount;
		this.address = address;
		this.agentFirst = agentFirst;
		this.agentLast = agentLast;
		this.agentMi = agentMi;
		this.homePhone = homePhone;
		this.cellPhone = cellPhone;
		this.workPhone = workPhone;
		this.email = email;
		this.region = region;
		this.ssn = ssn;
		this.dateOfBirth = dateOfBirth;
		this.isAdvance = isAdvance;
		this.advanceComm = advanceComm;
		this.startDate = startDate;
		this.endDate = endDate;
		this.restartDate = restartDate;
		this.comments = comments;
		this.active = active;
		this.fixedNew = fixedNew;
		this.fixedRenew = fixedRenew;
		this.note = note;
		this.dba = dba;
		this.isBroker = isBroker;
		this.installmentComm = installmentComm;
		this.vestedDate = vestedDate;
		this.adpIdentifier = adpIdentifier;
		this.useFlatRate = useFlatRate;
	}

	public Integer getAgentId() {
		return agentId;
	}

	public void setAgentId(Integer agentId) {
		this.agentId = agentId;
	}

	public String getAgentNum() {
		return agentNum;
	}

	public void setAgentNum(String agentNum) {
		this.agentNum = agentNum;
	}

	public Integer getManager() {
		return manager;
	}

	public void setManager(Integer manager) {
		this.manager = manager;
	}

	public Boolean getIsManualManager() {
		return isManualManager;
	}

	public void setIsManualManager(Boolean isManualManager) {
		this.isManualManager = isManualManager;
	}

	public String getAgentAccount() {
		return agentAccount;
	}

	public void setAgentAccount(String agentAccount) {
		this.agentAccount = agentAccount;
	}

	public Integer getAddress() {
		return address;
	}

	public void setAddress(Integer address) {
		this.address = address;
	}

	public String getAgentFirst() {
		return agentFirst;
	}

	public void setAgentFirst(String agentFirst) {
		this.agentFirst = agentFirst;
	}

	public String getAgentLast() {
		return agentLast;
	}

	public void setAgentLast(String agentLast) {
		this.agentLast = agentLast;
	}

	public String getAgentMi() {
		return agentMi;
	}

	public void setAgentMi(String agentMi) {
		this.agentMi = agentMi;
	}

	public String getHomePhone() {
		return homePhone;
	}

	public void setHomePhone(String homePhone) {
		this.homePhone = homePhone;
	}

	public String getCellPhone() {
		return cellPhone;
	}

	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}

	public String getWorkPhone() {
		return workPhone;
	}

	public void setWorkPhone(String workPhone) {
		this.workPhone = workPhone;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getRegion() {
		return region;
	}

	public void setRegion(String region) {
		this.region = region;
	}

	public String getSsn() {
		return ssn;
	}

	public void setSsn(String ssn) {
		this.ssn = ssn;
	}

	public Date getDateOfBirth() {
		return dateOfBirth;
	}

	public void setDateOfBirth(Date dateOfBirth) {
		this.dateOfBirth = dateOfBirth;
	}

	public Boolean getIsAdvance() {
		return isAdvance;
	}

	public void setIsAdvance(Boolean isAdvance) {
		this.isAdvance = isAdvance;
	}

	public BigDecimal getAdvanceComm() {
		return advanceComm;
	}

	public void setAdvanceComm(BigDecimal advanceComm) {
		this.advanceComm = advanceComm;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public Date getRestartDate() {
		return restartDate;
	}

	public void setRestartDate(Date restartDate) {
		this.restartDate = restartDate;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	public Boolean getActive() {
		return active;
	}

	public void setActive(Boolean active) {
		this.active = active;
	}

	public BigDecimal getFixedNew() {
		return fixedNew;
	}

	public void setFixedNew(BigDecimal fixedNew) {
		this.fixedNew = fixedNew;
	}

	public BigDecimal getFixedRenew() {
		return fixedRenew;
	}

	public void setFixedRenew(BigDecimal fixedRenew) {
		this.fixedRenew = fixedRenew;
	}

	public String getNote() {
		return note;
	}

	public void setNote(String note) {
		this.note = note;
	}

	public String getDba() {
		return dba;
	}

	public void setDba(String dba) {
		this.dba = dba;
	}

	public Boolean getIsBroker() {
		return isBroker;
	}

	public void setIsBroker(Boolean isBroker) {
		this.isBroker = isBroker;
	}

	public BigDecimal getInstallmentComm() {
		return installmentComm;
	}

	public void setInstallmentComm(BigDecimal installmentComm) {
		this.installmentComm = installmentComm;
	}

	public Date getVestedDate() {
		return vestedDate;
	}

	public void setVestedDate(Date vestedDate) {
		this.vestedDate = vestedDate;
	}

	public String getAdpIdentifier() {
		return adpIdentifier;
	}

	public void setAdpIdentifier(String adpIdentifier) {
		this.adpIdentifier = adpIdentifier;
	}

	public Boolean getUseFlatRate() {
		if(null == useFlatRate)
			return false;
		return useFlatRate;
	}
	public void setUseFlatRate(Boolean useFlatRate) {
		this.useFlatRate = useFlatRate;
	}
	/*public Set<AgentCommission> getCommissions() {
		return commissions;
	}

	public void setCommissions(Set<AgentCommission> commissions) {
		this.commissions = commissions;
	}
	
	public void addCommission(AgentCommission commission) {
		this.commissions.add(commission);
		commission.setAgent(this);
	}
	
	public void removeCommission(AgentCommission commission) {
		this.commissions.remove(commission);
		commission.setAgent(null);
	}*/
	
	/*public Set<AgentManager> getManagers() {
		return managers;
	}

	public void setManagers(Set<AgentManager> managers) {
		this.managers = managers;
	}*/

	public interface AgentSummary {
		Integer getAgentId();
		String getAgentNum();
	}

	public SalesChannel getSalesChannel() {
		return salesChannel;
	}

	public void setSalesChannel(SalesChannel salesChannel) {
		this.salesChannel = salesChannel;
	}

	public String getAgentFullName() {
		return agentFullName;
	}

	public void setAgentFullName(String agentFullName) {
		this.agentFullName = agentFullName;
	}

	@Override
	public String toString() {
		return "Agent{" +
				"agentId=" + agentId +
				", agentNum='" + agentNum + '\'' +
				", manager=" + manager +
				", isManualManager=" + isManualManager +
				", agentAccount='" + agentAccount + '\'' +
				", address=" + address +
				", agentFirst='" + agentFirst + '\'' +
				", agentLast='" + agentLast + '\'' +
				", agentMi='" + agentMi + '\'' +
				", homePhone='" + homePhone + '\'' +
				", cellPhone='" + cellPhone + '\'' +
				", workPhone='" + workPhone + '\'' +
				", email='" + email + '\'' +
				", region='" + region + '\'' +
				", ssn='" + ssn + '\'' +
				", dateOfBirth=" + dateOfBirth +
				", isAdvance=" + isAdvance +
				", advanceComm=" + advanceComm +
				", startDate=" + startDate +
				", endDate=" + endDate +
				", restartDate=" + restartDate +
				", comments='" + comments + '\'' +
				", active=" + active +
				", fixedNew=" + fixedNew +
				", fixedRenew=" + fixedRenew +
				", note='" + note + '\'' +
				", dba='" + dba + '\'' +
				", isBroker=" + isBroker +
				", installmentComm=" + installmentComm +
				", vestedDate=" + vestedDate +
				", adpIdentifier='" + adpIdentifier + '\'' +
				", useFlatRate=" + useFlatRate +
				", agentFullName='" + agentFullName + '\'' +
				", salesChannel='" + salesChannel + '\'' +
				", commissions=" + commissions +
				'}';
	}

	public interface AgentIdInfo {
		Integer getAgentId();
		String getAgentNum();
	}
	
}
