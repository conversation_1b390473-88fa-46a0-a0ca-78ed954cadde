package com.masa.pts.core.domain;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.annotation.concurrent.Immutable;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.Formula;
import org.hibernate.annotations.Where;

@Entity
@Table(name="TDAT_MEMBER",schema = "dbo")
@Immutable
public class ClaimMember implements Serializable {

	private static final long serialVersionUID = 174332733534500667L;

	private static final Date DEFULT_DATE_1900 =  Date.from(LocalDate.of(1900, 1, 1).atStartOfDay(java.time.ZoneId.systemDefault()).toInstant());
	
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="member_id")
	private Integer memberId;
	
	@Column(name="employee_id")
	private String employeeId;
	
	@Column(name="first_name")
	private String firstName;
	
	@Column(name="last_name")
	private String lastName;
	
	private String mi;
	
	private String title;
	
	@Temporal(TemporalType.DATE	)
	@Column(name="birth_date")
	private Date birthDate;
	
	@Column(name="spouse_first")
	private String spouseFirst;
	
	@Column(name="spouse_last")
	private String spouseLast;
	
	@Column(name="spouse_mi")
	private String spouseMi;
	
	@Column(name="spouse_title")
	private String spouseTitle;
	
	@Temporal(TemporalType.DATE	)
	@Column(name="spouse_birth_date")
	private Date spouseBirthDate;
	
	private String phone;
	
	@Column(name="cell_phone")
	private String cellPhone;
	
	private String email;
	
	@Temporal(TemporalType.DATE	)
	@Column(name="cancel_date")	
	private Date cancelDate=DEFULT_DATE_1900;

	@Temporal(TemporalType.DATE	)
	@Column(name="effective_date")
	private Date effectiveDate;
	
	@Temporal(TemporalType.DATE	)
	@Column(name="renew_date")
	private Date renewDate;
	private Boolean active=true;
	
	@Formula("(SELECT TDAT_GROUP.GROUP_CODE FROM TDAT_GROUP WHERE TDAT_GROUP.GROUP_ID = GROUP_ID)")
	private String groupCode;

	
	@OneToMany(fetch=FetchType.EAGER,cascade = CascadeType.ALL, orphanRemoval = true)
	@JoinColumn(name="member_id")
	@OrderBy(value="product_id,fee_id")
	@Where(clause="product_id > 0")
	private Set<MemberFee> memberFee = new HashSet<MemberFee>();
	
	
	@OneToMany(fetch=FetchType.EAGER,cascade = CascadeType.ALL,mappedBy = "member", orphanRemoval = true)
	@OrderBy(value="product_Id,agent_position")
	private Set<MemberCommission> commission = new HashSet<MemberCommission>();


	public Integer getMemberId() {
		return memberId;
	}

	public void setMemberId(Integer memberId) {
		this.memberId = memberId;
	}

	public String getEmployeeId() {
		return employeeId;
	}

	public void setEmployeeId(String employeeId) {
		this.employeeId = employeeId;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public String getMi() {
		return mi;
	}

	public void setMi(String mi) {
		this.mi = mi;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public Date getBirthDate() {
		return birthDate;
	}

	public void setBirthDate(Date birthDate) {
		this.birthDate = birthDate;
	}

	public String getSpouseFirst() {
		return spouseFirst;
	}

	public void setSpouseFirst(String spouseFirst) {
		this.spouseFirst = spouseFirst;
	}

	public String getSpouseLast() {
		return spouseLast;
	}

	public void setSpouseLast(String spouseLast) {
		this.spouseLast = spouseLast;
	}

	public String getSpouseMi() {
		return spouseMi;
	}

	public void setSpouseMi(String spouseMi) {
		this.spouseMi = spouseMi;
	}

	public String getSpouseTitle() {
		return spouseTitle;
	}

	public void setSpouseTitle(String spouseTitle) {
		this.spouseTitle = spouseTitle;
	}

	public Date getSpouseBirthDate() {
		return spouseBirthDate;
	}

	public void setSpouseBirthDate(Date spouseBirthDate) {
		this.spouseBirthDate = spouseBirthDate;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getCellPhone() {
		return cellPhone;
	}

	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}
	
	public Date getCancelDate() {
		return cancelDate;
	}

	public void setCancelDate(Date cancelDate) {
		this.cancelDate = cancelDate;
	}

	public Date getEffectiveDate() {
		return effectiveDate;
	}

	public void setEffectiveDate(Date effectiveDate) {
		this.effectiveDate = effectiveDate;
	}

	public Date getRenewDate() {
		return renewDate;
	}

	public void setRenewDate(Date renewDate) {
		this.renewDate = renewDate;
	}

	
	public Boolean getActive() {
		return active;
	}

	public void setActive(Boolean active) {
		this.active = active;
	}

	
	public Set<MemberFee> getMemberFee() {
		return memberFee;
	}

	public void setMemberFee(Set<MemberFee> memberFee) {
		this.memberFee = memberFee;
	}
	

	public Set<MemberCommission> getCommission() {
		return commission;
	}

	public void setCommission(Set<MemberCommission> commission) {
		this.commission = commission;
	}
	public String getGroupCode() {
		return groupCode;
	}

	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}

	public ClaimMember() {
		super();
	}

	public ClaimMember(Integer memberId, String employeeId, String firstName, String lastName, String mi, String title, Date birthDate, String spouseFirst, String spouseLast, String spouseMi,
			String spouseTitle, Date spouseBirthDate, String phone, String cellPhone, String email, Date cancelDate, Date effectiveDate, Date renewDate, Boolean active) {
		super();
		this.memberId = memberId;
		this.employeeId = employeeId;
		this.firstName = firstName;
		this.lastName = lastName;
		this.mi = mi;
		this.title = title;
		this.birthDate = birthDate;
		this.spouseFirst = spouseFirst;
		this.spouseLast = spouseLast;
		this.spouseMi = spouseMi;
		this.spouseTitle = spouseTitle;
		this.spouseBirthDate = spouseBirthDate;
		this.phone = phone;
		this.cellPhone = cellPhone;
		this.email = email;
		this.cancelDate = cancelDate;
		this.effectiveDate = effectiveDate;
		this.renewDate = renewDate;
		this.active = active;
	}	
}
