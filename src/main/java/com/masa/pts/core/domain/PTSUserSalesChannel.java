package com.masa.pts.core.domain;

import java.io.Serializable;

import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;


@Entity
@Table(name="TLNK_EMPLOYEE_SALES_CHANNEL",schema = "dbo")
public class PTSUserSalesChannel implements Serializable {

	private static final long serialVersionUID = -1725280177855393183L;

	@EmbeddedId
	private PTSUserSalesChannelId userSalesChannelId;
	
	/*
	@Formula("(SELECT TDAT_SALES_CHANNEL.NAME FROM DBO.TDAT_SALES_CHANNEL WHERE TDAT_SALES_CHANNEL.ID = SALES_CHANNEL_ID)")
	private String name;
	*/
	
	public PTSUserSalesChannel() {
		super();
	}

	public PTSUserSalesChannelId getUserSalesChannelId() {
		return userSalesChannelId;
	}

	public void setUserSalesChannelId(PTSUserSalesChannelId userSalesChannelId) {
		this.userSalesChannelId = userSalesChannelId;
	}
}
