package com.masa.pts.core.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

@Entity
@Table(name="TDAT_NOTICE",schema = "dbo")
public class Notice implements Serializable {

	private static final long serialVersionUID = -6801464149456927871L;
	
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="id")
	private Integer id;
	
	/*@ManyToOne	
	@JoinColumn(name="member_id")
	private Member member;*/
	@Column(name="member_id")
	private Integer memberId;
	
	@Temporal(TemporalType.DATE)
	private Date noticeDate;
	
	@Column(name="paydetail_id")	
	private Integer payDetailId;
	
	@Column(name="notice2_date")
	@Temporal(TemporalType.DATE)
	private Date notice2Date;
	
	@Temporal(TemporalType.DATE)
	@Column(name="notice3_date")
	private Date notice3Date;
	
	@Temporal(TemporalType.DATE)
	@Column(name="notice4_date")
	private Date notice4Date;

	private Boolean noticePrint;
	@Column(name="notice2_print")
	private Boolean notice2Print;
	@Column(name="notice3_print")
	private Boolean notice3Print;
	@Column(name="notice4_print")
	private Boolean notice4Print;
	
	private BigDecimal amount;
	private Date renewDate;
	private Integer payId;
	public Notice() {
		super();
	}
	public Notice(Integer id, Date noticeDate, Integer payDetailId, Date notice2Date, Date notice3Date, Date notice4Date, Boolean noticePrint, Boolean notice2Print, Boolean notice3Print,
			Boolean notice4Print, BigDecimal amount, Date renewDate, Integer payId) {
		super();
		this.id = id;
		this.noticeDate = noticeDate;
		this.payDetailId = payDetailId;
		this.notice2Date = notice2Date;
		this.notice3Date = notice3Date;
		this.notice4Date = notice4Date;
		this.noticePrint = noticePrint;
		this.notice2Print = notice2Print;
		this.notice3Print = notice3Print;
		this.notice4Print = notice4Print;
		this.amount = amount;
		this.renewDate = renewDate;
		this.payId = payId;
	}
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public Date getNoticeDate() {
		return noticeDate;
	}
	public void setNoticeDate(Date noticeDate) {
		this.noticeDate = noticeDate;
	}
	public Integer getPayDetailId() {
		return payDetailId;
	}
	public void setPayDetailId(Integer payDetailId) {
		this.payDetailId = payDetailId;
	}
	public Date getNotice2Date() {
		return notice2Date;
	}
	public void setNotice2Date(Date notice2Date) {
		this.notice2Date = notice2Date;
	}
	public Date getNotice3Date() {
		return notice3Date;
	}
	public void setNotice3Date(Date notice3Date) {
		this.notice3Date = notice3Date;
	}
	public Date getNotice4Date() {
		return notice4Date;
	}
	public void setNotice4Date(Date notice4Date) {
		this.notice4Date = notice4Date;
	}
	public Boolean getNoticePrint() {
		return noticePrint;
	}
	public void setNoticePrint(Boolean noticePrint) {
		this.noticePrint = noticePrint;
	}
	public Boolean getNotice2Print() {
		return notice2Print;
	}
	public void setNotice2Print(Boolean notice2Print) {
		this.notice2Print = notice2Print;
	}
	public Boolean getNotice3Print() {
		return notice3Print;
	}
	public void setNotice3Print(Boolean notice3Print) {
		this.notice3Print = notice3Print;
	}
	public Boolean getNotice4Print() {
		return notice4Print;
	}
	public void setNotice4Print(Boolean notice4Print) {
		this.notice4Print = notice4Print;
	}
	public BigDecimal getAmount() {
		return amount;
	}
	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}
	public Date getRenewDate() {
		return renewDate;
	}
	public void setRenewDate(Date renewDate) {
		this.renewDate = renewDate;
	}
	public Integer getPayId() {
		return payId;
	}
	public void setPayId(Integer payId) {
		this.payId = payId;
	}	
	public Integer getMemberId() {
		return memberId;
	}
	public void setMemberId(Integer memberId) {
		this.memberId = memberId;
	}
	@Override
	public String toString() {
		return "Notice [id=" + id + ",noticeDate=" + noticeDate + ", payDetailId=" + payDetailId + ", notice2Date=" + notice2Date + ", notice3Date=" + notice3Date
				+ ", notice4Date=" + notice4Date + ", noticePrint=" + noticePrint + ", notice2Print=" + notice2Print + ", notice3Print=" + notice3Print + ", notice4Print=" + notice4Print + ", amount="
				+ amount + ", renewDate=" + renewDate + ", payId=" + payId + "]";
	}	
}
