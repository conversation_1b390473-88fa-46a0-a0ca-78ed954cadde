package com.masa.pts.core.domain;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.masa.pts.core.constant.MemberActiveStatus;

/**
 * <AUTHOR>
 *
 */
@Entity
@Table(name="TDAT_MEMBER_COVERAGE_LAPSE",schema = "dbo")
public class MemberCoverageLapse implements Serializable {

	private static final long serialVersionUID = -7244536980377046967L;

	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="id")
	private Integer id;
	
	@ManyToOne	
	@JoinColumn(name="member_id")
	private Member member;
	
	@Column(name="lapse_start_date")
	private Date lapseStartDate;
	
	@Column(name="lapse_end_date")
	private Date lapseEndDate;
	
	@Enumerated(EnumType.ORDINAL)
	@Column(name="status_type")
	private MemberActiveStatus statusType;
	
	private Integer reasonCode;
	
	private Boolean isDelete=Boolean.FALSE;
	
	private Date deletedDate=Constant.DEFULT_DATE_1900;
	
	private Integer deletedBy;
	
	private Date createdDate;
	
	private Integer createdBy;
	
	private Date modifiedDate;
	
	private Integer modifiedBy;

	public MemberCoverageLapse() {
		super();
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Date getLapseStartDate() {
		return lapseStartDate;
	}

	public void setLapseStartDate(Date lapseStartDate) {
		this.lapseStartDate = lapseStartDate;
	}

	public Date getLapseEndDate() {
		return lapseEndDate;
	}

	public void setLapseEndDate(Date lapseEndDate) {
		this.lapseEndDate = lapseEndDate;
	}

	public MemberActiveStatus getStatusType() {
		return statusType;
	}

	public void setStatusType(MemberActiveStatus statusType) {
		this.statusType = statusType;
	}

	public Integer getReasonCode() {
		return reasonCode;
	}

	public void setReasonCode(Integer reasonCode) {
		this.reasonCode = reasonCode;
	}

	public Boolean getIsDelete() {
		return isDelete;
	}

	public void setIsDelete(Boolean isDelete) {
		this.isDelete = isDelete;
	}

	public Date getDeletedDate() {
		return deletedDate;
	}

	public void setDeletedDate(Date deletedDate) {
		this.deletedDate = deletedDate;
	}

	public Integer getDeletedBy() {
		return deletedBy;
	}

	public void setDeletedBy(Integer deletedBy) {
		this.deletedBy = deletedBy;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public Integer getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}

	public Date getModifiedDate() {
		return modifiedDate;
	}

	public void setModifiedDate(Date modifiedDate) {
		this.modifiedDate = modifiedDate;
	}

	public Integer getModifiedBy() {
		return modifiedBy;
	}

	public void setModifiedBy(Integer modifiedBy) {
		this.modifiedBy = modifiedBy;
	}

	public Member getMember() {
		return member;
	}

	public void setMember(Member member) {
		this.member = member;
	}
}
