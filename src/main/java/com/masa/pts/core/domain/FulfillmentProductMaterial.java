package com.masa.pts.core.domain;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name="TLNK_FULFILLMENT_TYPE_TO_MATERIAL",schema = "dbo")
public class FulfillmentProductMaterial implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2350151169722280020L;
	
	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="fulfillment_type_to_material_id")
	private Integer productToMaterialId;
	
	@Column(name="sales_channel_id")
	private Integer salesChannelId;
	
	@Column(name="product_id")
	private Integer productId;
	
	@Column(name="fulfillment_request_type_id")
	private Integer fulfillmentRequestTypeId;
	
	@Column(name="material_id")
	private Integer materialId;

	public FulfillmentProductMaterial() {
		super();
	}

	public Integer getProductToMaterialId() {
		return productToMaterialId;
	}

	public void setProductToMaterialId(Integer productToMaterialId) {
		this.productToMaterialId = productToMaterialId;
	}

	public Integer getSalesChannelId() {
		return salesChannelId;
	}

	public void setSalesChannelId(Integer salesChannelId) {
		this.salesChannelId = salesChannelId;
	}

	public Integer getProductId() {
		return productId;
	}

	public void setProductId(Integer productId) {
		this.productId = productId;
	}

	public Integer getFulfillmentRequestTypeId() {
		return fulfillmentRequestTypeId;
	}

	public void setFulfillmentRequestTypeId(Integer fulfillmentRequestTypeId) {
		this.fulfillmentRequestTypeId = fulfillmentRequestTypeId;
	}

	public Integer getMaterialId() {
		return materialId;
	}

	public void setMaterialId(Integer materialId) {
		this.materialId = materialId;
	}
}
