package com.masa.pts.core.domain;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.Objects;

@Entity
@Table(name = "tlst_Shipping_Method", schema = "dbo")
public class ShippingMethod implements Serializable {

    private static final long serialVersionUID = 2507766750548704118L;

    @Id
    @GeneratedValue(strategy= GenerationType.IDENTITY)
    private Integer id;
    private String name;
    private LocalDate createDate;
    private boolean isObsolete;
    private LocalDate obsolete_date;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public LocalDate getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDate createDate) {
        this.createDate = createDate;
    }

    public boolean isObsolete() {
        return isObsolete;
    }

    public void setObsolete(boolean obsolete) {
        isObsolete = obsolete;
    }

    public LocalDate getObsolete_date() {
        return obsolete_date;
    }

    public void setObsolete_date(LocalDate obsolete_date) {
        this.obsolete_date = obsolete_date;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof ShippingMethod)) return false;
        ShippingMethod that = (ShippingMethod) o;
        return isObsolete == that.isObsolete &&
                Objects.equals(id, that.id) &&
                Objects.equals(name, that.name) &&
                Objects.equals(createDate, that.createDate) &&
                Objects.equals(obsolete_date, that.obsolete_date);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, name, createDate, isObsolete, obsolete_date);
    }

    @Override
    public String toString() {
        return "ShippingMethod{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", createDate=" + createDate +
                ", isObsolete=" + isObsolete +
                ", obsolete_date=" + obsolete_date +
                '}';
    }
}
