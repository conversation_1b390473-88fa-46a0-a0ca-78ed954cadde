package com.masa.pts.core.domain;

import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import org.hibernate.annotations.Formula;

@Entity
@Table(name="TDAT_EMPLOYEE",schema = "dbo")
public class PTSUserSearch implements Serializable {

	private static final long serialVersionUID = 2920486757152627986L;

	@Id
	@GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name="employee_id")
	private Integer employeeId;
	
	private String username;
	
	private boolean active;
	
	@Column(name="last_login")
	private Date lastLogin;

	@Column(name="first_name")
	private String firstName;
	
	@Column(name="last_name")
	private String lastName;
	
	@Column(name="user_type")
	private Integer userType;
	
	private Date createdDate;
	
	private Integer createdBy;
	
	private Date modifiedDate;
	
	private Integer modifiedBy;
	
	@Formula("(SELECT TDAT_EMPLOYEE.FIRST_NAME + ' ' + TDAT_EMPLOYEE.LAST_NAME FROM TDAT_EMPLOYEE WHERE TDAT_EMPLOYEE.EMPLOYEE_ID = CREATED_BY)")
	private String createdByUser;
	
	@Formula("(SELECT TDAT_EMPLOYEE.FIRST_NAME + ' ' + TDAT_EMPLOYEE.LAST_NAME FROM TDAT_EMPLOYEE WHERE TDAT_EMPLOYEE.EMPLOYEE_ID = MODIFIED_BY)")
	private String modifiedByUser;
	
	@OneToMany(fetch=FetchType.LAZY,cascade = CascadeType.ALL,orphanRemoval = true,targetEntity = PTSUserGroup.class,mappedBy = "userGroupId.user")
	private Set<PTSUserGroup> userGroups = new HashSet<>();
	
	@OneToMany(fetch=FetchType.LAZY,cascade = CascadeType.ALL,orphanRemoval = true,targetEntity = PTSUserRole.class,mappedBy="userRoleId.user")
	private Set<PTSUserRole> userRoles = new HashSet<>();
	
	@OneToMany(fetch=FetchType.LAZY,cascade = CascadeType.ALL,orphanRemoval = true,targetEntity = PTSUserSalesChannel.class,mappedBy="userSalesChannelId.user")
	private Set<PTSUserSalesChannel> userSalesChannels = new HashSet<>();

	public PTSUserSearch() {
		super();
	}

	public Integer getEmployeeId() {
		return employeeId;
	}

	public void setEmployeeId(Integer employeeId) {
		this.employeeId = employeeId;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public boolean isActive() {
		return active;
	}

	public void setActive(boolean active) {
		this.active = active;
	}

	public Date getLastLogin() {
		return lastLogin;
	}

	public void setLastLogin(Date lastLogin) {
		this.lastLogin = lastLogin;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public Integer getUserType() {
		return userType;
	}

	public void setUserType(Integer userType) {
		this.userType = userType;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public Integer getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}

	public Date getModifiedDate() {
		return modifiedDate;
	}

	public void setModifiedDate(Date modifiedDate) {
		this.modifiedDate = modifiedDate;
	}

	public Integer getModifiedBy() {
		return modifiedBy;
	}

	public void setModifiedBy(Integer modifiedBy) {
		this.modifiedBy = modifiedBy;
	}

	public String getCreatedByUser() {
		return createdByUser;
	}

	public void setCreatedByUser(String createdByUser) {
		this.createdByUser = createdByUser;
	}

	public String getModifiedByUser() {
		return modifiedByUser;
	}

	public void setModifiedByUser(String modifiedByUser) {
		this.modifiedByUser = modifiedByUser;
	}

	public Set<PTSUserGroup> getUserGroups() {
		return userGroups;
	}

	public void setUserGroups(Set<PTSUserGroup> userGroups) {
		this.userGroups = userGroups;
	}

	public Set<PTSUserRole> getUserRoles() {
		return userRoles;
	}

	public void setUserRoles(Set<PTSUserRole> userRoles) {
		this.userRoles = userRoles;
	}

	public Set<PTSUserSalesChannel> getUserSalesChannels() {
		return userSalesChannels;
	}

	public void setUserSalesChannels(Set<PTSUserSalesChannel> userSalesChannels) {
		this.userSalesChannels = userSalesChannels;
	}
}
