package com.masa.pts.core.dbadvisor;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.sql.Date;

@Converter
public class DateConverter implements AttributeConverter<java.util.Date, java.sql.Date> {

    @Override
    public Date convertToDatabaseColumn(java.util.Date attribute) {
        return new Date(attribute.getTime());
    }

    @Override
    public java.util.Date convertToEntityAttribute(Date dbData) {
        return new java.util.Date(dbData.getTime());
    }
}
