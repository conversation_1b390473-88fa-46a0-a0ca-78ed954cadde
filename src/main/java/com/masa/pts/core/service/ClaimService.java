package com.masa.pts.core.service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.masa.pts.core.domain.Claim;
import com.masa.pts.core.domain.ClaimNote;
import com.masa.pts.core.domain.ClaimPayment;
import com.masa.pts.core.repository.ClaimNoteRepository;
import com.masa.pts.core.repository.ClaimPaymentRepository;
import com.masa.pts.core.repository.ClaimRepository;

@Service
public class ClaimService {

	@Autowired
	private ClaimRepository claimRepository;
	
	@Autowired
	private ClaimNoteRepository claimNoteRepository;
	
	@Autowired
	private ClaimPaymentRepository claimPaymentRepository;
	
	public List<Claim> getMemberClaimsByMemberId(Integer memberId){
		return claimRepository.findByMemberMemberId(memberId);
	}
	
	public Set<ClaimPayment> getClaimPaymentByClaimId(Integer claimId){
		Set<ClaimPayment> claimPayments = claimPaymentRepository.findByClaimClaimID(claimId);
		return claimPayments.stream().filter(item->!item.getIsDelete()).collect(Collectors.toSet());		
	}
	
	public Set<ClaimNote> getClaimNotes(Integer claimId){
		return this.claimNoteRepository.findAllByclaimClaimID(claimId);
	}
}
