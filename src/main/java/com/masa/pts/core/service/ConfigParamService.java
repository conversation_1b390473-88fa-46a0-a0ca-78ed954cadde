package com.masa.pts.core.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.masa.pts.core.domain.ConfigParam;
import com.masa.pts.core.repository.ConfigParamRepository;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class ConfigParamService {

    @Autowired
    private ConfigParamRepository configParamRepository;

    public String getParamValue(String paramName) {
        Optional<ConfigParam> configParam = configParamRepository.findByParamName(paramName);        
        return configParam.isPresent()?configParam.get().getParamValue():null;
    }
    public static List<String> convertCommaSeparatedStringToList(String str){
    	if(str==null) {
    		log.warn("Given Comma Seperated String is null");
    		str = "";
    	}
    	return Arrays.asList(str.split("\\s*,\\s*"));
    }
    public static List<Integer> convertCommaSeparatedIntegerToList(String str){
    	if(str==null) {
    		log.warn("Given Comma Seperated Integer is null");
    		str = "";
    	}
    	List<String> stringIntList = Arrays.asList(str.split("\\s*,\\s*"));
    	List<Integer> intList = new ArrayList<Integer>();
    	stringIntList.forEach(x->{
    		try {
    			intList.add(Integer.parseInt(x));
    		} catch (Exception e) {
    			log.warn("Given data ("+x+" is not a number)");
			}
    	});
    	return intList;
    }
}