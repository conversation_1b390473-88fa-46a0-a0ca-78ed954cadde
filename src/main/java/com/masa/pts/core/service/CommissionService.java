package com.masa.pts.core.service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.masa.pts.core.constant.CommissionAdvanceType;
import com.masa.pts.core.constant.CommissionType;
import com.masa.pts.core.constant.GroupBillType;
import com.masa.pts.core.constant.PaymentCommissionType;
import com.masa.pts.core.domain.Agent;
import com.masa.pts.core.domain.Commission;
import com.masa.pts.core.domain.CommissionDetail;
import com.masa.pts.core.domain.Constant;
import com.masa.pts.core.domain.GroupEntity;
import com.masa.pts.core.domain.MemberCommission;
import com.masa.pts.core.domain.MemberFee;
import com.masa.pts.core.domain.PaymentDetail;
import com.masa.pts.core.repository.AgentRepository;
import com.masa.pts.core.repository.CommissionDetailRepository;
import com.masa.pts.core.repository.CommissionRepository;
import com.masa.pts.core.repository.MemberCommissionRepository;
import com.masa.pts.core.repository.PaymentDetailRepository;

@Service
public class CommissionService  {

	private static final Logger log = LoggerFactory.getLogger(CommissionService.class);

	@Autowired
	private MemberProductFeeService memberPrdFeeService;
	@Autowired
	private MemberCommissionRepository memberCommRepository;
	@Autowired
	private AgentRepository agentRepository;
	@Autowired
	private CommissionRepository commissionRepository;
	@Autowired
	private CommissionDetailRepository commissionDetailRepository;	
	@Autowired
	private PaymentDetailRepository paymentDetailRepository;
	
	@Autowired
	private MemberService memberService;
	@Autowired
	private GroupService groupService;
	
	@Value("${installment.twentyMonth.productIds}")
    List<Integer> INSTALLMENT_20_MONTH_PRD_IDS;
	
	// Get Agent for member
	// check if agent has advance commission setup
	// generate commission
	public void createAdvanceCommission(Integer memberId,BigDecimal memberAdvanceCommPer,Date effectiveDate) {

		Set<MemberFee> memFeesSet = memberPrdFeeService.getCommissionMemberFees(memberId);
		
		if(!isValidMemberForAdvComm(memberId,memFeesSet,effectiveDate)) {
			log.info("Member Id [{}] not elgibile for advance comission generation",memberId);
			return ;
		}			
		
		Set<MemberCommission> memCommissionSet = memberCommRepository.findByMemberMemberId(memberId);

		for (MemberFee memFee : memFeesSet) {
			
			BigDecimal feeYearAmt =  BigDecimal.ZERO;

			for (MemberCommission memComm : memCommissionSet) {

				if(memFee.getProduct().getProductId().compareTo(memComm.getProductId()) ==0) {
					
					Agent agent = agentRepository.findById(memComm.getAgentId()).get();
	
					if ( (agent.getIsAdvance() && (agent.getAdvanceComm()!=null && agent.getAdvanceComm().compareTo(BigDecimal.ZERO) > 0)) ||
							(agent.getIsAdvance() && (agent.getInstallmentComm()!=null && agent.getInstallmentComm().compareTo(BigDecimal.ZERO) >0) &&
							Constant.INSTALLMENT_FEE_IDS.contains(memFee.getFeeDetails().getFeeId()))  ) { //agent has advance
						
						
						BigDecimal advanceCommPercent = Constant.INSTALLMENT_FEE_IDS.contains(memFee.getFeeDetails().getFeeId()) ? 
															agent.getInstallmentComm() : agent.getAdvanceComm();
						
						BigDecimal commissionAmount = BigDecimal.ZERO;
						BigDecimal monthlyCommAmount = BigDecimal.ZERO;
						BigDecimal monthlyPremAmount = BigDecimal.ZERO;
						
						BigDecimal commSalePercent = (agent.getFixedNew() !=null && agent.getFixedNew().compareTo(BigDecimal.ZERO) > 0) ? agent.getFixedNew() 
								: new BigDecimal(Double.toString(memComm.getNewSalePercent())) ;
						
						if( null != memComm.getUseFlatRate() &&  memComm.getUseFlatRate()) {
							feeYearAmt = new BigDecimal(Double.toString(memComm.getNewSalePercent()))
												.multiply(new BigDecimal(Double.toString(memFee.getFrequencyDetails().getYearParts())));
							
						    if(INSTALLMENT_20_MONTH_PRD_IDS.contains(memFee.getProduct().getProductId()))
							{
								feeYearAmt = new BigDecimal(Double.toString(memComm.getNewSalePercent()))
								.multiply(new BigDecimal(Integer.toString(20)));
							}

							commissionAmount = feeYearAmt.multiply(advanceCommPercent);
							
							monthlyCommAmount = new BigDecimal(Double.toString(memComm.getNewSalePercent()));
							
						}
						else {
							feeYearAmt =  new BigDecimal(Double.toString(memFee.getAmount()))
										.multiply(new BigDecimal(Double.toString(memFee.getFrequencyDetails().getYearParts())));
							
							if(INSTALLMENT_20_MONTH_PRD_IDS.contains(memFee.getProduct().getProductId()))
							{
								feeYearAmt = new BigDecimal(Double.toString(memFee.getAmount()))
								.multiply(new BigDecimal(Integer.toString(20)));
							}			

							commissionAmount = feeYearAmt.multiply(advanceCommPercent)
														 .multiply(commSalePercent);  
							
							monthlyPremAmount = new BigDecimal(Double.toString(memFee.getAmount()));
							
							monthlyCommAmount = monthlyPremAmount.multiply(commSalePercent); 
						}
						Commission commission = new Commission();
						commission.setActive(agent.getActive());
						commission.setAgent(agent);
						commission.setCommTotalAmount(commissionAmount.setScale(Constant.BIG_DECIMAL_DEFAULT_SCALE,Constant.BIG_DECIMAL_DEFAULT_ROUNGDING_MODE));
						commission.setDeleted(false);
						commission.setPaid(false);
						commission.setCommissionDate(new java.util.Date());
						commission.setCommType(CommissionType.GROUP.getType());
						commission.setCreatedDate(new java.util.Date());
						commission.setPayDate(Constant.DEFULT_DATE_1900);
						
						while(commissionAmount.compareTo(BigDecimal.ZERO) >0) {
							CommissionDetail commDetail = new CommissionDetail();
							commDetail.setMemberId(memberId);
							commDetail.setPayDetailId(0);
							commDetail.setChargeback(false);
							commDetail.setLeftCommAmount(BigDecimal.ZERO);							
							commDetail.setProductId(memFee.getProduct().getProductId());
							commDetail.setAdvance(CommissionAdvanceType.ADVANCE.getAdvanceType());
							commDetail.setLeftCommAmount(BigDecimal.ZERO);
							BigDecimal amt = commissionAmount.compareTo(monthlyCommAmount) >0 ? monthlyCommAmount : commissionAmount;
							commDetail.setAmount(  amt.setScale(Constant.BIG_DECIMAL_DEFAULT_SCALE, Constant.BIG_DECIMAL_DEFAULT_ROUNGDING_MODE) );							
							
							commDetail.setCommission(commission);
							commission.addCommDetails(commDetail);
							commissionAmount = commissionAmount.subtract(monthlyCommAmount);							
						}
						commissionRepository.save(commission);
						log.info("Commission created for Member [{}] Agent [{}] Commission ID [{}]",memberId,agent.getAgentNum(),commission.getId());
					}				
				}
			}
		}
	}

	/**
	 * @param memberId
	 * @param productId
	 */
	public void createAdvanceCommissionForMemberAndProduct(Integer memberId,Integer productId,Date effectiveDate) {

		Set<MemberFee> memFeesSet = memberPrdFeeService.getCommissionMemberFees(memberId);
		
		if(!isValidMemberForAdvComm(memberId,memFeesSet,effectiveDate)) 
		{
			log.info("Member Id [{}] not elgibile for advance comissions",memberId);
			return ;
		}			
		
		Set<MemberCommission> memCommissionSet = memberCommRepository.findByMemberMemberIdAndProductId(memberId,productId);

		for (MemberFee memFee : memFeesSet) {
			
			BigDecimal feeYearAmt =  BigDecimal.ZERO;

			for (MemberCommission memComm : memCommissionSet) {

				if(memFee.getProduct().getProductId().compareTo(memComm.getProductId()) ==0) {
					
					Agent agent = agentRepository.findById(memComm.getAgentId()).get();
	
					if ( (agent.getIsAdvance() && (agent.getAdvanceComm()!=null && agent.getAdvanceComm().compareTo(BigDecimal.ZERO) > 0)) ||
							(agent.getIsAdvance() && (agent.getInstallmentComm()!=null && agent.getInstallmentComm().compareTo(BigDecimal.ZERO) >0) &&
							Constant.INSTALLMENT_FEE_IDS.contains(memFee.getFeeDetails().getFeeId()))  ) { //agent has advance
						
						
						BigDecimal advanceCommPercent = Constant.INSTALLMENT_FEE_IDS.contains(memFee.getFeeDetails().getFeeId()) ? 
															agent.getInstallmentComm() : agent.getAdvanceComm();
						
						BigDecimal commissionAmount = BigDecimal.ZERO;
						BigDecimal monthlyCommAmount = BigDecimal.ZERO;
						BigDecimal monthlyPremAmount = BigDecimal.ZERO;
						
						BigDecimal commSalePercent = (agent.getFixedNew() !=null && agent.getFixedNew().compareTo(BigDecimal.ZERO) > 0) ? agent.getFixedNew() 
								: new BigDecimal(Double.toString(memComm.getNewSalePercent())) ;
						
						if( null != memComm.getUseFlatRate() &&  memComm.getUseFlatRate()) {
							
							feeYearAmt = new BigDecimal(Double.toString(memComm.getNewSalePercent()))
												.multiply(new BigDecimal(Double.toString(memFee.getFrequencyDetails().getYearParts())));
							
                            if(INSTALLMENT_20_MONTH_PRD_IDS.contains(memFee.getProduct().getProductId()))
							{
								feeYearAmt = new BigDecimal(Double.toString(memComm.getNewSalePercent()))
								.multiply(new BigDecimal(Integer.toString(20)));
							}


							commissionAmount = feeYearAmt.multiply(advanceCommPercent);
							
							monthlyCommAmount = new BigDecimal(Double.toString(memComm.getNewSalePercent()));
							
						}
						else {
							
							feeYearAmt =  new BigDecimal(Double.toString(memFee.getAmount()))
										.multiply(new BigDecimal(Double.toString(memFee.getFrequencyDetails().getYearParts())));

							if(INSTALLMENT_20_MONTH_PRD_IDS.contains(memFee.getProduct().getProductId()))
							{
								feeYearAmt = new BigDecimal(Double.toString(memFee.getAmount()))
								.multiply(new BigDecimal(Integer.toString(20)));
							}
										
							commissionAmount = feeYearAmt.multiply(advanceCommPercent)
														 .multiply(commSalePercent);  
							
							monthlyPremAmount = new BigDecimal(Double.toString(memFee.getAmount()));
							
							monthlyCommAmount = monthlyPremAmount.multiply(commSalePercent); 
						}
						Commission commission = new Commission();
						commission.setActive(agent.getActive());
						commission.setAgent(agent);
						commission.setCommTotalAmount(commissionAmount.setScale(Constant.BIG_DECIMAL_DEFAULT_SCALE,Constant.BIG_DECIMAL_DEFAULT_ROUNGDING_MODE));
						commission.setDeleted(false);
						commission.setPaid(false);
						commission.setCommissionDate(new java.util.Date());
						commission.setCommType(CommissionType.GROUP.getType());
						commission.setCreatedDate(new java.util.Date());
						commission.setPayDate(Constant.DEFULT_DATE_1900);
						
						while(commissionAmount.compareTo(BigDecimal.ZERO) >0) {
							CommissionDetail commDetail = new CommissionDetail();
							commDetail.setMemberId(memberId);
							commDetail.setPayDetailId(0);
							commDetail.setChargeback(false);
							commDetail.setLeftCommAmount(BigDecimal.ZERO);							
							commDetail.setProductId(memFee.getProduct().getProductId());
							commDetail.setAdvance(CommissionAdvanceType.ADVANCE.getAdvanceType());
							commDetail.setLeftCommAmount(BigDecimal.ZERO);
							BigDecimal amt = commissionAmount.compareTo(monthlyCommAmount) >0 ? monthlyCommAmount : commissionAmount;
							commDetail.setAmount(  amt.setScale(Constant.BIG_DECIMAL_DEFAULT_SCALE, Constant.BIG_DECIMAL_DEFAULT_ROUNGDING_MODE) );							
							
							commDetail.setCommission(commission);
							commission.addCommDetails(commDetail);
							commissionAmount = commissionAmount.subtract(monthlyCommAmount);							
						}
						commissionRepository.save(commission);
						log.info("Commission created for Member [{}] Agent [{}] Commission ID [{}]",memberId,agent.getAgentNum(),commission.getId());
					}				
				}
			}
		}
	}

	
	/**
	 * @param memberId
	 * @param memFeesSet
	 * @return
	 */
	private boolean isValidMemberForAdvComm(Integer memberId, Set<MemberFee> memFeesSet,Date effectiveDate) {
		
		String groupCode = memberService.getMemberGroupOnEffectiveDate(memberId,effectiveDate);		
		if(null == groupCode)
		{
			log.info("Member ID [{}] Advance Commission not applicable, Group code is [{}]",memberId,groupCode);
			return false;
		}
		
		GroupEntity group = groupService.getGroupByCode(groupCode);
		
		if(null == group)
		{
			log.info("Member ID [{}] Advance Commission not applicable, Group is [{}]",memberId,groupCode);
			return false;
		}
		
		if(group.getBillType().compareTo(GroupBillType.INDIVIDUAL.getBillType())==0)
		{
			log.info("Member ID [{}] Advance Commission not applicable, Group [{}] Bill Type is Individial [{}]",memberId,group.getGroupCode(),group.getBillType());
			return false;
		}
		
		if(group.getIsAdvComm())
		{
			log.info("Member ID [{}] Advance Commission not applicable, Group [{}] marked as no advance comm",memberId,group.getGroupCode());
			return false;
		}
		
		String primaryPrdFreqType = memFeesSet.stream().filter(item->item.getUpgradeType().intValue()==0).map(item->item.getFrequencyDetails().getFrequencyType()).findFirst().orElse(null);
		
		if(!"MONTHLY".equalsIgnoreCase(primaryPrdFreqType))
		{
			log.info("Member ID [{}] Advance Commission not applicable as Product Frequency is not monthly [{}]",memberId,primaryPrdFreqType);
			return false;
		}		
		return true;
	}
	
	/**
	 * @param memberId
	 * @param productId
	 * @return
	 */
	public Set<CommissionDetail> unPaidAdvCommsForMemberAndProduct(Integer memberId,Integer productId)
	{
		return commissionDetailRepository.findAllByParams(memberId, productId);
	}
	
	
	public void chargebackUnPaidAdvComm(Integer memberId,Integer productId,String reasonNote,Integer commType)
	{
		log.info("Processing chargeback of un paid advance comm for Member ID [{}] Product ID [{}]",memberId,productId);
		Set<CommissionDetail> commDetailSet = unPaidAdvCommsForMemberAndProduct(memberId,productId);
		
		log.info("Commission Detail count [{}]",commDetailSet.size());
		
		Set<Integer> commIdSet = commDetailSet.stream().map(item->item.getCommission().getId()).collect(Collectors.toSet());
		
		log.info("Commission ID set [{}]",commIdSet);
		
		//commission id, total amount
		Map<Integer,Double> commIdAmtMap = commDetailSet.stream().collect(
				Collectors.groupingBy(item->item.getCommission().getId(),
	Collectors.summingDouble(item-> (item.getLeftCommAmount() !=null && item.getLeftCommAmount().doubleValue() >0) ? item.getLeftCommAmount().doubleValue() : item.getAmount().doubleValue())));
		
		log.info("Commission ID and Total Amt Map [{}]",commIdAmtMap);
		
		Set<Commission> commissionSetToSave = new HashSet<>();
				
		commIdSet.forEach(commId->
		{
			Agent agent =  commDetailSet.stream().filter(item->item.getCommission().getId().compareTo(commId)==0).map(item->item.getCommission().getAgent()).findFirst().orElse(null); 
			Double commTotalAmt = commIdAmtMap.getOrDefault(commId, 0.0);
			
			Commission commission = new Commission();			
			if(null != agent)
			{
				commission.setAgent(agent);
				commission.setActive(Boolean.TRUE);
				commission.setCommissionDate(new Date());
				commission.setCommTotalAmount(BigDecimal.valueOf(commTotalAmt).negate());
				commission.setCommType(commType);
				commission.setCreatedDate(new Date());
				commission.setDeleted(Boolean.FALSE);
				commission.setPaid(Boolean.FALSE);
				commission.setPayDate(Constant.DEFULT_DATE_1900);
				
				CommissionDetail commissionDetail = new CommissionDetail();
				commissionDetail.setCommission(commission);
				commission.addCommDetails(commissionDetail);
				
				commissionDetail.setAdvance(CommissionAdvanceType.CHARGE_BACK.getAdvanceType());
				commissionDetail.setAmount(BigDecimal.valueOf(commTotalAmt).negate());
				commissionDetail.setChargeback(Boolean.TRUE);
				commissionDetail.setLeftCommAmount(BigDecimal.ZERO);
				commissionDetail.setMemberId(memberId);
				commissionDetail.setNote(reasonNote);
				commissionDetail.setPayDetailId(0);
				commissionDetail.setProductId(productId);
				commissionSetToSave.add(commission);
			}
		});
		
		log.info("Saving commission records ,Total [{}]",commissionSetToSave.size());
		commissionRepository.saveAll(commissionSetToSave);
		
		log.info("Updating commission detail records as CB ,Total [{}]",commDetailSet.size());	
		//update commission detail records as  CB
		commDetailSet.forEach(item->item.setChargeback(Boolean.TRUE));
		commissionDetailRepository.saveAll(commDetailSet);
		
		log.info("End of chargeback processing of un paid advance commission");
	}
	
	/**
	 * @param memberId
	 * @param productId
	 * @return
	 */
	public boolean advanceCommExistsForMemberAndProduct(Integer memberId,Integer productId) {		
		return commissionDetailRepository.existsByMemberIdAndProductIdAndAdvanceAndChargeback(memberId, productId, 1, Boolean.FALSE);
	}

	/**
	 * @param paymentDetail
	 */
	public BigDecimal payAdanceCommissionWithMemPayment(PaymentDetail paymentDetail) {
		
		BigDecimal totalCommAmount= BigDecimal.ZERO;
		
		try {
			//find all commission details (and corresponding commission record) for the member , product id and where is_advance =1 and not chargebacks.
			Set<CommissionDetail> advCommDetails = commissionDetailRepository.findAllByMemberIdAndProductIdAndAdvanceAndChargebackOrderById(paymentDetail.getMemberId(),
					paymentDetail.getProductId(), CommissionAdvanceType.ADVANCE.getAdvanceType(), Boolean.FALSE); 
			
			if(advCommDetails.isEmpty())
				return BigDecimal.ZERO;
			
			Set<MemberCommission> memCommissionSet = memberCommRepository.findByMemberMemberId(paymentDetail.getMemberId());
			
			//its possible that advance commission created for one agent and at that time of payment
			// that agent is no longer part of member.
			List<Integer> currentMemAgentList = memCommissionSet.stream().map(MemberCommission::getAgentId).collect(Collectors.toList());
			
			BigDecimal paymentAmount = new BigDecimal(Double.toString(paymentDetail.getPeriodFee()));
			
			Map<Integer,BigDecimal> agentLeftComm = new HashMap<Integer,BigDecimal>();
			
			Set<Integer> processedAgentIds= new HashSet<Integer>();
			
			for(CommissionDetail commDetail : advCommDetails) {
				// log.info("Review Comm " +commDetail.toString());
				// Agent agent = commDetail.getCommission().getAgent();
				MemberCommission memberCommission = memCommissionSet.stream()
						  .filter(MemComm -> {
							  return commDetail.getCommission().getAgent() != null && 
							  commDetail.getCommission().getAgent().getAgentId().compareTo(MemComm.getAgentId())==0;
						  })
						  .findAny()
						  .orElse(new MemberCommission());
				
				log.info("memberCommission  Comm " +memberCommission.toString());

				BigDecimal leftCommAmount = commDetail.getLeftCommAmount()==null ? BigDecimal.ZERO : commDetail.getLeftCommAmount();
				if(commDetail.getMemberId().compareTo(paymentDetail.getMemberId()) ==0 && commDetail.getProductId().compareTo(paymentDetail.getProductId())==0
						&& ( commDetail.getPayDetailId()== 0 || (commDetail.getPayDetailId() >0 && leftCommAmount.compareTo(BigDecimal.ZERO) >0) )
						&& !processedAgentIds.contains(memberCommission.getAgentId())
						&& currentMemAgentList.contains(memberCommission.getAgentId()) )
				{
					
					
					//processedAgentIds.add(agent.getAgentId());
					
					BigDecimal advanceAmt = leftCommAmount.compareTo(BigDecimal.ZERO) > 0 ? leftCommAmount : commDetail.getAmount();
					
					paymentDetail.setIsCommission(PaymentCommissionType.ADVANCE_FOR_SOME.getType());
					
					BigDecimal availableCommAmount = BigDecimal.ZERO;
					
					Double newSalePercent = memCommissionSet.stream().filter(memComm->memComm.getProductId().compareTo(commDetail.getProductId())==0 
							&& memComm.getAgentId().compareTo(memberCommission.getAgentId())==0 )
						.mapToDouble(memComm->memComm.getNewSalePercent())
						.findFirst().orElse(0.0);//.getAsDouble();
					
					if(memberCommission.getUseFlatRate()) {
						availableCommAmount = new BigDecimal(Double.toString(newSalePercent));					 
					}
					else {
						availableCommAmount = new BigDecimal(Double.toString(newSalePercent)).multiply(paymentAmount);
					}
					
					if(agentLeftComm.getOrDefault(memberCommission.getAgentId(), BigDecimal.ZERO).compareTo(BigDecimal.ZERO) > 0) {
						availableCommAmount = agentLeftComm.get(memberCommission.getAgentId());
					}				
					totalCommAmount = totalCommAmount.add(availableCommAmount);
					//1->left amt - 2.5, commAmt-> 5 , availableCommAmt -> 7.5
					
					// advance comm. amt ==  payment comm amount
					if(advanceAmt.compareTo(availableCommAmount)== 0) {
						commDetail.setLeftCommAmount(BigDecimal.ZERO);
						Integer prevPayId = commDetail.getPayDetailId();
						String prevNote = commDetail.getNote();
						if(prevPayId > 0)
							commDetail.setNote(prevNote.concat(" ").concat(String.valueOf(prevPayId)));
						
						commDetail.setPayDetailId(paymentDetail.getId());
						
						processedAgentIds.add(memberCommission.getAgentId());
						
					}else if(advanceAmt.compareTo(availableCommAmount)<0) { // advance comm. amt  <  payment comm amount
						commDetail.setLeftCommAmount(BigDecimal.ZERO);
						Integer prevPayId = commDetail.getPayDetailId();
						String prevNote = commDetail.getNote();
						if(prevPayId > 0)
							commDetail.setNote(prevNote.concat(" ").concat(String.valueOf(prevPayId)));
						
						commDetail.setPayDetailId(paymentDetail.getId());
					}    //advance/comm amt -> 5 , availableCommAmt -> 3 , left comm -> 5-3
					else if(advanceAmt.compareTo(availableCommAmount) > 0) { // advance comm. amt  >  payment comm amount				
						commDetail.setLeftCommAmount(advanceAmt.subtract(availableCommAmount));
						Integer prevPayId = commDetail.getPayDetailId();			
						String prevNote = commDetail.getNote();
						if(prevPayId > 0)
							commDetail.setNote(prevNote.concat(" ").concat(String.valueOf(prevPayId)));
						
						commDetail.setPayDetailId(paymentDetail.getId());
						
						processedAgentIds.add(memberCommission.getAgentId());
					}
					BigDecimal leftCommAmt = availableCommAmount.subtract(advanceAmt).compareTo(BigDecimal.ZERO) > 0 ?  availableCommAmount.subtract(advanceAmt) : BigDecimal.ZERO;
					if(leftCommAmt.compareTo(BigDecimal.ZERO) !=0) {				
						agentLeftComm.put(memberCommission.getAgentId(),leftCommAmt);
					}
					
					commissionDetailRepository.save(commDetail);
					log.info("Payment applied to Adv Comm [{}]",commDetail.toString());					
				}
			}
		}
		catch(RuntimeException re) {
			log.error("Exception while mapping advance commission for Payment Detail ID [{}], Message [{}] Cause [{}]",paymentDetail.getId(),re.getMessage(),re.getCause());
		}
		return totalCommAmount;
	}

	/**
	 * @param Payment Detail id
	 * Create commission and mark it as charge back.
	 */
	public BigDecimal chargebackCommissionForPayment(Integer paymentDetailId) {
		
		BigDecimal commChargeBackAmt = BigDecimal.ZERO;
		try {
			Set<CommissionDetail> commDetails = this.commissionDetailRepository.findAllByPayDetailIdOrderById(paymentDetailId);
			
			Set<Commission> chargebackCommSet = new HashSet<>();
			Set<Integer> processedAgentIds = new HashSet<>();
				
			for(CommissionDetail commDetail : commDetails) {
				if(commDetail == null  || 
				   commDetail.getCommission() == null ||	
				   commDetail.getCommission().getAgent() == null) {
					continue;
				}
				int agentId = commDetail.getCommission().getAgent().getAgentId();

				if(commDetail.getPayDetailId().compareTo(paymentDetailId) == 0 && !commDetail.getChargeback() && !processedAgentIds.contains(agentId) ) {
					
					//commDetail.getCommission().getCommType().compareTo(CommissionAdvanceType.CHARGE_BACK.getAdvanceType())!=0
					
					//commDetail.setChargeback(true); //mark commission as charge back
					
					processedAgentIds.add(agentId);
					
					if(commDetail.getAdvance().compareTo(CommissionAdvanceType.ADVANCE.getAdvanceType())==0) 
					{
						commChargeBackAmt = commChargeBackAmt.add(commDetail.getAmount());
						commDetail.setPayDetailId(0);//remove payment mapping for this advance commission record
						commDetail.setLeftCommAmount(BigDecimal.ZERO);
						chargebackCommSet.add(commDetail.getCommission());
					}
					else 
					{
						commChargeBackAmt = commChargeBackAmt.add(commDetail.getAmount());					
						Commission chargebackComm = createChargebackComm(commDetail,false);
						chargebackCommSet.add(chargebackComm);
					}
				}
				
			}
			
			if(!chargebackCommSet.isEmpty()) {
				this.commissionRepository.saveAll(chargebackCommSet);
				commChargeBackAmt = commChargeBackAmt.negate();
			}			
		}
		catch(RuntimeException re) {			
			log.error("Exception while commission chargeback for Payment Detail ID [{}], Message [{}] Cause [{}]",paymentDetailId,re.getMessage(),re.getCause());
		}
		return commChargeBackAmt;
	}
	
	/**
	 * @param paymentDetailId
	 * @return
	 */
	public BigDecimal reverseChargebackCommissionForPayment(Integer paymentDetailId) {

		BigDecimal commChargeBackAmt = BigDecimal.ZERO;
		try {
			Set<CommissionDetail> commDetails = this.commissionDetailRepository.findAllByPayDetailIdOrderById(paymentDetailId);
			Set<Integer> processedAgentIds = new HashSet<>();
			Set<Commission> reverseChargebackCommSet = new HashSet<>();
			
			try {
				Optional<PaymentDetail> optPayDetail = this.paymentDetailRepository.findById(paymentDetailId);
				if(optPayDetail.isPresent()) {
					PaymentDetail paymentDetail = optPayDetail.get();
					Set<CommissionDetail> advCommDetails = commissionDetailRepository.findAllByMemberIdAndProductIdAndAdvanceAndChargebackOrderById(paymentDetail.getMemberId(),
					paymentDetail.getProductId(), CommissionAdvanceType.ADVANCE.getAdvanceType(), Boolean.FALSE); 
					//payAdanceCommissionWithMemPayment	
					if(!advCommDetails.isEmpty()) 
					{
						/*if(memberService.isMemberPartOfInternationalDivision(paymentDetail.getMemberId()))
						{
							paymentService.processAdvanceCommForMember(paymentDetail);
						}*/
						BigDecimal advCommAmt = this.payAdanceCommissionWithMemPayment(paymentDetail);
						commChargeBackAmt = commChargeBackAmt.add(advCommAmt);
					}
				}
			}
			catch(RuntimeException re) {
				log.error("Exception while commission reverse, advance commission mapping for Payment Detail ID [{}], Message [{}] Cause [{}]",paymentDetailId,re.getMessage(),re.getCause());
			}
			
			boolean chargebackCommExits = commDetails.stream().filter(item-> item.getPayDetailId().compareTo(paymentDetailId)==0 && item.getChargeback()).findAny().isPresent();
			
			if(!chargebackCommExits) {
				log.info("No Charge back commission exists for PDID [{}], New commission not created for this payment.",paymentDetailId);
				return commChargeBackAmt;
			}
			
			for (CommissionDetail commDetail : commDetails) {
				if(commDetail == null  || 
						   commDetail.getCommission() == null ||	
						   commDetail.getCommission().getAgent() == null) {
					continue;
				}
				int agentId = commDetail.getCommission().getAgent().getAgentId();
				
				if (commDetail.getPayDetailId().compareTo(paymentDetailId) == 0 && !commDetail.getChargeback() && !processedAgentIds.contains(agentId)) {
	
					processedAgentIds.add(agentId);
					commChargeBackAmt = commChargeBackAmt.add(commDetail.getAmount());
	
					Commission chargebackComm = createChargebackComm(commDetail,true);
					
					reverseChargebackCommSet.add(chargebackComm);
				}
	
			}
			if (!reverseChargebackCommSet.isEmpty())
				this.commissionRepository.saveAll(reverseChargebackCommSet);
		}
		catch(RuntimeException re) {
			log.error("Exception while commission reverse chargeback for Payment Detail ID [{}], Message [{}] Cause [{}]",paymentDetailId,re.getMessage(),re.getCause());
		}
		return commChargeBackAmt;
	}
	
	/**
	 * @param payDetailId
	 * @return
	 */
	public Set<Commission> commissionDataByPayDetail(Integer payDetailId) {		
		return commissionRepository.findAllByCommDetailsPayDetailIdAndCommDetailsAdvanceAndCommDetailsChargeback(payDetailId,1,Boolean.FALSE);		
	}

	/**
	 * @param commDetail
	 * @return
	 */
	private Commission createChargebackComm(CommissionDetail commDetail,boolean reverseCB) {
		Commission newChargebackComm = new Commission();
		CommissionDetail newChargebackCommDetail = new CommissionDetail();
		newChargebackComm.addCommDetails(newChargebackCommDetail);
		newChargebackCommDetail.setCommission(newChargebackComm);
		
		newChargebackComm.setActive(true);
		newChargebackComm.setAgent(commDetail.getCommission().getAgent());
		newChargebackComm.setCommissionDate(new java.util.Date());
		
		if(reverseCB)
			newChargebackComm.setCommTotalAmount(commDetail.getAmount());
		else 
			newChargebackComm.setCommTotalAmount(commDetail.getAmount().negate());
		
		newChargebackComm.setCommType(commDetail.getCommission().getCommType());
		newChargebackComm.setCreatedDate(new java.util.Date());
		newChargebackComm.setDeleted(false);
		newChargebackComm.setPaid(false);
		newChargebackComm.setPayDate(Constant.DEFULT_DATE_1900);
		
		if(reverseCB) {
			newChargebackCommDetail.setAmount(commDetail.getAmount());
			newChargebackCommDetail.setChargeback(false);
			newChargebackCommDetail.setAdvance(commDetail.getAdvance());
			newChargebackCommDetail.setLeftCommAmount(commDetail.getLeftCommAmount());
		}
		else {
			newChargebackCommDetail.setAmount(commDetail.getAmount().negate());
			newChargebackCommDetail.setChargeback(true);
			newChargebackCommDetail.setAdvance(CommissionAdvanceType.CHARGE_BACK.getAdvanceType());
			newChargebackCommDetail.setLeftCommAmount(BigDecimal.ZERO);
		}
		
		newChargebackCommDetail.setMemberId(commDetail.getMemberId());
		newChargebackCommDetail.setProductId(commDetail.getProductId());
		newChargebackCommDetail.setPayDetailId(commDetail.getPayDetailId());
		
		return newChargebackComm;
	}
	/**
	 * @param commDetail
	 * @param commAmount
	 * @return
	 */
	public Commission createChargebackComm(CommissionDetail commDetail,BigDecimal commAmount) 
	{
		Commission newChargebackComm = new Commission();
		CommissionDetail newChargebackCommDetail = new CommissionDetail();
		
		newChargebackComm.addCommDetails(newChargebackCommDetail);
		newChargebackCommDetail.setCommission(newChargebackComm);
		
		newChargebackComm.setActive(true);
		newChargebackComm.setAgent(commDetail.getCommission().getAgent());
		newChargebackComm.setCommissionDate(new java.util.Date());
		
		if(commAmount.compareTo(BigDecimal.ZERO)==0)
			newChargebackComm.setCommTotalAmount(commDetail.getAmount());
		else
			newChargebackComm.setCommTotalAmount(commAmount);
				
		newChargebackComm.setCommType(commDetail.getCommission().getCommType());
		newChargebackComm.setCreatedDate(new java.util.Date());
		newChargebackComm.setDeleted(false);
		newChargebackComm.setPaid(false);
		newChargebackComm.setPayDate(Constant.DEFULT_DATE_1900);
		
		newChargebackCommDetail.setAmount(commDetail.getAmount().negate());
		newChargebackCommDetail.setChargeback(true);
		newChargebackCommDetail.setAdvance(CommissionAdvanceType.CHARGE_BACK.getAdvanceType());
		newChargebackCommDetail.setLeftCommAmount(BigDecimal.ZERO);
		newChargebackCommDetail.setMemberId(commDetail.getMemberId());
		newChargebackCommDetail.setProductId(commDetail.getProductId());
		newChargebackCommDetail.setPayDetailId(0);
		
		return newChargebackComm;
	}
	
	public void deleteCommissionDetailRow(CommissionDetail commissionDetail) {
		log.info("Commission detail record [{}] Member ID [{}], deleted",commissionDetail.getId(),commissionDetail.getMemberId());
		commissionDetail.setCommission(null);
		this.commissionDetailRepository.delete(commissionDetail);		
	}
	public void saveCommissionDetailRow(CommissionDetail commissionDetail) {
		log.info("Commission detail record [{}] Member ID [{}], saved",commissionDetail.getId(),commissionDetail.getMemberId());
		this.commissionDetailRepository.save(commissionDetail);
	}
}
