package com.masa.pts.core.service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.masa.pts.core.constant.PaymentCommissionType;
import com.masa.pts.core.domain.Constant;
import com.masa.pts.core.domain.Member;
import com.masa.pts.core.domain.Member.MemberInfo;
import com.masa.pts.core.domain.MemberFee;
import com.masa.pts.core.domain.Payment;
import com.masa.pts.core.domain.PaymentDetail;
import com.masa.pts.core.domain.PaymentDetail.PaymentDetailSummary;
import com.masa.pts.core.domain.PaymentDetailForteTransaction;
import com.masa.pts.core.domain.ProductFee;
import com.masa.pts.core.model.PaymentGroupProductDTO;
import com.masa.pts.core.model.PaymentGroupProductSummaryDTO;
import com.masa.pts.core.repository.MemberRepository;
import com.masa.pts.core.repository.PaymentDetailRepository;
import com.masa.pts.core.repository.PaymentForteTransactionRepository;
import com.masa.pts.core.repository.PaymentRepository;

import static com.masa.pts.core.domain.Constant.DEFAULT_DATE_PATTERN;

@Service
public class PaymentService  {

	private static final Logger log = LoggerFactory.getLogger(PaymentService.class);
	
	@Autowired
	MemberRepository memberRepository ;
	@Autowired
	PaymentRepository paymentRepository;
	@Autowired
	MemberService memberService;
	@Autowired
	MemberProductFeeService memberProductFeeService;
	
	@Autowired
	PaymentDetailRepository paymentDetailRepository;
	
	@Autowired
	PaymentForteTransactionRepository paymentForteTransactionRepository;
	
	@Autowired
	GroupService groupService;
	
	@Autowired
	CommissionService commissionService;
	
	@Autowired
	ProductService productService;
	
	
	/**
	 * @param memberId
	 * @param transactionDate 
	 * @return
	 */
	public Set<PaymentDetail> createPaymentDetail(int memberId, Date transactionDate,Boolean includeAlternatePayor,Boolean onlyActive) {
		
		Set<PaymentDetail> payDetails = new HashSet<>();
		
		
		Optional<Member> optMember = memberRepository.findByMemberId(memberId, Member.class);
		
		if(!optMember.isPresent())
			return payDetails;
		
		Member member = optMember.get();
		
		Set<MemberFee> activeMemFees = member.getMemberFee().stream().filter(item-> isDateWithInRange(transactionDate, item.getEffectiveStartDate(),item.getEffectiveEndDate() ))
				.collect(Collectors.toSet());
		
		long start=System.currentTimeMillis();
		//Calculate amount paid by product / product multipliers
		memberProductFeeService.calculateMemberProductFeesAndBalances(member.getMemberId(), activeMemFees,Boolean.FALSE);
		long end = System.currentTimeMillis();
		if((end-start)>=Constant.THREASHOLD_TO_LOG)
			log.info("calculateMemberProductFeesAndBalances method took [{}]",(end-start));
		
		start=System.currentTimeMillis();
		payDetails.addAll(getMemberPaymentDetail(member,member.getAlternatePayer(),activeMemFees));
		end = System.currentTimeMillis();
		if((end-start)>=Constant.THREASHOLD_TO_LOG)
			log.info("getMemberPaymentDetail method took [{}]",(end-start));
		
		if(!member.getSubMembers().isEmpty() && includeAlternatePayor.booleanValue()) {	
			member.getSubMembers().forEach(
						subMemberItem-> {
								Set<MemberFee> subMemActiveMemFees = subMemberItem.getMemberFee().stream().filter(item-> isDateWithInRange(transactionDate, item.getEffectiveStartDate(),item.getEffectiveEndDate() ))
									.collect(Collectors.toSet());
								if(onlyActive.booleanValue())
								{
									if(( subMemberItem.isStatusActive() || subMemberItem.isStatusSuspended() ) || (subMemberItem.isStatusCancelled() && transactionDate.before(subMemberItem.getCancelDate())) ) 
									{	//only if member is active
										memberProductFeeService.calculateMemberProductFeesAndBalances(subMemberItem.getMemberId(), subMemActiveMemFees,Boolean.FALSE);
										payDetails.addAll(getMemberPaymentDetail(subMemberItem,subMemberItem.getAlternatePayer(),subMemActiveMemFees));
									}
								}
								else
								{
									memberProductFeeService.calculateMemberProductFeesAndBalances(subMemberItem.getMemberId(), subMemActiveMemFees,Boolean.FALSE);
									payDetails.addAll(getMemberPaymentDetail(subMemberItem,subMemberItem.getAlternatePayer(),subMemActiveMemFees));
								}
						}
					);					
		}
		//
		boolean memberPartOfDownpaymentGroup =  groupService.isDownPaymentGroup(member.getGroup().getGroupCode());
		if(memberPartOfDownpaymentGroup) 
		{
			Integer productId = activeMemFees.stream().filter(item->item.getFeeDetails().getFeeId() !=1 && item.getProduct().getUpgradeProduct().intValue()==0)
					.map(item->item.getProduct().getProductId()).findFirst().orElse(0);			
			boolean paymentsExists = memberHasPayments(memberId, productId,transactionDate);
			if(!paymentsExists)
			{
				//if first payment, then payment should be posted to only primary product not admin fee product.
				//remove that product from the list.
				payDetails.removeIf(payDetailItem->payDetailItem.getProductId().compareTo(productId) !=0);
			}
		}
		//
		
		return payDetails;		
	}
	
	/**
	 * @param memberFees
	 * @param member
	 * @return
	 */
	private Set<PaymentDetail> getMemberPaymentDetail(Member member,Integer alternatePayorId,Set<MemberFee> activeMemFees){
		
		Set<PaymentDetail> payDetails = new HashSet<>();
		
		PaymentDetail paymentDetail ;
		
		MemberFee initFee = activeMemFees.stream().filter(feeItem->feeItem.getFeeDetails().getFeeId()==1)
				.findFirst().orElse(new MemberFee());
		
		double setupFeeYearParts = memberProductFeeService.findSetupFeeYearParts(activeMemFees);
		
		double memTaxRate =  member.getTaxRate();
		
		for(MemberFee fee : activeMemFees) {
			
			if(fee.getFeeDetails().getFeeId() != 1) {
				
				paymentDetail = new PaymentDetail();				
				
				paymentDetail.setIsNew(isMemberProductPaymentCommissionNew(member,fee));
				paymentDetail.setIsChargeBack(false);
				paymentDetail.setMemberId(member.getMemberId());
				paymentDetail.setTransactionDate(new java.util.Date());
				paymentDetail.setProductId(fee.getProduct().getProductId());
				paymentDetail.setIsCommission(PaymentCommissionType.UNPAID.getType());
				paymentDetail.setCommPeriod(0.0);
				paymentDetail.setPeriodFee(fee.getAmount());
				//paymentDetail.setAmountDue(fee.getAmount());//place holder to calculate premium due and renewal date
				paymentDetail.setTaxRate(memTaxRate );
				paymentDetail.setHasTax( member.getTaxRate().compareTo(Double.valueOf(0.0)) == 0 ? Boolean.FALSE: Boolean.TRUE);
				
				paymentDetail.setCreatedBy(Constant.DEFAULT_USER_ID);
				paymentDetail.setModifiedBy(Constant.DEFAULT_USER_ID);
				paymentDetail.setCreatedDate(new Date());
				paymentDetail.setModifiedDate(new Date());
				
				if(null != alternatePayorId && alternatePayorId.intValue() > 0)
					paymentDetail.setAlternatePayorId(alternatePayorId);
				else
					paymentDetail.setAlternatePayorId(0);
				
				double setupFeeAmt = 0;
				if ( initFee.getAmountPaid() < initFee.getAmount() && initFee.getProduct().getProductId().compareTo(fee.getProduct().getProductId()) == 0)
				{
					if (member.getOneTimeInit()!=null && member.getOneTimeInit())
					{
						setupFeeAmt =  initFee.getAmount() - initFee.getAmountPaid();
					}
					else
					{    
						if ( (initFee.getAmount() - initFee.getAmountPaid()) <  initFee.getAmount() / setupFeeYearParts ) 
						{
							setupFeeAmt = initFee.getAmount() - initFee.getAmountPaid();
						}
						else
						{
							setupFeeAmt = initFee.getAmount() / setupFeeYearParts;
						}
					}
				}
				paymentDetail.setAmountPaid(  (setupFeeAmt + paymentDetail.getPeriodFee()) * (1 + memTaxRate));
				
				payDetails.add(paymentDetail);
			}
		}		
		return payDetails;
	}

	/**
	 * @param memberId
	 * @return
	 */
	public java.util.Date getMemberFirstPaymentDate(Integer memberId) {
		 
		Optional<Payment>	optPayment = paymentRepository.findFirstByPaymentDetailMemberIdOrderByPaymentDetailId(memberId);
				
		return optPayment.isPresent() ? (optPayment.get().getPayDate()) : null; 
	}
	
	/**
	 * @param memberId
	 * @param memProductIds
	 * @param reInstateDate
	 * @param excludeTax
	 * @return
	 * taking too long to execute - getting payment detail took average 4 secs findAllByPaymentDetailMemberIdAndPaymentDetailProductId
	 */
	
	public Map<Integer, Double> getMemberTotalPaymentsByProduct(Integer memberId, Set<Integer> memProductIds,Date reInstateDate, boolean excludeTax,Date grpPastDueEffectiveDate) {
		Map<Integer, Double> memPrdPaidAmtMap = new HashMap<>();
		boolean memberReInstated = memberService.isMemberReinstated(reInstateDate);
		
		Date postDate = grpPastDueEffectiveDate == null? Constant.DEFULT_DATE_1900 : grpPastDueEffectiveDate;
		
		memProductIds.forEach(productId -> {
			long start=System.currentTimeMillis();
			Set<PaymentDetail.PaymentDetailSummary> payDetails = this.paymentDetailRepository.findAllByMemberIdAndProductIdAndPaymentPayDateGreaterThanOrderByTransactionDate(memberId, productId, postDate, PaymentDetail.PaymentDetailSummary.class);
			long end=System.currentTimeMillis();
			if((end-start)>=Constant.THREASHOLD_TO_LOG)
				log.info("findAllByMemberIdAndProductIdAndPostDateOrderByTransactionDate  execution time [{}]",(end-start));
			
			payDetails.forEach(payDetailItem->{						
						Double amt = 0.0;
						if (payDetailItem.getMemberId().compareTo(memberId) == 0 
								&& payDetailItem.getProductId().compareTo(productId) == 0 
								&& validPayment(payDetailItem,memberReInstated,reInstateDate) ) {
							// check for member, when Payment object is retrieved,
							// it can have other member pay details.
							double taxRate = payDetailItem.getTaxRate() == null ? 0.0 : payDetailItem.getTaxRate();
							if (excludeTax)
								amt = memPrdPaidAmtMap.getOrDefault(productId, 0.0) + (payDetailItem.getAmountPaid() / (1 + taxRate));
							else
								amt = memPrdPaidAmtMap.getOrDefault(productId, 0.0) + payDetailItem.getAmountPaid();// includes
																													// tax
							amt = Math.round(amt * 100.0) / 100.0;
							memPrdPaidAmtMap.put(productId, amt);
						}						
					});
		});
		return memPrdPaidAmtMap;
	}
		
	
	public List<PaymentGroupProductSummaryDTO> getMemberTotalPaymentsByGroupProduct(Integer memberId, Date reInstateDate,
			boolean excludeTax, Date grpPastDueEffectiveDate) {

		Date postDate = getMinOfDates(reInstateDate, grpPastDueEffectiveDate);

		if (postDate == null) {
			postDate = Constant.DEFULT_DATE_1900;
		}

		Set<PaymentGroupProductDTO> paymentData = paymentDetailRepository.getMemberPaymentsByGroupAndProduct(memberId,
				postDate);

		List<PaymentGroupProductSummaryDTO> returnData = new ArrayList<>();

		for (PaymentGroupProductDTO paymentRecord : paymentData) {
			Double amt;
			double taxRate = paymentRecord.getTaxRate() == null ? 0.0 : paymentRecord.getTaxRate();

			Optional<PaymentGroupProductSummaryDTO> optSummaryRow = returnData.stream()
					.filter(item -> item.getGroupId().compareTo(paymentRecord.getGroupId()) == 0
							&& item.getProductId().compareTo(paymentRecord.getProductId()) == 0)
					.findFirst();
			
			PaymentGroupProductSummaryDTO summaryRow;
			
			if(optSummaryRow.isPresent()) {
				summaryRow = optSummaryRow.get();				
			}
			else {
				summaryRow = new PaymentGroupProductSummaryDTO();
				summaryRow.setGroupId(paymentRecord.getGroupId());
				summaryRow.setProductId(paymentRecord.getProductId());
				summaryRow.setTotalAmountPaid(0.0);
				returnData.add(summaryRow);
			}
			
			if (excludeTax) {
				amt = summaryRow.getTotalAmountPaid() + (paymentRecord.getAmountPaid() / (1 + taxRate));
			} else {
				amt = summaryRow.getTotalAmountPaid() + (paymentRecord.getAmountPaid());
			}

			amt = Math.round(amt * 100.0) / 100.0;

			summaryRow.setTotalAmountPaid(amt);
		}
		
		return returnData;
	}
	public Map<Integer,Double> getMemberTotalPaymentsByGroupProduct(Integer memberId, Date reInstateDate,
			boolean excludeTax, Date grpPastDueEffectiveDate,Integer activeGroupId) {

		Date postDate = getMinOfDates(reInstateDate, grpPastDueEffectiveDate);

		if (postDate == null) {
			postDate = Constant.DEFULT_DATE_1900;
		}

		Set<PaymentGroupProductDTO> paymentData = paymentDetailRepository.getMemberPaymentsByGroupAndProduct(memberId,
				postDate);

		List<PaymentGroupProductSummaryDTO> returnData = new ArrayList<>();

		for (PaymentGroupProductDTO paymentRecord : paymentData) {
			Double amt;
			double taxRate = paymentRecord.getTaxRate() == null ? 0.0 : paymentRecord.getTaxRate();

			Optional<PaymentGroupProductSummaryDTO> optSummaryRow = returnData.stream()
					.filter(item -> item.getGroupId().compareTo(paymentRecord.getGroupId()) == 0
							&& item.getProductId().compareTo(paymentRecord.getProductId()) == 0)
					.findFirst();
			
			PaymentGroupProductSummaryDTO summaryRow;
			
			if(optSummaryRow.isPresent()) {
				summaryRow = optSummaryRow.get();				
			}
			else {
				summaryRow = new PaymentGroupProductSummaryDTO();
				summaryRow.setGroupId(paymentRecord.getGroupId());
				summaryRow.setProductId(paymentRecord.getProductId());
				summaryRow.setTotalAmountPaid(0.0);
				returnData.add(summaryRow);
			}
			
			if (excludeTax) {
				amt = summaryRow.getTotalAmountPaid() + (paymentRecord.getAmountPaid() / (1 + taxRate));
			} else {
				amt = summaryRow.getTotalAmountPaid() + (paymentRecord.getAmountPaid());
			}

			amt = Math.round(amt * 100.0) / 100.0;

			summaryRow.setTotalAmountPaid(amt);
		}
		
		return returnData.stream()
					.filter(item->item.getGroupId().compareTo(activeGroupId)==0)
					.collect(
							Collectors.groupingBy(
									PaymentGroupProductSummaryDTO::getProductId, Collectors.summingDouble(PaymentGroupProductSummaryDTO::getTotalAmountPaid)
									)
							)
					;
	}
	
	private Date getMinOfDates(Date date1,Date date2) {
		
		if(date1==null && date2==null) {
			return null;
		}
		else if( date1==null) {
			return date2;
		}
		else if(date2==null) {
			return date1;
		}
		else if(date1.after(date2)){
			return date1;
		}
		else {
			return date2;
		}
	}
	
	/**
	 * @param memberId
	 * @param memProductIds
	 * @param memberFees
	 * @return
	 */
	public Map<Integer, Double> getMasaPaymentsByProduct(Integer memberId, Set<Integer> memProductIds, Set<MemberFee> memberFees) {

		Map<Integer, Double> memPrdPaidAmtMap = new HashMap<>();

		memProductIds.forEach(productId -> {
			Set<Payment> memberPayments = this.paymentRepository.findAllByPayMasaAndPaymentDetailMemberIdAndPaymentDetailProductIdAndPaymentDetailVoidPayment(Boolean.TRUE, memberId, productId,Boolean.FALSE);

			for (Payment pay : memberPayments) {
				if (pay.getPayMasa()) {
					double amount = pay.getPaymentDetail().stream().filter(item -> {
						return memberId.compareTo(item.getMemberId()) == 0 && productId.compareTo(item.getProductId()) == 0 && !item.getIsChargeBack();
					}).mapToDouble(PaymentDetail::getPeriodFee).sum();
					Double amt = memPrdPaidAmtMap.getOrDefault(productId, 0.0);
					memPrdPaidAmtMap.put(productId, (amount + amt));
				}
			}
		});
		return memPrdPaidAmtMap;
	}
	
	public Map<Integer, Double> getMasaPaymentsForMember(Integer memberId, Set<MemberFee> memberFees,Date reinstateDate) {

		Map<Integer, Double> memPrdPaidAmtMap = new HashMap<>();
		Set<Payment> memberPayments = this.paymentRepository
				.findAllByPayMasaAndPaymentDetailMemberIdAndPaymentDetailVoidPayment(Boolean.TRUE, memberId,
						Boolean.FALSE);

		//exclude these product ids, as they are current and excluded 
		Set<Integer> currentProductIdSet = new HashSet<>(); 
				//memberFees.stream().map(item->item.getProduct().getProductId()).collect(Collectors.toSet());
		
		for (Payment pay : memberPayments) {
			if (Boolean.TRUE.equals(pay.getPayMasa())) {
				
				Set<PaymentDetail> payDetailSet = pay.getPaymentDetail().stream()
						.filter(item -> {return memberId.compareTo(item.getMemberId()) == 0 && !currentProductIdSet.contains(item.getProductId()) && !item.getIsChargeBack();})
						.collect(Collectors.toSet());
				
				payDetailSet.forEach(item->{
					Double masaAmt = item.getPeriodFee() ==0 ? item.getAmountPaid() : item.getPeriodFee();
					Double totalMasaAmt = memPrdPaidAmtMap.getOrDefault(item.getProductId(), 0.0);
					memPrdPaidAmtMap.put(item.getProductId(), (masaAmt + totalMasaAmt));
				});
			}
		}
		return memPrdPaidAmtMap;
	}
	
	/**
	 * @param chargeback
	 * @param memberReinstated
	 * @param reInstateDate
	 * @param transactionDate
	 * @return
	 */
	private boolean validPayment(PaymentDetail.PaymentDetailSummary payDetailItem,boolean memberReinstated,Date reInstateDate) {
		
		Boolean chargeback = payDetailItem.getIsChargeBack();
		Date transactionDate = payDetailItem.getTransactionDate();
		Boolean isVoid = payDetailItem.getVoidPayment();
		
		if(Boolean.TRUE.equals(chargeback)) //exclude chargebacks
			return false;
		
		if(Boolean.TRUE.equals(isVoid))
			return false;
		
		if(null == transactionDate)
			return true;//changed as some member have transaction date null example- 297566
		
		//if member is reinstated and payment transaction is before reinstatement date then exclude
		if(memberReinstated && (transactionDate.before(reInstateDate)) ) {
			return false;			
		}
		
		return true;
	}
	
	public Map<Integer, Double> getMemberPaymentsByProduct(Integer memberId,Date reInstateDate,List<PaymentDetail.PaymentDetailSummary> payDetailList)
	{
		
		boolean memberReInstated = memberService.isMemberReinstated(reInstateDate);
		
		Map<Integer, Double> memPrdPaidAmtMap = new HashMap<>();
		
		payDetailList.forEach(payDetailItem->{
			Double productAmount=0.0;
			Double payAmount=0.0;
			
			if(validPayment(payDetailItem, memberReInstated, reInstateDate))
			{
				payAmount = payDetailItem.getPeriodFee()== 0 ? payDetailItem.getAmountPaid() : payDetailItem.getPeriodFee();
				
				productAmount = memPrdPaidAmtMap.getOrDefault(payDetailItem.getProductId(), 0.0) + payAmount;
				
				productAmount = Math.round(productAmount * 100.0) / 100.0;
				memPrdPaidAmtMap.put(payDetailItem.getProductId(),productAmount);				
			}
		});
		log.info("Member ID [{}] Payments by product [{}]",memberId,memPrdPaidAmtMap);
		return memPrdPaidAmtMap;
	}	
		
	public Double getMemberTotalPaidCommAmount(Integer memberId,Date reinstateDate, Set<MemberFee> memberFeeSet, String groupCode,Integer productId)
	{
		Double totalPaidAmount=0.0;
		
		List<PaymentDetail.PaymentDetailSummary> payDetailList = paymentDetailRepository.findAllByMemberIdAndVoidPayment(memberId, Boolean.FALSE, PaymentDetailSummary.class);
		
		//consider only new commission payments
		List<PaymentDetail.PaymentDetailSummary> newCommPayDetailList = payDetailList.stream().filter(item->Boolean.TRUE.equals(item.getIsNew())
				&& item.getProductId().compareTo(productId)==0).collect(Collectors.toList());
		
		//Total payments by product
		Map<Integer, Double> memPrdPaidAmtMap = getMemberPaymentsByProduct(memberId,reinstateDate,newCommPayDetailList);
		
		//products - max amount for new commission/per year amount
		Map<Integer,Double> maxYearAmtByPrdMap  = getProductMaxYearAmount(memberId,memPrdPaidAmtMap,memberFeeSet,groupCode);
		
		Map<Integer, Double> masaPaymentsMap = getMasaPaymentsForMember(memberId, memberFeeSet,reinstateDate);
		
		Set<Integer> memberPrdIdset = memPrdPaidAmtMap.keySet();
		for(Integer prdId: memberPrdIdset)
		{
			double prdPaidAmt = memPrdPaidAmtMap.getOrDefault(prdId, 0.0);
			double prdMaxYearAmt = maxYearAmtByPrdMap.getOrDefault(prdId, 0.0);
			double pendingNewCommAmt= (prdMaxYearAmt - prdPaidAmt) <=0 ? prdMaxYearAmt : prdPaidAmt  ;
			pendingNewCommAmt -= masaPaymentsMap.getOrDefault(prdId, 0.0);
			totalPaidAmount += pendingNewCommAmt;
		}		
		log.info("Member ID [{}] Total Paid New Commission amount [{}]",memberId,totalPaidAmount);
		return totalPaidAmount;
	}
	public Double getMemberTotalPaidCommAmount(Integer memberId,Date reinstateDate, Set<MemberFee> memberFeeSet, String groupCode,boolean isInternationalMember)
	{
		Double totalPaidAmount=0.0;
		
		Set<Integer> primaryPrdIds = new HashSet<>();
		
		if(isInternationalMember) {
			 primaryPrdIds = filterUpgradeProd(memberId);			
		}
		
		//member payments 
		List<PaymentDetail.PaymentDetailSummary> payDetailList = paymentDetailRepository.findAllByMemberIdAndVoidPayment(memberId, Boolean.FALSE, PaymentDetailSummary.class);
		
		
		List<PaymentDetail.PaymentDetailSummary> newCommPayDetailList;
		
		if(isInternationalMember && !primaryPrdIds.isEmpty()) {	
			Set<Integer> primaryPrdIdTmp = primaryPrdIds;
			newCommPayDetailList = payDetailList.stream().filter(item->Boolean.TRUE.equals(item.getIsNew()) && primaryPrdIdTmp.contains(item.getProductId())).collect(Collectors.toList());
		}
		else {
			//consider only new commission payments
			newCommPayDetailList = payDetailList.stream().filter(item->Boolean.TRUE.equals(item.getIsNew())).collect(Collectors.toList());	
		}	
		
		//Total payments by product
		Map<Integer, Double> memPrdPaidAmtMap = getMemberPaymentsByProduct(memberId,reinstateDate,newCommPayDetailList);
		
		//products - max amount for new commission/per year amount
		Map<Integer,Double> maxYearAmtByPrdMap  = getProductMaxYearAmount(memberId,memPrdPaidAmtMap,memberFeeSet,groupCode);
		
		Map<Integer, Double> masaPaymentsMap = getMasaPaymentsForMember(memberId, memberFeeSet,reinstateDate);
		
		Set<Integer> memberPrdIdset = memPrdPaidAmtMap.keySet();
		for(Integer prdId: memberPrdIdset)
		{
			double prdPaidAmt = memPrdPaidAmtMap.getOrDefault(prdId, 0.0);
			double prdMaxYearAmt = maxYearAmtByPrdMap.getOrDefault(prdId, 0.0);
			double pendingNewCommAmt= (prdMaxYearAmt - prdPaidAmt) <=0 ? prdMaxYearAmt : prdPaidAmt  ;
			pendingNewCommAmt -= masaPaymentsMap.getOrDefault(prdId, 0.0);
			totalPaidAmount += pendingNewCommAmt;
		}		
		log.info("Member ID [{}] Total Paid New Commission amount [{}]",memberId,totalPaidAmount);
		return totalPaidAmount;
	}
	
	
	private Set<Integer> filterUpgradeProd(Integer memberId) {
		return paymentDetailRepository.getMemberPaymentPrimaryProducts(memberId);
	}

	/**
	 * @param memberFee
	 * @param memberId 
	 * @return true = new commission , false = renew commission
	 */
	public boolean isMemberProductPaymentCommissionNew(Member member,MemberFee memberFee)
	{
		Double masaPayAmt = memberFee.getAmountPaidMasa();
		
		Double totalPaidAmount;
		
		int memDivId=memberService.getMemberDivision(member.getMemberId());
		boolean internatioanlDivMem = PTSUtilityService.isInternationalDivision(memDivId);
		
		if(PTSUtilityService.isDivisionWithNewCommissionProcess(memDivId))
		{
			if(internatioanlDivMem && memberFee.getUpgradeType().intValue() !=0) {
				totalPaidAmount = getMemberTotalPaidCommAmount(member.getMemberId(),member.getReinstateDate(),member.getMemberFee(),member.getGroup().getGroupCode(),
						memberFee.getProduct().getProductId());
			}
			else {
				totalPaidAmount = getMemberTotalPaidCommAmount(member.getMemberId(),member.getReinstateDate(),member.getMemberFee(),member.getGroup().getGroupCode(),internatioanlDivMem);	
			}
		}
		else
		{
			totalPaidAmount = memberFee.getAmountPaid()-masaPayAmt;
		}
		
		if (memberFee.getMaxAmountDue() > 0 && (totalPaidAmount) >= memberFee.getMaxAmountDue())
		{
			return false;
		}
		else if (memberFee.getMaxAmountDue() <= 0 && (totalPaidAmount) >= (memberFee.getAmount() * memberFee.getFrequencyDetails().getYearParts()))
		{
			return false;
		}
		else if(Boolean.TRUE.equals(memberFee.getOverrideNewComm()))
		{
			return false;
		}
		else
		{
			return true;
		}
	}

	/**
	 * @param memPrdPaidAmtMap
	 * @param member
	 * @return
	 */
	private Map<Integer, Double> getProductMaxYearAmount(Integer memberId,Map<Integer, Double> memPrdPaidAmtMap, Set<MemberFee> memberFees,String groupCode) {
		Map<Integer,Double> maxYearAmtByPrdMap  = new HashMap<>();
		Set<Integer> prdIdSet = memPrdPaidAmtMap.keySet();
		for(Integer prdId: prdIdSet)
		{
			MemberFee memberFee = memberFees.stream().filter(item->item.getProduct().getProductId().compareTo(prdId)==0 && item.getFeeDetails().getFeeId().intValue() !=1).findFirst().orElse(null);
			
			//TODO change process for 5 year / installament plans - where year amount is no based on year parts
			if(null != memberFee)
			{
				maxYearAmtByPrdMap.put(prdId, memberFee.getFrequencyDetails().getYearParts() * memberFee.getAmount());
			}
			else
			{
				// not current product, look in group setup for that product or in product setup
				Double productYearAmt = groupService.getProductMaxYearAmount(groupCode, prdId);
				if(productYearAmt != 0)
				{
					maxYearAmtByPrdMap.put(prdId, productYearAmt);
				}
				else
				{
					//product not available in group, check in product setup
					Set<ProductFee> prdFeeSet = productService.getProductFees(prdId);
					ProductFee productFee = prdFeeSet.stream().filter(item->item.getProductFeeId().getFeeId().intValue() !=1).findFirst().orElse(null);
					if(null != productFee && null != productFee.getFrequencyDetails())
					{
						maxYearAmtByPrdMap.put(prdId, productFee.getAmount() * productFee.getFrequencyDetails().getYearParts());
					}
				}				
			}
		}
		log.info("Member ID [{}] Products Max Year Amount [{}]",memberId,maxYearAmtByPrdMap);
		return maxYearAmtByPrdMap;
	}

	/**
	 * @param memOneTimeInit
	 * @param memTaxRate
	 * @param productId
	 * @param amountPaid
	 * @param initFee
	 * @return
	 */
	public Double getPaymentPeriodFee(Boolean memOneTimeInit,double memTaxRate,Integer productId,Double amountPaid,MemberFee initFee) {
		
		double periodFeeAmt = 0;
		double premimumPayment = 0.00;
		double remainingInitAmt = initFee.getAmount() - initFee.getAmountPaid();
		double afterTaxAmtPaid = (amountPaid / (1 + memTaxRate));

		if(memOneTimeInit ==null)
			memOneTimeInit = Boolean.FALSE;
		
		if (remainingInitAmt > 0 && productId.compareTo(initFee.getProduct().getProductId()) ==0)
		{
			if (memOneTimeInit)
			{
				if (afterTaxAmtPaid > remainingInitAmt)
					periodFeeAmt = afterTaxAmtPaid - remainingInitAmt;
				else
					periodFeeAmt = 0;
			}
			else
			{
				premimumPayment = afterTaxAmtPaid * (1 - initFee.getInitMultipler());

				if ((afterTaxAmtPaid - premimumPayment) > remainingInitAmt)
				{
					periodFeeAmt = afterTaxAmtPaid - remainingInitAmt;
				}
				else
					periodFeeAmt = premimumPayment;
			}
		}
		else
		{
			periodFeeAmt = afterTaxAmtPaid;
		}

		return periodFeeAmt;
	}
	
	/**
	 * @param payDetailItem
	 * @return
	 */
	public Date getMemberNextRenewDate(PaymentDetail payDetailItem) {
		
		MemberInfo member = memberRepository.findByMemberId(payDetailItem.getMemberId()).get();
		
		Set<MemberFee> memberFees = this.memberProductFeeService.getMemberFees(payDetailItem.getMemberId());
		
		//amount due per month/freq
		double premiumAmt= memberFees.stream().filter(item->item.getFeeDetails().getFeeId() !=1).mapToDouble(MemberFee::getAmount).sum();
		
		Integer freqId = memberFees.stream().filter(item->item.getUpgradeType()==0 && item.getFeeDetails().getFeeId()!=1)
						.findFirst().get().getFrequencyDetails().getID();
		
		int paymentPeriods = (int)Math.floor(payDetailItem.getPeriodFee() / premiumAmt);
		if(paymentPeriods < 1)
			paymentPeriods = 1;
		
		
		return this.memberService.getMemberNextRenewDate(member.getRenewDate(), freqId, payDetailItem.getPeriodFee(), premiumAmt);
	}
	
	/**
	 * @param memberId
	 * @param transactionDate
	 * @return
	 */
	public Boolean isPaymentExistForTheDate(Integer memberId,Date transactionDate) {
		
		Set<PaymentDetail> details =  this.paymentDetailRepository.findAllByMemberIdAndTransactionDateAndVoidPayment(memberId, transactionDate,Boolean.FALSE);
		
		if(!details.isEmpty())
			return Boolean.TRUE;
		
		return Boolean.FALSE;
	}
	
	/**
	 * @param transactionId
	 * @return
	 */
	public Boolean isPaymentExistForForteTransaction(String transactionId) {
		
		Set<PaymentDetailForteTransaction> detail =  this.paymentForteTransactionRepository.findByTransactionId(transactionId);
		
		if(!detail.isEmpty())
			log.info("Found [{}] payment detail records for forte transaction id [{}]",detail.size(),transactionId);
		
		if(!detail.isEmpty())
			return Boolean.TRUE;
		
		return Boolean.FALSE;
	}
	
	public Boolean isPaymentExistWithForteSettlementDateRange(Integer memberId,BigDecimal transactionAmt, Date settlementDate,int startDays,int endDays) {
		
		Set<Date> settleDateSet = new HashSet<>();
		settleDateSet.add(settlementDate);
		
		Set<PaymentDetail> returnPayDetailSet =  findMemberPaymentByPostDate(memberId,settleDateSet,startDays, endDays);
		
		double payAmt = returnPayDetailSet.stream().filter(item->!item.getVoidPayment()).mapToDouble(PaymentDetail::getAmountPaid).sum();
		
		payAmt = (double)Math.round(payAmt * 100d) / 100d;
		
		if(!returnPayDetailSet.isEmpty()) {
			LocalDate temp = settlementDate.toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
			 log.info("Found [{}] payment detail records for forte settlement date [{}] Transaction Amt [{}] Payment Amt [{}]" ,returnPayDetailSet.size(),temp.format(DateTimeFormatter.ofPattern(DEFAULT_DATE_PATTERN))
					 ,transactionAmt.doubleValue(),payAmt);
		}			
		
		if(!returnPayDetailSet.isEmpty() && transactionAmt.doubleValue() == payAmt)
			return Boolean.TRUE;
		
		return Boolean.FALSE;
	}

	/**
	 * @param memberId
	 * @param productId
	 * @param position
	 * @return
	 */
	public PaymentDetail getMemberNthPaymentDetailForProduct(Integer memberId,Integer productId,Integer position) {
		
		List<PaymentDetail> memberPaymentDetails = this.paymentDetailRepository.findAllByMemberIdAndProductIdAndVoidPaymentOrderByTransactionDate(memberId, productId,Boolean.FALSE);
		
		if(position ==null || position < 0 || memberPaymentDetails.isEmpty())
			return null;
		
		return memberPaymentDetails.get(position);		
	}
	
	/**
	 * @param memberId
	 * @param productId
	 * @return
	 */
	public List<PaymentDetail> getAllPaymentDetailsForMemberByProduct(Integer memberId,Integer productId){
		
		return this.paymentDetailRepository.findAllByMemberIdAndProductIdAndVoidPaymentOrderByTransactionDate(memberId, productId,Boolean.FALSE);
	}



	/**
	 * @param ptsPaymentAppuser 
	 * @param payment
	 */
	public void savePaymentDetailForteTransactions(Set<PaymentDetail> paymentDetSet, Integer ptsPaymentAppuser) {
		
		Set<PaymentDetailForteTransaction> payDetFortTranSet = new HashSet<>();
		
		for(PaymentDetail paymentDetail : paymentDetSet) {
			
			if(null !=paymentDetail.getForteTransactionId() && !paymentDetail.getForteTransactionId().isEmpty())
			{
				Optional<PaymentDetailForteTransaction> optPayDtlTrn = paymentForteTransactionRepository.findByPaymentDetailIdAndTransactionIdAndMemberId(
						paymentDetail.getId(),paymentDetail.getForteTransactionId(),paymentDetail.getMemberId());
				
				PaymentDetailForteTransaction payDetailForteTransaction = optPayDtlTrn.orElse(new PaymentDetailForteTransaction());
				
				if(payDetailForteTransaction.getId() == null || (payDetailForteTransaction.getId()!=null && payDetailForteTransaction.getId().intValue()==0)) {
					payDetailForteTransaction.setPaymentDetailId(paymentDetail.getId());
					payDetailForteTransaction.setTransactionId(paymentDetail.getForteTransactionId());
					payDetailForteTransaction.setMemberId(paymentDetail.getMemberId());
					payDetailForteTransaction.setCreatedBy(ptsPaymentAppuser); 
					payDetailForteTransaction.setCreatedDate(new java.util.Date());
					payDetailForteTransaction.setModifiedBy(ptsPaymentAppuser);
					payDetailForteTransaction.setModifiedDate(new java.util.Date());	
				}
				else {
					payDetailForteTransaction.setModifiedBy(ptsPaymentAppuser);
					payDetailForteTransaction.setModifiedDate(new java.util.Date());
				}
				payDetFortTranSet.add(payDetailForteTransaction);				
			}
		}		
		if(!payDetFortTranSet.isEmpty())
			paymentForteTransactionRepository.saveAll(payDetFortTranSet);
	}
	
	/**
	 * @param memberId
	 * @param TransactionDate
	 * @param variance
	 * @return
	 * TODO check for scenario where multiple product exists for member and single forte transaction.
	 */
	public Set<PaymentDetail> findMemberPaymentDetailByTransactionDate(Integer memberId,Date TransactionDate,int variance) {
		
		LocalDate temp =   new java.util.Date(TransactionDate.getTime()).toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
		temp = temp.minusDays(variance);
		
		Date startTransactionDate = Date.from(temp.atStartOfDay(java.time.ZoneId.systemDefault()).toInstant());
		
		Set<PaymentDetail> paymentDetails = this.paymentDetailRepository.findAllByMemberIdAndTransactionDateBetweenAndVoidPaymentOrderByTransactionDate(memberId,startTransactionDate,TransactionDate,Boolean.FALSE);
		
		if(!paymentDetails.isEmpty()) {//chek for alternator payers
			
			Optional<Member> optMember = memberRepository.findById(memberId);
			
			if(!optMember.get().getSubMembers().isEmpty()) {
				
				optMember.get().getSubMembers().forEach(subMemItem -> {
				
					Set<PaymentDetail> subMemPayDetails = this.paymentDetailRepository.findAllByMemberIdAndTransactionDateBetweenAndVoidPaymentOrderByTransactionDate(subMemItem.getMemberId(),startTransactionDate, TransactionDate,Boolean.FALSE);
					
					paymentDetails.addAll(subMemPayDetails);
							
				});				
			}			
		}		
		return paymentDetails;
	}
	
	/**
	 * @param startDate
	 * @param noOfDays
	 * @return
	 */
	private Date addDaysToDate(Date startDate,int noOfDays) {
		LocalDate tempStartDate =   new java.util.Date(startDate.getTime()).toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
		tempStartDate = tempStartDate.plusDays(noOfDays);
		return  Date.from(tempStartDate.atStartOfDay(java.time.ZoneId.systemDefault()).toInstant());
	}
	
	public Set<PaymentDetail> findMemberPaymentByPostDate(Integer memberId,Set<Date> transactionDatesSet,int startVariance,int endVariance){
		Set<PaymentDetail> returnPayDetailSet  = new HashSet<>();
		
		Iterator<Date> itr = transactionDatesSet.iterator();
		
		while(itr.hasNext()) {			
			
			Set<PaymentDetail> paymentDetails  = new HashSet<>();
			
			Date transactionDate = itr.next();			
			Date startDate = addDaysToDate(transactionDate,startVariance);
			Date endTransactionDate = addDaysToDate(transactionDate,endVariance);			
			
			Set<Payment> payments = this.paymentRepository.findAllByPayDateBetweenAndPaymentDetailMemberIdAndPaymentDetailVoidPaymentOrderByPayDate(startDate,endTransactionDate,memberId,Boolean.FALSE);
			
			payments.forEach(item->
			paymentDetails.addAll(item.getPaymentDetail().stream().filter(record->record.getMemberId().compareTo(memberId)==0).collect(Collectors.toSet()))
			);
			
			if(paymentDetails.size()>1) {//multiple records found				
				//consider only min date tranasction payment.				
				PaymentDetail firstPayDetail = paymentDetails.stream().findFirst().get();				
				paymentDetails.removeIf(item-> item.getTransactionDate().after(firstPayDetail.getTransactionDate()) && item.getProductId().compareTo(firstPayDetail.getProductId()) ==0);
				
			}
			
			if(!paymentDetails.isEmpty()) {//chek for alternator payers
				
				Optional<Member> optMember = memberRepository.findById(memberId);
				
				if(!optMember.get().getSubMembers().isEmpty()) {
					
					Iterator<Member> subMemItr = optMember.get().getSubMembers().iterator();
					
					while(subMemItr.hasNext()) {
						Integer submMemberId = subMemItr.next().getMemberId();
						
						Set<PaymentDetail> subMemPayDetails = new HashSet<>();
						Set<Payment> subMemberPayments = this.paymentRepository.findAllByPayDateBetweenAndPaymentDetailMemberIdAndPaymentDetailVoidPaymentOrderByPayDate(startDate,endTransactionDate,submMemberId,Boolean.FALSE);
						
						subMemberPayments.forEach(item->
							subMemPayDetails.addAll(item.getPaymentDetail().stream().filter(record->record.getMemberId().compareTo(submMemberId)==0).collect(Collectors.toSet()))
						);
						
						if(!subMemPayDetails.isEmpty()) {//multiple records found
							
							//consider only min date tranasction payment.
							PaymentDetail firstPayDetail = subMemPayDetails.stream().findFirst().get();
							subMemPayDetails.removeIf(item-> item.getTransactionDate().after(firstPayDetail.getTransactionDate()) && item.getProductId().compareTo(firstPayDetail.getProductId()) ==0);
						}
						
						paymentDetails.addAll(subMemPayDetails);
					}				
					
							
				}			
			}
			returnPayDetailSet.addAll(paymentDetails);
		}		
		return returnPayDetailSet;
	}
	
	/**
	 * @param memberId
	 * @param transactionDatesSet
	 * @param variance
	 * @return
	 */
	public Set<PaymentDetail> findMemberPaymentDetailByTransactionDates(Integer memberId,Set<Date> transactionDatesSet,int startVariance,int endVariance) {
		
		Set<PaymentDetail> returnPayDetailSet  = new HashSet<>();
		
		Iterator<Date> itr = transactionDatesSet.iterator();
		
		while(itr.hasNext()) {			
			
			Set<PaymentDetail> paymentDetails  = new HashSet<>();
			
			Date transactionDate = itr.next();
			
			LocalDate tempStartdate =   new java.util.Date(transactionDate.getTime()).toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
			tempStartdate = tempStartdate.plusDays(startVariance); 
			
			Date startDate = Date.from(tempStartdate.atStartOfDay(java.time.ZoneId.systemDefault()).toInstant());
			
			LocalDate temp =   new java.util.Date(transactionDate.getTime()).toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
			temp = temp.plusDays(endVariance);
			
			Date endTransactionDate = Date.from(temp.atStartOfDay(java.time.ZoneId.systemDefault()).toInstant());
			
			paymentDetails = this.paymentDetailRepository.findAllByMemberIdAndTransactionDateBetweenAndVoidPaymentOrderByTransactionDate(memberId,startDate,endTransactionDate,Boolean.FALSE);
			
			if(paymentDetails.size()>1) {//multiple records found				
				//consider only min date tranasction payment.
				
				PaymentDetail firstPayDetail = paymentDetails.stream().findFirst().get();
				
				paymentDetails.removeIf(item-> item.getTransactionDate().after(firstPayDetail.getTransactionDate()) && item.getProductId().compareTo(firstPayDetail.getProductId()) ==0);
				
			}
			
			if(!paymentDetails.isEmpty()) {//chek for alternator payers
				
				Optional<Member> optMember = memberRepository.findById(memberId);
				
				if(!optMember.get().getSubMembers().isEmpty()) {
					
					Iterator<Member> subMemItr = optMember.get().getSubMembers().iterator();
					
					while(subMemItr.hasNext()) {
						Integer submMemberId = subMemItr.next().getMemberId();
						
						Set<PaymentDetail> subMemPayDetails = this.paymentDetailRepository.findAllByMemberIdAndTransactionDateBetweenAndVoidPaymentOrderByTransactionDate(submMemberId,startDate,endTransactionDate,Boolean.FALSE);
						
						if(!subMemPayDetails.isEmpty()) {//multiple records found
							
							//consider only min date tranasction payment.
							PaymentDetail firstPayDetail = subMemPayDetails.stream().findFirst().get();
							subMemPayDetails.removeIf(item-> item.getTransactionDate().after(firstPayDetail.getTransactionDate()) && item.getProductId().compareTo(firstPayDetail.getProductId()) ==0);
						}
						
						paymentDetails.addAll(subMemPayDetails);
					}				
					
							
				}			
			}
			returnPayDetailSet.addAll(paymentDetails);
		}		
		return returnPayDetailSet;
	}
	
	/**
	 * @param memberId
	 * @param forteTransactionId
	 * @param transactionDate
	 * @param variance
	 * @return
	 */
	public Set<PaymentDetail> findMemberPaymentDetailForChargeback(Integer memberId,String forteTransactionId,Date transactionDate,int variance) {
		
		Set<PaymentDetailForteTransaction> forteTransSet =  this.paymentForteTransactionRepository.findByTransactionId(forteTransactionId);
		Set<PaymentDetail> payDetailList = new HashSet<>();
		
		forteTransSet.forEach(item-> {
			Optional<PaymentDetail> payDetail = this.paymentDetailRepository.findById(item.getPaymentDetailId());
			if(payDetail.isPresent())
				payDetailList.add(payDetail.get());
		});
		
		if(!payDetailList.isEmpty())
			return payDetailList;
		else
		  return findMemberPaymentDetailByTransactionDate(memberId, transactionDate, variance);
	}
	
	/**
	 * @param memberId
	 * @param forteTransactionId
	 * @return
	 */
	public Set<PaymentDetail> findMemberPaymentDetailByTransactionId(Integer memberId,String forteTransactionId) {
		
		Set<PaymentDetailForteTransaction> forteTransSet =  this.paymentForteTransactionRepository.findByTransactionId(forteTransactionId);
		Set<PaymentDetail> payDetailList = new HashSet<>();
		
		forteTransSet.forEach(item-> {
			Optional<PaymentDetail> payDetail = this.paymentDetailRepository.findById(item.getPaymentDetailId());
			if(payDetail.isPresent())
				payDetailList.add(payDetail.get());
		});		
		return payDetailList;
	}
	
	/**
	 * @param paymentDetail
	 */
	public void savePaymentDetail(PaymentDetail paymentDetail) {
		this.paymentDetailRepository.save(paymentDetail);		
	}
	
	public void savePayment(Payment payment) {
		this.paymentRepository.save(payment);
	}
	
	/**
	 * @param memberId
	 * @return
	 */
	public List<PaymentDetail> getMemberPaymentDetails(Integer memberId){
		List<PaymentDetail> payDetails = new ArrayList<>();
		
		 List<PaymentDetail> memberPayDetails =  this.paymentDetailRepository.findAllByMemberId(memberId);
		 
		 payDetails.addAll(memberPayDetails);
		 
		 if(!memberPayDetails.isEmpty()) {			 
			 Optional<Member> optMember = this.memberRepository.findById(memberId);			 
			 if(optMember.isPresent()) {				 
				 for(Member member : optMember.get().getSubMembers()) {
					 List<PaymentDetail> altMemPayDetails =  this.paymentDetailRepository.findAllByMemberId(member.getMemberId());
					 payDetails.addAll(altMemPayDetails);
				 }
			 }
		 }
		 return payDetails;
	}
		
	public Integer getMemberProductCountFromPaymentDetails(Integer memberId)
	{
		List<PaymentDetail.PaymentDetailSummary> payList = paymentDetailRepository.findAllByMemberIdAndVoidPayment(memberId,Boolean.FALSE,PaymentDetail.PaymentDetailSummary.class);
		
		return payList.stream().map(PaymentDetail.PaymentDetailSummary::getProductId).collect(Collectors.toSet()).size();
		
	}
	/**
	 * @param memberId
	 * @param productId
	 * @return
	 */
	public boolean paymentExistsByMemberIdAndProductId(Integer memberId, Integer productId)
	{
		return paymentDetailRepository.existsByMemberIdAndProductIdAndIsChargeBackAndVoidPayment(memberId, productId, Boolean.FALSE, Boolean.FALSE);
	}
	/**
	 * @param memberId
	 * @param productId
	 * @return
	 */
	public boolean memberHasPayments(Integer memberId,Integer productId,Date paymentDate) {
		
		if(null == paymentDate)
			paymentDate = Constant.DEFULT_DATE_1900;
		
		Set<PaymentDetail.PaymentDetailSummary> payDetails = this.paymentDetailRepository.findAllByMemberIdAndProductIdAndPaymentPayDateLessThanAndVoidPaymentOrderByTransactionDate(memberId, productId, 
				paymentDate,Boolean.FALSE,PaymentDetail.PaymentDetailSummary.class);
		
		return !payDetails.isEmpty();
	}
	public LocalDate convertUtilToLocalDate(Date utilDate) {
		return new java.util.Date(utilDate.getTime()).toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
	}
	public boolean isDateWithInRange(Date dateToCompare,Date startDate,Date endDate)
	{
		LocalDate dateToCompareLD = convertUtilToLocalDate(dateToCompare);
		LocalDate startDateLD = convertUtilToLocalDate(startDate);
		LocalDate endDateLD = convertUtilToLocalDate(endDate);
		
		return (dateToCompareLD.equals(startDateLD) || dateToCompareLD.isAfter(startDateLD)) 
				&& (dateToCompareLD.equals(endDateLD) || dateToCompareLD.isBefore(endDateLD));
	}
	/*
	public void processAdvanceCommForMember(PaymentDetail paymentDetailItem) 
	{		
		try {
			boolean paymentExists = paymentExistsByMemberIdAndProductId(paymentDetailItem.getMemberId(), paymentDetailItem.getProductId());
			if (!paymentExists)
				return;

			if (commissionService.advanceCommExistsForMemberAndProduct(paymentDetailItem.getMemberId(),paymentDetailItem.getProductId()))
				return;

			commissionService.createAdvanceCommissionForMemberAndProduct(paymentDetailItem.getMemberId(),paymentDetailItem.getProductId());
			
		} catch (Exception e) {
			log.error("Error processing advance commision for international member , Member ID [{}], Error [{}]",paymentDetailItem.getMemberId(), e.getMessage());
		}
	}
	*/

	public boolean isValidPaymentExistsForMemberProduct(Integer memberId, Integer productFeeId) {
		return paymentRepository.isPaymentExistsForMemberProductFeeId(memberId, productFeeId);		
	}
	public Date getPaymentInvoiceDate(Integer paymentDetailId) {
		return paymentDetailRepository.getPaymentInvDate(paymentDetailId);
	}
}
