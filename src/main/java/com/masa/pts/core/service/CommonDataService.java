package com.masa.pts.core.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.masa.pts.core.domain.Company.CompanyInfo;
import com.masa.pts.core.domain.Division;
import com.masa.pts.core.domain.DivisionSalesChannel;
import com.masa.pts.core.domain.Fee;
import com.masa.pts.core.domain.FieldValueEntity;
import com.masa.pts.core.domain.Frequency;
import com.masa.pts.core.domain.Role;
import com.masa.pts.core.domain.SalesChannel;
import com.masa.pts.core.model.DropdownData;
import com.masa.pts.core.repository.CompanyRepository;
import com.masa.pts.core.repository.DivisionRepository;
import com.masa.pts.core.repository.FeeRepository;
import com.masa.pts.core.repository.FieldValueRepository;
import com.masa.pts.core.repository.FrequencyRepository;
import com.masa.pts.core.repository.PTSUserRoleAccessRepository;
import com.masa.pts.core.repository.RoleRepository;
import com.masa.pts.core.repository.SalesChannelRepository;

@Service
public class CommonDataService {

	DivisionRepository divisionRepository;
	SalesChannelRepository salesChannelRepository;
	RoleRepository roleRepository;
	PTSUserRoleAccessRepository ptsUserRoleAccessRepository;
	CompanyRepository companyRepository;
	FieldValueRepository fieldValueRepository;
	FeeRepository feeRepository;
	FrequencyRepository frequencyRepository;
	
	Map<Integer,String> divisions = new HashMap<>();
	Map<Integer,String> salesChannel = new HashMap<>();
	List<DropdownData> divisionsDropDowndata = new ArrayList<>();
	List<DropdownData> salesChannelDropDowndata = new ArrayList<>();
	
	
	@Autowired
	public CommonDataService(DivisionRepository divisionRepository, SalesChannelRepository salesChannelRepository,
			RoleRepository roleRepository, PTSUserRoleAccessRepository ptsUserRoleAccessRepository,
			CompanyRepository companyRepository, FieldValueRepository fieldValueRepository,FeeRepository feeRepository
			,FrequencyRepository frequencyRepository) {
		super();
		this.divisionRepository = divisionRepository;
		this.salesChannelRepository = salesChannelRepository;
		this.roleRepository = roleRepository;
		this.ptsUserRoleAccessRepository = ptsUserRoleAccessRepository;
		this.companyRepository = companyRepository;
		this.fieldValueRepository = fieldValueRepository;
		this.feeRepository = feeRepository;
		this.frequencyRepository = frequencyRepository;
	}

	public Iterable<Division> getAllDivisions()
	{
		return divisionRepository.findAll();
	}
	
	public Iterable<SalesChannel> getAllActiveSalesChannels()
	{
		return salesChannelRepository.findAllByIsObsolete(Boolean.FALSE);
	}
	
	public Iterable<SalesChannel> getAllSalesChannels()
	{
		return salesChannelRepository.findAll();
	}
	
	public Set<SalesChannel> getAllSalesChannelsByDivision(Integer divisionId)
	{
		return salesChannelRepository.findAllByDivisionIdAndIsObsolete(divisionId, Boolean.FALSE);
	}
	
	public Iterable<Role> getAllRoles()
	{
		return roleRepository.findAll();		
	}
	
	public void retrieveDivisions() {
		
		Iterable<Division> division = divisionRepository.findAll();
				
		division.forEach(item->{
				divisions.put(item.getId(), item.getName());
				DropdownData dataItem = new DropdownData(String.valueOf(item.getId()),item.getName());
				divisionsDropDowndata.add(dataItem);
		});
	}
	
	public Optional<SalesChannel> getSalesChannelById(Integer scId){
		return salesChannelRepository.findById(scId);
	}
	
	public void retrieveSalesChannel() {
		Iterable<SalesChannel> salesChannelItr = salesChannelRepository.findAll();		
		salesChannelItr.forEach(item->{
			salesChannel.put(item.getId(), item.getName());
			DropdownData dataItem = new DropdownData(String.valueOf(item.getId()),item.getName());
			salesChannelDropDowndata.add(dataItem);
		});
	}
	
	public DivisionSalesChannel getDivisionSalesChannel() {
		
		DivisionSalesChannel data = new DivisionSalesChannel();
		this.retrieveDivisions();
		this.retrieveSalesChannel();
		
		data.setDivision(this.divisions);
		data.setSalesChannel(this.salesChannel);
		data.setDivisionData(this.divisionsDropDowndata);
		data.setSalesChannelData(this.salesChannelDropDowndata);
		return data;
		
	}
	
	public List<CompanyInfo> getAllCompanyInfo(){		
		return companyRepository.findByActive(Boolean.TRUE);
	}
	
	public List<FieldValueEntity> getAllFieldValueByField(String field){
		return fieldValueRepository.findAllByField(field);
	}
	
	public List<Fee> getAllFeeTypes() {
		return StreamSupport.stream(feeRepository.findAll().spliterator(),false)
		.collect(Collectors.toList());
	}
	
	public List<Frequency> getAllFrequencyValues() {
		return StreamSupport.stream(frequencyRepository.findAll().spliterator(),false)
		.collect(Collectors.toList());
	}
}
