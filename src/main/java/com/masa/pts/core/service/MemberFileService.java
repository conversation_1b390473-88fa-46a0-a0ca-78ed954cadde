package com.masa.pts.core.service;

import com.masa.pts.core.constant.ProductFamilyType;
import com.masa.pts.core.domain.Agent;
import com.masa.pts.core.domain.GroupEntity;
import com.masa.pts.core.domain.MemberFile;
import com.masa.pts.core.domain.MemberFileReviewStatusEntity;
import com.masa.pts.core.domain.PTSUser;
import com.masa.pts.core.domain.Product;
import com.masa.pts.core.domain.ProductTypeMapping;
import com.masa.pts.core.domain.ReviewStatus;
import com.masa.pts.core.domain.UploadStatus;
import com.masa.pts.core.model.EdiUploadBulkUpdateFilterCriteria;
import com.masa.pts.core.model.MemberFileDTO;
import com.masa.pts.core.model.MemberFileIdStatusDTO;
import com.masa.pts.core.repository.MemberFileRepository;
import com.masa.pts.core.repository.MemberFileReviewStatusEntityRepository;
import com.masa.pts.core.repository.ProductTypeMappingRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ServerErrorException;

import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Service
public class MemberFileService {

	private ProductTypeMappingRepository productTypeMappingRepository;
	private ProductService productService;
	private AgentService agentService;
	private GroupService groupService;
	private MemberFileRepository memberFileRepository;
	private MemberFileReviewStatusEntityRepository memberFileReviewStatusEntityRepository;

	@Autowired
	public MemberFileService(ProductTypeMappingRepository productTypeMappingRepository, ProductService productService,
							 AgentService agentService, GroupService groupService, MemberFileRepository memberFileRepository,
							 MemberFileReviewStatusEntityRepository memberFileReviewStatusEntityRepository) {
		this.productTypeMappingRepository = productTypeMappingRepository;
		this.productService = productService;
		this.agentService = agentService;
		this.groupService = groupService;
		this.memberFileRepository = memberFileRepository;
		this.memberFileReviewStatusEntityRepository = memberFileReviewStatusEntityRepository;
	}

	/**
	 * @param productType
	 * @param paymentType
	 * @param soldDate
	 * @return
	 */
	public Optional<Product> getProductByProductTypeAndPaymentType(String productType, String paymentType,
			Date soldDate) {

		ProductTypeMapping prdTypeMapping = productTypeMappingRepository
				.findByProductTypeAndPaymentTypeAndSoldDate(productType, paymentType, soldDate);

		if (prdTypeMapping == null)
			return Optional.empty();

		if (prdTypeMapping.getProductId() == null)
			return Optional.empty();

		if (prdTypeMapping.getProductId().intValue() <= 0)
			return Optional.empty();

		return productService.getProductById(prdTypeMapping.getProductId());
	}

	public Optional<Product> getProductByProductTypeAndPaymentTypeAndGroupCode(String productType, String paymentType,
			Date soldDate, String groupCode) {
		// if a group has both PL S and PL F, then use F , during member update to PTS ,
		// if no dependent then change to S.
		List<ProductTypeMapping> prdTypeMapping = productTypeMappingRepository
				.findByProductTypeAndPaymentTypeAndSoldDateAndGroup(productType, paymentType, soldDate, groupCode);

		if (prdTypeMapping.isEmpty())
			return Optional.empty();

		if (prdTypeMapping.get(0).getProductId().intValue() <= 0)
			return Optional.empty();

		return productService.getProductById(prdTypeMapping.get(0).getProductId());
	}

	/**
	 * @param productType
	 * @param paymentType
	 * @param soldDate
	 * @param groupCode
	 * @param prdFamilyType
	 * @return
	 */
	public Optional<Product> getProductByProductTypeAndPaymentTypeAndGroupCode(String productType, String paymentType,
			Date soldDate, String groupCode, ProductFamilyType prdFamilyType) {
		List<ProductTypeMapping> prdTypeMapping = productTypeMappingRepository
				.findByProductTypeAndPaymentTypeAndSoldDateAndGroup(productType, paymentType, soldDate, groupCode);

		if (prdTypeMapping.isEmpty())
			return Optional.empty();

		Optional<Integer> optPrdId = prdTypeMapping.stream()
				.filter(item -> item.getPrdFamilyType().compareTo(prdFamilyType) == 0)
				.map(ProductTypeMapping::getProductId).findFirst();

		if (optPrdId.isPresent()) {
			return productService.getProductById(optPrdId.get());
		}
		return Optional.empty();
	}

	/**
	 * @param groupCode
	 * @return
	 */
	public Agent getGroupAgent(String groupCode) {
		GroupEntity group = groupService.getGroupByCode(groupCode);
		if (group == null)
			return null;

		return Optional.ofNullable(group.getAgent()).orElse(null);
	}

	public Agent getAgentByAgentNum(String agentNum) {
		return agentService.getAgentByAgentNum(agentNum);
	}

	public boolean isValidGroup(String groupCode) {
		return groupService.isExistsByGroupCode(groupCode);
	}

	public boolean isGroupValidAndActive(String groupCode) {
		return groupService.isExistsByGroupCodeAnActive(groupCode);
	}

	public void saveMemberFile(MemberFile memberFile,boolean addStatusRecord) {
		if(addStatusRecord) {
			addReviewStatusRecord(memberFile, null);
		}
		memberFileRepository.save(memberFile);
	}

	public boolean groupHasSingleProduct(String groupCode) {
		return productTypeMappingRepository.groupHasSingleProduct(groupCode);
	}	
	private void addReviewStatusRecord(MemberFile memberFile, ReviewStatus currentStatus) {
		Set<MemberFileReviewStatusEntity> reviewStatusSet = memberFile.getReviewStatusSet();
		MemberFileReviewStatusEntity recordStatus = new MemberFileReviewStatusEntity();
		recordStatus.setCurrentStatus(currentStatus);
		recordStatus.setNewStatus(memberFile.getReviewStatus());
		recordStatus.setMemberFile(memberFile);
		recordStatus.setStatusDate(new Date());
		recordStatus.setStatusUpdatedBy(memberFile.getStatusUpdatedBy());
		reviewStatusSet.add(recordStatus);
	}

	//addStatusRecord -> true: when called from skip listener,tasklet after processing to pts
	//false: when called from itemWriter
	public void saveAll(Set<MemberFile> memberFileSet,boolean addStatusRecord) {
		if(addStatusRecord) {
			memberFileSet.forEach(item -> addReviewStatusRecord(item, null));
		}
		memberFileRepository.saveAll(memberFileSet);
	}
	
	/*
	public Set<MemberFile> findAllByGroupCodeAndContractNumberAndRelationshipNotInAndJobExecutionIdAndProcessedAndStatusNotIn(
			String groupCode, String contractNumber, String relationship, Long jobExecutionId, boolean processed,UploadStatus status) {
		return memberFileRepository
				.findAllByGroupCodeAndContractNumberAndRelationshipNotInAndJobExecutionIdAndProcessedAndStatusNotIn(
						groupCode, contractNumber, relationship, jobExecutionId, processed,status);
	}
	
	public Set<MemberFile> findAllByGroupCodeAndContractNumberAndRelationshipNotInAndJobExecutionIdAndProcessed(
			String groupCode, String contractNumber, String relationship, Long jobExecutionId, boolean processed) {
		return memberFileRepository
				.findAllByGroupCodeAndContractNumberAndRelationshipNotInAndJobExecutionIdAndProcessed(
						groupCode, contractNumber, relationship, jobExecutionId, processed);
	}
	*/
	public Set<MemberFile> findAllByRelationshipAndSourceFileNameAndJobExecutionIdAndStatus(String relationship,String sourceFileName, Long jobExecutionId,
			UploadStatus processed) {
		return memberFileRepository.findAllByRelationshipAndSourceFileNameAndJobExecutionIdAndStatus(relationship,sourceFileName, jobExecutionId,
				processed);
	}

	public Set<MemberFile> getAllDependentRecordsForPrimary(String groupCode, String contractNumber,
			String relationShip, Long jobExecutionId, boolean processed) {
		return memberFileRepository.findAllByGroupCodeAndContractNumberAndRelationshipNotInAndJobExecutionIdAndProcessed(
				groupCode, contractNumber, relationShip, jobExecutionId, processed);
	}
	public Set<MemberFile> getAllDependentRecordsForPrimary(String groupCode, String contractNumber, String relationShip, Long jobExecutionId){		
		return memberFileRepository.findAllByGroupCodeAndContractNumberAndRelationshipNotInAndJobExecutionId(groupCode,contractNumber,relationShip,jobExecutionId);
	}
	
	public List<MemberFile> getAllRecordsForThisRun(String fileName, Long jobExecutionId) {
		return memberFileRepository.findAllBySourceFileNameAndJobExecutionId(fileName, jobExecutionId) ;
	}
	
	public Page<MemberFileDTO> searchMemberFileByParams(String groupCode, Integer masaMemberId, List<UploadStatus> status,
			String sourceFileName, String relationship, String contractNumber, List<String> errorCode, List<ReviewStatus> reviewStatus,
			Date createdDateStart, Date createdDateEnd, Boolean fulfillmentPending, PageRequest pageRequest) {
		if (errorCode == null || errorCode.isEmpty()) {
			return memberFileRepository.searchMemberFileByParams(groupCode, masaMemberId, status,
					Optional.ofNullable(sourceFileName).orElse(""), relationship, contractNumber, reviewStatus,
					createdDateStart != null ? PTSUtilityService.getStartOfDay(createdDateStart) : null,
					createdDateEnd != null ? PTSUtilityService.getEndOfDay(createdDateEnd) : null,
					fulfillmentPending, pageRequest);
		} else {
			return memberFileRepository.searchMemberFileByParamsWithErrorCodes(groupCode, masaMemberId, status,
					Optional.ofNullable(sourceFileName).orElse(""), relationship, contractNumber,errorCode, reviewStatus,
					createdDateStart != null ? PTSUtilityService.getStartOfDay(createdDateStart) : null,
					createdDateEnd != null ? PTSUtilityService.getEndOfDay(createdDateEnd) : null,
					fulfillmentPending, pageRequest);
		}

	}

	public Optional<MemberFile> findById(Integer id) {
		return memberFileRepository.findById(id);
	}

	//update record status , last modified by, status date log
	public void updateMemberFileRecordStatus(Set<Integer> recordIdSet, ReviewStatus reviewStatus, String employeeName) {
		Set<MemberFile> memberFileSet = new HashSet<>();
		for(Integer recordId: recordIdSet) {
			Optional<MemberFile> optMemFile = memberFileRepository.findById(recordId);
			if(optMemFile.isPresent()) {
				MemberFile memberFileRecord = optMemFile.get();
				ReviewStatus currentStatus = memberFileRecord.getReviewStatus();
				memberFileRecord.setReviewStatus(reviewStatus);
				memberFileRecord.setStatusUpdatedBy(employeeName);
				addReviewStatusRecord(memberFileRecord, currentStatus);
				memberFileSet.add(memberFileRecord);
			}
		}
		memberFileRepository.saveAll(memberFileSet);		
	}	

	@Transactional
	public int bulkUpdateMemberFileRecordStatusByFilter(EdiUploadBulkUpdateFilterCriteria input, PTSUser user) {
		List<MemberFileIdStatusDTO> fileIdStatus = memberFileRepository.searchMemberFileIdStatusDto(
				input.getGroupCode(),
				input.getMasaMemberId(),
				input.getStatus(),
				Optional.ofNullable(input.getSourceFileName()).orElse(""),
				input.getRelationship(),
				input.getContractNumber(),
				input.getErrorCode(),
				input.getReviewStatus(),
				PTSUtilityService.getStartOfDay(input.getCreatedDateStart()),
				PTSUtilityService.getEndOfDay(input.getCreatedDateEnd()));

		int updateCount = memberFileRepository.updateMemberFileWhere(input.getNewReviewStatus(),
				user.getUsername(),
				input.getGroupCode(),
				input.getMasaMemberId(),
				input.getStatus(),
				Optional.ofNullable(input.getSourceFileName()).orElse(""),
				input.getRelationship(),
				input.getContractNumber(),
				input.getErrorCode(),
				input.getReviewStatus(),
				PTSUtilityService.getStartOfDay(input.getCreatedDateStart()),
				PTSUtilityService.getEndOfDay(input.getCreatedDateEnd()));

		int logRecordCount = memberFileReviewStatusEntityRepository.batchInsertToLogTable(
				input.getNewReviewStatus(), user.getUsername(), fileIdStatus);
		
		if (updateCount != logRecordCount) {
			throw new ServerErrorException("Bulk update processed with errors. Please contact developers.");
		}

		return updateCount;
	}
}
