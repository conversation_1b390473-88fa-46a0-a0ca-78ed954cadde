package com.masa.pts.core.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.support.TransactionTemplate;

import com.masa.pts.core.constant.MemberContactEventStatus;
import com.masa.pts.core.constant.MemberContactEventType;
import com.masa.pts.core.domain.Constant;
import com.masa.pts.core.domain.DivisionSalesChannel;
import com.masa.pts.core.domain.Member.MemberSummaryWithGroup;
import com.masa.pts.core.domain.MemberEvent;
import com.masa.pts.core.domain.MemberFee;
import com.masa.pts.core.domain.MemberHistory;
import com.masa.pts.core.domain.PTSUser;
import com.masa.pts.core.repository.MemberEventRepository;
import com.masa.pts.core.repository.MemberHistoryRepository;
import com.masa.pts.core.repository.PTSUserRepository;

@Service
public class MemberEventService {

	private static final Logger log = LoggerFactory.getLogger(MemberEventService.class);
	
	@Autowired
	private MemberEventRepository memberEventRepository;

	@Autowired
	private MemberHistoryRepository memberHistoryRepository;
	
	@Autowired
	private MemberService memberService;
	
	@Autowired
	private MemberProductFeeService memberProductFeeService;
	
	@Autowired
	private PTSUserRepository userRepository;
	
	@Autowired
	private CommonDataService commonDataService;

	@Autowired
	PlatformTransactionManager transactionManager;
	
	@Value("${memberEvent.valid.divisions}")
	private List<String> VALID_DIVISIONS;
	
	@Value("${memberEvent.valid.salesChannels}")
	private List<String> VALID_SALES_CHANNELS;	
	
	public void addMembersToEventTableInTransaction(Set<Integer> memberIds,MemberContactEventType eventType) {
		TransactionTemplate transactionTemplate = createNewTransaction("MEMBEREVENT");
		transactionTemplate.execute(action->addMembersToEventTable(memberIds,eventType));
	}
	
	/**
	 * @param memberIds
	 * @param eventType
	 * @return
	 */
	public boolean addMembersToEventTable(Set<Integer> memberIds,MemberContactEventType eventType) {
		
		if(memberIds.isEmpty())
			return true;
		
		MemberEvent memberEvent ;
		Set<MemberEvent> memberEventSet = new HashSet<>();
				
		Integer ptsPaymentAppuser = getEventUser();
		
		DivisionSalesChannel divScData = commonDataService.getDivisionSalesChannel();
		
		Set<MemberEvent> memberIdsForHistory = new HashSet<>();
		
		for(Integer memberId: memberIds) 
		{
			Set<MemberEvent> memberEvents = memberEventRepository.findByMemberIdAndStatusIn(memberId,getEventStatusFilter());
			if(memberEvents.isEmpty()) //created/pending request not exists
			{			
				java.util.Optional<MemberSummaryWithGroup> optMemSmry = memberService.getMemberDetails(memberId, MemberSummaryWithGroup.class);
				if(optMemSmry.isPresent())
				{
					MemberSummaryWithGroup member = optMemSmry.get();
					if(isValidMember(member))
					{
						memberEvent = new MemberEvent();
						memberEvent.setMemberId(memberId);
						memberEvent.setEventType(eventType);
						memberEvent.setStatus(MemberContactEventStatus.CREATED);
						memberEvent.setCreatedBy(ptsPaymentAppuser);
						memberEvent.setModifiedBy(ptsPaymentAppuser);
						memberEvent.setCreatedDate(new java.util.Date());
						memberEvent.setModifiedDate(new java.util.Date());
						memberEventSet.add(memberEvent);
						log.info("Member ID [{}] added to Member Event table for event type [{}]",memberId,eventType);
						memberIdsForHistory.add(memberEvent);
					}
				}
			}
			else
			{
				log.info("Member ID [{}] not added as it is available in retention queue",memberId);
			}
		}
		try {
			memberEventRepository.saveAll(memberEventSet);
		}
		catch(RuntimeException re) {
			log.error("Error while adding member to Member Event table, Cause [{}] Message [{}]",re.getCause(),re.getMessage());
		}
		addMemberDetailToHistory(memberIdsForHistory,eventType,ptsPaymentAppuser, divScData);
		log.info("Total Members added to Member Event Table [{}] for event Type [{}]",memberEventSet.size(),eventType);
		log.info("Total Members added to Member History Table [{}] for event Type [{}]",memberIdsForHistory.size(),eventType);
		return true;
	}

	/**
	 * @param member
	 * @return
	 */
	private boolean isValidMember(MemberSummaryWithGroup member) {
		// List<String> validDivisions = Arrays.asList("B2C","MTS","B2B","Retention");
		// List<String> validSalesChannels = Arrays.asList("Precoa");
		
		if(null == member.getGroup())
			return Boolean.FALSE;
		
		boolean validDiv = VALID_DIVISIONS.contains(member.getGroup().getBusinessLineEntity().getSalesChannel().getDivision().getName());
		boolean validSc = VALID_SALES_CHANNELS.contains(member.getGroup().getBusinessLineEntity().getSalesChannel().getName());
		
		return (validDiv || validSc);
	}

	/**
	 * @param memberIds
	 * @param eventType
	 * @param ptsPaymentAppuser
	 * @param divScData
	 */
	private void addMemberDetailToHistory(Set<MemberEvent> memberEventsSet, MemberContactEventType eventType,Integer ptsPaymentAppuser,DivisionSalesChannel divScData) {
		
		if(memberEventsSet.isEmpty())
			return;
		
		Set<MemberHistory> memberHistorySet = new HashSet<>();
		
		for(MemberEvent memberEvent: memberEventsSet) 
		{
			java.util.Optional<MemberSummaryWithGroup> optMemSmry = memberService.getMemberDetails(memberEvent.getMemberId(), MemberSummaryWithGroup.class);
			if(optMemSmry.isPresent())
			{
					MemberSummaryWithGroup member = optMemSmry.get();
					Set<MemberFee> memberFees = memberProductFeeService.getMemberFees(memberEvent.getMemberId());
					
					Integer productId = memberFees.stream().filter(item->!Constant.SETUP_FEE_IDS.contains(item.getFeeDetails().getFeeId()) 
							&& (item.getProduct().getUpgradeProduct().intValue()==0)).findFirst().map(feeItem->feeItem.getProduct().getProductId()).orElse(0); 
					
					MemberHistory memberHistory = new MemberHistory();
					memberHistory.setMemberId(memberEvent.getMemberId());
					memberHistory.setEventType(eventType.getType());
					memberHistory.setActive(member.getActive());
					memberHistory.setGroupCode(member.getGroupGroupCode());
					memberHistory.setDivision(member.getGroup().getBusinessLineEntity().getSalesChannel().getDivision().getId()); 
					memberHistory.setSalesChannel(member.getGroup().getBusinessLineEntity().getSalesChannel().getId());
					memberHistory.setCreatedBy(ptsPaymentAppuser);
					memberHistory.setCreatedDate(new java.util.Date());
					memberHistory.setProductIdPrimary(productId);
					memberHistory.setEventId(memberEvent.getId());
					memberHistorySet.add(memberHistory);
					log.info("Member ID [{}] added to Member History table for event type [{}]",memberEvent.getMemberId(),eventType);
			}			
		}
		try {
			memberHistoryRepository.saveAll(memberHistorySet);
		}
		catch(RuntimeException re) {
			log.error("Error while adding member to Member History table, Cause [{}] Message [{}]",re.getCause(),re.getMessage());
		}		
	}

	private List<Integer> getEventStatusFilter(){
		List<Integer> statuses = new ArrayList<>();
		statuses.add(MemberContactEventStatus.CREATED.getStatus());
		statuses.add(MemberContactEventStatus.PENDING.getStatus());
		return statuses;
	}
	
	private Integer getEventUser() {
		Integer ptsPaymentAppuser = Constant.DEFAULT_USER_ID;
		Optional<PTSUser> ptsUser = this.userRepository.findByUsername(Constant.DEFAULT_PAYMENT_USERNAME);
		if(ptsUser.isPresent())		
			ptsPaymentAppuser = ptsUser.get().getEmployeeId();
		return ptsPaymentAppuser;
	}
	
	private TransactionTemplate createNewTransaction(String name) {
		TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
		transactionTemplate.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
		transactionTemplate.setName(name);
		return transactionTemplate;
	}
}
