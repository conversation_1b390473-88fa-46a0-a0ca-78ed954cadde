package com.masa.pts.core.service;

import com.masa.pts.core.domain.Constant;
import com.masa.pts.core.domain.PTSUser;
import com.masa.pts.core.domain.PTSUserGroup;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.masa.pts.core.domain.Constant.DEFAULT_DATE_PATTERN;

@Service
public class PTSUtilityService {

    private static final Logger log = LoggerFactory.getLogger(PTSUtilityService.class);

    private static final SimpleDateFormat dateFormat = new SimpleDateFormat(DEFAULT_DATE_PATTERN);
    private static final String TIMESTAMP_PATTERN = "yyyyMMddHHmmss";

    public static String formatUtilDate(Date input) {
        return dateFormat.format(input);
    }

    public static String formatUtilDate(Date input, String datePattern) {
        String dateStr = "";
        try {
            dateFormat.applyPattern(datePattern);
            dateStr = dateFormat.format(input);
        } catch (Exception e) {
            log.error("Date is printed with error: " + e.getMessage(), e);
        }
        return dateStr;
    }

    public static String formatUtilDate(LocalDate input) {
        String dateStr = "";
        try {
            dateStr = input.format(DateTimeFormatter.ofPattern(DEFAULT_DATE_PATTERN));
        } catch (Exception e) {
        }
        return dateStr;
    }

    public static String formatTimestamp(LocalDateTime input) {
        return input.format(DateTimeFormatter.ofPattern(TIMESTAMP_PATTERN));
    }

    public static LocalDate convertUtilToLocalDate(Date utilDate) {
        if (utilDate == null) {
            return null;
        }
        return new java.util.Date(utilDate.getTime()).toInstant().atZone(java.time.ZoneId.systemDefault())
                .toLocalDate();
    }

    public static Date convertLocalToUtilDate(LocalDate localDate) {
        if (localDate == null) {
            return null;
        }
        return Date.from(localDate.atStartOfDay(java.time.ZoneId.systemDefault()).toInstant());
    }

    public static LocalDateTime convertDateToLocalDateTime(Date date) {
        return Optional.ofNullable(date)
                .map(s -> date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime())
                .orElse(null);
    }

    public static Date getStartOfDay(Date day) {
        if (day == null) {
            return null;
        }
        LocalDateTime localDateTime =
                LocalDateTime.ofInstant(day.toInstant(), java.time.ZoneId.systemDefault()).with(LocalTime.MIN);
        return Date.from(localDateTime.atZone(java.time.ZoneId.systemDefault()).toInstant());
    }

    public static Date getEndOfDay(Date day) {
        if (day == null) {
            return null;
        }
        LocalDateTime localDateTime =
                LocalDateTime.ofInstant(day.toInstant(), java.time.ZoneId.systemDefault()).with(LocalTime.MAX);
        return Date.from(localDateTime.atZone(java.time.ZoneId.systemDefault()).toInstant());
    }

    /**
     * @param startDate
     * @param endDate
     * @return
     */
    public static long calculateDaysBetween(Date startDate, Date endDate) {
        LocalDate startLocalDate = convertUtilToLocalDate(startDate);
        LocalDate endLocalDate = convertUtilToLocalDate(endDate);
        return ChronoUnit.DAYS.between(startLocalDate, endLocalDate);
    }

    public static long calculateYearsBetween(Date startDate, Date endDate) {
        LocalDate startLocalDate = convertUtilToLocalDate(startDate);
        LocalDate endLocalDate = convertUtilToLocalDate(endDate);
        return ChronoUnit.YEARS.between(startLocalDate, endLocalDate);
    }

    public static List<String> getUserGroupCodes(PTSUser user) {
        return user.getUserGroups().stream()
                .map(PTSUserGroup::getGroupCode)
                .collect(Collectors.toList());
    }

    public static List<Integer> getUserGroupIds(PTSUser user) {
        return user.getUserGroups().stream()
                .map(group -> group.getUserGroupId().getGroup().getGroupId())
                .collect(Collectors.toList());
    }

    public static List<Integer> getUserSalesChannelIds(PTSUser user) {
        return user.getUserSalesChannels().stream()
                .map(channel -> channel.getUserSalesChannelId().getSalesChannel().getId())
                .collect(Collectors.toList());
    }

    public static List<Integer> getUserSalesChannelsMergedWithInput(PTSUser user, List<Integer> inputSalesChannels) {
        return CollectionUtils.isEmpty(inputSalesChannels) ? getUserSalesChannelIds(user) :
                new ArrayList<>(CollectionUtils.intersection(inputSalesChannels, getUserSalesChannelIds(user)));
    }

    public static boolean isDateWithInRange(Date dateToCompare, Date startDate, Date endDate) {
        LocalDate dateToCompareLD = convertUtilToLocalDate(dateToCompare);
        LocalDate startDateLD = convertUtilToLocalDate(startDate);
        LocalDate endDateLD = convertUtilToLocalDate(endDate);

        return (dateToCompareLD.equals(startDateLD) || dateToCompareLD.isAfter(startDateLD))
                && (dateToCompareLD.equals(endDateLD) || dateToCompareLD.isBefore(endDateLD));
    }

    public static boolean isDateWithInRange(Date dateToCompare, LocalDate startDateLD, LocalDate endDateLD) {
        LocalDate dateToCompareLD = convertUtilToLocalDate(dateToCompare);
        return (dateToCompareLD.equals(startDateLD) || dateToCompareLD.isAfter(startDateLD))
                && (dateToCompareLD.equals(endDateLD) || dateToCompareLD.isBefore(endDateLD));
    }

    public static boolean isDivisionWithNewCommissionProcess(Integer divId) {
        return (divId == Constant.B2B_DIVISION_ID || divId == Constant.INTERNATIONAL_DIVISION_ID
                || divId == Constant.RETENTION_DIVISION_ID);
    }

    public static boolean isInternationalDivision(Integer divId) {
        return (divId.intValue() == 1);
    }

    public static String concatenateNotEmptyString(String delimiter, String ...strings) {
        return Arrays.stream(strings)
                .filter(s -> s != null && !s.isEmpty())
                .collect(Collectors.joining(delimiter));
    }
}
