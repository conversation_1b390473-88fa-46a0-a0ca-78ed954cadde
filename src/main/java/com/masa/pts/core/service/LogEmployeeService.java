package com.masa.pts.core.service;

import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.masa.pts.core.domain.Constant;
import com.masa.pts.core.domain.LogEmployee;
import com.masa.pts.core.domain.PTSUser;
import com.masa.pts.core.repository.LogEmployeeRepository;

@Service
public class LogEmployeeService {

	@Autowired
	private LogEmployeeRepository logEmployeeRepository;
	
	@Autowired
	private PTSUserService ptsUserService;
	
	public void loginEmployee(String userName, String ip, String agent) 
	{
		Optional<PTSUser> optPtsUser = ptsUserService.findByUserName(userName);
		if(optPtsUser.isPresent())
		{
			LogEmployee logEmp = new LogEmployee();
			logEmp.setEmployeeId(optPtsUser.get().getEmployeeId());
			logEmp.setEnterTime(new java.util.Date());
			logEmp.setLeftTime(Constant.DEFULT_DATE_1901);
			logEmp.setIp(ip);
			logEmp.setAgent(agent);
			logEmployeeRepository.save(logEmp);
		}
	}
	
	public void logoutEmloyee(String userName) 
	{
		Optional<PTSUser> optPtsUser = ptsUserService.findByUserName(userName);
		if(optPtsUser.isPresent())
		{
			LogEmployee logEmp = logEmployeeRepository.findLastEmployeeEntryByEmployeeId(optPtsUser.get().getEmployeeId(),Constant.DEFULT_DATE_1901);
			if(logEmp !=null) {
				logEmp.setLeftTime(new java.util.Date());
				logEmployeeRepository.save(logEmp);
			}
		}
	}
}
