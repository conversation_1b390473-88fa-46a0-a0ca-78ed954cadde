package com.masa.pts.core.service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.core.namedparam.SqlParameterSource;
import org.springframework.stereotype.Service;

import com.masa.pts.core.domain.ConfigParamName;
import com.masa.pts.core.domain.Constant;
import com.masa.pts.core.model.MSADocsResponse;
import com.masa.pts.core.model.MSAObjectStateDetailsResponse;

@Service
public class MSAService {

    @PersistenceContext
    private EntityManager entityManager;

    private JdbcTemplate jdbcTemplate;
    
    private ConfigParamService configParamService;
    
    @Autowired
    public MSAService(EntityManager entityManager, JdbcTemplate jdbcTemplate,ConfigParamService configParamService) {
        this.entityManager = entityManager;
        this.jdbcTemplate = jdbcTemplate;
        this.configParamService = configParamService;
    }
    
    /*
    public List<Integer> getMasaInsuranceCompanyIds() {
    	List<Integer> data = ConfigParamService.convertCommaSeparatedIntegerToList(
    			configParamService.getParamValue(ConfigParamName.MASA_INSURANCE_COMPANY_IDS.name()));
    	if(data.isEmpty()) {
    		data.add(-1);
    	}
    	return data;
    }
    */
    
    public List<String> getMsaGroupAddressDivisions() {
    	return ConfigParamService.convertCommaSeparatedStringToList(
    			configParamService.getParamValue(ConfigParamName.INS_MSA_BY_GROUP_ADDRESS_DIVISIONS.name()));
    }
    
    public List<String> getMsaSalesChannelNameDivisions() {
    	return ConfigParamService.convertCommaSeparatedStringToList(
    			configParamService.getParamValue(ConfigParamName.INS_MSA_BY_SC_NAME_DIVISIONS.name()));
    }    
    
    public List<MSADocsResponse> getMSADocs(int id, int productId, int stateId, String type, int divisionId) {
        List<MSADocsResponse> msaDocsResponses = null;

        switch (type) {
            case "Group" :
                msaDocsResponses = findMSAUsingObjectStateDetails(
                					convertObjectToJson(findMSAByGroup(id, productId, divisionId)),
                					id,
                					type
                					);
                break;
            case "Member" :
                msaDocsResponses = findMSAUsingObjectStateDetails(
                					convertObjectToJson(findMSAByMember(id)),
                					id,
                					type
                					);
                break;
            case "MemberOOC" :
                msaDocsResponses = findMSAUsingObjectStateDetails(
                					convertObjectToJson(findOOCByMember(id)),
                					id,
                					type
                					);
                break;                
            case "SalesChannel" :
                msaDocsResponses = convertObjectToJson(findMSABySalesChannel(id, productId, divisionId));
                break;
            case "Product" :
                msaDocsResponses = convertObjectToJson(findMSAByProduct(id, divisionId));
                if(stateId > 0) {
                	String stateIdStr = stateId + "";
                	List<MSADocsResponse> filteredData = msaDocsResponses.stream()
                			.filter(x -> stateIdStr.equals(x.getStateId()))
                			.collect(Collectors.toList());
                	if(filteredData.isEmpty()) {
                		filteredData = msaDocsResponses.stream()
                    			.filter(x -> x.getStateId() == null)
                    			.collect(Collectors.toList());
                	}
                	msaDocsResponses = filteredData;
                }
                break;   
            case "GroupInsuranceProduct" :
                msaDocsResponses = convertObjectToJson(findMSAByGroupInsuranceProduct(id, divisionId));
                if(stateId > 0) {
                	String stateIdStr = stateId + "";
                	List<MSADocsResponse> filteredData = msaDocsResponses.stream()
                			.filter(x -> stateIdStr.equals(x.getStateId()))
                			.collect(Collectors.toList());
                	if(filteredData.isEmpty()) {
                		filteredData = msaDocsResponses.stream()
                    			.filter(x -> x.getStateId() == null)
                    			.collect(Collectors.toList());
                	}
                	msaDocsResponses = filteredData;
                }
                break;    
            case "MemberInsuranceProduct" :
                msaDocsResponses = convertObjectToJson(findMSAByMemberInsuranceProduct(id, divisionId));
                if(stateId > 0) {
                	String stateIdStr = stateId + "";
                	List<MSADocsResponse> filteredData = msaDocsResponses.stream()
                			.filter(x -> stateIdStr.equals(x.getStateId()))
                			.collect(Collectors.toList());
                	if(filteredData.isEmpty()) {
                		filteredData = msaDocsResponses.stream()
                    			.filter(x -> x.getStateId() == null)
                    			.collect(Collectors.toList());
                	}
                	msaDocsResponses = filteredData;
                }
                break;                
            default :
                break;
        }

        return msaDocsResponses;
    }
    
    private List<MSADocsResponse> findMSAUsingObjectStateDetails(List<MSADocsResponse> dataList, int id, String type){
    	if(dataList == null || dataList.isEmpty()) {
    	 	return dataList;
    	}
    	MSAObjectStateDetailsResponse details = null;
        switch (type) {
	        case "Group" :
	        	details = findMSAGroupStateDetails(id);
	            break;
	        case "Member" :
	        	details = findMSAMemberStateDetails(id);
	            break;
	        case "MemberOOC" :
	        	details = findMSAMemberStateDetails(id);
	            break;	            
	        case "SalesChannel" :
	            break;
	        default :
	            break;
        }
    	if(details == null) {
    		return dataList;
    	}
    	String stateName = "";
    	if(getMsaGroupAddressDivisions().contains(details.getDivisionName())) {
    		stateName= details.getGroupStateName();
    	} else if(getMsaSalesChannelNameDivisions().contains(details.getDivisionName())) {
    		stateName= details.getSalesChannelName();
    	} else {
    		stateName= details.getMemberStateName();
    	}
    	if(stateName == null) {
    		stateName ="";
    	}
    	final String objectStateName = stateName.trim().toLowerCase();
    	List<MSADocsResponse> filteredData = dataList.stream().filter(x -> {
    		String msaStateName = x.getStateName();
        	if(msaStateName == null) {
        		msaStateName ="";
        	}
        	msaStateName = msaStateName.trim().toLowerCase();
        	return !"".equals(msaStateName) && objectStateName.contains(msaStateName);
    	}).collect(Collectors.toList());
    	if(filteredData == null || filteredData.isEmpty()) {
    		filteredData = dataList.stream().filter(x -> x.getStateName()==null).collect(Collectors.toList());
    	}
    	return filteredData;
    }
    private static final int FILE_LOCATION = 1;
    private static final int DISPLAY_NAME = 2;
    private static final int STATE_ID = 3;
    private static final int STATE_NAME = 4;
    private static final int PRODUCT_NAME = 7; 
    private static final int PRODUCT_INSURANCE_TYPE = 10; 
    private List<MSADocsResponse> convertObjectToJson(List<Object[]> objects) {
        List<MSADocsResponse> msaEntityList = new ArrayList<MSADocsResponse>();
        objects.forEach(object -> {
        	String productInsuranceType = null;
        	try {
        		productInsuranceType = objectToString(object[PRODUCT_INSURANCE_TYPE]);
        	} catch (ArrayIndexOutOfBoundsException e) { }
            msaEntityList.add(new MSADocsResponse(String.valueOf(object[0]),// objects.indexOf(object)
            		objectToString(object[DISPLAY_NAME]), objectToString(object[FILE_LOCATION]), "Other",
                    objectToString(object[PRODUCT_NAME]), objectToString(object[STATE_ID]),
                    objectToString(object[STATE_NAME]),null,null,productInsuranceType));
        });
        return msaEntityList;
    }
    
    private String objectToString(Object o) {
    	if(o == null) {
    		return null;
    	} else {
    		return o.toString();
    	}
    }
    
    public MSAObjectStateDetailsResponse  findMSAMemberStateDetails(int memberId) {
    	try {
    		/*
    		SqlParameterSource parameters = new MapSqlParameterSource("ids", getMasaInsuranceCompanyIds())
    				.addValue("memberId", memberId);
    		*/
    		SqlParameterSource parameters = new MapSqlParameterSource("memberId", memberId);
    		String query = "SELECT \r\n"
    				+ "	d.id [divisionId], d.name [divisionName],\r\n"
    				+ "	s.id [salesChannelId],s.name  [salesChannelName],\r\n"
    				+ "	g.group_name [groupName],g.group_id [groupId],g.group_code [groupCode],\r\n"
    				+ "	gas.state_id [groupStateId],gas.state [groupStateName],gas.symbol [groupStateSymbol],\r\n"
    				+ "	mas.state_id [memberStateId],mas.state [memberStateName],mas.symbol [memberStateSymbol]\r\n"
    				+ "FROM dbo.tdat_Member m\r\n"
    				+ "INNER JOIN dbo.tdat_Group g ON g.group_id=m.group_id AND m.member_id=:memberId\r\n"
    				+ "INNER JOIN dbo.tlnk_Group_Line gl ON g.group_id=gl.idGroup\r\n" // AND g.company_id IN (:ids)
    				+ "INNER JOIN dbo.tdat_Business_Line b ON b.id=gl.idLine\r\n"
    				+ "INNER JOIN dbo.tdat_Sales_Channel s ON s.id=b.channel\r\n"
    				+ "INNER JOIN dbo.tdat_Division d ON d.id=s.division\r\n"
    				+ "LEFT JOIN dbo.tdat_Address ma ON ma.address_id=m.address\r\n"
    				+ "LEFT JOIN dbo.tlst_State2 mas ON mas.state_id=ma.state\r\n"
    				+ "LEFT JOIN dbo.tdat_Address ga ON ga.address_id=g.mailing_address\r\n"
    				+ "LEFT JOIN dbo.tlst_State2 gas ON gas.state_id=ga.state";
    		return (MSAObjectStateDetailsResponse) new NamedParameterJdbcTemplate(jdbcTemplate.getDataSource())
    				.queryForObject(query, parameters, new BeanPropertyRowMapper(MSAObjectStateDetailsResponse.class));
    	} catch (EmptyResultDataAccessException e) {
			return null;
		}
    }
    
    public MSAObjectStateDetailsResponse  findMSAGroupStateDetails(int groupId) {
    	try {
    		/*
    		SqlParameterSource parameters = new MapSqlParameterSource("ids", getMasaInsuranceCompanyIds())
    				.addValue("groupId", groupId);
    		*/
    		SqlParameterSource parameters = new MapSqlParameterSource("groupId", groupId);
    		String query = "SELECT \r\n"
    				+ "	d.id [divisionId], d.name [divisionName],\r\n"
    				+ "	s.id [salesChannelId],s.name  [salesChannelName],\r\n"
    				+ "	g.group_name [groupName],g.group_id [groupId],g.group_code [groupCode],\r\n"
    				+ "	gas.state_id [groupStateId],gas.state [groupStateName],gas.symbol [groupStateSymbol]\r\n"
    				+ "FROM dbo.tdat_Group g\r\n"
    				+ "INNER JOIN dbo.tlnk_Group_Line gl ON g.group_id=gl.idGroup AND g.group_id = :groupId\r\n" // AND g.company_id IN (:ids)
    				+ "INNER JOIN dbo.tdat_Business_Line b ON b.id=gl.idLine\r\n"
    				+ "INNER JOIN dbo.tdat_Sales_Channel s ON s.id=b.channel\r\n"
    				+ "INNER JOIN dbo.tdat_Division d ON d.id=s.division\r\n"
    				+ "LEFT JOIN dbo.tdat_Address ga ON ga.address_id=g.mailing_address\r\n"
    				+ "LEFT JOIN dbo.tlst_State2 gas ON gas.state_id=ga.state";
    		return (MSAObjectStateDetailsResponse) new NamedParameterJdbcTemplate(jdbcTemplate.getDataSource())
    				.queryForObject(query, parameters, new BeanPropertyRowMapper(MSAObjectStateDetailsResponse.class));
    	} catch (EmptyResultDataAccessException e) {
			return null;
		}
    }
    
    public List<Object[]>  findMSABySalesChannel(int scId, int productId, int divisionId) {
        Query query = entityManager.createNativeQuery(
            "SELECT msa.id,file_loc,display_name,st.state_id,st.state [state_name],st.symbol [state_symbol],ttm.sales_channel_id, p.name "+
                    " FROM dbo.tlnk_Fulfillment_Type_To_Material ttm "+
                    (divisionId<=0? "" : (" JOIN tdat_Sales_Channel sc ON sc.id=ttm.sales_channel_id AND sc.division="+divisionId) )+
                    " JOIN dbo.tdat_Product p ON p.product_id = ttm.product_id AND ISNULL(ttm.is_obsolete,0) = 0 "+
            		" JOIN dbo.tlnk_MSA msa on msa.is_deleted = 0 AND "+
                    " msa.material_id = ttm.material_id AND ttm.fulfillment_request_type_id = 3 AND  "+
                    " ttm.product_id = :productId AND ttm.sales_channel_id = :scId  "+
                    " AND CAST(msa.start_date as date) <= cast(getDate() as date) "+
                    " AND cast(msa.end_date as date) >= cast(getDate() as date) "+
                    " LEFT JOIN tlst_State2 st ON st.state_id = msa.state ");
        query.setParameter("scId", scId);
        query.setParameter("productId", productId);
        return query.getResultList();

    }
       
    public List<Object[]>  findMSAByMemberInsuranceProduct(int productId, int divisionId) {
        Query query = entityManager.createNativeQuery(
            "SELECT DISTINCT msa.id,msa.file_loc [file_loc],msa.display_name [display_name],"
            + " st.state_id,st.state [state_name],st.symbol [state_symbol],ttm.product_id, p.name "+
                    " FROM dbo.tlnk_Fulfillment_Type_To_Material ttm "+
                    (divisionId<=0? "" : (" JOIN tdat_Sales_Channel sc ON sc.id=ttm.sales_channel_id AND sc.division="+divisionId) )+
                    " JOIN dbo.tdat_Product p ON p.product_id = ttm.product_id AND ISNULL(ttm.is_obsolete,0) = 0 "+
            		" JOIN dbo.tlnk_MSA msa on msa.is_deleted = 0 AND "+
                    " msa.material_id = ttm.material_id AND ttm.fulfillment_request_type_id = 3 AND  "+
                    " ttm.product_id = :productId AND msa.group_file_loc IS NULL "+
                    " AND CAST(msa.start_date as date) <= cast(getDate() as date) "+
                    " AND cast(msa.end_date as date) >= cast(getDate() as date) "+
                    " LEFT JOIN tlst_State2 st ON st.state_id = msa.state ");
        query.setParameter("productId", productId);
        return query.getResultList();

    }
    
    public List<Object[]>  findMSAByGroupInsuranceProduct(int productId, int divisionId) {
        Query query = entityManager.createNativeQuery(
            "SELECT DISTINCT msa.id,msa.group_file_loc [file_loc],msa.group_display_name [display_name],"
            + " st.state_id,st.state [state_name],st.symbol [state_symbol],ttm.product_id, p.name "+
                    " FROM dbo.tlnk_Fulfillment_Type_To_Material ttm "+
                    (divisionId<=0? "" : (" JOIN tdat_Sales_Channel sc ON sc.id=ttm.sales_channel_id AND sc.division="+divisionId) )+
                    " JOIN dbo.tdat_Product p ON p.product_id = ttm.product_id AND ISNULL(ttm.is_obsolete,0) = 0 "+
            		" JOIN dbo.tlnk_MSA msa on msa.is_deleted = 0 AND "+
                    " msa.material_id = ttm.material_id AND ttm.fulfillment_request_type_id = 3 AND  "+
                    " ttm.product_id = :productId AND msa.group_file_loc IS NOT NULL "+
                    " AND CAST(msa.start_date as date) <= cast(getDate() as date) "+
                    " AND cast(msa.end_date as date) >= cast(getDate() as date) "+
                    " LEFT JOIN tlst_State2 st ON st.state_id = msa.state ");
        query.setParameter("productId", productId);
        return query.getResultList();

    }
    
    public List<Object[]>  findMSAByProduct(int productId, int divisionId) {
        Query query = entityManager.createNativeQuery(
            "SELECT DISTINCT msa.id,file_loc,display_name,st.state_id,st.state [state_name],st.symbol [state_symbol],ttm.product_id, p.name "+
                    " FROM dbo.tlnk_Fulfillment_Type_To_Material ttm "+
            		(divisionId<=0? "" : (" JOIN tdat_Sales_Channel sc ON sc.id=ttm.sales_channel_id AND sc.division="+divisionId) )+
                    " JOIN dbo.tdat_Product p ON p.product_id = ttm.product_id AND ISNULL(ttm.is_obsolete,0) = 0 "+
            		" JOIN dbo.tlnk_MSA msa on msa.is_deleted = 0 AND "+
                    " msa.material_id = ttm.material_id AND ttm.fulfillment_request_type_id = 3 AND  "+
                    " ttm.product_id = :productId "+
                    " AND CAST(msa.start_date as date) <= cast(getDate() as date) "+
                    " AND cast(msa.end_date as date) >= cast(getDate() as date) "+
                    " LEFT JOIN tlst_State2 st ON st.state_id = msa.state ");
        query.setParameter("productId", productId);
        return query.getResultList();

    }
    //This Query will change once nishant will review it.
    public List<Object[]>  findMSAByMember(int id) {
        String sql = "SELECT distinct msa.id,msa.file_loc,msa.display_name,"+
        		"st.state_id,st.state [state_name],st.symbol [state_symbol],ttm.sales_channel_id,p.name, "+
                "member.renew_date,member_fee.product_id, i.[value] AS [ProductInsuranceType]  "+
                "FROM dbo.tlnk_Fulfillment_Type_To_Material ttm "+
                "INNER JOIN dbo.tdat_Product p ON p.product_id = ttm.product_id AND ISNULL(ttm.is_obsolete,0) = 0 "+
                "INNER JOIN dbo.tlnk_MSA msa on msa.material_id = ttm.material_id AND msa.file_loc IS NOT NULL AND msa.is_deleted = 0 "+
                "INNER JOIN dbo.vw_group_company cg on cg.sales_channel_id = ttm.sales_channel_id "+
                "INNER JOIN dbo.tdat_Member member on member.group_id = cg.group_id "+
                "INNER JOIN dbo.tlnk_Member_Fee member_fee on member_fee.product_id = ttm.product_id "+
                "AND member_fee.member_id = member.member_id "+
                "INNER JOIN dbo.tlst_Product_MSA_Type msa_type on ttm.product_id = msa_type.product_id "+
                "LEFT JOIN tlst_State2 st ON st.state_id = msa.state "+
                "LEFT JOIN insurance.InsuranceType i ON i.InsuranceType_ID=p.InsuranceType_ID "+
                "WHERE 1=1 "+
                "AND fulfillment_request_type_id = 3 "+
                "AND member_fee.member_id = :memberId "+
                "AND member_fee.fee_id <> 1 AND member_fee.product_id not in (182,268) "+
                "AND ( msa_type.type = 1 "+
                "	OR member_fee.frequency in ("+Constant.FREQ_WEEK+","+Constant.FREQ_BIWEEK+","+Constant.FREQ_MONTH+")) "+
                "AND (GETDATE() between effective_start_date and  effective_end_date) "+
                "AND CAST(GETDATE() AS DATE) between CAST(msa.start_date AS DATE) and  CAST(msa.end_date  AS DATE) "+
                "UNION "+
                "SELECT distinct msa.id,msa.file_loc,msa.display_name, "+
                "st.state_id,st.state [state_name],st.symbol [state_symbol],ttm.sales_channel_id,p.name, "+
                "member.renew_date,member_fee.product_id, i.[value] AS [ProductInsuranceType] "+
                "FROM dbo.tlnk_Fulfillment_Type_To_Material ttm "+
                "INNER JOIN dbo.tdat_Product p ON p.product_id = ttm.product_id AND ISNULL(ttm.is_obsolete,0) = 0 "+
                "INNER JOIN dbo.tlnk_MSA msa on msa.material_id = ttm.material_id AND msa.file_loc IS NOT NULL  AND msa.is_deleted = 0 "+
                "INNER JOIN dbo.vw_group_company cg on cg.sales_channel_id = ttm.sales_channel_id "+
                "INNER JOIN dbo.tdat_Member member on member.group_id = cg.group_id "+
                "INNER JOIN dbo.tlnk_Member_Fee member_fee on member_fee.product_id = ttm.product_id AND "+
                "member_fee.member_id = member.member_id "+
                "INNER JOIN dbo.tlst_Product_MSA_Type msa_type on ttm.product_id = msa_type.product_id "+
                "LEFT JOIN tlst_State2 st ON st.state_id = msa.state "+
                "LEFT JOIN insurance.InsuranceType i ON i.InsuranceType_ID=p.InsuranceType_ID "+
                "WHERE 1=1 "+
                "AND fulfillment_request_type_id = 3 "+
                "AND member_fee.member_id = :memberId "+
                "AND member_fee.fee_id <> 1 AND member_fee.product_id not in (182,268) "+
                "AND ( msa_type.type = 2 "+
                "	OR member_fee.frequency in ("+Constant.FREQ_YEAR+")) "+
                "AND (GETDATE() between effective_start_date and  effective_end_date) "+
                "AND (CAST(DATEADD(MONTH,-12,member.renew_date) as DATE) between CAST(msa.start_date AS DATE ) and "+
                "  CAST(msa.end_date as DATE)) "+
                "UNION "+
                "SELECT distinct msa.id,msa.file_loc,msa.display_name, "+
                "st.state_id,st.state [state_name],st.symbol [state_symbol],ttm.sales_channel_id,p.name, "+
                "member.renew_date,member_fee.product_id, i.[value] AS [ProductInsuranceType] "+
                "FROM dbo.tlnk_Fulfillment_Type_To_Material ttm "+
                "INNER JOIN dbo.tdat_Product p ON p.product_id = ttm.product_id AND ISNULL(ttm.is_obsolete,0) = 0 "+
                "INNER JOIN dbo.tlnk_MSA msa on msa.material_id = ttm.material_id AND msa.file_loc IS NOT NULL  AND msa.is_deleted = 0 "+
                "INNER JOIN dbo.vw_group_company cg on cg.sales_channel_id = ttm.sales_channel_id "+
                "INNER JOIN dbo.tdat_Member member on member.group_id = cg.group_id "+
                "INNER JOIN dbo.tlnk_Member_Fee member_fee on member_fee.product_id = ttm.product_id "+
                "AND member_fee.member_id = member.member_id "+
                "INNER JOIN dbo.tlst_Product_MSA_Type msa_type on ttm.product_id = msa_type.product_id "+
                "LEFT JOIN tlst_State2 st ON st.state_id = msa.state "+
                "LEFT JOIN insurance.InsuranceType i ON i.InsuranceType_ID=p.InsuranceType_ID "+
                "WHERE 1=1 "+
                "AND fulfillment_request_type_id = 3 "+
                "AND member_fee.member_id = :memberId "+
                "AND member_fee.fee_id <> 1 AND member_fee.product_id not in (182,268) "+
                "AND msa_type.type = 3 "+
                "AND CAST(msa.start_date as DATE) <=  "+
                "IIF(member_fee.effective_start_date > CAST(DATEADD(MONTH,-60,member.renew_date) as DATE), "+
                " member_fee.effective_start_date, CAST(DATEADD(MONTH,-60,member.renew_date) as DATE)) "+
                "AND CAST(msa.end_date as DATE) >=  IIF(member_fee.effective_start_date > "+
                " CAST(DATEADD(MONTH,-60,member.renew_date) as DATE), member_fee.effective_start_date, "+
                " CAST(DATEADD(MONTH,-60,member.renew_date) as DATE)) "+
                "UNION "+
                "select distinct msa.id,msa.file_loc,msa.display_name, "+
                "st.state_id,st.state [state_name],st.symbol [state_symbol],ttm.sales_channel_id,p.name, "+
                "member.renew_date,membFee.product_id, i.[value] AS [ProductInsuranceType] "+
                "FROM dbo.tlnk_Fulfillment_Type_To_Material ttm "+
                "INNER JOIN dbo.tdat_Product p ON p.product_id = ttm.product_id AND ISNULL(ttm.is_obsolete,0) = 0 "+
                "inner join dbo.tlnk_MSA msa on msa.material_id = ttm.material_id AND msa.file_loc IS NOT NULL AND msa.is_deleted = 0 "+
                "inner join dbo.vw_group_company cg on cg.sales_channel_id = ttm.sales_channel_id "+
                "inner join dbo.tdat_Member member on member.group_id = cg.group_id "+
                "INNER JOIN ( "+
                "SELECT "+
                "member_fee.member_id , "+
                "member_fee.fee_id, member_fee.product_id, effective_start_date,effective_end_date , "+
                "case product_date when '1900-01-01 00:00:00.000' then CAST(effective_start_date as date) "+
                "else CAST(product_date as DATE) END AS [compare_effective_date] "+
                "FROM dbo.tlnk_Member_Fee member_fee "+
                ") AS membFee ON membFee.member_id = member.member_id AND membFee.product_id = ttm.product_id "+
                "inner join dbo.tlst_Product_MSA_Type msa_type on ttm.product_id = msa_type.product_id "+
                "LEFT JOIN tlst_State2 st ON st.state_id = msa.state "+
                "LEFT JOIN insurance.InsuranceType i ON i.InsuranceType_ID=p.InsuranceType_ID "+
                "WHERE 1=1 "+
                "and fulfillment_request_type_id = 3 "+
                "and membFee.member_id = :memberId "+
                "and membFee.fee_id <> 1 and membFee.product_id not in (182,268) "+
                "and msa_type.type = 4 "+
                " AND (GETDATE() between effective_start_date and  effective_end_date) "+
                " AND (CAST(membFee.compare_effective_date as DATE) between CAST(msa.start_date AS DATE) "+
                " and CAST(msa.end_date as DATE))";
        Query query = entityManager.createNativeQuery(sql);
        query.setParameter("memberId", id);
        return query.getResultList();
    }
    //This Query will change once nishant will review it.
    public List<Object[]>  findOOCByMember(int id) {
        String sql = "SELECT  "
        		+ "	DISTINCT ooc.ooc_document_id,ooc.file_loc,ooc.display_name, "
        		+ "        	 st.state_id,st.state [state_name],st.symbol [state_symbol],0 sales_channel_id,p.name,  "
        		+ "             m.renew_date,f.product_id, i.[value] AS [ProductInsuranceType] "
        		+ "from tdat_Member m "
        		+ "JOIN tlnk_Member_Fee f ON m.member_id=:memberId "
        		+ "	AND f.member_id=m.member_id AND f.fee_id <> 1 AND f.product_id not in (182,268) "
        		+ "	AND GETDATE() between f.effective_start_date and  f.effective_end_date "
        		+ "JOIN tdat_Product p ON p.product_id=f.product_id "
        		+ "JOIN ooc_document ooc ON ooc.product_id=p.product_id AND ooc.file_loc IS NOT NULL AND ooc.is_deleted=0 "
        		+ "	AND CAST(GETDATE() AS DATE) between CAST(ooc.start_date AS DATE) and  CAST(ooc.end_date  AS DATE) "
        		+ "LEFT JOIN tlst_State2 st ON st.state_id = ooc.state  "
        		+ "LEFT JOIN insurance.InsuranceType i ON i.InsuranceType_ID=p.InsuranceType_ID ";
        Query query = entityManager.createNativeQuery(sql);
        query.setParameter("memberId", id);
        return query.getResultList();
    }
    public List<Object[]>  findMSAByGroup(int id, int productId,int divisionId) {
        String sql = "SELECT msa.id, ISNULL(msa.group_file_loc, msa.file_loc) [file_loc], ISNULL(msa.group_display_name, msa.display_name) [display_name], "+
        		"st.state_id,st.state [state_name],st.symbol [state_symbol],ttm.sales_channel_id, "+
        		"p.name "+
                "FROM dbo.tlnk_Fulfillment_Type_To_Material ttm "+
                (divisionId<=0? "" : (" JOIN tdat_Sales_Channel sc ON sc.id=ttm.sales_channel_id AND sc.division="+divisionId) )+
        		"JOIN dbo.tdat_Product p ON p.product_id = ttm.product_id AND ISNULL(ttm.is_obsolete,0) = 0 "+
                "JOIN dbo.tlnk_MSA msa on msa.material_id = ttm.material_id AND msa.is_deleted = 0 AND ISNULL(msa.group_file_loc, msa.file_loc) IS NOT NULL "+
                "JOIN dbo.vw_group_company cg on cg.sales_channel_id = ttm.sales_channel_id "+
                "AND cg.group_id = :groupId "+
                "AND ttm.fulfillment_request_type_id = 3 "+
                "AND (0 = :productId OR ttm.product_id = :productId) "+
                "AND cast(msa.start_date as date) <= cast(getDate() as date) "+
                "AND cast(msa.end_date as date) >= cast(getDate() as date) "+
                "LEFT JOIN tlst_State2 st ON st.state_id = msa.state ";
        Query query = entityManager.createNativeQuery(sql);
        query.setParameter("groupId", id);
        query.setParameter("productId", productId);
        return query.getResultList();
    }
}