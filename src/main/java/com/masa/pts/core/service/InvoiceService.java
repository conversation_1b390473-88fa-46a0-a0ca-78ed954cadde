package com.masa.pts.core.service;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import com.masa.pts.core.constant.PTSUserType;
import com.masa.pts.core.domain.GroupDocument;
import com.masa.pts.core.domain.Invoice;
import com.masa.pts.core.domain.Invoice.InvoiceSummary;
import com.masa.pts.core.domain.PTSUser;
import com.masa.pts.core.model.InvoiceSearchDTO;
import com.masa.pts.core.repository.GroupDocumentRepository;
import com.masa.pts.core.repository.InvoiceRepository;

@Service
public class InvoiceService {

	private InvoiceRepository invoiceRepository;
	private GroupDocumentRepository groupDocumentRepository;
	
	@Autowired
	public InvoiceService(InvoiceRepository invoiceRepository,GroupDocumentRepository groupDocumentRepository) {
		this.invoiceRepository = invoiceRepository;
		this.groupDocumentRepository = groupDocumentRepository;
	}
	
	public Page<InvoiceSearchDTO> searchGroupInvoices(Integer groupId,String groupCode,Integer invoiceId,Date invoiceDate,Boolean isPaid, PTSUser user,Pageable pageable) {
		
		Page<InvoiceSearchDTO> invoices;
		
		if (user.getUserType().equals(PTSUserType.EXTERNAL.getUserType())) {
			invoices =  invoiceRepository.searchInvoicesByParamsExternalUser(groupId,groupCode,invoiceId,invoiceDate,isPaid,user.getEmployeeId(), pageable); 
		}
		else {
			invoices =  invoiceRepository.searchInvoicesByParamsInternalUser(groupId,groupCode,invoiceId,invoiceDate,isPaid,user.getEmployeeId(), pageable);
		}
		return invoices;		
	}
	
	public Optional<Invoice> getInvoiceById(Integer invoiceId)
	{
		return invoiceRepository.findById(invoiceId);
	}
	
	public Invoice getInvoiceInfoById(Integer invoiceId) {
		return invoiceRepository.findInvoiceWithDetailsById(invoiceId);
	}
	
	public Optional<GroupDocument> getInvoicePDFDocument(Integer invoiceId)
	{
		Optional<GroupDocument> optGroup = Optional.empty();
		
		Optional<InvoiceSummary> optInv = invoiceRepository.findByInvoiceId(invoiceId,Invoice.InvoiceSummary.class);
		
		if(optInv.isPresent())
		{
			InvoiceSummary invoice = optInv.get();
		
			List<GroupDocument> grpDocSet = groupDocumentRepository.findAllByDocumentTypeAndGroupGroupIdAndDocumentInvDepositDateAndIsDeleteOrderByModifiedDate("Invoice",invoice.getGroupId(),
					invoice.getInvoiceDate(),Boolean.FALSE);
			
			if(!grpDocSet.isEmpty() && grpDocSet.size() >=1)
				return Optional.of(grpDocSet.get(0));		
		}			
		return optGroup;
	}

	public boolean isInvoiceValidForUser(Integer invoiceId, PTSUser user) {
		if (user.getUserType().equals(PTSUserType.EXTERNAL.getUserType())) {
			return invoiceRepository.externalUserHasAccessToInvoice(invoiceId, user.getEmployeeId());
		}
		else {
			return invoiceRepository.internalUserHasAccessToInvoice(invoiceId, user.getEmployeeId());
		}
	}
}
