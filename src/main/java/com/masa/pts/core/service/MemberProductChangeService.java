package com.masa.pts.core.service;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.masa.pts.core.constant.ProductChangeType;
import com.masa.pts.core.domain.Constant;
import com.masa.pts.core.domain.MemberFee;
import com.masa.pts.core.domain.ProductUpgradeDowngrade;
import com.masa.pts.core.model.GroupDivisionDTO;
import com.masa.pts.core.repository.ProductUpgradeDowngradeRepository;

@Service
public class MemberProductChangeService {

	private static final Logger log = LoggerFactory.getLogger(MemberProductChangeService.class);
	
	private ProductUpgradeDowngradeRepository productUpgradeDowngradeRepository;
	private MemberService memberService;
	private GroupService groupService;
	private ProductService productService;
	
	@Autowired
	public MemberProductChangeService(ProductUpgradeDowngradeRepository productUpgradeDowngradeRepository,
			MemberService memberService, GroupService groupService,ProductService productService) {
		this.productUpgradeDowngradeRepository = productUpgradeDowngradeRepository;
		this.memberService = memberService;
		this.groupService = groupService;
		this.productService = productService;
	}
	
	public boolean isValidMemberProductChange(Integer memberId,String groupCode,Integer nextProductId) 
	{
		Optional<GroupDivisionDTO> optGrpSmry = groupService.getGroupDivisionInfo(groupCode);
		
		if(!optGrpSmry.isPresent())
		{
			log.error(" isValidMemberProductChange return false as Group code [{}] not found",groupCode);
			return false;
		}
		
		String scName = optGrpSmry.get().getSalesChannelName();
		String divName = optGrpSmry.get().getDivisionName();
		
		ProductChangeType prdChangeType = productService.getProductChangeType(scName, divName);
		
		if(ProductChangeType.TABLE_DATA.compareTo(prdChangeType)==0)
		{
			return checkValidUpgradeInTable(memberId,groupCode,nextProductId);
		}
		else if(ProductChangeType.GROUP_DATA.compareTo(prdChangeType)==0)
		{
			return checkValidUpgradeInGroup(groupCode,nextProductId);
		}
		return false;
	}

	/**
	 * @param groupCode
	 * @param nextProductId
	 * @return
	 */
	private boolean checkValidUpgradeInGroup(String groupCode,Integer nextProductId)
	{
		return groupService.isProductValidForGroup(groupCode, nextProductId);
	}
	
	private boolean checkValidUpgradeInTable(Integer memberId,String groupCode,Integer nextProductId)
	{
		Set<MemberFee> memberFees =  memberService.getMemberProducts(memberId);		
		Integer nextProductFrequency = groupService.getProductFrequency(groupCode,nextProductId);
		
		MemberFee memFeePrimary = memberFees.stream().filter(item->!Constant.SETUP_FEE_IDS.contains(item.getFeeDetails().getFeeId()) && item.getUpgradeType().intValue() == 0)
				.findFirst().orElse(null);
		
		if(null !=memFeePrimary) 
		{		
			List<ProductUpgradeDowngrade> prdUpgradeList = productUpgradeDowngradeRepository.findAllByParams(memFeePrimary.getProduct().getProductId(), memFeePrimary.getFrequencyDetails().getID(), nextProductId, nextProductFrequency, groupCode, Boolean.FALSE); 
			
			if(!prdUpgradeList.isEmpty())
				return true;
		}		
		return false;
	}
}
