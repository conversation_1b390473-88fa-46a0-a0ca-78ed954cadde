package com.masa.pts.core.service;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.OptionalDouble;
import java.util.Set;
import java.util.stream.Collector;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.StringUtils;

import com.masa.pts.core.constant.InvoiceConstant.InvoiceType;
import com.masa.pts.core.constant.MemberActiveStatus;
import com.masa.pts.core.constant.MemberEventType;
import com.masa.pts.core.domain.Constant;
import com.masa.pts.core.domain.GroupEntity;
import com.masa.pts.core.domain.Invoice;
import com.masa.pts.core.domain.InvoiceDetail;
import com.masa.pts.core.domain.Member;
import com.masa.pts.core.domain.Member.MemberInfo;
import com.masa.pts.core.domain.MemberFee;
import com.masa.pts.core.domain.MemberIdentifierType;
import com.masa.pts.core.domain.Payment;
import com.masa.pts.core.domain.PaymentDetail;
import com.masa.pts.core.domain.PaymentProcessOutput;
import com.masa.pts.core.model.MemberPayment;
import com.masa.pts.core.model.PaymentPostStatus;
import com.masa.pts.core.repository.GroupRepository;
import com.masa.pts.core.repository.InvoiceRepository;
import com.masa.pts.core.repository.MemberFeeRepository;
import com.masa.pts.core.repository.MemberRepository;
import com.masa.pts.core.repository.PaymentDetailRepository;
import com.masa.pts.core.repository.PaymentRepository;

import static com.masa.pts.core.domain.Constant.DEFAULT_DATE_PATTERN;

@Service
public class PaymentPostService {

	public static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern(DEFAULT_DATE_PATTERN);
	@Autowired
	PaymentRepository paymentRepository;
	
	@Autowired
	PaymentDetailRepository paymentDetailRepository;

	@Autowired
	MemberRepository memberRepository;

	@Autowired
	InvoiceRepository invoiceRepository;

	@Autowired
	GroupRepository groupRepository;

	@Autowired
	MemberFeeRepository memberFeeRepository;

	@Autowired
	PaymentService paymentService;

	@Autowired
	MemberProductFeeService memberProductFeeService;

	@Autowired
	MemberService memberService;
	
	@Autowired
	CommissionService commissionService;
	
	@Autowired
	NoticeService noticeService;
	
	@Autowired
	GroupService groupService;
	
	@Autowired
	PlatformTransactionManager transactionManager;

	@Autowired
	ProductService productService;
	
	@Autowired
	MemberEmployeeIdService memberEmployeeIdService;
	
	
	private static final Logger log = LoggerFactory.getLogger(PaymentPostService.class);

	/**
	 * @param memPayment
	 * @return
	 */
	public PaymentProcessOutput validatePaymentInput(MemberPayment memPayment) {

		Optional<Member.MemberInfo> optMember = null;
		PaymentProcessOutput paymentProcessOutput = new PaymentProcessOutput();		
		
		if (memPayment.getIdentifierType() == MemberIdentifierType.EMPLOYERID) {			
			optMember = lookupMemberByEmployeeID(memPayment,paymentProcessOutput);
		} 
		else if(memPayment.getIdentifierType().equals(MemberIdentifierType.GROUP))
		{// check if Group has forte payments
			GroupEntity group = groupService.getGroupByCode(memPayment.getGroupCode());
			if(group.getAutoPay()==null || (null != group.getAutoPay() && !group.getAutoPay())) {
				paymentProcessOutput.setStatus(PaymentPostStatus.NOT_FOUND);
				paymentProcessOutput.setTransactionDate(memPayment.getTransactionDate());
				paymentProcessOutput.setAmountPaid(memPayment.getPaymentAmt());
				paymentProcessOutput.setEmployerId(memPayment.getMemberIdentifier());
				if(StringUtils.isEmpty(paymentProcessOutput.getMemberId())) {
					paymentProcessOutput.setMemberId(0);
				}
				paymentProcessOutput.setComments("Group ["+memPayment.getMemberIdentifier()+"] is not setup as Auto Pay to process Forte Payment,  Payment not processed.");
			}
			else {
				paymentProcessOutput.setMemberName(group.getGroupName());
				paymentProcessOutput.setStatus(PaymentPostStatus.VALID);
				paymentProcessOutput.setEmployerId(group.getGroupCode());
				paymentProcessOutput.setMemberId(0);
			}
		}		
		else {
			 	Integer memberId=0;
			try {
				 memberId = Integer.valueOf(memPayment.getMemberIdentifier());
				 paymentProcessOutput.setMemberId(memberId);
			} catch (Exception e) {
				paymentProcessOutput.setMemberId(0);
				log.error("Exception while parsing Forte customer id, Message :[{}], Transaction ID:[{}]",e.getMessage(),memPayment.getForteTransactionId());
			}
			
			if(memberId > 0) {
				log.info("Validating member [{}]",memberId);
				optMember = this.memberRepository.findByMemberId(memberId);
			}
			
			if (optMember == null || (optMember != null && !optMember.isPresent())) {
				paymentProcessOutput.setStatus(PaymentPostStatus.NOT_FOUND);
				paymentProcessOutput.setTransactionDate(memPayment.getTransactionDate());
				paymentProcessOutput.setAmountPaid(memPayment.getPaymentAmt());
				paymentProcessOutput.setComments("Member not found with Member ID [" + memPayment.getMemberIdentifier() + "] Payment not processed.");
			} 
			else if((optMember.get().getLastName() == null || optMember.get().getBirthDate() == null || optMember.get().getEffectiveDate() == null) ) {
				paymentProcessOutput.setStatus(PaymentPostStatus.NOT_FOUND);
				paymentProcessOutput.setTransactionDate(memPayment.getTransactionDate());
				paymentProcessOutput.setAmountPaid(memPayment.getPaymentAmt());
				paymentProcessOutput.setComments("Invalid Member information [Last Name , DOB , Effective Date Empty ] for Member ID [" + memPayment.getMemberIdentifier() + "] Payment not processed.");
			}
			else {
				paymentProcessOutput.setStatus(PaymentPostStatus.VALID);
			}
		}
		
		if(PaymentPostStatus.NOT_FOUND.getStatus().equals(paymentProcessOutput.getStatus().getStatus()) || memPayment.getIdentifierType().equals(MemberIdentifierType.GROUP)) {
			return paymentProcessOutput;
		}
		
		
		paymentProcessOutput.setMemberId(optMember.get().getMemberId());
		String fname = optMember.get().getFirstName() == null ? "" : optMember.get().getFirstName();
		String lname = optMember.get().getLastName() == null ? "" : optMember.get().getLastName();
		String mi = optMember.get().getMi() == null ? "" : optMember.get().getMi();
		paymentProcessOutput.setMemberName(fname.concat(" ").concat(mi).concat(" ").concat(lname));		
		paymentProcessOutput.setEmployerId(optMember.get().getEmployeeId());
		paymentProcessOutput.setJoinDate(optMember.get().getEffectiveDate());
		
		if(!memPayment.isChargeback())
		{
			if(null !=memPayment.getForteTransactionId() && !memPayment.getForteTransactionId().isEmpty()) 
			{
				if (paymentService.isPaymentExistForForteTransaction(memPayment.getForteTransactionId())) 
				{
					paymentProcessOutput.setStatus(PaymentPostStatus.DUPLICATE);
					paymentProcessOutput.setTransactionDate(memPayment.getTransactionDate());
					paymentProcessOutput.setComments("Payment has already been processed for this forte transaction.["
							+ memPayment.getForteTransactionId() + "]");
				}
			}
			else 
			{
				if(memPayment.getTransactionDate() != null && paymentService.isPaymentExistForTheDate(optMember.get().getMemberId(), memPayment.getTransactionDate())) {
					paymentProcessOutput.setStatus(PaymentPostStatus.DUPLICATE);
					paymentProcessOutput.setTransactionDate(memPayment.getTransactionDate());
					paymentProcessOutput.setAmountPaid(memPayment.getPaymentAmt());					 
					LocalDate temp = memPayment.getTransactionDate().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();					
					paymentProcessOutput.setComments("Payment has already been processed for this date.["+temp.format(DateTimeFormatter.ofPattern(DEFAULT_DATE_PATTERN))+"]");
				}
				
			}
			if(PaymentPostStatus.VALID.compareTo(paymentProcessOutput.getStatus()) ==0) {				
				if (null != optMember.get().getAlternatePayer() && optMember.get().getAlternatePayer().compareTo(Integer.valueOf(0)) > 0) {
					// member has alternate payer.
					paymentProcessOutput.setStatus(PaymentPostStatus.NOT_PAYOR);
					paymentProcessOutput.setAmountPaid(memPayment.getPaymentAmt());
					paymentProcessOutput.setTransactionDate(memPayment.getTransactionDate());
					paymentProcessOutput.setComments("Member is paid for by [" + optMember.get().getAlternatePayer()+"]");
				} else if ((optMember.get().getActive().compareTo(MemberActiveStatus.CANCELLED.getStatus())==0) ) {
					paymentProcessOutput.setStatus(PaymentPostStatus.VALID);
					paymentProcessOutput.setComments("Payment Posted to Cancelled member. ");
					paymentProcessOutput.setActive(MemberActiveStatus.CANCELLED.name());
				}
				else if ((optMember.get().getActive().compareTo(MemberActiveStatus.SUSPENDED.getStatus())==0) ) {
					paymentProcessOutput.setStatus(PaymentPostStatus.VALID);
					paymentProcessOutput.setComments("Payment Posted to Suspended member. ");
					paymentProcessOutput.setActive(MemberActiveStatus.SUSPENDED.name());
				 }
				else if(memPayment.getPaymentAmt().compareTo(BigDecimal.ZERO) ==0) {
					paymentProcessOutput.setStatus(PaymentPostStatus.SKIP);
					paymentProcessOutput.setTransactionDate(memPayment.getTransactionDate());
					paymentProcessOutput.setAmountPaid(memPayment.getPaymentAmt());
					paymentProcessOutput.setComments("Member Payment amount is 0, Payment skipped.");
				}
				else {
					paymentProcessOutput.setStatus(PaymentPostStatus.VALID);				
				}
			}
		}
		else 
		{
			paymentProcessOutput.setStatus(PaymentPostStatus.VALID);
		}
		return paymentProcessOutput;
	}
	
	
	
	/**
	 * @param memPayment
	 * @param paymentProcessOutput
	 * @return
	 */
	private Optional<MemberInfo> lookupMemberByEmployeeID(MemberPayment memPayment,
			PaymentProcessOutput paymentProcessOutput) {
		Optional<MemberInfo> optMember = null;
		
		List<Integer> memIdSet = memberEmployeeIdService
				.getMemberInfoByEmployeeId(memPayment.getMemberIdentifier(),memPayment.getGroupCode(),memPayment.getTransactionDate());
		
		List<Member.MemberInfo> optMemSet = memberRepository.findByMemberIdIn(memIdSet);
		
		//this.memberRepository.findByEmployeeIdAndGroupGroupCodeOrderByActive(memPayment.getMemberIdentifier(), memPayment.getGroupCode());

		if(optMemSet.isEmpty() || (!optMemSet.isEmpty() && optMemSet.size() >=2))
		{
			paymentProcessOutput.setStatus(PaymentPostStatus.NOT_FOUND);
			paymentProcessOutput.setTransactionDate(memPayment.getTransactionDate());
			paymentProcessOutput.setAmountPaid(memPayment.getPaymentAmt());
			paymentProcessOutput.setEmployerId(memPayment.getMemberIdentifier());
			if(StringUtils.isEmpty(paymentProcessOutput.getMemberId())) {
				paymentProcessOutput.setMemberId(0);
			}
			if(optMemSet.isEmpty())
			{
				paymentProcessOutput.setComments("Member not found with employee_id [" + memPayment.getMemberIdentifier() + "] in Group [" + memPayment.getGroupCode() + "] Payment not processed.");
			}
			else if(optMemSet.size() >=2) 
			{
				List<Member.MemberInfo> activeMemSet = optMemSet.stream().filter(item-> (item.getActive().compareTo(MemberActiveStatus.ACTIVE.getStatus()) == 0) || 
						(item.getActive().compareTo(MemberActiveStatus.ACTIVE.getStatus()) != 0 && memPayment.getTransactionDate().before(item.getCancelDate()) ) ).collect(Collectors.toList());
				
				if(activeMemSet.isEmpty())
					paymentProcessOutput.setComments("No active Member IDs found with employee_id [" + memPayment.getMemberIdentifier() + "] in Group [" + memPayment.getGroupCode() + "] Payment not processed.");
				else if(activeMemSet.size()>=2)
					paymentProcessOutput.setComments("Multiple active Member IDs found with employee_id [" + memPayment.getMemberIdentifier() + "] in Group [" + memPayment.getGroupCode() + "] Payment not processed.");
				else
				{
					optMember = Optional.of(activeMemSet.get(0));
					paymentProcessOutput.setStatus(PaymentPostStatus.VALID);
				}				
			}
		}
		else
		{
			optMember = Optional.of(optMemSet.get(0));
			paymentProcessOutput.setStatus(PaymentPostStatus.VALID);
		}		
		return optMember;
	}
	
	/**
	 * @param name
	 * @return Transaction Template
	 */
	private TransactionTemplate createNewTransaction(String name) {
		TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
		transactionTemplate.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
		transactionTemplate.setName(name);
		return transactionTemplate;
	}
	
	public Set<PaymentProcessOutput> postGroupPaymentWithTransactionNew(List<MemberPayment> memberPayments, String groupCode, boolean createInvoice, Integer ptsPaymentAppuser, 
			Date depositDate, Date paymentPostDate,String paymentNote, String checkNum, BigDecimal checkAmount) {
		Payment payment = new Payment();
		Invoice invoice = new Invoice();
		log.info("In Posting Group Payment to PTS, Record count [{}] for Group [{}]", memberPayments.size(), groupCode);
		Set<PaymentProcessOutput> outputList;
		boolean isGroupCustomerIdPayment=false;
		
		// step 1
		log.info("Step 1, Record count [{}] for Group [{}]", memberPayments.size(), groupCode);
		outputList = validateInputGroupInTransaction(groupCode, memberPayments, createInvoice, ptsPaymentAppuser);

		// if invalid group, return
		if (!outputList.isEmpty())
			return outputList;
		
		//assume that group setup with forte payment will get only 1 settled transaction per day
		if(memberPayments.size()==1 && MemberIdentifierType.GROUP.equals(memberPayments.get(0).getIdentifierType()))
		{
			log.info("Step 2 Group, Record count [{}] for Group [{}]", memberPayments.size(), groupCode);
			MemberPayment memPayment = memberPayments.get(0);	
			PaymentProcessOutput paymentProcessOutput ;
			invoice.setInvoiceDate(calculateInvoiceDate(memPayment,groupCode,Boolean.TRUE));
			paymentProcessOutput = processGroupCustomerPaymentInTransaction(memPayment,groupCode,payment,invoice);
			isGroupCustomerIdPayment = paymentProcessOutput.getCustomerIdGroup();
			outputList.add(paymentProcessOutput);
			
		}
		else 
		{		
			log.info("Step 2 Validating input payment and creating payment object, Record count [{}] for Group [{}]", memberPayments.size(), groupCode);
			// Step2
			// validate payments & create payment object 
			// old method - replaced validatePaymentAndCreateInTransaction(groupCode, memberPayments,ptsPaymentAppuser,payment)
			int index=1;
			int total = memberPayments.size();
			BigDecimal totalPaidAmt = BigDecimal.ZERO;
			boolean isFortePayment= Boolean.TRUE;
			for (MemberPayment memPayment : memberPayments) 
			{
				if(index==1 && (memPayment.getForteTransactionId()==null || (memPayment.getForteTransactionId() !=null && memPayment.getForteTransactionId().isEmpty())) ) 
				{		
						isFortePayment= Boolean.FALSE;
				}					
				log.info("Creating payment detail for member [{}] record [{}] of total [{}]",memPayment.getMemberIdentifier(),(index++),total);
				totalPaidAmt = totalPaidAmt.add(memPayment.getPaymentAmt());
				long start = System.currentTimeMillis();
				outputList.add(createMemberPaymentDetailInTransaction(memPayment,groupCode,ptsPaymentAppuser,payment));
				long end = System.currentTimeMillis();
				if((end-start)>=Constant.THREASHOLD_TO_LOG)
					log.info("createMemberPaymentDetailInTransaction method execution time [{}] ",(end-start));
			}
			if(isFortePayment)
			{
				payment.setCheckNumber("Forte Payment");
			}
			else if(StringUtils.hasText(checkNum))
			{
				payment.setCheckNumber(checkNum);
				payment.setCheckAmount(checkAmount.compareTo(BigDecimal.ZERO)==0 ? totalPaidAmt.doubleValue() : checkAmount.doubleValue());
			}	
			else
			{
				payment.setCheckNumber("ACH");
				payment.setCheckAmount(totalPaidAmt.doubleValue());
			}
			payment.setTotalPaid(totalPaidAmt.doubleValue());			
			invoice.setInvoiceDate(calculateInvoiceDate(memberPayments.get(0),groupCode,Boolean.FALSE));
		}
		
		if(groupService.isGroupInvoiceDateFirstOfMonth(groupCode)) {
			payment.setPayInvoiceDate(invoice.getInvoiceDate());
		}
		else {
			payment.setPayInvoiceDate(new Date());
		}
		
		if(payment.getPaymentDetail().isEmpty())
			return outputList;
		
		int payDetailCnt = payment.getPaymentDetail().size();
		
		payment.setDepositDate(depositDate);
		payment.setReceiveDate(depositDate);
		
		if(null != paymentPostDate)
			payment.setPayDate(paymentPostDate);
				
		//Step3
		//save payment		
		log.info("Step 3 Saving Payment to DB & Step 4 Generating advance commission , Record count [{}] for Group [{}]", payDetailCnt, groupCode);
		Map<Integer,BigDecimal> memAdvCommAmtMap = savePaymentInTransaction(payment,ptsPaymentAppuser,paymentNote,groupCode);
		
		//step 5
		log.info("Step 5 Updating member renew and invoice date, Record count [{}] for Group [{}]", payDetailCnt, groupCode);
		
		Set<Integer> memIds = payment.getPaymentDetail().stream().map(PaymentDetail::getMemberId).collect(Collectors.toSet());
		int index=0;
		for(Integer memberId: memIds) 
		{
			log.info("Step 5 Updating member renew and invoice date for Member [{}], Record [{}] of Total [{}]", memberId,++index,memIds.size());
			Set<PaymentDetail> memPayDetails =  payment.getPaymentDetail().stream().filter(item->memberId.compareTo(item.getMemberId())==0).collect(Collectors.toSet());
			Set<PaymentProcessOutput> memProcessOutput = outputList.stream().filter(item->memberId.compareTo(item.getMemberId())==0).collect(Collectors.toSet());
			updateMemberAfterPaymentInTransaction(memPayDetails,memProcessOutput,ptsPaymentAppuser,groupCode);
		}
		//step 6
		for(PaymentProcessOutput item : outputList) {		
			if((PaymentPostStatus.isStatusSuccess(item.getStatus()) || PaymentPostStatus.isStatusPartialSuccess(item.getStatus())) && !item.getCustomerIdGroup() ) {
				Integer memberId = item.getMemberId();
			
				List<Integer> payDetailIdList = payment.getPaymentDetail().stream()
						.filter(payItem->payItem.getMemberId().compareTo(memberId)==0 || (payItem.getAlternatePayorId() !=null && payItem.getAlternatePayorId().compareTo(memberId)==0) )
						.map(PaymentDetail::getId).collect(Collectors.toList());
						
				String payDetailIds =  java.util.Arrays.toString(payDetailIdList.toArray());
				String prevComm = item.getComments()!=null ? item.getComments() : "";
				BigDecimal memAdvComm = memAdvCommAmtMap.getOrDefault(memberId, BigDecimal.ZERO);
				String commComment = memAdvComm.compareTo(BigDecimal.ZERO)==0 ? "" : " Adv Comm amt["+NumberFormat.getCurrencyInstance().format(memAdvComm)+"]";
				item.setComments(prevComm + " Pmt: [" + payment.getPayId() + "], Det: "+ payDetailIds + "" + commComment);
				item.setCommAmount(memAdvComm);
			}			
		}		
		//step 7
		if(createInvoice || isGroupCustomerIdPayment)
		{
			log.info("Step 7 Create/Update invoice , Record count [{}] for Group [{}]", payDetailCnt, groupCode);
			createOrUpdateInvoiceInTransaction(payment,ptsPaymentAppuser,invoice,groupCode,isGroupCustomerIdPayment);
		}		
		return outputList;
	}	
	
	/**
	 * @param memPayment
	 * @param groupCode
	 * @param isGroupCustomerPosting
	 * @return
	 */
	private Date calculateInvoiceDate(MemberPayment memPayment, String groupCode, Boolean isGroupCustomerPosting) {
		
		Date invoiceDate = getDateWithFirstDateOfMonth(memPayment.getTransactionDate());
		if(isGroupCustomerPosting.booleanValue())
		{
			GroupEntity grp = groupService.getGroupByCode(groupCode);
			if(grp.getPaymentMonthsBehind().intValue()>0) 
			{
				LocalDate tmp = new java.util.Date(invoiceDate.getTime()).toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
				tmp = tmp.minusMonths(grp.getPaymentMonthsBehind());
				invoiceDate = Date.from(tmp.atStartOfDay(java.time.ZoneId.systemDefault()).toInstant());
			}
			Date invEndDate = getDateWithLastDateOfMonth(invoiceDate);
			Optional<Invoice >optInvoice = groupService.getGroupFirstInvoiceByInvoiceDateRange(groupCode, invoiceDate, invEndDate);
			if(optInvoice.isPresent())
				invoiceDate = optInvoice.get().getInvoiceDate();			
		}
		return invoiceDate;
	}



	//Step 1
	private Set<PaymentProcessOutput> validateInputGroupInTransaction(String groupCode, List<MemberPayment> memberPayments, boolean createInvoice, Integer ptsPaymentAppuser) {
		TransactionTemplate transactionTemplate = createNewTransaction("VALIDATEGROUP");
		return transactionTemplate.execute(action->validateInputGroup(groupCode,memberPayments,createInvoice,ptsPaymentAppuser));		
	}
	private PaymentProcessOutput processGroupCustomerPaymentInTransaction(MemberPayment memPayment,String groupCode,Payment payment,Invoice invoice) {
		TransactionTemplate transactionTemplate = createNewTransaction("VALIDATEGROUPCUSTOMER");
		return transactionTemplate.execute(action->processGroupCustomerPayment(memPayment,groupCode,payment,invoice));
	}
	private PaymentProcessOutput processGroupCustomerPayment(MemberPayment memPayment, String groupCode,Payment payment,Invoice invoice) {
		PaymentProcessOutput paymentProcessOutput = validatePaymentInput(memPayment);	
		paymentProcessOutput.setTransactionId(memPayment.getForteTransactionId());
		paymentProcessOutput.setGroupCd(groupCode);
		if(PaymentPostStatus.VALID.compareTo(paymentProcessOutput.getStatus())==0) 
		{
			processPaymentForGroupCustomer(paymentProcessOutput,memPayment,payment,invoice);
		}	
		return paymentProcessOutput;
	}
	private PaymentProcessOutput createMemberPaymentDetailInTransaction(MemberPayment memPayment,String groupCode, Integer ptsPaymentAppuser,Payment payment) {
		TransactionTemplate transactionTemplate = createNewTransaction("VALIDATEPAYMENT");
		return transactionTemplate.execute(action->createMemberPaymentDetails(memPayment,groupCode,ptsPaymentAppuser,payment));
	}
	//Step 3
	private Map<Integer,BigDecimal> savePaymentInTransaction(Payment payment,Integer ptsPaymentAppuser,String paymentNote,String groupCode) {
		TransactionTemplate transactionTemplate = createNewTransaction("SAVEPAYMENT");
		return transactionTemplate.execute(action->savePayment(payment,ptsPaymentAppuser,paymentNote,groupCode));		
	}
	//step 5
	private boolean updateMemberAfterPaymentInTransaction(Set<PaymentDetail> paymentDetails, Set<PaymentProcessOutput> outputList, Integer ptsPaymentAppuser,String groupCode) {
		TransactionTemplate transactionTemplate = createNewTransaction("UPDATEMEMBER");
		return transactionTemplate.execute(action->updateMemberAfterPayment(paymentDetails,outputList,ptsPaymentAppuser,groupCode));		
	}
	//step 7
	private boolean createOrUpdateInvoiceInTransaction(Payment payment, Integer ptsPaymentAppuser,Invoice invoice,String groupCode,boolean isGroupCustomerIdPayment) {
		TransactionTemplate transactionTemplate = createNewTransaction("PAYMENTINVOICE");
		return transactionTemplate.execute(action->paymentInvoiceCreateOrUpdate(payment,ptsPaymentAppuser,invoice,groupCode,isGroupCustomerIdPayment));		
	}
	
	
	private Map<Integer,BigDecimal> savePayment(Payment payment,Integer ptsPaymentAppuser,String paymentNote,String groupCode) {
		setDefaultsForPayment(payment,ptsPaymentAppuser,paymentNote);		
		//review and split payment for new/renew commission
		reviewPaymentsForCommission(payment,ptsPaymentAppuser,groupCode);		
		//save payment ,payment details and forte transaction details if available
		paymentRepository.save(payment);	
		this.paymentService.savePaymentDetailForteTransactions(payment.getPaymentDetail(),ptsPaymentAppuser);
		return payAdavanceCommission(payment);		
	}
	private boolean updateMemberAfterPayment(Set<PaymentDetail> paymentDetails, Set<PaymentProcessOutput> outputList, Integer ptsPaymentAppuser,String groupCode) {
		updateMemberRenewAndInvoiceDate(paymentDetails,outputList,ptsPaymentAppuser,groupCode);		
		return true;
	}
	private boolean paymentInvoiceCreateOrUpdate(Payment payment, Integer ptsPaymentAppuser, Invoice invoice, String groupCode, boolean isGroupCustomerIdPayment) {
		createOrUpdateInvoice(payment,ptsPaymentAppuser,invoice,groupCode,isGroupCustomerIdPayment);
		return true;
	}	
	
	/**
	 * @param memPayment
	 * @param groupCode
	 * @param ptsPaymentAppuser
	 * @param payment
	 * @return
	 */
	private PaymentProcessOutput createMemberPaymentDetails(MemberPayment memPayment,String groupCode,Integer ptsPaymentAppuser,Payment payment){
		PaymentProcessOutput paymentProcessOutput = new PaymentProcessOutput();
		Optional<Member.MemberInfo> optMember;
	
		try {
			//validate payment input data for payment processing.
			long start = System.currentTimeMillis();
			paymentProcessOutput = validatePaymentInput(memPayment);
			long end = System.currentTimeMillis();
			if((end-start)>=Constant.THREASHOLD_TO_LOG)
				log.info("Validate Payment Input time taken [{}]",(end-start));
			
			paymentProcessOutput.setTransactionId(memPayment.getForteTransactionId());
			paymentProcessOutput.setGroupCd(groupCode);
			
			if (PaymentPostStatus.VALID.compareTo(paymentProcessOutput.getStatus()) == 0) {

				optMember = this.memberRepository.findByMemberId(paymentProcessOutput.getMemberId());
				paymentProcessOutput.setAmountPaid(memPayment.getPaymentAmt());
				paymentProcessOutput.setTransactionDate(memPayment.getTransactionDate());

				Member.MemberInfo memberInfo = optMember.get();

				paymentProcessOutput.setMemberId(memberInfo.getMemberId());
				paymentProcessOutput.setMemberName(memberInfo.getFirstName() + " " + memberInfo.getMi() + " " + memberInfo.getLastName());
				paymentProcessOutput.setEmployerId(memberInfo.getEmployeeId());
				paymentProcessOutput.setJoinDate(memberInfo.getEffectiveDate());
				
				MemberFee initFee = memberProductFeeService.getMemberInitFee(memberInfo.getMemberId());
								
				start=System.currentTimeMillis();
				Set<PaymentDetail> memPayDetails = paymentService.createPaymentDetail(memberInfo.getMemberId(),memPayment.getTransactionDate(),Boolean.TRUE,Boolean.FALSE);
				end=System.currentTimeMillis();
				if((end-start)>=Constant.THREASHOLD_TO_LOG)
					log.info("Create Pay Detail - pay detail took [{}]",(end-start));
				
				BigDecimal totalDue = new BigDecimal(Double.toString(memPayDetails.stream().mapToDouble(PaymentDetail::getAmountPaid).sum()));
				
				totalDue = totalDue.setScale(Constant.BIG_DECIMAL_DEFAULT_SCALE, Constant.BIG_DECIMAL_DEFAULT_ROUNGDING_MODE);

				paymentProcessOutput.setAmountDue(totalDue);
				boolean partialPayment=false;

				if (memPayment.getPaymentAmt().compareTo(totalDue) != 0) 
				{
					String prevComm = paymentProcessOutput.getComments()!=null ? paymentProcessOutput.getComments() : "";
					paymentProcessOutput.setComments(prevComm.concat("Member owes [" + NumberFormat.getCurrencyInstance().format(totalDue) + "] but paid [" + NumberFormat.getCurrencyInstance().format(memPayment.getPaymentAmt()) + "]."));
					partialPayment=true;
					double ratioDobVal = memPayment.getPaymentAmt().doubleValue() / totalDue.doubleValue();
					double tmpTotalAmt= 0.0;
					
					if(memPayDetails.size()==1) 
					{   //single product/payer
						for(PaymentDetail payDetailsItem : memPayDetails) 
						{
							payDetailsItem.setAmountPaid(memPayment.getPaymentAmt().doubleValue());
							double memTaxRate = memberInfo.getTaxRate() == null ? 0.0 : memberInfo.getTaxRate();
							payDetailsItem.setPeriodFee(
									paymentService.getPaymentPeriodFee(memberInfo.getOneTimeInit(), memTaxRate, payDetailsItem.getProductId(), payDetailsItem.getAmountPaid(), initFee));
						}
					}
					else { //multiple products/payer scenario
					
						for(PaymentDetail payDetailsItem : memPayDetails) {
							Double amountPaid = payDetailsItem.getAmountPaid() * ratioDobVal; 
							amountPaid = (double) Math.round(amountPaid * 100)/100;
							tmpTotalAmt = tmpTotalAmt + amountPaid;							
							tmpTotalAmt = (double)Math.round(tmpTotalAmt * 100) / 100;
							//new
							payDetailsItem.setAmountPaid(amountPaid);
							double memTaxRate = memberInfo.getTaxRate() == null ? 0.0 : memberInfo.getTaxRate();
							
							payDetailsItem.setPeriodFee(
									paymentService.getPaymentPeriodFee(memberInfo.getOneTimeInit(), memTaxRate, payDetailsItem.getProductId(), payDetailsItem.getAmountPaid(), initFee));
						}
						if(memPayment.getPaymentAmt().compareTo(new BigDecimal(tmpTotalAmt)) !=0) {
							log.error("Member ID [{}] , Payment amount and Pay Detail amount paid do NOT Match. Paid Amount [{}],Pay Detail Amt Paid [{}]",paymentProcessOutput.getMemberId(),
									memPayment.getPaymentAmt(),tmpTotalAmt);
						}
					}
				}

				memPayDetails.forEach(paymentDetailItem -> {
					paymentDetailItem.setTransactionDate(memPayment.getTransactionDate());
					
					if(null != memPayment.getForteTransactionId() && !memPayment.getForteTransactionId().isEmpty()) {							
						paymentDetailItem.setForteTransactionId(memPayment.getForteTransactionId());						
					}						
					payment.addPaymentDetail(paymentDetailItem);						
					paymentDetailItem.setPayment(payment);

				});
				
				if(partialPayment)
					paymentProcessOutput.setStatus(PaymentPostStatus.PARTIAL);
				else
					paymentProcessOutput.setStatus(PaymentPostStatus.SUCCESS);
			}
		}
		catch(Exception e) {
			paymentProcessOutput.setGroupCd(groupCode);
			paymentProcessOutput.setStatus(PaymentPostStatus.ERROR);
			paymentProcessOutput.setTransactionDate(memPayment.getTransactionDate());
			String comments = "Exception while processing member payment.["+e.getMessage()+"] ,["+e.getCause()+"]";
			comments = comments.concat(memPayment.getForteTransactionId() !=null ? memPayment.getForteTransactionId() : "");
			paymentProcessOutput.setComments(comments);
			comments="Exception while processing member payment, Member Identifier["+memPayment.getMemberIdentifier()+"] Group Code ["+groupCode+"] Exception ["+e.getMessage()+":"+e.getCause()+"]";
			comments = comments.concat(memPayment.getForteTransactionId() !=null ? "Forte Transaction ID ["+memPayment.getForteTransactionId()+"]" : "");
			log.error(comments);
		}
		paymentProcessOutput.setProcessedBy(ptsPaymentAppuser);
		
		return paymentProcessOutput;
	}
	
	/**
	 * @param payment
	 * @param ptsPaymentAppuser
	 * @param invoice
	 * @param groupCode
	 * @param isGroupCustomerIdPayment
	 */
	private void createOrUpdateInvoice(Payment payment, Integer ptsPaymentAppuser,Invoice invoice,String groupCode,boolean isGroupCustomerIdPayment) 
	{
		GroupEntity group = groupService.getGroupByCode(groupCode);
		if(group !=null)
		{			
			Date invoiceDate = invoice.getInvoiceDate(); 
			
			Optional<Invoice> optInvoice = java.util.Optional.empty();
			if(isGroupCustomerIdPayment)
				optInvoice = groupService.getGroupInvoiceByInvoiceDate(groupCode, invoiceDate);
			
			if(optInvoice.isPresent() && Boolean.FALSE.equals(optInvoice.get().getIsPaid())) 
			{
				invoice = optInvoice.get();				
				invoice.setIsPaid(Boolean.TRUE);
				invoice.setModifiedBy(ptsPaymentAppuser);
				invoice.setModifiedDate(new java.util.Date());
			}
			else 
			{
				Set<InvoiceDetail> invoiceDetails = getInvoiceDetailFromPayDetails(payment.getPaymentDetail(),ptsPaymentAppuser);
				for(InvoiceDetail item: invoiceDetails) {
					item.setInvoiceDate(invoice.getInvoiceDate());
					item.setInvoice(invoice);
					invoice.addInvoiceDetail(item);					
				}				 
				invoice.setGroup(group);				
				invoice.setInvoiceType(InvoiceType.GROUP_ROSTER.getType());
				invoice.setIsPaid(Boolean.TRUE);
				invoice.setTotalDue(payment.getTotalPaid());
				invoice.setCreatedBy(ptsPaymentAppuser);
				invoice.setCreatedDate(new java.util.Date());
				invoice.setModifiedBy(ptsPaymentAppuser);
				invoice.setModifiedDate(new java.util.Date());				
			}
			invoice.setPayment(payment);
			invoiceRepository.save(invoice);
			
			invoice.getInvoiceDetail().forEach(invoiceDetailItem->noticeService.updateMemberNotieFromInvoice(invoiceDetailItem,payment.getPayId()));
			
			log.info("Invoice created with Invoice Id [{}] , for Payment ID [{}] Group [{}]-[{}]",invoice.getInvoiceId(),payment.getPayId(),group.getGroupCode(),group.getGroupId());
		}
		else {
			log.error("Group Not found to create Invoice, Group [{}], Payment Id [{}]",groupCode,payment.getPayId());
		}		
	}



	/**
	 * @param dateToConvert
	 * @return
	 */
	private Date getDateWithFirstDateOfMonth(Date dateToConvert) {
		LocalDate localDate = new java.util.Date(dateToConvert.getTime()).toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
		localDate = localDate.withDayOfMonth(1);
		return Date.from(localDate.atStartOfDay(java.time.ZoneId.systemDefault()).toInstant());		
	}
	
	private Date getDateWithLastDateOfMonth(Date dateToConvert) {
		LocalDate localDate = new java.util.Date(dateToConvert.getTime()).toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
		localDate = localDate.withDayOfMonth(localDate.lengthOfMonth());
		return Date.from(localDate.atStartOfDay(java.time.ZoneId.systemDefault()).toInstant());		
	}
	
	/*
	 * Get active group members ,create payment & payment detail
	 * Create invoice
	 * validate if forte amount  = invoice total amount
	 * save payment , invoice.
	 */
	private void processPaymentForGroupCustomer(PaymentProcessOutput paymentProcessOutput, MemberPayment memPayment, Payment payment,Invoice invoice) {

		Date invoiceDate = invoice.getInvoiceDate();
		
		Optional<Invoice> optInvoice = groupService.getGroupInvoiceByInvoiceDate(paymentProcessOutput.getGroupCd(), invoiceDate);

		Constant.dateFormat.applyPattern(DEFAULT_DATE_PATTERN);
		String invDateStr = Constant.dateFormat.format(invoiceDate);
		
		paymentProcessOutput.setAmountPaid(memPayment.getPaymentAmt());
		paymentProcessOutput.setTransactionDate(memPayment.getTransactionDate());
		paymentProcessOutput.setCustomerIdGroup(Boolean.TRUE);

		if (optInvoice.isPresent() && optInvoice.get().getIsPaid()) {
			paymentProcessOutput.setStatus(PaymentPostStatus.SKIP);
			paymentProcessOutput.setComments("Group has existing paid invoice with invoice Date[" + invDateStr + "],Payment skipped.");
		} else if (optInvoice.isPresent() && !optInvoice.get().getIsPaid() && (memPayment.getPaymentAmt().compareTo(new BigDecimal(Double.toString(optInvoice.get().getTotalDue()))) != 0)) {
			paymentProcessOutput.setStatus(PaymentPostStatus.SKIP);
			paymentProcessOutput.setComments("Group Invoice Amt [" + optInvoice.get().getTotalDue() + "] Payment Amount [" + memPayment.getPaymentAmt() + "] do not match. " + "Invoice Date ["
					+ invDateStr + "].Payment skipped.");
			paymentProcessOutput.setAmountDue(new BigDecimal(Double.toString(optInvoice.get().getTotalDue())));
		} 
		else if(optInvoice.isPresent() && !optInvoice.get().getIsPaid() &&  (memPayment.getPaymentAmt().compareTo(new BigDecimal(Double.toString(optInvoice.get().getTotalDue()))) == 0)  )
		{
			invoice = optInvoice.get();
			//invoice exists and amounts match , post payment to that invoice
			updatePaymentFromInvoice(payment,invoice,memPayment);
			
			Double totalPayAmt = payment.getPaymentDetail().stream().mapToDouble(PaymentDetail::getAmountPaid).sum();
			if(totalPayAmt.doubleValue() != invoice.getTotalDue()) 
			{
				paymentProcessOutput.setStatus(PaymentPostStatus.ERROR);
				paymentProcessOutput.setComments("Group Invoice Amt [" + optInvoice.get().getTotalDue() + "] Payment Amount [" + totalPayAmt + "] do not match. " + "Invoice Date ["
						+ invDateStr + "].Payment skipped.");
				paymentProcessOutput.setAmountDue(new BigDecimal(Double.toString(optInvoice.get().getTotalDue())));
			}
			else
			{
				payment.setTotalPaid(totalPayAmt);
				paymentProcessOutput.setComments("Group payment processed to Invoice with invoice date [" + invDateStr + "].");
				paymentProcessOutput.setStatus(PaymentPostStatus.SUCCESS);
				paymentProcessOutput.setAmountPaid(memPayment.getPaymentAmt());
				paymentProcessOutput.setAmountDue(new BigDecimal(Double.toString(invoice.getTotalDue())));
			}
		}
		else {

			Set<Integer> memberIdSet = memberService.getAllActiveMembersByGroupCode(paymentProcessOutput.getGroupCd(), invoiceDate);

			Set<PaymentDetail> memPayDetails = new HashSet<>();
			
			memberIdSet.forEach(memberId -> memPayDetails.addAll(paymentService.createPaymentDetail(memberId, memPayment.getTransactionDate(),Boolean.FALSE,Boolean.TRUE)));

			BigDecimal totalPaidAmt = new BigDecimal(Double.toString(memPayDetails.stream().mapToDouble(PaymentDetail::getAmountPaid).sum()));		
			totalPaidAmt = totalPaidAmt.setScale(2, Constant.BIG_DECIMAL_DEFAULT_ROUNGDING_MODE);
			if (totalPaidAmt.doubleValue() != memPayment.getPaymentAmt().doubleValue()) 
			{
				paymentProcessOutput.setStatus(PaymentPostStatus.ERROR);
				paymentProcessOutput.setComments("Group Invoice Amount [" + totalPaidAmt + "] does not match with transaction amount [" + memPayment.getPaymentAmt() + "],Payment not processed.");
			} 
			else 
			{
				memPayDetails.forEach(paymentDetailItem -> {
					paymentDetailItem.setTransactionDate(memPayment.getTransactionDate());

					if (null != memPayment.getForteTransactionId() && !memPayment.getForteTransactionId().isEmpty()) {
						paymentDetailItem.setForteTransactionId(memPayment.getForteTransactionId());
					}
					payment.addPaymentDetail(paymentDetailItem);
					paymentDetailItem.setPayment(payment);

				});				
				payment.setTotalPaid(totalPaidAmt.doubleValue());
				paymentProcessOutput.setComments("Group payment processed to Invoice with invoice date [" + invDateStr + "].");
				paymentProcessOutput.setStatus(PaymentPostStatus.SUCCESS);

			}
		}
	}



	/**
	 * @param payment
	 * @param invoice
	 * @param memberPayment
	 */
	private void updatePaymentFromInvoice(Payment payment, Invoice invoice,MemberPayment memberPayment) {
		Set<InvoiceDetail> invDetails = invoice.getInvoiceDetail();
		for(InvoiceDetail invDetail: invDetails) 
		{
			Integer memberId = invDetail.getMember().getMemberId();
			Integer productId = invDetail.getProduct().getProductId();
			Set<PaymentDetail> memPayDetails = paymentService.createPaymentDetail(memberId,memberPayment.getTransactionDate(),Boolean.FALSE,Boolean.TRUE);
			
			memPayDetails = memPayDetails.stream().filter(item->item.getMemberId().compareTo(memberId)==0 
					&& item.getProductId().compareTo(productId)==0).collect(Collectors.toSet());
			
			Double payAmt = memPayDetails.stream().mapToDouble(PaymentDetail::getAmountPaid).sum();
			payAmt = new BigDecimal(Double.toString(payAmt.doubleValue())).setScale(2, Constant.BIG_DECIMAL_DEFAULT_ROUNGDING_MODE).doubleValue();
			if(memPayDetails.size() > 1) 
			{
				log.error("Error in updatePaymentFromInvoice, Found [{}] payment details for member [{}]",memPayDetails.size(),memberId);
			}
			else if(payAmt.doubleValue() != invDetail.getAmountDue().doubleValue()) 
			{
				log.error("Error in updatePaymentFromInvoice, Member Amount [{}] and Invoice Amount [{}] do not match ",payAmt,invDetail.getAmountDue());
			}
			else 
			{
				memPayDetails.forEach(item->
				{
					item.setAmountPaid(invDetail.getAmountDue());
					item.setTransactionDate(memberPayment.getTransactionDate());
					if(null != memberPayment.getForteTransactionId())
						item.setForteTransactionId(memberPayment.getForteTransactionId());
					payment.addPaymentDetail(item);
					item.setPayment(payment);
				});				
			}
		}
	}



	/**
	 * @param groupCode
	 * @param memberPayments
	 * @param createInvoice
	 * @param ptsPaymentAppuser 
	 * @return
	 */
	private Set<PaymentProcessOutput> validateInputGroup(String groupCode, List<MemberPayment> memberPayments, boolean createInvoice, Integer ptsPaymentAppuser) {
		Set<PaymentProcessOutput> outputList = new HashSet<>();
			
		if(createInvoice)
		{
			Optional<GroupEntity.GroupSummary> optGroup = groupRepository.findByGroupCode(groupCode, GroupEntity.GroupSummary.class);
			if(!optGroup.isPresent())
			{
				memberPayments.forEach(paymentItem-> {
					PaymentProcessOutput outputItem = new PaymentProcessOutput();
					outputItem.setAmountPaid(paymentItem.getPaymentAmt());
					outputItem.setTransactionDate( paymentItem.getTransactionDate() );
					outputItem.setGroupCd(groupCode);
					outputItem.setStatus(PaymentPostStatus.ERROR);
					outputItem.setComments("Invalid group code.["+groupCode+ "] Payment not processed.");
					outputItem.setTransactionId(paymentItem.getForteTransactionId());
					outputItem.setProcessedBy(ptsPaymentAppuser);
					try {
					outputItem.setMemberId(Integer.valueOf(paymentItem.getMemberIdentifier()));
					}catch(Exception e) {
						log.error("Exception while parsing Forte customer id, Message :[{}], Transaction ID:[{}]",e.getMessage(),paymentItem.getForteTransactionId());
					}
					outputList.add(outputItem);
				});
			}
		}
		
		return outputList;
	}

	/**
	 * @param payment
	 * @param ptsPaymentAppuser 
	 */
	private void setDefaultsForPayment(Payment payment, Integer ptsPaymentAppuser,String note) {
		payment.setAdjustEntry(0.0);
		payment.setCancel(false);
		payment.setCreateDate(new java.util.Date());
		payment.setCancelDate(Constant.DEFULT_DATE_1900);
		payment.setEmployeeId(ptsPaymentAppuser);
		payment.setNote(note);
		payment.setOrderId("");
		payment.setPayMasa(Boolean.FALSE);
		payment.setModifiedBy(ptsPaymentAppuser);
		payment.setModifiedDate(new Date());
		payment.getPaymentDetail().forEach(payDetail-> {
			payDetail.setCreatedBy(ptsPaymentAppuser);
			payDetail.setModifiedBy(ptsPaymentAppuser);
		});
	}

	/**
	 * @param payment
	 * @param ptsPaymentAppuser 
	 */
	private void reviewPaymentsForCommission(Payment payment, Integer ptsPaymentAppuser,String groupCode) {
		
		Set<PaymentDetail> newPayDetails = new HashSet<>();
		
		Integer divId = groupService.getGroupDivision(groupCode);
		boolean isInternationalDiv = PTSUtilityService.isInternationalDivision(divId);
		
		for(PaymentDetail paymentDetail : payment.getPaymentDetail()) {
			
			double prdTotalPaid=0.0;
			double prdYearAmount=0.0;
			double amtToMatchMax=0;
			double nextPayDetailAmt=0;
			double currentPayPremAmt=0;
			double currentPayInitAmt=0;
			double currenyPayAfterTaxAmt=0;
			double originalAmt = paymentDetail.getAmountPaid();
			double masaPaidAmt=0;
			
			if(Boolean.TRUE.equals(paymentDetail.getIsNew())) {	
				
				Member.MemberSummary memberSummary = memberRepository.findByMemberId(paymentDetail.getMemberId(), Member.MemberSummary.class).get();
				
				Set<MemberFee> memberFees =  memberProductFeeService.calculateMemberBalanceForProduct(paymentDetail.getMemberId(),paymentDetail.getProductId(),Boolean.FALSE);
				
				MemberFee memberInitFee = memberFees.stream().filter(item-> Constant.SETUP_FEE_IDS.contains(item.getFeeDetails().getFeeId())).findFirst().orElse(new MemberFee());
				
				OptionalDouble optPrdTotalPaid  = memberFees.stream()
						.filter(item-> !Constant.SETUP_FEE_IDS.contains(item.getFeeDetails().getFeeId())
								&& item.getGroup().getGroupCode().equalsIgnoreCase(groupCode)
								)
						.mapToDouble(MemberFee::getAmountPaid)
						.findFirst();
				
				prdTotalPaid = optPrdTotalPaid.isPresent() ? optPrdTotalPaid.getAsDouble() : 0.0;
				
				log.info("===== First product total paid : "+prdTotalPaid);

				
				masaPaidAmt = memberFees.stream().filter(item-> !Constant.SETUP_FEE_IDS.contains(item.getFeeDetails().getFeeId())).mapToDouble(item->item.getAmountPaidMasa()).findFirst().getAsDouble();
				prdTotalPaid = (prdTotalPaid - masaPaidAmt);
				log.info("===== Second product total paid : "+prdTotalPaid);
				prdYearAmount = memberProductFeeService.calculateProductYearAmount(memberSummary,memberFees);
				log.info("=====  product prdYearAmount : "+prdYearAmount);

				Map<String,Double> paidAmtsMap = memberProductFeeService.getFeeBreakDownForPayment(memberSummary,memberInitFee,paymentDetail);				
				
				if(memberService.isGroupPartOfDivisionWithNewCommissionProcess(paymentDetail.getMemberId()))
				{
					boolean upgradePrd = memberFees.stream().anyMatch(item->item.getProduct().getProductId().compareTo(paymentDetail.getProductId())==0 
							&& item.getProduct().getUpgradeProduct().intValue() !=0 );
							
					log.info("===== upgradePrd: "+upgradePrd);

					if(isInternationalDiv && upgradePrd) {
						prdTotalPaid = paymentService.getMemberTotalPaidCommAmount(paymentDetail.getMemberId(), memberSummary.getReinstateDate(), memberFees, 
								memberSummary.getGroupGroupCode(),paymentDetail.getProductId());
						log.info("===== Third product total paid : "+prdTotalPaid);

					}
					else {
						prdTotalPaid = paymentService.getMemberTotalPaidCommAmount(paymentDetail.getMemberId(), memberSummary.getReinstateDate(), memberFees, 
								memberSummary.getGroupGroupCode(),isInternationalDiv);	
						log.info("===== Fourth product total paid : "+prdTotalPaid);

					}
										
				}				
				currentPayPremAmt = paidAmtsMap.get("PREMIUM_AMT");
				currenyPayAfterTaxAmt =paidAmtsMap.get("AFTER_TAX_AMT");
				currentPayInitAmt =  currenyPayAfterTaxAmt - currentPayPremAmt;
				log.info("===== currentPayPremAmt : "+currentPayPremAmt);
				if( (currentPayPremAmt+prdTotalPaid) > prdYearAmount ) {
					amtToMatchMax = prdYearAmount - prdTotalPaid;
					nextPayDetailAmt = currentPayPremAmt - amtToMatchMax;
				}
				log.info("===== amtToMatchMax : "+amtToMatchMax);

				log.info("===== nextPayDetailAmt : "+nextPayDetailAmt);

				if(amtToMatchMax>0 && nextPayDetailAmt>0) {
					double tmpAmt = (amtToMatchMax + currentPayInitAmt) * (1+paymentDetail.getTaxRate());
					paymentDetail.setAmountPaid(tmpAmt);
					paymentDetail.setPeriodFee(amtToMatchMax);									
					PaymentDetail nextPayDetail = getCopyOfPayDetail(paymentDetail,ptsPaymentAppuser);
					nextPayDetail.setAmountPaid(nextPayDetailAmt * (1+paymentDetail.getTaxRate()));
					nextPayDetail.setPeriodFee(nextPayDetailAmt);
					nextPayDetail.setIsNew(false);
					nextPayDetail.setPayment(payment);
					newPayDetails.add(nextPayDetail);
					boolean match = (originalAmt - (paymentDetail.getAmountPaid()+nextPayDetail.getAmountPaid())) == 0;
					log.info("Payment Split=== Original Amt [{}] Payment 1: Amt Paid [{}]Period Fee [{}] \nPayment 2: Amt Paid [{}]Period Fee [{}] \n Amtount Match ? [{}]"
							,originalAmt,paymentDetail.getAmountPaid(),paymentDetail.getPeriodFee(),nextPayDetail.getAmountPaid(),nextPayDetail.getPeriodFee(),match);
				}
								
			}
		}
		if(!newPayDetails.isEmpty()) {
			payment.getPaymentDetail().addAll(newPayDetails);
		}
	}

	/**
	 * @param paymentDetail
	 * @param ptsPaymentAppuser 
	 * @return
	 */
	private PaymentDetail getCopyOfPayDetail(PaymentDetail paymentDetail, Integer ptsPaymentAppuser) {
		
		PaymentDetail nextPaymentDetail = new PaymentDetail();
		nextPaymentDetail.setAmountPaid(paymentDetail.getAmountPaid());
		nextPaymentDetail.setCommPeriod(paymentDetail.getCommPeriod());
		nextPaymentDetail.setHasTax(paymentDetail.getHasTax());
		nextPaymentDetail.setIsChargeBack(paymentDetail.getIsChargeBack());
		nextPaymentDetail.setIsCommission(paymentDetail.getIsCommission());
		nextPaymentDetail.setIsNew(paymentDetail.getIsNew());
		nextPaymentDetail.setMemberId(paymentDetail.getMemberId());
		nextPaymentDetail.setPeriodFee(paymentDetail.getPeriodFee());
		nextPaymentDetail.setProductId(paymentDetail.getProductId());
		nextPaymentDetail.setRenewDate(paymentDetail.getRenewDate());
		nextPaymentDetail.setTaxRate(paymentDetail.getTaxRate());
		nextPaymentDetail.setTransactionDate(paymentDetail.getTransactionDate());
		nextPaymentDetail.setAlternatePayorId(paymentDetail.getAlternatePayorId());		
		nextPaymentDetail.setCreatedBy(ptsPaymentAppuser);
		nextPaymentDetail.setModifiedBy(ptsPaymentAppuser);
		nextPaymentDetail.setCreatedDate(new Date());
		nextPaymentDetail.setModifiedDate(new Date());
		
		return nextPaymentDetail;
	}

	/**
	 * @param payment
	 */
	private Map<Integer,BigDecimal> payAdavanceCommission(Payment payment) 
	{
		Map<Integer,BigDecimal> memAdvComm = new HashMap<>();
		Set<PaymentDetail> negativePayments = new HashSet<>();
		
		for(PaymentDetail paymentDetailItem: payment.getPaymentDetail())
		{
			/*if(memberService.isMemberPartOfInternationalDivision(paymentDetailItem.getMemberId()))
			{
				paymentService.processAdvanceCommForMember(paymentDetailItem);
			}
			*/
			if(paymentDetailItem.getAmountPaid() > 0.0)
			{
				BigDecimal tmpCommAmt = commissionService.payAdanceCommissionWithMemPayment(paymentDetailItem);
				memAdvComm.put(paymentDetailItem.getMemberId(), tmpCommAmt);
			}
			else if(paymentDetailItem.getAmountPaid() < 0.0)
			{ //-ve payment
				negativePayments.add(paymentDetailItem);
			}
		}			
		return memAdvComm;
	}
	
	/** Update member Renew Date based on the amount paid and Invoice Date.
	 * @param paymentDetails
	 * @param outputList 
	 * @param ptsPaymentAppuser 
	 */
	private void updateMemberRenewAndInvoiceDate(Set<PaymentDetail> paymentDetails, Set<PaymentProcessOutput> outputList, Integer ptsPaymentAppuser
			,String groupCode) {
		
		List<Integer> memberIdsList = paymentDetails.stream().map(PaymentDetail::getMemberId).collect(Collectors.toList());
		try {
			Collector<PaymentDetail, ?, Double> summingPayments = Collectors.summingDouble(PaymentDetail::getPeriodFee);
			
			//member id and amount paid map
			Map<Integer, Double> memPaidAmts = paymentDetails.stream().collect(Collectors.groupingBy(PaymentDetail::getMemberId, HashMap::new, summingPayments));

			Iterator<Integer> memIdsIter = memPaidAmts.keySet().iterator();
			
			Map<Integer,Date> memberIdRenewDateSet = new HashMap<>();
			Map<Integer,Date> memberIdOldRenewDateSet = new HashMap<>();
			Map<Integer,Date> memberIdTransctionDateSet ;
			
			memberIdTransctionDateSet = paymentDetails.stream().collect(Collectors.toMap(PaymentDetail::getMemberId, PaymentDetail::getTransactionDate,(paydet1,paydet2)->paydet1));
			
			while (memIdsIter.hasNext()) {
				
				Integer memberId = memIdsIter.next();

				//
				Optional<Member.MemberInfo> optMemInfo = this.memberService.getMemberDetails(memberId, Member.MemberInfo.class);
				Date oldRenewDate = optMemInfo.get().getRenewDate();
				memberIdOldRenewDateSet.put(memberId, oldRenewDate);
				//
				Date lastPayDate = memberIdTransctionDateSet.getOrDefault(memberId, new java.util.Date());
				Date newRenewDate =  this.memberService.updateMemberRenewDate(memberId,memPaidAmts.get(memberId),ptsPaymentAppuser,
						MemberEventType.PAYMENT,lastPayDate,groupCode,lastPayDate);
				
				memberIdRenewDateSet.put(memberId, newRenewDate);
				
			}
			
			//set renew date to payment detail for use in downstream process
			for(PaymentDetail paymentDetail : paymentDetails) {			
				paymentDetail.setRenewDate(memberIdRenewDateSet.get(paymentDetail.getMemberId()));
				paymentDetail.setOldRenewDate(memberIdOldRenewDateSet.get(paymentDetail.getMemberId()));
			}
			for(PaymentProcessOutput paymentProcessOutput : outputList) {
				paymentProcessOutput.setRenewDate(memberIdRenewDateSet.get(paymentProcessOutput.getMemberId()) );
				paymentProcessOutput.setRenewDateOld(memberIdOldRenewDateSet.get(paymentProcessOutput.getMemberId()) );
			}
		} catch (Exception e) {			
			String memIds =  java.util.Arrays.toString(memberIdsList.toArray());
			log.error("Error while updating member information , Member ID {} Exception [{}] ",memIds,e.getMessage());
		}
	}

	/**
	 * @param memPayDetails
	 * @param ptsPaymentAppuser 
	 * @return
	 */
	private Set<InvoiceDetail> getInvoiceDetailFromPayDetails(Set<PaymentDetail> memPayDetails, Integer ptsPaymentAppuser) {

		Set<InvoiceDetail> invoiceDetails = new HashSet<>();

		memPayDetails.forEach(payDetailsItem -> {
			InvoiceDetail invDetail = new InvoiceDetail();
			invDetail.setAmountDue(payDetailsItem.getAmountPaid());
			invDetail.setHasTax(payDetailsItem.getHasTax());
			double initFee = Boolean.TRUE.equals(payDetailsItem.getHasTax())
					? (payDetailsItem.getAmountPaid() * (1 + payDetailsItem.getTaxRate()) - payDetailsItem.getPeriodFee())
					: (payDetailsItem.getAmountPaid() - payDetailsItem.getPeriodFee());
			invDetail.setInitFee(initFee);
			invDetail.setMember(memberService.findMemberById(payDetailsItem.getMemberId()).get()) ;//payDetailsItem.getMemberId());
			invDetail.setPeriodFee(payDetailsItem.getPeriodFee());
			invDetail.setProduct(productService.getProductById(payDetailsItem.getProductId()).get());//payDetailsItem.getProductId());
			invDetail.setRemoved(false);
			invDetail.setRemovedDate(Constant.DEFULT_DATE_1900);
			invDetail.setRenewDate(payDetailsItem.getRenewDate());			
			invDetail.setInvoiceDate(getDateWithFirstDateOfMonth(payDetailsItem.getTransactionDate()));
			invDetail.setTaxRate(payDetailsItem.getTaxRate());
			invDetail.setCreatedBy(ptsPaymentAppuser);
			invDetail.setCreatedDate(new java.util.Date());
			invDetail.setModifiedBy(ptsPaymentAppuser);
			invDetail.setModifiedDate(new java.util.Date());
			invoiceDetails.add(invDetail);
		});
		return invoiceDetails;
	}

	
	/**
	 * @param memPayment
	 * @param paymentDetailSet
	 * @param memberId
	 * @param paymentProcessOutput
	 * @param ptsPaymentAppuser
	 */
	public void processPaymentChargeback(MemberPayment memPayment,Set<PaymentDetail> paymentDetailSet,Integer memberId,PaymentProcessOutput paymentProcessOutput, Integer ptsPaymentAppuser) {
		
		
		if(!paymentDetailSet.isEmpty()) { //member has multiple products/payment details
			BigDecimal chargeBackAmt = memPayment.getPaymentAmt();
			chargeBackAmt = chargeBackAmt.negate();
			BigDecimal totalPaid = new BigDecimal(Double.toString(paymentDetailSet.stream().mapToDouble(PaymentDetail::getAmountPaid).sum()));
			
			if(chargeBackAmt.compareTo(totalPaid)==0) {
				String comments= "";
				Date newRenewDate = Constant.DEFULT_DATE_1900;
				Date oldRenewDate = newRenewDate;
				Set<Integer> memIdsProcessed = new HashSet<>();
				Set<Integer> pmtDetIdsProcessed = new HashSet<>();
				BigDecimal commChargeBackAmt = BigDecimal.ZERO;
				
				boolean successChargeback = false;
				
				for(PaymentDetail paymentDetailRecord : paymentDetailSet) {
				
					if(paymentDetailRecord.getIsChargeBack()) {
						comments = comments.concat("Payment already charged back, PDID [" + paymentDetailRecord.getId() + "]");
						paymentProcessOutput.setStatus(PaymentPostStatus.SKIP);
						paymentProcessOutput.setComments(comments);
					}
					else {//process charge back on the payment detail record
					
						try {
							paymentDetailRecord.setIsChargeBack(true);
							paymentDetailRecord.setModifiedBy(ptsPaymentAppuser);
							paymentDetailRecord.setModifiedDate(new java.util.Date());
							paymentDetailRecord.setLastChargebackDate(new java.util.Date());							
							String newNote = paymentDetailRecord.getNote() != null
									? paymentDetailRecord.getNote()
											.concat("\nPmtDet[" + paymentDetailRecord.getId() + "] CB by paymentapp on " + LocalDateTime.now().format(DATE_TIME_FORMATTER))
									: "\nPmtDet[" + paymentDetailRecord.getId() + "] CB by paymentapp on " + LocalDateTime.now().format(DATE_TIME_FORMATTER);

							if (newNote.length() < 1000)
								paymentDetailRecord.setNote(newNote);
							else {
								log.error("Payment Id[{}] note greater than 1000",paymentDetailRecord.getId());
							}

							this.paymentDetailRepository.save(paymentDetailRecord);

							log.info("Chargeback applied for Member [{}] Pay Det Id [{}]",memberId,paymentDetailRecord.getId());
						} catch (Exception e) {
							log.error("Exception while updating payment note, Message :[ " + e.getMessage() + "], Transaction ID:[" + memPayment.getForteTransactionId() + "] PDID ["
									+ paymentDetailRecord.getId() + "]");
						}
												
						if(memberId.compareTo(paymentDetailRecord.getMemberId())==0) {
							Optional<Member.MemberInfo> optMemInfo = this.memberService.getMemberDetails(memberId, Member.MemberInfo.class);
							oldRenewDate = optMemInfo.get().getRenewDate();
						}
						
						Date paymentInvoiceDate = paymentService.getPaymentInvoiceDate(paymentDetailRecord.getId());
						Date tmpNewRenewDate = this.memberService.updateMemberRenewDate(paymentDetailRecord.getMemberId(),(-paymentDetailRecord.getPeriodFee()),ptsPaymentAppuser,MemberEventType.CHARGEBACK,null
								,paymentProcessOutput.getGroupCd(),paymentInvoiceDate) ;
						if(memberId.compareTo(paymentDetailRecord.getMemberId())==0) {
							newRenewDate = tmpNewRenewDate;
						}
						// update renew date && //check commission
					
						/*
						boolean internationalDivMem = memberService.isMemberPartOfInternationalDivision(paymentDetailRecord.getMemberId());
						Set<Commission> commData = new HashSet<>();
						if(internationalDivMem)
						{
							commData = getAdvanceCommPaidStatusForPayDetail(paymentDetailRecord.getId());
						}	
						*/					
						BigDecimal tempCommAmt = this.commissionService.chargebackCommissionForPayment(paymentDetailRecord.getId());
						commChargeBackAmt = commChargeBackAmt.add(tempCommAmt);
						
						memIdsProcessed.add(paymentDetailRecord.getMemberId());
						pmtDetIdsProcessed.add(paymentDetailRecord.getId());
						/*
						if(internationalDivMem)
						{
							processAdvanceCommCBOrDelete(paymentDetailRecord,commData);
						}
						*/
						successChargeback=true;
					}
				}
				if(successChargeback) {
					paymentProcessOutput.setRenewDate(newRenewDate);
					paymentProcessOutput.setRenewDateOld(oldRenewDate);
					paymentProcessOutput.setComments(comments.concat(!memIdsProcessed.isEmpty() ? 
							"Chargeback processed for Member on Pmt Det "+ java.util.Arrays.toString(pmtDetIdsProcessed.toArray()) : "")
							.concat(" Comm Chargeback Total amt["+NumberFormat.getCurrencyInstance().format(commChargeBackAmt)+"]"));
					paymentProcessOutput.setStatus(PaymentPostStatus.SUCCESS);
					paymentProcessOutput.setCommAmount(commChargeBackAmt);
				}
				//save forte transaction to payment detail mapping
				if("C00".equalsIgnoreCase(paymentProcessOutput.getTransactionResponseCode())) {					
					paymentDetailSet.forEach(item->item.setForteTransactionId(memPayment.getForteTransactionId()));
					//save forte transaction-payment detail details
					this.paymentService.savePaymentDetailForteTransactions(paymentDetailSet,ptsPaymentAppuser);
				}
			}
			else if(chargeBackAmt.compareTo(totalPaid)!=0) {
				paymentProcessOutput.setComments("PTS and Forte amounts don't match. Chargeback not processed.");
				paymentProcessOutput.setStatus(PaymentPostStatus.ERROR);
			}
			
		}
		else {
			paymentProcessOutput.setComments("No corresponding record found in PTS. Chargeback not processed.");
			paymentProcessOutput.setStatus(PaymentPostStatus.ERROR);
		}		
	}

	/*
	private void processAdvanceCommCBOrDelete(PaymentDetail paymentDetailItem, Set<Commission> commData) {
	
		log.info("In processAdvanceCommCBOrDelete for Member ID[{}]",paymentDetailItem.getMemberId());
		
		try {
			boolean paymentExists = paymentService.paymentExistsByMemberIdAndProductId(paymentDetailItem.getMemberId(), paymentDetailItem.getProductId());
			if (paymentExists)
				return;
			
			if (!commissionService.advanceCommExistsForMemberAndProduct(paymentDetailItem.getMemberId(),paymentDetailItem.getProductId()))
				return;
			 
			//if advance comm are not paid - delete advance comm records.
			//if advance comm are paid - create a CB record with amt as total advance comm amount.
			
			commData.forEach(item -> {
				boolean isPaid = item.getPaid();
				item.getCommDetails().forEach(detailItem -> {
					if (detailItem.getPayDetailId().intValue() == 0
							&& detailItem.getMemberId().compareTo(paymentDetailItem.getMemberId()) == 0
							&& detailItem.getProductId().compareTo(paymentDetailItem.getProductId()) == 0
							&& (detailItem.getAdvance().intValue() == 1 || detailItem.getAdvance().intValue() == 2)) {
						if (isPaid) 
						{
							log.info("Commission ID[{}] is Paid, Creating CB Commission for Member ID,[{}]",detailItem.getId(),detailItem.getMemberId());
							commissionService.createChargebackComm(detailItem, item.getCommTotalAmount());						
							detailItem.setChargeback(Boolean.TRUE);
							commissionService.saveCommissionDetailRow(detailItem);
						} else 
						{
							log.info("Commission ID[{}] is UnPaid, Removing commission for Member ID,[{}]",detailItem.getId(),detailItem.getMemberId());
							commissionService.deleteCommissionDetailRow(detailItem);
						}
					}
				});
			});
		} 
		catch (Exception e) 
		{
			log.error("In processAdvanceCommCBOrDelete , Error processing Adv commission. Message [{}] Cause [{}]",e.getMessage(),e.getCause());
		}
	}
	
	private void removeUnMappedAdvCommRecord(Commission commission, PaymentDetail paymentDetailItem) {
		commission.getCommDetails().forEach(detailItem->{			
			if(detailItem.getPayDetailId().intValue()==0 && 
					detailItem.getMemberId().compareTo(paymentDetailItem.getMemberId())==0 
					&& detailItem.getProductId().compareTo(paymentDetailItem.getProductId())==0
					&& (detailItem.getAdvance().intValue()==1 || detailItem.getAdvance().intValue()==2)) 
			{
				commissionService.deleteCommissionDetailRow(detailItem);
			}
		});
	}
	
	private Set<Commission> getAdvanceCommPaidStatusForPayDetail(Integer payDetailId) {
		return commissionService.commissionDataByPayDetail(payDetailId);
	}
	*/


	/**
	 * @param memberIdentifier
	 * @param groupCode
	 * @param appendLeadingZero
	 * @return
	 */
	public String validateEmployeeId(String memberIdentifier, String groupCode, Boolean appendLeadingZero,Date paymentTransDate) {
		
		boolean exists = this.memberEmployeeIdService.isMemberExistsInGroupWithEmployeeIdOnDate(memberIdentifier, groupCode, paymentTransDate);
		if(exists)
			return memberIdentifier;
		
		String employeeId = memberIdentifier;
		if(Boolean.TRUE.equals(appendLeadingZero)){			
			while(employeeId.length()< 9 && !exists) 
			{	
				employeeId="0"+employeeId;
				exists = this.memberService.isEmployeeExistsInGroup(groupCode, employeeId);
			}			
		}
		return employeeId;
	}
	/**
	 * @param memberId
	 * @param productId
	 * @return
	 */
	public boolean memberHasPayments(Integer memberId,Integer productId,Date paymentDate) {
		
		if(null == paymentDate)
			paymentDate = Constant.DEFULT_DATE_1900;
			
		Set<PaymentDetail.PaymentDetailSummary> payDetails = this.paymentDetailRepository.findAllByMemberIdAndProductIdAndPaymentPayDateLessThanAndVoidPaymentOrderByTransactionDate(memberId, productId, paymentDate,Boolean.FALSE,PaymentDetail.PaymentDetailSummary.class);
		
		return !payDetails.isEmpty();
	}
}
