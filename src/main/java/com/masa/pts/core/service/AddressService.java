package com.masa.pts.core.service;

import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.masa.pts.core.constant.AddressType;
import com.masa.pts.core.domain.Address;
import com.masa.pts.core.domain.AddressHistory;
import com.masa.pts.core.domain.Country;
import com.masa.pts.core.domain.Member;
import com.masa.pts.core.domain.State;
import com.masa.pts.core.repository.AddressHistoryRepository;
import com.masa.pts.core.repository.AddressRepository;
import com.masa.pts.core.repository.CountryRepository;
import com.masa.pts.core.repository.StateRepository;

@Service
public class AddressService {

	@Autowired
	StateRepository stateRepository;
	
	@Autowired
	CountryRepository countryRepository;
	
	@Autowired
	AddressRepository addressRepository;
	
	@Autowired
	AddressHistoryRepository addressHistoryRepository;
	
	public boolean existsByStateSymbol(String stateSymbol) {
		return stateRepository.existsBySymbol(stateSymbol);
	}
	
	public boolean existsByStateAndCountry(String stateCode,Integer countryId) {
		return stateRepository.existsBySymbolAndCountryId(stateCode, countryId);
	}
	public State getStateByCodeAndCountry(String stateCode,Integer countryId)
	{
		return stateRepository.findBySymbolAndCountryId(stateCode, countryId);
	}
	
	public boolean existsByCountryCode(String countryCode) {
		return countryRepository.existsByName(countryCode);
	}
	
	public String getStateCodeById(Integer stateId) {
		Optional<State> state = stateRepository.findById(stateId);
		return state.isPresent() ? state.get().getSymbol() : "";
	}
	
	public Country getCountryByCode(String countryCode) {
		return countryRepository.findByName(countryCode);
	}
	
	public Country getCountryByCodeWithStates(String countryCode) {
		Country cntry =  countryRepository.findByName(countryCode);
		
		Iterable<State> states = stateRepository.findAllByCountryId(cntry.getCountryId());

		cntry.setStates(StreamSupport.stream(states.spliterator(), true).collect(Collectors.toSet()));
		
		return cntry;
	}
	
	public String getCountryNameById(Integer countryId) {
		Optional<Country> country = countryRepository.findById(countryId);
		return country.isPresent() ? country.get().getName() : "";
	}
	
	public Address saveAddress(Address address) {
		addressRepository.save(address);
		return address;
	}
	
	public Address getAddressById(Integer addressId) {
		return addressRepository.findByaddressId(addressId);		
	}
	
	public Iterable<State> getAllStates()
	{
		return stateRepository.findAll();
	}
	
	public Iterable<State> getAllStatesByCountry(Integer countryId)
	{
		return stateRepository.findAllByCountryId(countryId);
	}

	public Iterable<Country> getAllCountry() {

		Iterable<Country> cntry = countryRepository.findByOrderByNameAsc();

		Iterable<State> states = stateRepository.findAllByCountryIdIn(StreamSupport.stream(cntry.spliterator(), true)
				.map(Country::getCountryId).collect(Collectors.toList()));

		StreamSupport.stream(cntry.spliterator(), false).map(country -> {
			country.setStates(StreamSupport.stream(states.spliterator(), true)
					.filter(stateItem -> stateItem.getCountryId().compareTo(country.getCountryId()) == 0)
					.collect(Collectors.toSet()));
			return country;
		}).collect(Collectors.toList());

		return cntry;
	}

	public Address save(Address address) {
		return addressRepository.save(address);
	}
	
	public void delete(Integer addressId) {
		addressRepository.deleteById(addressId);
	}
	
	/**
	 * @param address
	 * @param addressType
	 * @param member
	 * @return
	 */
	public AddressHistory saveAddressToHistory(Address address,AddressType addressType,Member member) {
		if(null == address)
			return null;
		AddressHistory addressHistory = new AddressHistory();
		addressHistory.setAddress1(address.getAddress1());
		addressHistory.setAddress2(address.getAddress2());
		addressHistory.setAddress3(address.getAddress3());
		addressHistory.setCity(address.getCity());
		addressHistory.setState(address.getState());
		addressHistory.setType(addressType);
		addressHistory.setZip(address.getZip());
		addressHistory.setZip4(address.getZip4());
		addressHistory.setCountry(address.getCountry());
		addressHistory.setType(addressType);
		addressHistory.setMemberId(member.getMemberId());
		addressHistory.setLastModifiedDate(new java.util.Date());
		return addressHistoryRepository.save(addressHistory);
	}

	/**
	 * @param addressType
	 * @param newAddress
	 * @param currentAddr
	 * @return
	 */
	public boolean isAddressChanged(Address newAddress, Address currentAddr) 
	{
		if(currentAddr ==null)
			return true;
		
		if (!currentAddr.getAddress1().equalsIgnoreCase(newAddress.getAddress1()))
			return true;
		
		if (!currentAddr.getCity().equalsIgnoreCase(newAddress.getCity()))
			return true;
		
		if(currentAddr.getState().intValue() != newAddress.getState().intValue())
			return true;
					
			return false;
	}
	
	public boolean existsBySymbolAndCountryId(String stateSymbol,Integer countryId) {
		return stateRepository.existsBySymbolAndCountryId(stateSymbol,countryId);
	}
	
	
	/**
	 * @param address
	 * @return
	 */
	public boolean isValidAddressForShipping(Address address) {
		
		if(address==null)
			return false;
		
		if(address.getAddressId() <=0)
			return false;
		
		if(!StringUtils.hasText(address.getAddress1()))
			return false;
		
		if(!StringUtils.hasText(address.getCity()))
			return false;
						
		if(address.getState()==null)
			return false;
		
		if(address.getState().intValue()==0)
			return false;
		
		return true;
	}
}
