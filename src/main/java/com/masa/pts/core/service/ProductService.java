package com.masa.pts.core.service;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.masa.pts.core.constant.ProductChangeType;
import com.masa.pts.core.domain.Constant;
import com.masa.pts.core.domain.Product;
import com.masa.pts.core.domain.ProductFee;
import com.masa.pts.core.repository.ProductFeeRepository;
import com.masa.pts.core.repository.ProductRepository;
import com.querydsl.core.types.Predicate;

@Service
public class ProductService {

	ProductRepository productRepository;
	ProductFeeRepository productFeeRepository;
	
	@Autowired
	public ProductService(ProductRepository productRepository,ProductFeeRepository productFeeRepository) {
		this.productRepository = productRepository;
		this.productFeeRepository = productFeeRepository;
	}

	public Map<Integer,Product> getAllProducts() {
		
		Map<Integer,Product> productMap = new HashMap<>();
		
		Iterable<Product> productItr = this.productRepository.findAll();
		
		productItr.forEach(prdItem->{
			productMap.put(prdItem.getProductId(), prdItem);
		});
		
		return productMap;
	}
	
	public Iterable<Product> listAllProducts()
	{
		return this.productRepository.findAll();
	}
	
	public Iterable<Product> listAllProducsByParam(Predicate predicate)
	{
		return this.productRepository.findAll(predicate);
	}
	
	/**
	 * @param productName
	 * @return
	 */
	public Optional<Product> getProductByProductName(String productName) {
		return productRepository.findByNameEquals(productName);		
	}
	
	public boolean existsByName(String productName) {
		return productRepository.existsByName(productName);
	}
	
	public Optional<Product> getProductById(Integer productId){
		return productRepository.findById(productId);
	}
	
	public boolean existsByProductId(Integer productId) {
		return productRepository.existsByProductId(productId);
	}
	
	public Set<ProductFee> getProductFees(Integer productId)
	{
		return productFeeRepository.findAllByProductFeeIdProductIdOrderByProductFeeIdFeeId(productId);
	}
	
	/**
	 * @param scName
	 * @param divName
	 * @return
	 */
	public ProductChangeType getProductChangeType(String scName,String divName)
	{
		if(Constant.PRODUCT_CHANGE_BY_TABLE_SC.contains(scName) || Constant.PRODUCT_CHANGE_BY_TABLE_DIVISION.contains(divName))
			return ProductChangeType.TABLE_DATA;
		else if(Constant.PRODUCT_CHANGE_BY_GRORUP_SC.contains(scName) || Constant.PRODUCT_CHANGE_BY_GRORUP_DIVISION.contains(divName))
			return ProductChangeType.GROUP_DATA;
		else
			return ProductChangeType.GROUP_DATA;
	}
} 
