package com.masa.pts.core.service;

import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.masa.pts.core.domain.Constant;
import com.masa.pts.core.domain.GroupDocument;
import com.masa.pts.core.domain.GroupEntity;
import com.masa.pts.core.domain.GroupEntity.GroupCodeSmry;
import com.masa.pts.core.domain.GroupEntity.GroupIDCodeSmry;
import com.masa.pts.core.domain.GroupProductFee;
import com.masa.pts.core.domain.Invoice;
import com.masa.pts.core.domain.Invoice.GroupInvoiceDates;
import com.masa.pts.core.model.GroupDivisionDTO;
import com.masa.pts.core.model.ProductSale;
import com.masa.pts.core.repository.GroupDocumentRepository;
import com.masa.pts.core.repository.GroupProductFeeRepository;
import com.masa.pts.core.repository.GroupRepository;
import com.masa.pts.core.repository.InvoiceRepository;
import com.masa.pts.core.repository.PaymentRepository;
import com.masa.pts.core.repository.SalesChannelRepository;

@Service
public class GroupService {

	@Autowired
	GroupRepository groupRepository;
	
	@Autowired
	InvoiceRepository invoiceRepository;
	
	@Autowired
	PaymentRepository paymentRepository;
	
	@Autowired
	SalesChannelRepository salesChannelRepository;
	
	@Autowired
	GroupProductFeeRepository groupProductFeeRepository;

	@Autowired
	GroupDocumentRepository groupDocumentRepository;
	
	public GroupEntity getGroupById(Integer groupId) {
		Optional<GroupEntity> optGroup = groupRepository.findById(groupId);
		
		return optGroup.orElseGet(null);
	}
	
	public GroupEntity getGroupByCode(String groupCode) {
		return groupRepository.findGroupWithDetailsByGroupCode(groupCode);
	}
	/**
	 * @return Set of Group Codes
	 */
	public Set<String> getAllGroupCodes() {
		
		Set<GroupCodeSmry> grpSmrySet = this.groupRepository.findAllByOrderByGroupCodeAsc();
		Set<String> grpCodeSet = new HashSet<>();
		grpSmrySet.forEach(item->{
			grpCodeSet.add(item.getGroupCode().toUpperCase());
		});
		return grpCodeSet;
	}
	
	public Map<Integer,String> getGroupCodeByIds(Set<Integer> groupIds)
	{
		Set<GroupIDCodeSmry> grpSmrySet = this.groupRepository.findAllByGroupIdInOrderByGroupCodeAsc(groupIds);
		
		return grpSmrySet.stream().collect(Collectors.toMap(GroupIDCodeSmry::getGroupId, GroupIDCodeSmry::getGroupCode));
	}
	
	public Set<GroupEntity> getGroupSetByIds(Set<Integer> groupIds){
		return groupRepository.findAllByGroupIdInOrderByGroupNameAsc(groupIds);
	}
	
	/**
	 * @return
	 */
	/*
	public Map<String,GroupDetailWithSalesChannelDivision> getAllGroupDetailsWithDivisionSaleschannel() {
		
		Set<GroupDetailWithSalesChannelDivision> grpSmrySet = this.groupRepository.findAllByOrderByGroupCodeAsc(null);
		
		Map<String,GroupDetailWithSalesChannelDivision> groupInfo = new HashMap<String,GroupDetailWithSalesChannelDivision>();
		
		grpSmrySet.forEach(groupItem->{
			groupInfo.put(groupItem.getGroupCode(), groupItem);
		});
		
		return groupInfo;
	}*/
	
	/**
	 * @param groupCd
	 * @param type
	 * @return
	 */
	public <T> Optional<T> getGroupDetailsByGroupCode(String groupCd,Class<T> type) {
		return this.groupRepository.findByGroupCode(groupCd, type);		
	}

	public Date getGroupPastDueEffectiveDateByMemberId(Integer memberId){
		return this.groupRepository.findGroupPastDueEffectiveDateByMemberId(memberId);
	}
	
	public Integer getGroupDivision(String groupCode) {
		Optional<GroupDivisionDTO> optGrp = getGroupDivisionInfo(groupCode);
		return optGrp.isPresent() ? optGrp.get().getDivisionId() : 0;				
	}
	public boolean isGroupInvoiceDateFirstOfMonth(String groupCode) {
		int divId = getGroupDivision(groupCode);
		
		return (divId == Constant.B2B_DIVISION_ID);
	}
	
	public boolean isExistsByGroupCode(String groupCode) {
		return this.groupRepository.existsByGroupCode(groupCode);
	}

	public boolean isExistsAndActiveByGroupCode(String groupCode) {
		return this.groupRepository.existsByGroupCodeAndActiveIsTrue(groupCode);
	}

	public boolean isExistsByGroupCodeAnActive(String groupCode) {
		return this.groupRepository.existsByGroupCodeAndActive(groupCode, Boolean.TRUE);
	}
	/**
	 * @param excludeDivisionForCBUCB
	 * @return
	 */
	public Set<String> getGroupCodesByDivision(List<String> excludeDivisionForCBUCB) {
		
		Set<String> grpCodeSet = new HashSet<>();
		if(excludeDivisionForCBUCB.isEmpty()) {
			return grpCodeSet;
		}
		
		Set<GroupDivisionDTO> grpSmrySet = this.groupRepository.findAllByDivisionInOrderByGroupCodeAsc(excludeDivisionForCBUCB);
		
		return grpSmrySet.stream().map(item->item.getGroupCode().toUpperCase()).collect(Collectors.toSet());
	}
	
	public Optional<GroupDivisionDTO> getGroupDivisionInfo(String groupCode) {
		GroupDivisionDTO group =  groupRepository.findGroupDivisionSalesChannelByGroupCode(groupCode);
		if(null == group) {
			return Optional.empty();
		}
		return Optional.of(group);
	}
	
	/**
	 * @param groupCode
	 * @param invoiceDate
	 * @return
	 */
	public Boolean isGroupInvoiceExists(String groupCode,java.util.Date invoiceDate) {

		long count = invoiceRepository.countByGroupGroupCodeAndInvoiceDate(groupCode,invoiceDate);
		
		return count > 0;
	}
	
	/**
	 * @param groupCode
	 * @param invoiceDate
	 * @return
	 */
	public Optional<Invoice> getGroupInvoiceByInvoiceDate(String groupCode, java.util.Date invoiceDate){
		return invoiceRepository.findByGroupGroupCodeAndInvoiceDate(groupCode, invoiceDate);
	}
	
	/**
	 * @param groupCode
	 * @param invoiceStartDate
	 * @param invoiceEndDate
	 * @return
	 */
	public Optional<Invoice> getGroupFirstInvoiceByInvoiceDateRange(String groupCode, Date invoiceStartDate,Date invoiceEndDate){
		
		List<GroupInvoiceDates> invDates = invoiceRepository.findByGroupGroupCodeAndInvoiceDateGreaterThanEqualAndInvoiceDateLessThanEqualOrderByInvoiceDate(groupCode,invoiceStartDate,invoiceEndDate);
		
		if(invDates.isEmpty())
			return Optional.empty();
		
		return invoiceRepository.findByGroupGroupCodeAndInvoiceDate(groupCode, invDates.get(0).getInvoiceDate());
	}
	
	public Boolean isDownPaymentGroup(String groupCode) {
		return groupRepository.findByGroupCodeAndDownPaymentGrp(groupCode, Boolean.TRUE).isPresent();
	}
	
	public Boolean isLifeTimeGroup(String groupCode) {
		return groupRepository.findByGroupCodeAndLifetimeGrp(groupCode, Boolean.TRUE).isPresent();
	}
	
	/**
	 * @param groupCode
	 * @param invoiceDate
	 * @return
	 */
	public Optional<Date> getGroupInvoicePaymentDate(String groupCode, java.util.Date invoiceDate) {
		Optional<Invoice> optInv = invoiceRepository.findByGroupGroupCodeAndInvoiceDate(groupCode, invoiceDate);
		Optional<Date> paymentDate = java.util.Optional.empty();
		
		if(optInv.isPresent() && Boolean.TRUE.equals(optInv.get().getIsPaid()) && optInv.get().getPayment() != null) 
		{
			paymentDate =  Optional.ofNullable(optInv.get().getPayment().getPayDate());
		}		
		return paymentDate;
	}
	
	public Set<Invoice> getTop10GroupInvoices(Integer groupId){
		return this.invoiceRepository.findTop10ByGroupGroupIdOrderByInvoiceDateDesc(groupId);
	}
	
	public Set<Invoice> getAllGroupInvoicesByGroupCode(String groupCode,boolean isPaid){
		return this.invoiceRepository.findAllByGroupGroupCodeAndIsPaidOrderByInvoiceDateDesc(groupCode,isPaid);
	}
	
	public Optional<Invoice> getInvoiceByInvoiceId(Integer invoiceId) {		
		return this.invoiceRepository.findById(invoiceId);
	}
	
	/**
	 * @param groupCode
	 * @param productId
	 * @return
	 */
	public boolean isProductValidForGroup(String groupCode,Integer productId) {
		return !groupProductFeeRepository.findAllByGroupGroupCodeAndProductProductId(groupCode, productId).isEmpty();
	}
	
	/**
	 * @param groupCode
	 * @param productId
	 * @return
	 */
	public Integer getProductFrequency(String groupCode, Integer productId) 
	{		
		Set<GroupProductFee> grpPrdFees = groupProductFeeRepository.findAllByGroupGroupCodeAndProductProductId(groupCode,productId);
		return grpPrdFees.stream().filter(item->item.getFeeDetails().getFeeId().intValue() !=1).findFirst().map(item->item.getFrequencyDetails().getID()).orElse(0);		
	}
	
	public double getProductPrice(String groupCode,Integer productId) 
	{
		Set<GroupProductFee> grpPrdFees = groupProductFeeRepository.findAllByGroupGroupCodeAndProductProductId(groupCode,productId);
		return grpPrdFees.stream().filter(item->item.getFeeDetails().getFeeId().intValue() !=1).findFirst().map(GroupProductFee::getAmount).orElse(0.0);		
	}
	
	public double getProductMaxYearAmount(String groupCode,Integer productId)
	{
		//TODO to be updated for 5 year & installament products
		Set<GroupProductFee> grpPrdFees = groupProductFeeRepository.findAllByGroupGroupCodeAndProductProductId(groupCode,productId);
		
		if(grpPrdFees.isEmpty())
			return 0;
		
		GroupProductFee groupPrdFee = grpPrdFees.stream().filter(item->item.getFeeDetails().getFeeId().intValue() !=1).findFirst().orElse(null);
		
		if(null == groupPrdFee || null == groupPrdFee.getFrequencyDetails())
			return 0;
		else if(groupPrdFee.getMaxAmountDue() !=0)
			return groupPrdFee.getMaxAmountDue();
		else
			return groupPrdFee.getAmount() * groupPrdFee.getFrequencyDetails().getYearParts();		
	}
	
	/**
	 * @param groupCodes
	 * @return
	 */
	public Map<String,String> getGroupPortalLogo(Set<String> groupCodes)
	{
		Map<String,String> grpLogoPaths = new HashMap<>();
		Set<GroupDocument> grpDocumentSet = groupDocumentRepository.findAllByDocumentTypeAndGroupGroupCodeInAndIsDeleteOrderByModifiedDate("Portal Logo",groupCodes,Boolean.FALSE);		
		for(GroupDocument item: grpDocumentSet)
		{
			if(grpLogoPaths.getOrDefault(item.getGroup().getGroupCode(), null)==null)
			{
				grpLogoPaths.put(item.getGroup().getGroupCode(), item.getDocumentLink());
			}			
		}		
		return grpLogoPaths;
	}

	public List<ProductSale> getGroupProductsForExternal(String groupCode) {
		List<ProductSale> productSaleList = groupRepository.getGroupProductsForExternal(groupCode);

		if (getGroupDivision(groupCode) == Constant.B2B_DIVISION_ID) {
			// for b2b group - filter only primary product and exclude upgrades
			productSaleList = productSaleList.stream().filter(product -> product.getUpgradeProduct().intValue() == 0)
					.collect(Collectors.toList());
		}
		return productSaleList;
	}
	
	public boolean internalUserHasAccessToGroup(String groupCode,Integer employeeId) {
		return groupRepository.internalUserHasAccessToGroup(groupCode, employeeId);
	}
	
	public Optional<GroupEntity> getByCode(String code) {
		return groupRepository.findByGroupCode(code);
	}
}
