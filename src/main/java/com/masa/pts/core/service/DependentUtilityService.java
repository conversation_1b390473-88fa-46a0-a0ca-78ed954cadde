package com.masa.pts.core.service;

import java.util.Arrays;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.masa.pts.core.domain.Constant;
import com.masa.pts.core.domain.GroupEntity;

@Service
public class DependentUtilityService {

	private static final List<Integer> wySCIds = Arrays.asList(66, 97, 98, 99,104);
	private static final List<Integer> usviSCIds = Arrays.asList(13);
	private static final int US_DEPENDENT_AGE_LIMIT = 26;
	private static final int NON_US_DEPENDENT_AGE_LIMIT = 23;
	private static final Integer WY_DEPENDENT_AGE_LIMIT = null;// no limit

	private GroupService groupService;

	@Autowired
	public DependentUtilityService(GroupService groupService) {
		this.groupService = groupService;
	}

	public Integer getDepedentAgeLimit(Integer scId, Integer divId) {
		if (wySCIds.contains(scId)) {
			return WY_DEPENDENT_AGE_LIMIT;
		} // if international div and not usvi
		else if (Constant.INTERNATIONAL_DIVISION_ID == divId && !usviSCIds.contains(scId)) {
			return NON_US_DEPENDENT_AGE_LIMIT;
		}
		return US_DEPENDENT_AGE_LIMIT;
	}

	public Integer getDepedentAgeLimit(String groupCode) {
		GroupEntity group = groupService.getGroupByCode(groupCode);
		if (group == null) {
			return NON_US_DEPENDENT_AGE_LIMIT;
		}
		Integer scId = group.getBusinessLineEntity().getSalesChannel().getId();
		Integer divId = group.getBusinessLineEntity().getSalesChannel().getDivision().getId();

		return getDepedentAgeLimit(scId, divId);
	}
}
