package com.masa.pts.core.service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.masa.pts.core.constant.MemberActiveStatus;
import com.masa.pts.core.constant.ProductUpgradeType;
import com.masa.pts.core.domain.Constant;
import com.masa.pts.core.domain.Member;
import com.masa.pts.core.domain.Member.MemberInfo;
import com.masa.pts.core.domain.Member.MemberSummary;
import com.masa.pts.core.model.PaymentGroupProductSummaryDTO;
import com.masa.pts.core.domain.MemberCoverageLapse;
import com.masa.pts.core.domain.MemberFee;
import com.masa.pts.core.domain.PaymentDetail;
import com.masa.pts.core.repository.MemberFeeRepository;
import com.masa.pts.core.repository.MemberRepository;

@Service
public class MemberProductFeeService {

	private static final Logger log = LoggerFactory.getLogger(MemberProductFeeService.class);

	@Autowired
	private MemberRepository memberRepository;
	@Autowired
	private PaymentService paymentService;	
	@Autowired
	private MemberFeeRepository memberFeeRepository;
	@Autowired
	private MemberService memberService;

	@Autowired
	private GroupService groupService;
	
	@Value("${installment.twentyMonth.productIds}")
    List<Integer> INSTALLMENT_20_MONTH_PRD_IDS;
	
	/**
	 * @param member
	 * @param memberPartOfDownpaymentGroup 
	 */
	public void getMemberProductBalance(Member member, boolean memberPartOfDownpaymentGroup) {
		
		Set<MemberFee> activeMemFees = member.getMemberFee().stream().filter(memFee-> PTSUtilityService.isDateWithInRange(new java.util.Date(),
				memFee.getEffectiveStartDate(),memFee.getEffectiveEndDate())).collect(Collectors.toSet());
		
		calculateMemberProductFeesAndBalances(member.getMemberId(), activeMemFees,Boolean.TRUE);
		
		Double yearPartsForSetupFee = findSetupFeeYearParts(activeMemFees);
		
		if(memberPartOfDownpaymentGroup)
			calculateDownPaymentMemberOutstandingBalance(member,activeMemFees);
		else
			calculateMemberOutstandingBalance(member,yearPartsForSetupFee);
	}
	
	/**
	 * @param memberFees
	 * @param memberId
	 * @param productId
	 * @param reinstateDate
	 * @return
	 */
	public double productPaidAmount(Set<MemberFee> memberFees,Integer memberId,Integer productId,Date reinstateDate)
	{
		Set<Integer> memProductIds = memberFees.stream().map(item -> item.getProduct().getProductId()).collect(Collectors.toCollection(HashSet::new));
		//Map<Integer, Double> memPaidAmt = paymentService.getMemberTotalPaymentsByProduct(memberId, memProductIds,reinstateDate, true,Constant.DEFULT_DATE_1900); 
		
		List<PaymentGroupProductSummaryDTO> memPaidAmtByGroupPrd = paymentService.getMemberTotalPaymentsByGroupProduct(memberId, reinstateDate, true, Constant.DEFULT_DATE_1900);	
		
		double totalPrdamt = memPaidAmtByGroupPrd.stream().filter(item->item.getProductId().compareTo(productId)==0)
				.collect(Collectors.summingDouble(PaymentGroupProductSummaryDTO::getTotalAmountPaid));
		
		Map<Integer, Double> masaPaidAmt = paymentService.getMasaPaymentsByProduct(memberId,memProductIds,memberFees);		
		return (totalPrdamt-masaPaidAmt.getOrDefault(productId, 0.0));
	}
	
	/**
	 * @param memberId
	 * @param memberFees
	 */
	public void calculateMemberProductFeesAndBalances(Integer memberId,Set<MemberFee> memberFees,boolean balanceForPayHistory) {

		Optional<Member.MemberInfo> optMemberInfo = this.memberRepository.findByMemberId(memberId);
		if(!optMemberInfo.isPresent())
				return;
		
		Member.MemberInfo memberInfo = optMemberInfo.get();
		
		Double yearPartsForSetupFee = findSetupFeeYearParts(memberFees);
		
		calculateMemberFeePrdMultipliers(memberInfo,yearPartsForSetupFee,memberFees);
		
		Set<Integer> memProductIds = memberFees.stream().map(item -> item.getProduct().getProductId()).collect(Collectors.toCollection(HashSet::new));
		
		Date grpPastDueEffectiveDate = Constant.DEFULT_DATE_1900;
		if(balanceForPayHistory)
		{
			grpPastDueEffectiveDate = groupService.getGroupPastDueEffectiveDateByMemberId(memberId);			
		}		
		long start=System.currentTimeMillis();
		//Map<Integer, Double> memPaidAmt = paymentService.getMemberTotalPaymentsByProduct(memberInfo.getMemberId(), memProductIds,memberInfo.getReinstateDate(), true,grpPastDueEffectiveDate);
		List<PaymentGroupProductSummaryDTO> memPaidAmtByGrpPrd =  paymentService.getMemberTotalPaymentsByGroupProduct(memberInfo.getMemberId(), memberInfo.getReinstateDate(), true, grpPastDueEffectiveDate);
		
		long end= System.currentTimeMillis();
		if((end-start)>=Constant.THREASHOLD_TO_LOG)
			log.info("getMemberTotalPaymentsByProduct  method took [{}]",(end-start));
		
		Map<Integer, Double> masaPaidAmt = paymentService.getMasaPaymentsByProduct(memberId,memProductIds,memberFees);
		
		calculateMasaPaymentsByProductForMember(memberFees,masaPaidAmt);
		start=System.currentTimeMillis();
		calculateMemberBalancesByProduct(memberInfo,memberFees,memPaidAmtByGrpPrd,yearPartsForSetupFee);
		end= System.currentTimeMillis();
		if((end-start)>=Constant.THREASHOLD_TO_LOG)
			log.info("calculateMemberBalancesByProduct  method took [{}]",(end-start));
	}	

	/**
	 * @param memberFees
	 * @param masaPaidAmt
	 */
	public void calculateMasaPaymentsByProductForMember(Set<MemberFee> memberFees,Map<Integer, Double> masaPaidAmt){
		for(MemberFee fee: memberFees) {
			fee.setAmountPaidMasa(masaPaidAmt.getOrDefault(fee.getProduct().getProductId(), 0.0));
		}
	}
	
	/**
	 * @param member
	 */
	public void calculateDownPaymentMemberOutstandingBalance(Member member,Set<MemberFee> memActiveFees) {		
		
		Optional<MemberFee> optInstallmentFee = memActiveFees.stream().filter(item->item.getFeeDetails().getFeeId()!=1 && "INSTALLMENT".equals(item.getFeeDetails().getFeeType())
				&& item.getUpgradeType().intValue()==0 ).findFirst();
	
		Date firstInstallmentPostDate;
		double memTaxRate = 0;
		
		if(optInstallmentFee.isPresent()) {
			
			List<PaymentDetail> payDetailList = paymentService.getAllPaymentDetailsForMemberByProduct(member.getMemberId(), optInstallmentFee.get().getProduct().getProductId());
			double downPayment =  !payDetailList.isEmpty() ? payDetailList.get(0).getPeriodFee() : 0;
			firstInstallmentPostDate = getMemberFirstInstallmentPostDate(member.getEffectiveDate(),optInstallmentFee.get().getProduct().getProductId(),payDetailList);
			
			Integer memberFeeId = optInstallmentFee.get().getId();
			
			for(MemberFee memberFee: memActiveFees)
			{
				Double maxAmountDue = memberFee.getMaxAmountDue();
				Double amountPaid = memberFee.getAmountPaid();
				
				if(memberFeeId.compareTo(memberFee.getId())==0) {//installment product						
					maxAmountDue = (memberFee.getMaxAmountDue() - downPayment);
					amountPaid = (memberFee.getAmountPaid() - downPayment);
				}
				
				if(maxAmountDue >0 && amountPaid >= maxAmountDue)
				{
					memberFee.setOutstanndingBalance(0.0);
				}
				else
				{
					Date prdEffectiveDt = getProductEffectiveDate(member,memberFee,Constant.DEFULT_DATE_1900);
					Date effectiveDate = getEffectiveDateForInstallPlanMember(prdEffectiveDt,firstInstallmentPostDate);
					
					long  gap = findElapsedFreq(effectiveDate,new java.util.Date(),memberFee.getFrequencyDetails().getID(),member);
					
					if(gap==0 && memberFee.getAmount() >0) 
					{
						gap =1;
					}
					if(effectiveDate.after(new Date())) 
					{
					    memberFee.setOutstanndingBalance(0.0);//if member is not effective yet then dont show any balance.
					}
					else 
					{
					    memberFee.setOutstanndingBalance(  ( Math.round((amountPaid) * 100.0) / 100.0 ) - ( (memberFee.getAmount() * (1+memTaxRate)) * gap ) ) ;					    
					}
					//if maxamtdue is available, outstanding balance cannot be more than amount paid-max amt due.
					if( maxAmountDue >0 &&  (amountPaid - maxAmountDue) < 0 &&  
							(amountPaid- maxAmountDue) > memberFee.getOutstanndingBalance() )
					{
							memberFee.setOutstanndingBalance(amountPaid - maxAmountDue);
					}
				}
			}
		}		
	}
	
	/**
	 * @param memberId
	 * @param memberEffectiveDate
	 * @param productId
	 * @return
	 */
	private Date getMemberFirstInstallmentPostDate(Date memberEffectiveDate, Integer productId,List<PaymentDetail> payDetailList) {
		
		Date firstInstallmentPostDate = memberEffectiveDate;
		
		if(!payDetailList.isEmpty()) 
		{
			firstInstallmentPostDate = payDetailList.size() > 2 ? payDetailList.get(1).getTransactionDate() : memberEffectiveDate;
			
			if(payDetailList.size()==1) {//only down payment posted, then first installament post date will be effective date + 1 month
				
				 LocalDate localEffectiveDt = new java.util.Date(memberEffectiveDate.getTime()).toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
				 localEffectiveDt = localEffectiveDt.plusMonths(1);
				 firstInstallmentPostDate = Date.from(localEffectiveDt.atStartOfDay(java.time.ZoneId.systemDefault()).toInstant());	
			}
			
		}
		return firstInstallmentPostDate;
	}

	/**
	 * @param prdEffectiveDt
	 * @param firstInstallmentPostDate
	 * @return
	 */
	private Date getEffectiveDateForInstallPlanMember(Date prdEffectiveDt, Date firstInstallmentPostDate) {
		Date effectiveDate;
		
		LocalDate temp =   new java.util.Date(prdEffectiveDt.getTime()).toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
		temp = temp.plusMonths(1);//2 - change to 1 month on jan 2019
		
		Date effectiveDatePlus2 = Date.from(temp.atStartOfDay(java.time.ZoneId.systemDefault()).toInstant());
		
		effectiveDate =  firstInstallmentPostDate.before(effectiveDatePlus2) ? firstInstallmentPostDate : effectiveDatePlus2;
		
		return effectiveDate;
	}

	/**
	 * @param member
	 */
	public void calculateMemberOutstandingBalance(Member member,Double setupFeeYearParts) {
		
		Integer setupFeePrdId = member.getMemberFee().stream().filter(item -> item.getFeeDetails().getFeeId() == 1).mapToInt(item -> item.getProduct().getProductId()).findFirst().orElse(0);

		Integer setupFeePrdFreqId = member.getMemberFee().stream().filter(item -> item.getProduct().getProductId().compareTo(setupFeePrdId)==0 && item.getFeeDetails().getFeeId() != 1)
				.mapToInt(item -> item.getFrequencyDetails().getID()).findFirst().orElse(0);
				
		double memTaxRate = 0;
		
		int grpMonthsBehind=0;
		Date grpPastDueEffectiveDate = Constant.DEFULT_DATE_1900;
		
		for (MemberFee item : member.getMemberFee()) {
			if (item.getMaxAmountDue() > 0 && item.getAmountPaid() >= (item.getMaxAmountDue())) {
				item.setOutstanndingBalance(0.0);
			} else {

				if(null != member.getGroup()) {
					grpMonthsBehind = member.getGroup().getPaymentMonthsBehind();
					grpPastDueEffectiveDate = member.getGroup().getPastDueEffectiveDate();
				}
				Date effectiveDate = getProductEffectiveDate(member, item,grpPastDueEffectiveDate);	
				
				if(item.getFeeDetails().getFeeId()==1) {
					long gap = findElapsedFreq(effectiveDate, new java.util.Date(), setupFeePrdFreqId,member);
					
					if(null != member.getReinstateDate() && (item.getEffectiveEndDate().before(member.getReinstateDate()) || item.getEffectiveEndDate().equals(member.getReinstateDate()))) {
						gap=0;
					}
					
					double tempAmt=0;
					if(Boolean.TRUE.equals(member.getOneTimeInit())) {	
						tempAmt = item.getAmount() * (1+memTaxRate);
					}
					else {
						 tempAmt = gap * (item.getAmount() * (1+memTaxRate)) / setupFeeYearParts;
					}
					item.setOutstanndingBalance(item.getAmountPaid() - tempAmt);
				}
				else {
					long gap = findElapsedFreq(effectiveDate, new java.util.Date(), item.getFrequencyDetails().getID(),member);
					if(grpMonthsBehind>0 && item.getEffectiveEndDate().after(new Date()))
					{
						gap = (gap-grpMonthsBehind);
					}
					else if(gap==0 && item.getAmount() >0) {
						gap =1;
					}
					else if(null != member.getReinstateDate() && (item.getEffectiveEndDate().before(member.getReinstateDate()) || item.getEffectiveEndDate().equals(member.getReinstateDate()))) {
						gap=0;
					}
					item.setOutstanndingBalance(  item.getAmountPaid() - ( (item.getAmount() * (1+memTaxRate)) * gap )) ;
				}
				if(item.getMaxAmountDue() >0) {//if maxamtdue is available, outstanding balance cannot be more than amount paid-max amt due.
					if( (item.getAmountPaid() - item.getMaxAmountDue()) < 0 &&  (item.getAmountPaid() - item.getMaxAmountDue()) > item.getOutstanndingBalance() )
						item.setOutstanndingBalance( item.getAmountPaid() - item.getMaxAmountDue() );
					
				}
				//round outstanding balance
				item.setOutstanndingBalance( Math.round(item.getOutstanndingBalance() * 100.0)/100.0);
			}
		}
	}
	
	/**
	 * @param member
	 * @param grpPastDueEffectiveDate 
	 * @param MemberFee
	 *            Record
	 * @return
	 */
	private Date getProductEffectiveDate(Member member, MemberFee item, Date grpPastDueEffectiveDate) {

		Date effectiveDate = null;

		Date firstPaymentDate = paymentService.getMemberFirstPaymentDate(member.getMemberId());

		LocalDate localDateOf20060101 = LocalDate.of(2006, 1, 1);
		LocalDate localDateOf19000101 = LocalDate.of(1900, 1, 1);

		Date dateOf20060101 = Date.from(localDateOf20060101.atStartOfDay(java.time.ZoneId.systemDefault()).toInstant());
		Date dateOf19000101 = Date.from(localDateOf19000101.atStartOfDay(java.time.ZoneId.systemDefault()).toInstant());

		Date productDate = item.getEffectiveStartDate();
		
		if(item.getFeeDetails().isSetupFee()) 
		{
			productDate = member.getMemberFee().stream()
							.filter(prdItem -> prdItem.getProduct().getProductId().compareTo(item.getProduct().getProductId())==0 && !prdItem.getFeeDetails().isSetupFee())
							.map(MemberFee::getProductDate).findFirst().orElse(null);
			if(null==productDate)
				productDate = item.getProductDate();
		}
		
		if(grpPastDueEffectiveDate.compareTo(Constant.DEFULT_DATE_1900) !=0 && grpPastDueEffectiveDate.after(member.getEffectiveDate()))
		{
			effectiveDate = grpPastDueEffectiveDate;			
		}
		else if(item.getUpgradeType().intValue()==0 && productDate != null && member.getEffectiveDate().after(productDate)) //primary product
		{
			effectiveDate = member.getEffectiveDate();
		}
		else if (productDate != null && (productDate.compareTo(dateOf19000101) != 0) && productDate.after(dateOf20060101)) 
		{
			effectiveDate = productDate;
		} // product date is not null and product date is 01/01/1900 or //
			// product // date is before 01/01/1900
		else if (productDate != null && (productDate.equals(dateOf19000101) || productDate.before(dateOf19000101))) 
		{
			effectiveDate = member.getEffectiveDate();
		} 
		else
		{						
			effectiveDate = firstPaymentDate == null ? Constant.DEFULT_DATE_1900 : firstPaymentDate;
		}
		
		if(null != member.getReinstateDate() && effectiveDate.before(member.getReinstateDate())) {
			effectiveDate = member.getReinstateDate();
		}
		else if (effectiveDate.before(dateOf20060101))
			effectiveDate = dateOf20060101;
		
		return effectiveDate;
	}
	/**
	 * @param member
	 * @return
	 */
	public Double findSetupFeeYearParts(Set<MemberFee> memberFees) {

		// product id of Setup Fee Product
		Integer prdId = memberFees.stream().filter(item -> Constant.SETUP_FEE_IDS.contains(item.getFeeDetails().getFeeId())).mapToInt(item -> item.getProduct().getProductId()).findFirst().orElse(0);

		Double yearParts = memberFees.stream().filter(item -> item.getProduct().getProductId() == prdId && !Constant.SETUP_FEE_IDS.contains(item.getFeeDetails().getFeeId()) ).mapToDouble(item -> item.getFrequencyDetails().getYearParts()).findFirst().orElse(1);

		return yearParts;
	}
	
	/**
	 * @param member
	 * @return
	 */
	public Double findSetupFeeYearParts(Integer memberId) {

		Set<MemberFee> memberFees = memberFeeRepository.findAllByMemberIdAndProductProductIdGreaterThanOrderByProduct(memberId,0);
		
		// product id of Setup Fee Product
		Integer prdId = memberFees.stream().filter(item -> item.getFeeDetails().getFeeId() == 1).mapToInt(item -> item.getProduct().getProductId()).findFirst().orElse(0);

		Double yearParts = memberFees.stream().filter(item -> item.getProduct().getProductId() == prdId).mapToDouble(item -> item.getFrequencyDetails().getYearParts()).findFirst().orElse(1);

		return yearParts;
	}

	/**
	 * @param startDate
	 * @param endDate
	 * @param freqId
	 * @return
	 */
	public long findElapsedFreq(Date startDate, Date endDate, int freqId,Member member) {

		LocalDate localStartdate;
		LocalDate localEndDate;

		localStartdate = new java.util.Date(startDate.getTime()).toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
		localEndDate = (endDate).toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();

		long gap = 0;

		long noCoverageUnits = getUnitsMemberDoesNotHaveCoverage(member,freqId,startDate);
		
		switch (freqId) {
			case 1 : // weekly
				gap = ChronoUnit.WEEKS.between(localStartdate, localEndDate);
				break;
			case 2 : // biweekly
				gap = ChronoUnit.WEEKS.between(localStartdate, localEndDate) / 2;
				break;
			case 3 : // monthly
				gap = ChronoUnit.MONTHS.between(localStartdate, localEndDate);
				break;
			case 4 : // Annually
				gap = ChronoUnit.YEARS.between(localStartdate, localEndDate);
				break;
			case 7 : // semi monthly
				gap = ChronoUnit.DAYS.between(localStartdate, localEndDate) / 15;
				break;
			case 8 : // quinquennially
				gap = ChronoUnit.YEARS.between(localStartdate, localEndDate) / 5;
				break;
		}
		gap = (gap-noCoverageUnits);
		LocalDate tempDate = localStartdate.plusMonths(gap);
		if(tempDate.isBefore(localEndDate) || tempDate.isEqual(localEndDate)) {
		    gap = gap+1;//if gap between start date and current date is month plus some days, consider it as whole month.increment gap to next integer.		    
		}
		return gap;
	}

	private long getUnitsMemberDoesNotHaveCoverage(Member member, int freqId, Date effectiveStartDate) 
	{	// check for any coverage lapses
		Set<MemberCoverageLapse> coverageLapseSet = memberService.getMemberCoverageLapses(member.getMemberId());
		long units = 0;
		for (MemberCoverageLapse item : coverageLapseSet) 
		{
			if(item.getLapseEndDate().before(effectiveStartDate))
				continue;
				
			switch (freqId) {
			case 1: // weekly
				units += ChronoUnit.WEEKS.between(PTSUtilityService.convertUtilToLocalDate(item.getLapseStartDate()),
						PTSUtilityService.convertUtilToLocalDate(item.getLapseEndDate()));
				break;
			case 2: // biweekly
				units += ChronoUnit.WEEKS.between(PTSUtilityService.convertUtilToLocalDate(item.getLapseStartDate()),
						PTSUtilityService.convertUtilToLocalDate(item.getLapseEndDate())) / 2;
				break;
			case 3: // monthly
				units += ChronoUnit.MONTHS.between(PTSUtilityService.convertUtilToLocalDate(item.getLapseStartDate()),
						PTSUtilityService.convertUtilToLocalDate(item.getLapseEndDate()));
				break;
			case 4: // Annually
				units += ChronoUnit.YEARS.between(PTSUtilityService.convertUtilToLocalDate(item.getLapseStartDate()),
						PTSUtilityService.convertUtilToLocalDate(item.getLapseEndDate()));
				break;
			case 7: // semi monthly
				units += ChronoUnit.DAYS.between(PTSUtilityService.convertUtilToLocalDate(item.getLapseStartDate()),
						PTSUtilityService.convertUtilToLocalDate(item.getLapseEndDate())) / 15;
				break;
			case 8: // quinquennially
				units += ChronoUnit.YEARS.between(PTSUtilityService.convertUtilToLocalDate(item.getLapseStartDate()),
						PTSUtilityService.convertUtilToLocalDate(item.getLapseEndDate())) / 5;
				break;
			}
		}
		if(member.getActive().compareTo(MemberActiveStatus.SUSPENDED.getStatus())==0)//member is suspended
		{
			switch (freqId) {
			case 1: // weekly
				units += ChronoUnit.WEEKS.between(PTSUtilityService.convertUtilToLocalDate(member.getCancelDate()),
						LocalDate.now());
				break;
			case 2: // biweekly
				units += ChronoUnit.WEEKS.between(PTSUtilityService.convertUtilToLocalDate(member.getCancelDate()),
						LocalDate.now()) / 2;
				break;
			case 3: // monthly
				units += ChronoUnit.MONTHS.between(PTSUtilityService.convertUtilToLocalDate(member.getCancelDate()),
						LocalDate.now());
				break;
			case 4: // Annually
				units += ChronoUnit.YEARS.between(PTSUtilityService.convertUtilToLocalDate(member.getCancelDate()),
						LocalDate.now());
				break;
			case 7: // semi monthly
				units += ChronoUnit.DAYS.between(PTSUtilityService.convertUtilToLocalDate(member.getCancelDate()),
						LocalDate.now()) / 15;
				break;
			case 8: // quinquennially
				units += ChronoUnit.YEARS.between(PTSUtilityService.convertUtilToLocalDate(member.getCancelDate()),
						LocalDate.now()) / 5;
				break;
			}
		}
		return units;
	}
	public long findMonthsBetweenForCredit(Date startDate, Date endDate, int freqId) {

		LocalDate localStartdate;
		LocalDate localEndDate;

		localStartdate = new java.util.Date(startDate.getTime()).toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
		localEndDate = (endDate).toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();

		long gap = 0;

		switch (freqId) {
			case 1 : // weekly
				gap = ChronoUnit.WEEKS.between(localStartdate, localEndDate);
				break;
			case 2 : // biweekly
				gap = ChronoUnit.WEEKS.between(localStartdate, localEndDate) / 2;
				break;
			case 3 : // monthly
				gap = ChronoUnit.MONTHS.between(localStartdate, localEndDate);
				break;
			case 4 : // Annually
				gap = ChronoUnit.YEARS.between(localStartdate, localEndDate);
				break;
			case 7 : // semi monthly
				gap = ChronoUnit.DAYS.between(localStartdate, localEndDate) / 15;
				break;
			case 8 : // quinquennially
				gap = ChronoUnit.YEARS.between(localStartdate, localEndDate) / 5;
				break;
		}
		LocalDate tempDate = localStartdate.plusMonths(gap);
		
		long days = ChronoUnit.DAYS.between(tempDate, localEndDate);
		if(days>=15) {
			gap=gap+1;
		}		
		return gap;
	}

	
	/**
	 * @param memberFee
	 * @param oneTimeInit
	 */
	private void calculateMemberFeePrdMultipliers(Member.MemberInfo memberInfo,Double setupFeeYearParts,Set<MemberFee> memberFees) {

		Double initiationAmt = 0.0, premiumAmt = 0.0, upgradeAmt = 0.0;
		Map<String, Double> feeAmts = new HashMap<String, Double>();
		initiationAmt = memberFees.stream().filter(item -> item.getFeeDetails().getFeeId() == 1 && item.getUpgradeType() == 0).mapToDouble(MemberFee::getAmount).sum();
		feeAmts.put("INITIAION_AMT", initiationAmt);
		premiumAmt = memberFees.stream().filter(item -> item.getFeeDetails().getFeeId() != 1 && item.getUpgradeType() == 0).mapToDouble(MemberFee::getAmount).sum();
		feeAmts.put("PREMIUM_AMT", premiumAmt);
		upgradeAmt = memberFees.stream().filter(item -> item.getFeeDetails().getFeeId() != 1 && item.getUpgradeType() > 0).mapToDouble(MemberFee::getAmount).sum();
		feeAmts.put("UPGRADE_AMT", upgradeAmt);

		for (MemberFee item : memberFees) {
			double tmpAmt = 1;

			if (memberInfo.getOneTimeInit() !=null && memberInfo.getOneTimeInit()) {
				tmpAmt = item.getAmount() / ((initiationAmt + premiumAmt + upgradeAmt) == 0 ? 1 : ((initiationAmt + premiumAmt + upgradeAmt)));
			} else {
				if (item.getFeeDetails().getFeeId() == 1) {
					tmpAmt = (item.getAmount() / setupFeeYearParts);
				} else {
					tmpAmt = item.getAmount();
				}
			}
			// Fee Multipler
			item.setFeeMultipler(tmpAmt / ((premiumAmt + upgradeAmt + (initiationAmt / item.getFrequencyDetails().getYearParts())) == 0
					? 1
					: (premiumAmt + upgradeAmt + (initiationAmt / item.getFrequencyDetails().getYearParts()))));

			// Premium Multipler
			if (item.getFeeDetails().getFeeId() == 1) {
				item.setPremMultipler(0.0);
			} else {
				item.setPremMultipler(item.getAmount() / ((premiumAmt + upgradeAmt) == 0 ? 1 : (premiumAmt + upgradeAmt)));
			}

			// init multipler
			if (item.getUpgradeType() == 0) {
				if (memberInfo.getOneTimeInit() !=null && memberInfo.getOneTimeInit()) {
					item.setInitMultipler(item.getAmount() / ((premiumAmt + initiationAmt) == 0 ? 1 : (premiumAmt + initiationAmt)));
				} else {
					tmpAmt = 1;
					if (item.getFeeDetails().getFeeId() == 1) {
						tmpAmt = item.getAmount() / setupFeeYearParts;
					} else {
						tmpAmt = item.getAmount();
					}
					item.setInitMultipler(tmpAmt
							/ ((premiumAmt + (initiationAmt / setupFeeYearParts)) == 0 ? 1 : (premiumAmt + (initiationAmt / setupFeeYearParts))));
				}
			}
		}
	}
	/**
	 * @param member
	 */
	private void calculateMemberBalancesByProduct(Member.MemberInfo memberInfo,Set<MemberFee> memberFees,List<PaymentGroupProductSummaryDTO> memPaidAmtByGroupPrd,Double setupFeeYearParts) {
		
		// setup fee
		MemberFee initFee = memberFees.stream().filter(item -> item.getFeeDetails().getFeeId() == 1).findFirst().orElse(new MemberFee());
		
		double memTaxRate = 0;

		for (MemberFee item : memberFees) {

			double totalAmtPaidForPrd = memPaidAmtByGroupPrd.stream()
					.filter(smryItem-> smryItem.getProductId().compareTo(item.getProduct().getProductId())==0 &&
					(smryItem.getGroupId() == 0 || smryItem.getGroupId().compareTo(item.getGroup().getGroupId())==0))
					.map(PaymentGroupProductSummaryDTO::getTotalAmountPaid).findFirst().orElse(0.0);
			
			//memPaidAmt.get(item.getProduct().getProductId()) == null ? 0 : memPaidAmt.get(item.getProduct().getProductId());

			if (item.getFeeDetails().getFeeId() == 1) { // setup fee

				if (memberInfo.getOneTimeInit() !=null && memberInfo.getOneTimeInit()) {
					if ((totalAmtPaidForPrd - (initFee.getAmount() * (1 + memTaxRate))) > 0) {
						item.setAmountPaid(initFee.getAmount() * (1 + memTaxRate));
					} else {
						item.setAmountPaid(totalAmtPaidForPrd);
					}
				} else if (memberInfo.getOneTimeInit() ==null || (memberInfo.getOneTimeInit()!=null && !memberInfo.getOneTimeInit())) {
					if ((totalAmtPaidForPrd * item.getInitMultipler()) < (initFee.getAmount() * (1 + memTaxRate)))
						item.setAmountPaid(totalAmtPaidForPrd * item.getInitMultipler());
					else if ((totalAmtPaidForPrd * item.getInitMultipler()) >= (initFee.getAmount() * (1 + memTaxRate)))
						item.setAmountPaid(initFee.getAmount() * (1 + memTaxRate));
					else
						item.setAmountPaid(totalAmtPaidForPrd);
				}
			} else {
				if (item.getUpgradeType() > 0) {
					item.setAmountPaid(totalAmtPaidForPrd);
				} else if (memberInfo.getOneTimeInit()!=null && memberInfo.getOneTimeInit()) {
					item.setAmountPaid((totalAmtPaidForPrd - (initFee.getAmount() * (1 + memTaxRate))) > 0 ? (totalAmtPaidForPrd - (initFee.getAmount() * (1 + memTaxRate))) : 0);
				} else if (memberInfo.getOneTimeInit() ==null || (memberInfo.getOneTimeInit()!=null && !memberInfo.getOneTimeInit())) {
					if ((totalAmtPaidForPrd * (1 - item.getInitMultipler())) < (initFee.getAmount() * (1 + memTaxRate)))
						item.setAmountPaid(totalAmtPaidForPrd * item.getInitMultipler());
					else if ((totalAmtPaidForPrd * (1 - item.getInitMultipler())) >= (initFee.getAmount() * (1 + memTaxRate)))
						item.setAmountPaid(totalAmtPaidForPrd - (initFee.getAmount() * (1 + memTaxRate)));
					else
						item.setAmountPaid(totalAmtPaidForPrd);
				} else {
					item.setAmountPaid(totalAmtPaidForPrd);
				}				
			}
			item.setAmountPaid(Math.round(item.getAmountPaid() * 100.0) / 100.0);
			
			//max amt due being null for some member data.
			item.setMaxAmountDue(item.getMaxAmountDue() == null ? 0.0 : item.getMaxAmountDue());
			
			// balance due
			if (item.getMaxAmountDue() * (1 + memTaxRate) == 0) {
				item.setBalanceDue(0.0);
			} else {
				item.setBalanceDue((item.getMaxAmountDue() * (1 + memTaxRate)) - item.getAmountPaid());
				item.setBalanceDue( (Math.round(item.getBalanceDue() * 100.0) / 100.0 ) );
			}
			
			//setup fee 
			if (item.getFeeDetails().getFeeId() == 1) {
				Double setupFeeAmt=0.0;
				if(memberInfo.getOneTimeInit()!=null && memberInfo.getOneTimeInit()) {
					setupFeeAmt = (item.getAmount() - item.getAmountPaid()) < 0 ? 0 : item.getAmount() - item.getAmountPaid();
				}
				else {
					if( (item.getAmount()-item.getAmountPaid()) < item.getAmount()/setupFeeYearParts  ) {
						setupFeeAmt = item.getAmount() - item.getAmountPaid();
					}
					else {
						setupFeeAmt = item.getAmount() / setupFeeYearParts;
					}
				}
				item.setSetupFeeAmt(Math.round(setupFeeAmt * 100.0)/100.0);
			}
		}
	}
	
	/**
	 * @param memberId
	 * @return
	 */
	public MemberFee getMemberInitFee(int memberId) {
		
		Set<MemberFee> memberFees = memberFeeRepository.findAllByMemberIdAndProductProductIdGreaterThanOrderByProduct(memberId,0);
		
		return memberFees.stream().filter(item->item.getFeeDetails().getFeeId()==1).findFirst().orElse(new MemberFee());
	}
	
	/**
	 * @param memberId
	 * @return
	 */
	public Set<MemberFee> getMemberFees(int memberId) {
		return memberFeeRepository.findAllByMemberIdAndProductProductIdGreaterThanOrderByProduct(memberId,0);
	}
	
	/**
	 * @param memberId
	 * @return
	 */
	public Set<MemberFee> getCommissionMemberFees(int memberId) {
		Set<MemberFee> memFeeSet = memberFeeRepository.findAllByMemberIdAndProductProductIdGreaterThanOrderByProduct(memberId,0);
		Set<MemberFee> memFeeSetReturn = new HashSet<>();
		for(java.util.Iterator<MemberFee> itr=memFeeSet.iterator();itr.hasNext();) {
			MemberFee item = itr.next();
			if(!Constant.SETUP_FEE_IDS.contains(item.getFeeDetails().getFeeId())) {
				memFeeSetReturn.add(item);
			}
		}
		return memFeeSetReturn;
	}
	
	/**
	 * @param fee
	 * @return Year Amount based on Fee Frequency
	 */
	public BigDecimal calculateTotalYearAmount(MemberFee fee) {
		BigDecimal yearAmount;
		BigDecimal premAmount = new BigDecimal(Double.toString(fee.getAmount()));
		yearAmount = premAmount.multiply(new BigDecimal(Double.toString(fee.getFrequencyDetails().getYearParts())));		
		return yearAmount;
	}
	
	
	/**
	 * @param memberId
	 * @param productId
	 * @param balanceForPayHistory 
	 * TRUE -> Member product balance for payment history/ outstanding balance calculation and should consider Group past due effective date
	 * FALSE -> Balance during process payment , commission determination of New/Renew
	 * @return
	 */
	public Set<MemberFee> calculateMemberBalanceForProduct(Integer memberId,Integer productId,Boolean balanceForPayHistory) {
		
		Set<MemberFee> memPrdFees =  memberFeeRepository.findAllByMemberIdAndProductProductId(memberId,productId);
		
		MemberInfo memberInfo = this.memberRepository.findByMemberId(memberId).get();
		
		Double yearPartsForSetupFee = findSetupFeeYearParts(memPrdFees);
		
		calculateMemberFeePrdMultipliers(memberInfo,yearPartsForSetupFee,memPrdFees);
		
		Set<Integer> memProductIds = memPrdFees.stream().map(item -> item.getProduct().getProductId()).collect(Collectors.toCollection(HashSet::new));
		
		Date grpPastDueEffectiveDate = Constant.DEFULT_DATE_1900;
		if(Boolean.TRUE.equals(balanceForPayHistory))
		{
			grpPastDueEffectiveDate = groupService.getGroupPastDueEffectiveDateByMemberId(memberId);
		}
		List<PaymentGroupProductSummaryDTO> memPaidAmtByGroupPrd = paymentService.getMemberTotalPaymentsByGroupProduct(memberInfo.getMemberId(),memberInfo.getReinstateDate(),true,grpPastDueEffectiveDate);
		
		//Map<Integer, Double> memPaidAmt = paymentService.getMemberTotalPaymentsByProduct(memberInfo.getMemberId(), memProductIds,memberInfo.getReinstateDate(), true,grpPastDueEffectiveDate);
		
		Map<Integer, Double> masaPaidAmt = paymentService.getMasaPaymentsByProduct(memberId,memProductIds,memPrdFees);
		
		calculateMasaPaymentsByProductForMember(memPrdFees,masaPaidAmt);
		
		calculateMemberBalancesByProduct(memberInfo,memPrdFees,memPaidAmtByGroupPrd,yearPartsForSetupFee);
		
		return memPrdFees;
	}
	
	/**
	 * @param memberId
	 * @param productId
	 * @return
	 */
	public double calculateProductYearAmount(MemberSummary memberSummary,Set<MemberFee> memberFees) {
		
		double productYearAmount=0;
		
		boolean downPaymentGrp = groupService.isDownPaymentGroup(memberSummary.getGroupGroupCode());
		
		for(MemberFee memberFee : memberFees) {
			
			if(!Constant.SETUP_FEE_IDS.contains(memberFee.getFeeDetails().getFeeId())) {
			
				productYearAmount = memberFee.getAmount() * memberFee.getFrequencyDetails().getYearParts();
				
				if(INSTALLMENT_20_MONTH_PRD_IDS.contains(memberFee.getProduct().getProductId())){
					productYearAmount = memberFee.getMaxAmountDue();
					continue;
				}
				if(memberFee.getFrequencyDetails().getID() == Constant.FREQ_FIVE_YEAR)
					productYearAmount = memberFee.getAmount();
				
				if(Constant.INSTALLMENT_FEE_IDS.contains(memberFee.getFeeDetails().getFeeId()) && memberFee.getFrequencyDetails().getID() != Constant.FREQ_FIVE_YEAR
						&& !Constant.INSTALLMENT_ADMIN_PRODUCT_IDS.contains(memberFee.getProduct().getProductId())) {
					
					if(downPaymentGrp) {
						PaymentDetail payDetail = paymentService.getMemberNthPaymentDetailForProduct(memberSummary.getMemberId(), memberFee.getProduct().getProductId(), 0);//index starts with 0.
						productYearAmount = productYearAmount + (null != payDetail ? payDetail.getPeriodFee() : 0);
					}
				}
			}
		}
		return productYearAmount;
	}
	
	public Map<String,Double> getFeeBreakDownForPayment(MemberSummary memberSummary, MemberFee initFee, PaymentDetail payDetail) {
		
		java.util.HashMap<String,Double> amtsMap = new java.util.HashMap<String,Double>();
		
		double newPeriodAmt = 0.0;
		double premiumPayment= 0.0;
		double remainingInitAmt = initFee.getAmount() - initFee.getAmountPaid();
		double afterTaxAmountPaid = (payDetail.getAmountPaid()/ (1+ (memberSummary.getTaxRate() ==null ? 0 : memberSummary.getTaxRate() )));
		
		amtsMap.put("REM_INIT_AMT", remainingInitAmt);
		amtsMap.put("AFTER_TAX_AMT",afterTaxAmountPaid);
		if (remainingInitAmt > 0 && payDetail.getProductId().compareTo(initFee.getProduct().getProductId())  == 0)
		{
			if (memberSummary.getOneTimeInit() !=null && memberSummary.getOneTimeInit())
			{
				if (afterTaxAmountPaid > remainingInitAmt)
					newPeriodAmt = afterTaxAmountPaid - remainingInitAmt;
				else
					newPeriodAmt = 0;
			}
			else
			{
				premiumPayment = afterTaxAmountPaid * (1 - initFee.getInitMultipler());

				if ((afterTaxAmountPaid - premiumPayment) > remainingInitAmt)
				{
					newPeriodAmt = afterTaxAmountPaid - remainingInitAmt;
				}
				else
					newPeriodAmt = premiumPayment;
			}
		}
		else
		{
			newPeriodAmt = afterTaxAmountPaid;
		}
		amtsMap.put("PREMIUM_AMT",newPeriodAmt );
		
		return amtsMap;
	}
	
	/**
	 * @param memberId
	 * @return
	 */
	public Double getMemberPremium(Integer memberId) {		
		Set<MemberFee> memberFees = memberFeeRepository.findAllByMemberIdAndProductProductIdGreaterThanOrderByProduct(memberId,0);
		return  memberFees.stream().filter(item->item.getFeeDetails().getFeeId() !=1).mapToDouble(MemberFee::getAmount).sum();		
	}
	
	/**
	 * @param memberFeeSet
	 * @return
	 */
	public Integer getMemberPrimaryProductId(Set<MemberFee> memberFeeSet)
	{
		return memberFeeSet.stream()
				.filter(item -> item.getUpgradeType().compareTo(ProductUpgradeType.PRIMARY.getValue()) == 0)				
				.map(item -> item.getProduct().getProductId()).findFirst().orElse(0);
	}
}
