package com.masa.pts.core.service;

import com.masa.pts.core.domain.SalesChannel;
import com.masa.pts.core.domain.SalesChannelForteMerchant;
import com.masa.pts.core.model.SalesChannelSearchDto;
import com.masa.pts.core.repository.SalesChannelForteMerchantRepository;
import com.masa.pts.core.repository.SalesChannelRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class SalesChannelService {

	private final SalesChannelForteMerchantRepository salesChannelForteMerchantRepository;
	private final SalesChannelRepository salesChannelRepository;
	private final SalesChannelForteMerchantRepository merchantRepository;

	public SalesChannelService(SalesChannelForteMerchantRepository salesChannelForteMerchantRepository,
							   SalesChannelRepository salesChannelRepository,
							   SalesChannelForteMerchantRepository merchantRepository) {
		this.salesChannelForteMerchantRepository = salesChannelForteMerchantRepository;
		this.salesChannelRepository = salesChannelRepository;
		this.merchantRepository = merchantRepository;
	}

	public Optional<String> getMerchantIdOfMember(Integer memberId) {
		return salesChannelForteMerchantRepository.findMerchantIdByMemberId(memberId);
	}

	public Optional<String> getMerchantIdOfGroup(String groupCode) {
		return salesChannelForteMerchantRepository.findMerchantIdByGroupCode(groupCode);
	}

	public Set<String> getAllSalesChannelByDivision(String division){
		Set<SalesChannel> salesChannelSet = this.salesChannelRepository.findAllByDivisionName(division);

		return salesChannelSet.stream().map(SalesChannel::getName).collect(Collectors.toSet());
	}

	@Transactional(readOnly = true)
	public Page<SalesChannelSearchDto> getSalesChannels(Pageable pageable) {
		return salesChannelRepository.getSalesChannelPage(pageable);
	}

	@Transactional(readOnly = true)
	public Optional<SalesChannel> getSalesChannel(Integer salesChannelId) {
		return salesChannelRepository.getSalesChannelOptional(salesChannelId);
	}

	@Transactional(readOnly = true)
	public boolean isSalesChannelNameExist(String name) {
		return salesChannelRepository.existsByName(name);
	}

	@Transactional
	public SalesChannel save(SalesChannel salesChannel) {
		return salesChannelRepository.save(salesChannel);
	}

	@Transactional
	public SalesChannelForteMerchant saveMerchant(SalesChannelForteMerchant merchant) {
		return salesChannelForteMerchantRepository.save(merchant);
	}
	public Optional<String> getMerchantIdOfGroupWithProductId(String groupCode,Integer productId) {
		return salesChannelForteMerchantRepository.findMerchantIdByGroupCodeAndProductId(groupCode,productId);
	}
}
