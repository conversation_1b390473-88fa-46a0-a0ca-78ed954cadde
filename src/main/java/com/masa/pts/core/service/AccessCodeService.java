package com.masa.pts.core.service;

import com.google.common.collect.Sets;
import com.masa.pts.core.domain.Role;
import com.masa.pts.core.domain.UserAccess;
import com.masa.pts.core.exception.NotFoundException;
import com.masa.pts.core.repository.RoleRepository;
import com.masa.pts.core.repository.UserAccessRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ServerWebInputException;

import java.util.Date;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.masa.pts.core.domain.Constant.DEFULT_DATE_1900;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;

@Service
public class AccessCodeService {

    private final UserAccessRepository userAccessRepository;
    private final RoleRepository roleRepository;

    public AccessCodeService(UserAccessRepository userAccessRepository, RoleRepository roleRepository) {
        this.userAccessRepository = userAccessRepository;
        this.roleRepository = roleRepository;
    }

    @Transactional(readOnly = true)
    public Set<UserAccess> accessCodes() {
        return Sets.newHashSet(userAccessRepository.findAllByIsObsoleteIsFalse());
    }

    @Transactional
    public UserAccess save(UserAccess e) {
        if (e.getObsoleteDate() == null) {
            e.setObsoleteDate(DEFULT_DATE_1900);
        }
        e.setIsObsolete(false);
        e.setCreatedDate(new Date());
        return userAccessRepository.save(e);
    }

    @Transactional
    public UserAccess update(UserAccess in) {
        UserAccess updated = userAccessRepository.findByIdAndIsObsoleteIsFalse(in.getId())
                .map(u -> {
                    u.setCode(getCodeOrThrow(in, u));
                    u.setDescription(in.getDescription());
                    return u;
                })
                .orElseThrow(
                        () -> new NotFoundException("Access code with id " + in.getId() + " does not exist."));
        return userAccessRepository.save(updated);
    }

    private String getCodeOrThrow(UserAccess newone, UserAccess old) {
        if (Objects.equals(newone.getCode(), old.getCode())) {
            return old.getCode();
        } else if (noCodeExists(newone.getCode())) {
            return newone.getCode();
        } else {
            throw new ServerWebInputException("Access code must be unique.");
        }
    }

    @Transactional
    public boolean delete(Integer id) {
        Set<Role> relatedRoles = roleRepository.findByAccessCodeId(id);
        if (isNotEmpty(relatedRoles)) {
            throw new ServerWebInputException("Could not delete access code assigned to role.");
        }

        return userAccessRepository.findByIdAndIsObsoleteIsFalse(id)
                .map(r -> {
                    r.setIsObsolete(true);
                    r.setObsoleteDate(new Date());
                    return r;
                })
                .isPresent();
    }

    public boolean isCodeExists(String code) {
        return userAccessRepository.findByCodeAndIsObsoleteIsFalse(code).isPresent();
    }

    public boolean noCodeExists(String code) {
        return !isCodeExists(code);
    }
}
