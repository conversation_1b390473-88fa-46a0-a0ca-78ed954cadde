package com.masa.pts.core.service;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.masa.pts.core.domain.Agent;
import com.masa.pts.core.domain.Agent.AgentIdInfo;
import com.masa.pts.core.domain.AgentCommission;
import com.masa.pts.core.domain.AgentManager;
import com.masa.pts.core.domain.AgentSaleschannel;
import com.masa.pts.core.repository.AgentCommissionRepository;
import com.masa.pts.core.repository.AgentManagerRepository;
import com.masa.pts.core.repository.AgentRepository;

@Service
public class AgentService {

	
	private AgentRepository agentRepository;	
	private AgentCommissionRepository agentCommissionRepository;	
	private AgentManagerRepository agentManagerRepository;
	
	@Autowired
	public AgentService(AgentRepository agentRepository, AgentCommissionRepository agentCommissionRepository,
			AgentManagerRepository agentManagerRepository) {
		super();
		this.agentRepository = agentRepository;
		this.agentCommissionRepository = agentCommissionRepository;
		this.agentManagerRepository = agentManagerRepository;
	}

	/**
	 * @param agentNum
	 * @return
	 */
	public Agent getAgentByAgentNum(String agentNum) {
		
		Optional<Agent> optAgent = agentRepository.findByAgentNum(agentNum, Agent.class);
		
		if(optAgent.isPresent())
			return optAgent.get();
		
		return null;
	}
	
	/**
	 * @param agentId
	 * @return
	 */
	public Agent getAgentByAgentId(Integer agentId) {
		
		Optional<Agent> optAgent = agentRepository.findById(agentId);
		
		if(optAgent.isPresent())
			return optAgent.get();
		
		return null;
	}
	
	public boolean existsByAgentNum(String agentNum) {
		return agentRepository.existsByAgentNum(agentNum);
	}
	
	/**
	 * @param agentId
	 * @param productId
	 * @return
	 */
	public boolean isAgentCommissionSetupForProduct(Integer agentId,Integer productId) {
		 return agentCommissionRepository.findAllByAgentAgentIdAndProductId(agentId,productId)!=null;
				 
	}
	
	public boolean isAgentCommissionSetupForProduct(String agentNum,Integer productId) {
		return agentCommissionRepository.findByAgentAgentNumAndProductId(agentNum, productId).isPresent();
	}
	
	public Set<AgentCommission> getAllAgentCommissionByAgent(String agentNum){
		return agentCommissionRepository.findAllByAgentAgentNum(agentNum);
	}
	public Set<AgentCommission> getAllAgentCommissionByAgent(Integer agentId){
		return agentCommissionRepository.findAllByAgentAgentId(agentId);
	}
	
	public Optional<Integer> getAgentIdByAgentNum(String agentNum)
	{
		Optional<AgentIdInfo> optAgent = agentRepository.findByAgentNum(agentNum, AgentIdInfo.class);
		if(optAgent.isPresent())
		{
			return Optional.of(optAgent.get().getAgentId());
		}		
		return Optional.empty();
	}
	public Optional<String> getAgentNumByAgentId(Integer agentId)
	{
		Optional<AgentIdInfo> optAgent = agentRepository.findByAgentId(agentId, AgentIdInfo.class);
		if(optAgent.isPresent())
		{
			return Optional.of(optAgent.get().getAgentNum());
		}		
		return Optional.empty();
	}
	
	public Set<AgentManager> getAgentManagers(Integer agentId)
	{
		return agentManagerRepository.findAllByAgentIdOrderByManagerPosition(agentId);		
	}
	
	public AgentCommission getAgentCommission(Integer agentId,Integer productId)
	{
		return agentCommissionRepository.findAllByAgentAgentIdAndProductId(agentId, productId);		
	}
	
	/**
	 * @param agentNum
	 * @param productId
	 * @return
	 */
	public boolean isAgentHierarchySetupForProduct(String agentNum,Integer productId)
	{
		Optional<Integer> optAgent = getAgentIdByAgentNum(agentNum);
		if(optAgent.isPresent())
		{
			Set<AgentManager> agentManagerSet =  getAgentManagers(optAgent.get());
			for(AgentManager mgrItem: agentManagerSet)
			{
				if(getAgentCommission(mgrItem.getManagerId(),productId)==null)
				{
					return false;
				}
			}
		}
		else
		{
			return false;
		}
		return true;
	}
	public List<AgentSaleschannel> getAgentsByEapp(String agentNum) {
		
		List<AgentSaleschannel> optAgent = agentRepository.findAgentsAssocitedWithEapp(agentNum);
		
		if(!optAgent.isEmpty())
			return optAgent;
		
		return null;
	}
}
