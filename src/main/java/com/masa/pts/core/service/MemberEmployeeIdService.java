package com.masa.pts.core.service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.masa.pts.core.domain.Member.MemberSummary;
import com.masa.pts.core.repository.MemberEmployeeIdRepository;

@Service
public class MemberEmployeeIdService {

	private final MemberEmployeeIdRepository memberEmployeeIdRepository;
	private final MemberService memberService;

	@Autowired
	public MemberEmployeeIdService(MemberEmployeeIdRepository memberEmployeeIdRepository, MemberService memberService) {
		this.memberEmployeeIdRepository = memberEmployeeIdRepository;
		this.memberService = memberService;
	}

	/**
	 * @param employeeId
	 * @param groupCode
	 * @return Set of MemberSummary Employee Id is unique in the group due to existing
	 *         data issue, possibility of having multiple members with the same
	 *         employee id in a group
	 */
	public Set<MemberSummary> findByEmployeeIdAndGroupCode(String employeeId, String groupCode) {

		Set<Integer> memberIdSet = memberEmployeeIdRepository
				.findActiveMemberIdByEmployeeIdAndGroupCodeOrderByMemberId(employeeId, groupCode);

		if (memberIdSet.isEmpty())
			return Collections.emptySet();

		return memberIdSet.stream().map(item -> memberService.getMemberDetails(item, MemberSummary.class))
				.filter(Optional::isPresent).map(Optional::get).collect(Collectors.toSet());
	}

	public  boolean isMemberExistsInGroupWithEmployeeIdOnDate(String employeeId,String groupCode,Date activeDate) {
		return memberEmployeeIdRepository.findByEmployeeIdAndGroupGroupCode(employeeId, groupCode).stream()
				.anyMatch(item-> PTSUtilityService.isDateWithInRange(activeDate, item.getEffectiveStartDate(), item.getEffectiveEndDate()))
				;		
	}

	public List<Integer> getMemberInfoByEmployeeId(String employeeId, String groupCode, Date transactionDate) {
		 return memberEmployeeIdRepository
		 	.findMemberIdsByEmployeeIdAndGroupCodeActiveOnDate(employeeId, groupCode, 
		 			PTSUtilityService.convertUtilToLocalDate(transactionDate))
		 	;
	}
}
