package com.masa.pts.core.service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.masa.pts.core.constant.AuditEventType;
import com.masa.pts.core.domain.ApplicationSource;
import com.masa.pts.core.domain.Member;
import com.masa.pts.core.domain.MemberAuditEvent;
import com.masa.pts.core.repository.ApplicationSourceRepository;
import com.masa.pts.core.repository.MemberAuditEventRepository;

@Service
public class MemberAuditEventService {

	private static final Logger log = LoggerFactory.getLogger(MemberAuditEventService.class);
	
	private MemberAuditEventRepository memberAuditEventRepository;
	private ApplicationSourceRepository applicationSourceRepository;
	
	@Autowired
	public MemberAuditEventService(MemberAuditEventRepository memberAuditEventRepository, ApplicationSourceRepository applicationSourceRepository) {
		this.memberAuditEventRepository = memberAuditEventRepository;
		this.applicationSourceRepository = applicationSourceRepository;
	}
	
	/**
	 * @param member
	 * @param eventType
	 * @param processedBy
	 * @param applicationSource
	 */
	public void addMemberAuditEvent(Member member,AuditEventType eventType,Integer processedBy,String applicationSource)
	{
		MemberAuditEvent memAuditEvent = new MemberAuditEvent();
		
		memAuditEvent.setApplicationSource(getApplicationSourceIDByCode(applicationSource));
		memAuditEvent.setAuditEventType(eventType);		
		memAuditEvent.setEffectiveDate(member.getEffectiveDate());
		memAuditEvent.setRenewDate(member.getRenewDate());
		memAuditEvent.setProcessedDate(new Date());
		memAuditEvent.setGroupId(member.getGroup().getGroupId());
		memAuditEvent.setMember(member);
		memAuditEvent.setProcessedBy(processedBy);
		
		String comments="";
		
		if (AuditEventType.CANCEL.compareTo(eventType) == 0) {

			Map<String, String> data = new HashMap<>();
			data.put("cancel_date", PTSUtilityService.formatUtilDate(member.getCancelDate()));
			data.put("cancel_reason", String.valueOf(member.getCancelCode()));

			try {
				comments = new ObjectMapper().writeValueAsString(data);
			} catch (JsonProcessingException e) {
			}
		}
		memAuditEvent.setComments(comments);
		try
		{
			memberAuditEventRepository.save(memAuditEvent);
		}
		catch(Exception e)
		{
			log.error("Error adding member audit event for Member[{}], Event[{}]",member.getMemberId(),memAuditEvent);
		}
	}	
	
	/**
	 * @param memberId
	 * @return
	 */
	public Optional<MemberAuditEvent> getLastProductChangeEvent(Integer memberId)
	{
		return memberAuditEventRepository.findLastProductChangeEvent(memberId, AuditEventType.PRODUCT.getType());
	}
	
	private Integer getApplicationSourceIDByCode(String code)
	{
		if(!StringUtils.hasText(code)) {
			return 0;
		}
		Optional<ApplicationSource> optAppSource = applicationSourceRepository.findByCode(code);
		return optAppSource.isPresent() ? optAppSource.get().getId() : 0; //0-default to pts
	}
}
