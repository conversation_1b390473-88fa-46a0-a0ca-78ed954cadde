package com.masa.pts.core.service;

import com.masa.pts.core.domain.FieldValueEntity;
import com.masa.pts.core.domain.GroupDocument;
import com.masa.pts.core.repository.FieldValueRepository;
import com.masa.pts.core.repository.GroupDocumentRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class GroupDocumentService {
    private static final String GROUP_DOCUMENT_TYPE = "GroupDocumentType";
    private final GroupDocumentRepository groupDocumentRepository;
    private final FieldValueRepository fieldValueRepository;

    public GroupDocumentService(GroupDocumentRepository groupDocumentRepository,
                                FieldValueRepository fieldValueRepository) {
        this.groupDocumentRepository = groupDocumentRepository;
        this.fieldValueRepository = fieldValueRepository;
    }

    @Transactional(readOnly = true)
    public Page<GroupDocument> documents(String groupCode, Pageable pageable) {
        return groupDocumentRepository.findByGroupGroupCodeAndIsDeleteIsFalse(groupCode, pageable);
    }

    @Transactional
    public Optional<GroupDocument> document(Integer documentId) {
        return groupDocumentRepository.findByIdAndIsDeleteIsFalse(documentId);
    }

    @Transactional(readOnly = true)
    public List<String> findMetadata() {
        return fieldValueRepository.findAllByField(GROUP_DOCUMENT_TYPE)
                .stream()
                .map(FieldValueEntity::getName)
                .collect(Collectors.toList());
    }
}
