package com.masa.pts.core.service;

import com.masa.pts.core.model.PaymentDTO;
import com.masa.pts.core.repository.PaymentRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.time.LocalDate;

import static com.masa.pts.core.service.PTSUtilityService.convertLocalToUtilDate;

@Service
public class PaymentRestService {

    private final PaymentRepository paymentRepository;

    public PaymentRestService(PaymentRepository paymentRepository) {
        this.paymentRepository = paymentRepository;
    }

    public Page<PaymentDTO> findPageWithMetaByParams(Integer memberId, LocalDate from, LocalDate to, int page,
                                                     int size,
                                                     Sort.Direction order,
                                                     String sortField) {

        return paymentRepository.findByParams(memberId, convertLocalToUtilDate(from), convertLocalToUtilDate(to),
                        PageRequest.of(page, size, order, sortField));
    }
}
