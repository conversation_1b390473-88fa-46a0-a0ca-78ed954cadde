package com.masa.pts.core.service;

import com.google.common.collect.Lists;
import com.masa.pts.core.domain.PTSUserRole;
import com.masa.pts.core.domain.Role;
import com.masa.pts.core.domain.UserAccess;
import com.masa.pts.core.exception.NotFoundException;
import com.masa.pts.core.repository.PTSUserRoleRepository;
import com.masa.pts.core.repository.RoleRepository;
import com.masa.pts.core.repository.UserAccessRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.server.ServerWebInputException;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.masa.pts.core.domain.Constant.DEFULT_DATE_1900;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;

@Service
public class RoleService {

    private final RoleRepository roleRepository;
    private final UserAccessRepository userAccessRepository;
    private final PTSUserRoleRepository ptsUserRoleRepository;

    public RoleService(RoleRepository roleRepository, UserAccessRepository userAccessRepository,
                       PTSUserRoleRepository ptsUserRoleRepository) {
        this.roleRepository = roleRepository;
        this.userAccessRepository = userAccessRepository;
        this.ptsUserRoleRepository = ptsUserRoleRepository;
    }

    @Transactional(readOnly = true)
    public List<Role> roles() {
        return Lists.newArrayList(roleRepository.findAll()).stream()
                .filter(role -> !role.isObsolete())
                .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public Optional<Role> role(Integer id) {
        return roleRepository.findById(id);
    }

    @Transactional
    public Role save(Role newRole) {
        if (newRole.getObsoleteDate() == null) {
            newRole.setObsoleteDate(DEFULT_DATE_1900);
        }
        return roleRepository.save(newRole);
    }

    @Transactional
    public boolean delete(Integer id) {
        Set<PTSUserRole> assignees = ptsUserRoleRepository.findByUserRoleIdRoleId(id);

        if (isNotEmpty(assignees)) {
            throw new ServerWebInputException("Could not delete role assigned to user.");
        }

        return roleRepository.findById(id)
                .map(r -> {
                    r.setObsolete(true);
                    r.setObsoleteDate(new Date());
                    return r;
                })
                .isPresent();
    }

    @Transactional
    public Role update(Role in, Set<Integer> accessCodeIds) {
        Role updated = roleRepository.findById(in.getId())
                .map(e -> {
                    e.setCode(getCodeOrThrow(in, e));
                    e.setDescription(in.getDescription());
                    e.setAccessDetails(getAccessCodesByIds(accessCodeIds));
                    return e;
                })
                .orElseThrow(() -> new NotFoundException("Role with id " + in.getId() + " does not exist."));

        return roleRepository.save(updated);
    }

    @Transactional
    public Role patch(Role in, Set<Integer> accessCodeIds) {
        Role updated = roleRepository.findById(in.getId())
                .map(e -> {
                    e.setCode(in.getCode() != null ? getCodeOrThrow(in, e) : e.getCode());
                    e.setDescription(in.getDescription() != null ? in.getDescription() : e.getDescription());
                    e.setAccessDetails(
                            accessCodeIds != null ? getAccessCodesByIds(accessCodeIds) : e.getAccessDetails());
                    return e;
                })
                .orElseThrow(() -> new NotFoundException("Role with id " + in.getId() + " does not exist."));
        return roleRepository.save(updated);
    }

    private String getCodeOrThrow(Role newone, Role old) {
        if (Objects.equals(newone.getCode(), old.getCode())) {
            return old.getCode();
        } else if (noCodeExists(newone.getCode())) {
            return newone.getCode();
        } else {
            throw new ServerWebInputException("Role code must be unique.");
        }
    }

    public Set<UserAccess> getAccessCodesByIds(Set<Integer> ids) {
        return CollectionUtils.isEmpty(ids)
                ? Collections.emptySet()
                : userAccessRepository.findAllByIdAndIsObsoleteIsFalse(ids);
    }

    public boolean isCodeExists(String code) {
        return roleRepository.findByCode(code).isPresent();
    }

    public boolean noCodeExists(String code) {
        return !isCodeExists(code);
    }
}
