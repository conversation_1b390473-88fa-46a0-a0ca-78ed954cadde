package com.masa.pts.core.service;

import com.masa.pts.core.domain.MemberFulfillmentRequest;
import com.masa.pts.core.model.MemberFulfillmentDto;
import com.masa.pts.core.repository.MemberFulfillmentRequestRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import org.springframework.data.domain.Pageable;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class FulfillmentService {

	private final MemberFulfillmentRequestRepository memberFulfillmentRequestRepository;

	public FulfillmentService(MemberFulfillmentRequestRepository memberFulfillmentRequestRepository) {
		this.memberFulfillmentRequestRepository = memberFulfillmentRequestRepository;
	}

	@Transactional(readOnly = true)
	public PageImpl<MemberFulfillmentDto> getFulfilmentList(Integer memberId, Pageable pageable) {
		Page<MemberFulfillmentDto> page = memberFulfillmentRequestRepository.getFulfillmentByMemberId(memberId, pageable);

		List<MemberFulfillmentDto> sortedPage = page.getContent()
				.stream()
				.sorted(Comparator.comparing(MemberFulfillmentDto::getStatus).reversed()
						.thenComparing(MemberFulfillmentDto::getRequestedDate))
				.collect(Collectors.toList());

		return new PageImpl(sortedPage, page.getPageable(), page.getTotalElements());
	}

	@Transactional(readOnly = true)
	public Optional<MemberFulfillmentRequest> findByIdAndIsDeleteIsFalse(Integer id) {
		return this.memberFulfillmentRequestRepository.findByIdAndIsDeleteIsFalse(id);
	}

	@Transactional(readOnly = true)
	public MemberFulfillmentDto getFulfillmentById(Integer id) {
		return this.memberFulfillmentRequestRepository.getFulfillmentById(id);
	}

	@Transactional(readOnly = true)
	public boolean isFulfillmentPending(Integer memberId, Integer productId) {
		return memberFulfillmentRequestRepository.isFulfillmentPending(memberId, productId);
	}
}
