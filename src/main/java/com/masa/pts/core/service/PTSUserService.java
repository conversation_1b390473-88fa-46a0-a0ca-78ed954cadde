package com.masa.pts.core.service;

import com.masa.pts.core.constant.PTSUserType;
import com.masa.pts.core.domain.Constant;
import com.masa.pts.core.domain.PTSUser;
import com.masa.pts.core.domain.PTSUserGroup;
import com.masa.pts.core.domain.PTSUserRole;
import com.masa.pts.core.domain.PTSUserRoleAccess;
import com.masa.pts.core.domain.PTSUserSalesChannel;
import com.masa.pts.core.domain.Role;
import com.masa.pts.core.domain.SalesChannel;
import com.masa.pts.core.domain.UserAccess;
import com.masa.pts.core.model.PTSUserSearchDTO;
import com.masa.pts.core.repository.PTSUserGroupRepository;
import com.masa.pts.core.repository.PTSUserRepository;
import com.masa.pts.core.repository.PTSUserRoleAccessRepository;
import com.masa.pts.core.repository.PTSUserRoleRepository;
import com.masa.pts.core.repository.PTSUserSalesChannelRepository;
import com.masa.pts.core.repository.RoleRepository;
import com.masa.pts.core.repository.SalesChannelRepository;
import com.masa.pts.core.repository.UserAccessRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class PTSUserService {

	private static final Logger log = LoggerFactory.getLogger(PTSUserService.class);
	
	private PTSUserRepository ptsUserRepository;
	
	private PTSUserRoleAccessRepository ptsUserRoleAccessRepository;
	
	private UserAccessRepository userAccessRepository;
	
	private RoleRepository roleRepository;
	
	private PTSUserRoleRepository ptsUserRoleRepository;
	
	private PTSUserGroupRepository ptsUserGroupRepository;
	
	private SalesChannelRepository salesChannelRepository;
	
	private PTSUserSalesChannelRepository ptsUserSalesChannelRepository;
	
	@Autowired
	public PTSUserService(PTSUserRepository ptsUserRepository, PTSUserRoleAccessRepository ptsUserRoleAccessRepository,
			UserAccessRepository userAccessRepository, RoleRepository roleRepository,
			PTSUserRoleRepository ptsUserRoleRepository, PTSUserGroupRepository ptsUserGroupRepository,
			SalesChannelRepository salesChannelRepository,
			PTSUserSalesChannelRepository ptsUserSalesChannelRepository) {
		this.ptsUserRepository = ptsUserRepository;
		this.ptsUserRoleAccessRepository = ptsUserRoleAccessRepository;
		this.userAccessRepository = userAccessRepository;
		this.roleRepository = roleRepository;
		this.ptsUserRoleRepository = ptsUserRoleRepository;
		this.ptsUserGroupRepository = ptsUserGroupRepository;
		this.salesChannelRepository = salesChannelRepository;
		this.ptsUserSalesChannelRepository = ptsUserSalesChannelRepository;
	}

	/**
	 * @param userName
	 * @return
	 */
	public Integer getPTSUserIDByUserName(String userName) {
		Integer ptsAppUser = Constant.DEFAULT_USER_ID;
		Optional<PTSUser> ptsUser = this.ptsUserRepository.findByUsername(userName);
		if(ptsUser.isPresent())		
			ptsAppUser = ptsUser.get().getEmployeeId();	
		
		return ptsAppUser;
	}

	public Optional<String> findUserNameById(Integer id) {
		return Optional.ofNullable(id)
				.flatMap(v -> ptsUserRepository.findById(v))
				.map(PTSUser::getUsername);
	}
	
	public boolean existsUserBy(String userName,String firstName,String lastName,boolean active,Integer userType) {
		return ptsUserRepository.findByUsernameAndFirstNameAndLastNameAndActiveAndUserType(userName, firstName, lastName, active, userType).isPresent();
	}
	
	public Optional<PTSUser> findByUserName(String userName){
		return ptsUserRepository.findByUsername(userName);
	}
	
	public Optional<PTSUser> findByUserNameAndUserType(String userName,Integer userType){
		return ptsUserRepository.findByUsernameAndUserType(userName,userType);
	}
	
	/**
	 * @param username
	 * @return
	 */
	public Set<PTSUserRole> getUserRoles(String username,Integer userType){
		log.info("Retrieving user roles for [{}]",username);
		Optional<PTSUser> optPtsUser = ptsUserRepository.findByUsernameAndUserType(username,userType);

		Set<PTSUserRole> userRoleSet = new HashSet<>();
		if (optPtsUser.isPresent()) {
			userRoleSet = getUserRoleByUserId(optPtsUser.get().getEmployeeId());			
		}
		else
		{
			log.error("External user [{}] not found, no roles provided",username);
		}
		log.info("Found [{}] user roles for [{}]",userRoleSet.size(),username);
		return userRoleSet;
	}
	
	public Set<PTSUserRole> getUserRoleByUserId(Integer employeeId)
	{
		return ptsUserRoleRepository.findByUserRoleIdUserEmployeeId(employeeId);
	}
	
	public Set<PTSUserGroup> getUserGroupByUserId(Integer employeeId)
	{
		return ptsUserGroupRepository.findByUserGroupIdUserEmployeeId(employeeId);
	}
	
	public Set<SalesChannel> getAllSalesChannels(){
		return salesChannelRepository.findAll();
	}
	/**
	 * @param username
	 * @param userRoles
	 * @return
	 */
	public List<GrantedAuthority> getUserAuthorities(String username, Set<PTSUserRole> userRoles) {
		
		log.info("Retrieve authorities for User [{}]",username);
		Set<Integer> roleIdSet = userRoles.stream().map(item->item.getUserRoleId().getRole().getId()).collect(Collectors.toSet());
		
		Set<PTSUserRoleAccess> userRoleAccessSet = ptsUserRoleAccessRepository.findAllByRoleIdIn(roleIdSet);
		
		Set<Integer> accessIdSet = userRoleAccessSet.stream().map(item->item.getAccessId()).collect(Collectors.toSet());
		
		List<GrantedAuthority> grantedAuths = new ArrayList<>();
		
		Iterable<UserAccess> userAccessSet = userAccessRepository.findAllById(accessIdSet);
		
		userAccessSet.forEach(item-> grantedAuths.add(new SimpleGrantedAuthority(item.getCode())));
		log.info("Return authorities for User [{}] [{}]",username,grantedAuths);
		return grantedAuths;
	}
	
	/**
	 * @return
	 */
	public Set<PTSUser> getAllUsers()
	{
		Set<Integer> userTypeSet = new HashSet<>();
		userTypeSet.add(PTSUserType.EXTERNAL.getUserType());
		return ptsUserRepository.findAllByUserTypeIn(userTypeSet);
	}
	
	public Page<PTSUserSearchDTO> getAllUsers(Integer userType, String userName, String firstName
			, String lastName, Integer roleId,Pageable pageable)
	{
		return ptsUserRepository.searchUsersByParams(
				userType, 
				Optional.ofNullable(userName).orElse(""), 
				Optional.ofNullable(firstName).orElse(""),
				Optional.ofNullable(lastName).orElse(""),
				roleId, 
				pageable);
	}
	
	public Set<Role> getAllRoles()
	{
		return roleRepository.findAllByIsObsolete(Boolean.FALSE);
	}
	
	public void saveUser(PTSUser ptsUser)
	{
		ptsUserRepository.save(ptsUser);
	}
	public Optional<PTSUser> findUserById(Integer userId)
	{
		return ptsUserRepository.findById(userId);
	}
	
	public Boolean userHasAccessToAccessCode(Integer employeeId,String accessCode) {
		return ptsUserRepository.userHasAccessToAccessCode(employeeId, accessCode);
	}
	public Set<PTSUserSalesChannel> getUserSalesChannelEntitys(Integer employeeId){
		return ptsUserSalesChannelRepository.findAllByUserSalesChannelIdUserEmployeeIdOrderByUserSalesChannelIdSalesChannelName(employeeId);
	}
}
