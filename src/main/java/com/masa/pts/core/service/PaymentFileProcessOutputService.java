package com.masa.pts.core.service;

import com.masa.pts.core.domain.PTSUser;
import com.masa.pts.core.model.PaymentBulkUpdateFilterCriteria;
import com.masa.pts.core.repository.PaymentFileProcessOutputRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Service
public class PaymentFileProcessOutputService {

    private final PaymentFileProcessOutputRepository paymentFileProcessOutputRepository;

    public PaymentFileProcessOutputService(
            PaymentFileProcessOutputRepository paymentFileProcessOutputRepository) {
        this.paymentFileProcessOutputRepository = paymentFileProcessOutputRepository;
    }

    @Transactional
    public int bulkUpdatePaymentFileProcessOutputStatusByFilter(PaymentBulkUpdateFilterCriteria input, PTSUser user) {
        return paymentFileProcessOutputRepository.bulkUpdatePaymentFileProcessOutputStatusByFilter(
                input.getNewReviewStatus(),
                user.getUsername(),
                input.getGroupCode(),
                input.getMemberId(),
                input.getStatus(),
                Optional.ofNullable(input.getFileName()).orElse(""),
                input.getEmployerId(),
                input.getReviewStatus(),
                PTSUtilityService.getStartOfDay(input.getCreatedDateStart()),
                PTSUtilityService.getEndOfDay(input.getCreatedDateEnd()));
    }
}
