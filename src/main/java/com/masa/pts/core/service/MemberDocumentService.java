package com.masa.pts.core.service;

import com.masa.pts.core.domain.FieldValueEntity;
import com.masa.pts.core.domain.MemberDocument;
import com.masa.pts.core.repository.FieldValueRepository;
import com.masa.pts.core.repository.MemberDocumentRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class MemberDocumentService {

    public static final String MEMBER_DOCUMENT_TYPE = "MemberDocumentType";

    private final MemberDocumentRepository memberDocumentRepository;
    private final FieldValueRepository fieldValueRepository;

    public MemberDocumentService(MemberDocumentRepository memberDocumentRepository,
                                 FieldValueRepository fieldValueRepository) {
        this.memberDocumentRepository = memberDocumentRepository;
        this.fieldValueRepository = fieldValueRepository;
    }

    @Transactional(readOnly = true)
    public Page<MemberDocument> documents(Integer memberId, Pageable pageable) {
        return memberDocumentRepository.findByMemberMemberIdAndIsDeleteIsFalse(memberId, pageable);
    }

    @Transactional
    public Optional<MemberDocument> document(Integer documentId) {
        return memberDocumentRepository.findByIdAndIsDeleteIsFalse(documentId);
    }

    @Transactional(readOnly = true)
    public List<String> memberDocumentType() {
        return fieldValueRepository.findAllByField(MEMBER_DOCUMENT_TYPE).stream()
                .map(FieldValueEntity::getName)
                .collect(Collectors.toList());
    }
}
