package com.masa.pts.core.service;

import com.masa.pts.core.domain.Note;
import com.masa.pts.core.domain.PTSUser;
import com.masa.pts.core.model.NoteDto;
import com.masa.pts.core.repository.NoteRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Service
public class MemberNoteService {

	private final NoteRepository noteRepository;

	public MemberNoteService(NoteRepository noteRepository) {
		this.noteRepository = noteRepository;
	}

	@Transactional(readOnly = true)
	public Page<NoteDto> getNotes(Integer memberId, Pageable pageable) {
		return noteRepository.findMemberNotes(memberId, pageable);
	}

	@Transactional
	public NoteDto createMemberNote(Note note, PTSUser user) {
		return Optional.of(noteRepository.save(note))
				.map(entity -> new NoteDto(
						entity.getNoteId(),
						entity.getNote(),
						entity.getNoteDate(),
						user.getUsername(),
						user.getFirstName(),
						user.getLastName()))
				.orElse(null);
	}

	@Transactional(readOnly = true)
	public Optional<Note> getNote(Integer documentId) {
		return noteRepository.findByNoteIdAndIsDeleteIsFalse(documentId);
	}

}
