package com.masa.pts.core.service;

import com.masa.pts.core.utils.common.ExcelDocument;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.poi.ss.util.WorkbookUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;

@Service
public class ExcelExportUtilityService {

    private static final Logger log = LoggerFactory.getLogger(ExcelExportUtilityService.class);
    private PTSUtilityService ptsUtilityService;

    @Autowired
    public ExcelExportUtilityService(PTSUtilityService ptsUtilityService) {
        this.ptsUtilityService = ptsUtilityService;
    }

    /**
     * @param sheetName
     * @param excelDocumentData
     * @return
     */
    public byte[] exportToExcel(String sheetName, ExcelDocument<?> excelDocumentData) {
        Workbook wb = null;
        byte[] file = new byte[1024];

        try {
            wb = WorkbookFactory.create(false);
            Sheet sheet = wb.createSheet(WorkbookUtil.createSafeSheetName(sheetName));
            Row headerRow = sheet.createRow(0);

            populateExcelHeader(excelDocumentData, headerRow);
            AtomicInteger rowNum = populateExcelData(excelDocumentData, sheet);
            populateExcelTotal(excelDocumentData, sheet, rowNum);

            ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream(1024);
            wb.write(arrayOutputStream);
            file = arrayOutputStream.toByteArray();
            arrayOutputStream.close();
        } catch (IOException e) {
            log.error("Error exporting data to file, message [{}]", e.getMessage());
        } finally {
            Optional.ofNullable(wb).ifPresent(sheets -> {
                try {
                    sheets.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            });
        }

		return file;
}

    private void populateExcelTotal(ExcelDocument<?> excelDocumentData, Sheet sheet, AtomicInteger rowNum) {
        if(CollectionUtils.isNotEmpty(excelDocumentData.getTotalRow())) {
            excelDocumentData.getTotalRow().forEach(data -> createExcelRow(sheet, data, rowNum.getAndIncrement()));
        }
    }

    private AtomicInteger populateExcelData(ExcelDocument<?> excelDocumentData, Sheet sheet) {
        log.info(" Total # of rows to export into excel [{}]", excelDocumentData.getDataRows().size());

        AtomicInteger rowNum = new AtomicInteger(1);
        excelDocumentData.getDataRows().forEach(data -> createExcelRow(sheet, data, rowNum.getAndIncrement()));
        return rowNum;
    }

    private void populateExcelHeader(ExcelDocument<?> excelDocumentData, Row headerRow) {
        Cell headerCell;
        List<String> columnNames = excelDocumentData.getColumnNames();

        for (int i = 0; i < columnNames.size(); i++) {
            headerCell = headerRow.createCell(i);
            headerCell.setCellValue(columnNames.get(i));
        }
    }

    /**
     * @param sheet
     * @param rawData
     * @param rowNum
     */
    private void createExcelRow(Sheet sheet, List<Object> rawData, int rowNum) {
        Row row = sheet.createRow(rowNum);
        AtomicInteger cellIndex = new AtomicInteger();
        rawData.forEach(data -> createExcelCell(row, data, cellIndex.getAndIncrement()));
    }

    /**
     * @param row
     * @param cellData
     * @param cellIndex
     * @return
     */
    private Cell createExcelCell(Row row, Object cellData, int cellIndex) {
        Cell cell = row.createCell(cellIndex);

        if (cellData instanceof java.lang.Boolean) {
            Boolean boolData = (Boolean) cellData;
            cell.setCellValue(boolData ? "Yes" : "No");
        } else if (cellData instanceof java.lang.Integer) {
            cell.setCellValue((Integer) cellData);
        } else if (cellData instanceof java.util.Date) {
            cell.setCellValue(ptsUtilityService.formatUtilDate((java.util.Date) cellData));
        } else if (cellData instanceof java.lang.Double) {
            cell.setCellValue((java.lang.Double) cellData);
        } else if (cellData instanceof java.lang.Long) {
            cell.setCellValue((java.lang.Long) cellData);
        } else if (cellData instanceof java.lang.Float) {
            cell.setCellValue((java.lang.Float) cellData);
        } else {
            cell.setCellValue(cellData != null ? cellData.toString() : null);
        }

        return cell;
    }
}
