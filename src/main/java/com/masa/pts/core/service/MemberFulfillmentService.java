package com.masa.pts.core.service;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import com.masa.pts.core.constant.FulfillmentType;
import com.masa.pts.core.domain.Constant;
import com.masa.pts.core.domain.FulfillmentMaterial;
import com.masa.pts.core.domain.FulfillmentProductMaterial;
import com.masa.pts.core.domain.MemberFee;
import com.masa.pts.core.model.GroupDivisionDTO;
import com.masa.pts.core.repository.FulfillmentMaterialRepository;
import com.masa.pts.core.repository.FulfillmentProductMaterialRepository;


@Service
public class MemberFulfillmentService {

	private static final Logger log = LoggerFactory.getLogger(MemberFulfillmentService.class);
	
	
	FulfillmentProductMaterialRepository fulfillmentProductMaterialRepository;
	FulfillmentMaterialRepository fulfillmentMaterialRepository;
	GroupService groupService;
	
	@Autowired
	public MemberFulfillmentService(FulfillmentProductMaterialRepository fulfillmentProductMaterialRepository,
			FulfillmentMaterialRepository fulfillmentMaterialRepository, GroupService groupService) {
		this.fulfillmentProductMaterialRepository = fulfillmentProductMaterialRepository;
		this.fulfillmentMaterialRepository = fulfillmentMaterialRepository;
		this.groupService = groupService;
	}

	/**
	 * @param memberId
	 * @param groupCode
	 * @param memberFees
	 * @return
	 */
	public MultiValueMap<String, String>  getMemberFulfillment(Integer memberId,String groupCode,Set<MemberFee> memberFees,List<Integer> divIdsWithAllIconsOnCard){
		
		MultiValueMap<String, String> outputData = new LinkedMultiValueMap<>();
		
		Optional<GroupDivisionDTO> optGrpDetails =  groupService.getGroupDivisionInfo(groupCode);
		
		Integer grpScId=0;
		Integer grpDivId=0;
		
		if(optGrpDetails.isPresent())
		{
			grpScId = optGrpDetails.get().getSalesChannelId();
			grpDivId = optGrpDetails.get().getDivisionId();
		}
		else
		{
			log.error("Member ID [{}] GroupEntity Code [{}] cannot determine Division and Sales Channel ",memberId,groupCode);
			return outputData;
		}
		
		String productCategory=null;
		
		Integer primaryPrdId = memberFees.stream().filter(item->!Constant.SETUP_FEE_IDS.contains(item.getFeeDetails().getFeeId()) && item.getUpgradeType().intValue()==0)
				.map(item->item.getProduct().getProductId()).findFirst().orElse(0);
		
		 productCategory = memberFees.stream().filter(item->!Constant.SETUP_FEE_IDS.contains(item.getFeeDetails().getFeeId()) && item.getUpgradeType().intValue()==0)
			.map(item->item.getProduct().getProductCategory().getCode()).findFirst().orElse(null);
		
		if(primaryPrdId.intValue()==0) 
		{
			primaryPrdId = memberFees.stream().filter(item->!Constant.SETUP_FEE_IDS.contains(item.getFeeDetails().getFeeId()) && item.getUpgradeType().intValue()!=0)
					.map(item->item.getProduct().getProductId()).findFirst().orElse(0);
			productCategory = memberFees.stream().filter(item->!Constant.SETUP_FEE_IDS.contains(item.getFeeDetails().getFeeId()) && item.getUpgradeType().intValue()!=0)
					.map(item->item.getProduct().getProductCategory().getCode()).findFirst().orElse(null);
		}		
		
		Set<FulfillmentProductMaterial> prdMaterialSet = fulfillmentProductMaterialRepository.findAllBySalesChannelIdAndProductId(grpScId,primaryPrdId);
		
		List<Integer> materialIds = prdMaterialSet.stream().map(FulfillmentProductMaterial::getMaterialId).collect(Collectors.toList());
		
		List<FulfillmentMaterial> materialList = fulfillmentMaterialRepository.findByMaterialIdIn(materialIds);
		
		List<Integer> cardMaterialIds = prdMaterialSet.stream().filter(item->item.getFulfillmentRequestTypeId().compareTo(FulfillmentType.CARD.getType())==0)
				.map(FulfillmentProductMaterial::getMaterialId).collect(Collectors.toList());
		
		List<Integer> msaMaterialIds = prdMaterialSet.stream().filter(item->item.getFulfillmentRequestTypeId().compareTo(FulfillmentType.MSA.getType())==0)
				.map(FulfillmentProductMaterial::getMaterialId).collect(Collectors.toList());
		
		List<String> cardMaterial = materialList.stream()
				.filter(item->(cardMaterialIds.contains(item.getMaterialId()) && !"CRD_HOLD".equalsIgnoreCase(item.getName())))
				.map(FulfillmentMaterial::getName)
				.collect(Collectors.toList());
		
		List<String> msaMaterial = materialList.stream()
				.filter(item->(msaMaterialIds.contains(item.getMaterialId()) && !"LTR_ADD".equalsIgnoreCase(item.getName())))
				.map(FulfillmentMaterial::getName)
				.collect(Collectors.toList());
		
		outputData.addAll(FulfillmentType.CARD.name(), cardMaterial);
		outputData.addAll(FulfillmentType.MSA.name(), msaMaterial);
		
		if(null != productCategory && "EMG".equalsIgnoreCase(productCategory))
		{
			outputData.add("ICON", "AMB");
		}		
		else if(divIdsWithAllIconsOnCard.contains(grpDivId))
		{
			outputData.addAll("ICON", Arrays.asList("AMB","PLN","HEL"));
		}
		log.info("Member ID[{}], Fulfillment Cards {}",memberId,outputData.get(FulfillmentType.CARD.name()));
		log.info("Member ID[{}], Fulfillment MSA {}",memberId,outputData.get(FulfillmentType.MSA.name()));
		log.info("Member ID[{}], Card ICONS {}",memberId,outputData.get("ICON"));
		
		return outputData;
	}
	
}
