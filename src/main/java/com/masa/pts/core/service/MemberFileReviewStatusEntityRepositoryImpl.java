package com.masa.pts.core.service;

import com.masa.pts.core.domain.ReviewStatus;
import com.masa.pts.core.model.MemberFileIdStatusDTO;
import com.masa.pts.core.repository.MemberFileReviewStatusEntityRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

@Component
public class MemberFileReviewStatusEntityRepositoryImpl implements MemberFileReviewStatusEntityRepository {

    private final JdbcTemplate jdbcTemplate;

    @Autowired
    public MemberFileReviewStatusEntityRepositoryImpl(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    @Override
    public int batchInsertToLogTable(ReviewStatus reviewStatus, String updatedBy, List<MemberFileIdStatusDTO> list) {
        return jdbcTemplate.batchUpdate(
                "insert into ex_edi_record_status_log(" +
                        "record_id, current_status, new_status, status_date, status_updated_by) VALUES (?,?,?,?,?)",
                new BatchPreparedStatementSetter() {

                    @Override
                    public void setValues(PreparedStatement preparedStatement, int i) throws SQLException {
                        preparedStatement.setInt(1, list.get(i).getId());
                        preparedStatement.setString(2, list.get(i).getReviewStatus().name());
                        preparedStatement.setString(3, reviewStatus.name());
                        preparedStatement.setTimestamp(4, new Timestamp(new Date().getTime()));
                        preparedStatement.setString(5, updatedBy);
                    }

                    @Override
                    public int getBatchSize() {
                        return list.size();
                    }
                }).length;
    }
}
