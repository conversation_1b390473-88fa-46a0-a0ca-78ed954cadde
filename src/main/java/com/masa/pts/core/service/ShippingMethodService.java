package com.masa.pts.core.service;

import com.masa.pts.core.domain.ShippingMethod;
import com.masa.pts.core.repository.ShippingMethodRepository;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ShippingMethodService {

	private final ShippingMethodRepository shippingMethodRepository;

	public ShippingMethodService(ShippingMethodRepository shippingMethodRepository) {
		this.shippingMethodRepository = shippingMethodRepository;
	}

	public List<ShippingMethod> findAll() {
		return shippingMethodRepository.findAll();
	}
}
