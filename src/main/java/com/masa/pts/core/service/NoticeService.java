package com.masa.pts.core.service;

import java.io.Serializable;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.masa.pts.core.domain.InvoiceDetail;
import com.masa.pts.core.domain.Notice;
import com.masa.pts.core.repository.NoticeRepository;

@Service
public class NoticeService implements Serializable {

	private static final long serialVersionUID = 3672621120611029655L;

	@Autowired
	private NoticeRepository noticeRepository; 
	
	public void updateMemberNotieFromInvoice(InvoiceDetail invoiceDetail,Integer payId) {
		
		//first get existing latest pending notice
		Integer noticeId = noticeRepository.getMaxNoticeIdForMember(invoiceDetail.getMember().getMemberId());
		
		if(null != noticeId && noticeId.intValue() !=0) {
			Notice notice = noticeRepository.findById(noticeId).get();
			notice.setPayId(payId);
			noticeRepository.save(notice);
		}
		//commented out notice create logic, as notice should be created from Create Notice screen
		// notice being created for montly members which is not required.
		/*if(null == noticeId || noticeId==0) {
			//no notice exist for member, create one and mark with payment
			Notice notice = new Notice();
			notice.setAmount(new BigDecimal(Double.toString(invoiceDetail.getAmountDue())));
			notice.setMemberId(invoiceDetail.getMemberId());
			notice.setNoticeDate(new java.util.Date());
			notice.setPayId(payId);
			notice.setRenewDate(invoiceDetail.getRenewDate());
			noticeRepository.save(notice);
		}
		else {
			Notice notice = noticeRepository.findById(noticeId).get();
			notice.setPayId(payId);
			noticeRepository.save(notice);
		}*/
	}
}
