package com.masa.pts.core.service;

import com.google.common.collect.Lists;
import com.masa.pts.core.constant.AddressType;
import com.masa.pts.core.constant.AuditEventType;
import com.masa.pts.core.constant.CommissionType;
import com.masa.pts.core.constant.GroupBillType;
import com.masa.pts.core.constant.MemberActiveStatus;
import com.masa.pts.core.constant.MemberEventType;
import com.masa.pts.core.constant.MemberPayStatus;
import com.masa.pts.core.constant.MemberPayType;
import com.masa.pts.core.constant.ProductUpgradeType;
import com.masa.pts.core.domain.Address;
import com.masa.pts.core.domain.Agent;
import com.masa.pts.core.domain.AgentCommission;
import com.masa.pts.core.domain.AgentManager;
import com.masa.pts.core.domain.ApplicationSource;
import com.masa.pts.core.domain.ConfigParamName;
import com.masa.pts.core.domain.Constant;
import com.masa.pts.core.domain.Dependant;
import com.masa.pts.core.domain.GroupEntity;
import com.masa.pts.core.domain.GroupProductFee;
import com.masa.pts.core.domain.Member;
import com.masa.pts.core.domain.Member.MemberInfo;
import com.masa.pts.core.domain.Member.MemberSummary;
import com.masa.pts.core.domain.Member.MemberSummaryForGrp;
import com.masa.pts.core.domain.Member.MemberSummaryWithGroup;
import com.masa.pts.core.domain.MemberAttributes;
import com.masa.pts.core.domain.MemberCommission;
import com.masa.pts.core.domain.MemberCoverageLapse;
import com.masa.pts.core.domain.MemberEmployeeIdEntity;
import com.masa.pts.core.domain.MemberFee;
import com.masa.pts.core.domain.MemberFulfillmentRequest;
import com.masa.pts.core.domain.MemberIdentifierType;
import com.masa.pts.core.domain.Note;
import com.masa.pts.core.domain.PaymentProcessOutput;
import com.masa.pts.core.domain.Product;
import com.masa.pts.core.exception.MemberNotFoundException;
import com.masa.pts.core.exception.UnprocessableEntityException;
import com.masa.pts.core.model.MemberDependantInput;
import com.masa.pts.core.model.MemberFulfillmentDataDto;
import com.masa.pts.core.model.MemberPayment;
import com.masa.pts.core.model.MemberSetup;
import com.masa.pts.core.model.MemberSetupProduct;
import com.masa.pts.core.model.MemberShortInfoDto;
import com.masa.pts.core.model.MemberUpdateSetup;
import com.masa.pts.core.model.PaymentPostStatus;
import com.masa.pts.core.model.UpdateMemberInfoInput;
import com.masa.pts.core.repository.AddressRepository;
import com.masa.pts.core.repository.AgentCommissionRepository;
import com.masa.pts.core.repository.AgentManagerRepository;
import com.masa.pts.core.repository.ApplicationSourceRepository;
import com.masa.pts.core.repository.DependantRepository;
import com.masa.pts.core.repository.FeeRepository;
import com.masa.pts.core.repository.FrequencyRepository;
import com.masa.pts.core.repository.GroupProductFeeRepository;
import com.masa.pts.core.repository.MemberAttributesRepository;
import com.masa.pts.core.repository.MemberCoverageLapseRepository;
import com.masa.pts.core.repository.MemberFeeRepository;
import com.masa.pts.core.repository.MemberFulfillmentRequestRepository;
import com.masa.pts.core.repository.MemberRepository;
import com.masa.pts.core.repository.MemberEmployeeIdRepository;
import com.masa.pts.core.repository.ProductUpgradeDowngradeRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebInputException;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.Month;
import java.time.Year;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.masa.pts.core.constant.MemberActiveStatus.ACTIVE;
import static com.masa.pts.core.constant.MemberActiveStatus.CANCELLED;
import static com.masa.pts.core.constant.MemberActiveStatus.SUSPENDED;
import static com.masa.pts.core.constant.MemberActiveStatus.getStatusName;
import static com.masa.pts.core.domain.Constant.B2B_DIVISION_ID;
import static com.masa.pts.core.domain.Constant.DEFULT_DATE_1900;
import static com.masa.pts.core.domain.Constant.MONTHS_RANGE_FOR_ENROLL;
import static com.masa.pts.core.service.PTSUtilityService.convertLocalToUtilDate;
import static com.masa.pts.core.service.PTSUtilityService.convertUtilToLocalDate;
import static com.masa.pts.core.service.PTSUtilityService.isDateWithInRange;
import static org.apache.commons.collections4.CollectionUtils.isEmpty;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;

@Service
public class MemberService {

	private static final Logger log = LoggerFactory.getLogger(MemberService.class);
	public static final int MAX_ALLOWED_SQL_PARAMETERS_COUNT = 2050;

	@Autowired
	private MemberRepository memberRepository;

	@Autowired
	private AddressService addressService;

	@Autowired
	private MemberFeeRepository memberFeeRepository;

	@Autowired
	private MemberFulfillmentRequestRepository memberFulfillmentRequestRepository;

	@Autowired
	private PTSUserService ptsUserService;

	@Autowired
	private AgentCommissionRepository agentCommissionRepository;

	@Autowired
	private AgentManagerRepository agentManagerRepository;

	@Autowired
	private FeeRepository feeRepository;

	@Autowired
	private FrequencyRepository frequencyRepository;

	@Autowired
	private MemberAttributesRepository memberAttributesRepository;

	@Autowired
	private MemberProductFeeService memberProductFeeService;

	@Autowired
	private DependantRepository dependantRepository;

	@Autowired
	private AgentService agentService;

	@Autowired
	private GroupService groupService;

	@Autowired
	private ProductService productService;

	@Autowired
	private CommissionService commissionService;

	@Autowired
	private PaymentPostService paymentPostService;

	@Autowired
	PlatformTransactionManager transactionManager;

	@Autowired
	ProductUpgradeDowngradeRepository productUpgradeDowngradeRepository;

	@Autowired
	GroupProductFeeRepository groupProductFeeRepository;

	@Autowired
	MemberCoverageLapseRepository memberCoverageLapseRepository;

	@Autowired
	PTSUtilityService ptsUtilityService;

	@Autowired
	Environment environment;

	@Autowired
	ApplicationSourceRepository applicationSourceRepository;

	@Autowired
	MemberAuditEventService memberAuditEventService;

	@Autowired
	PaymentService paymentService;

	@Autowired
	private AddressRepository addressRepository;
	
    @Autowired
    private ConfigParamService configParamService;
    
    
    
    public boolean isNotValidStateForMemberAdd(String stateCd) {
    	if(stateCd == null) return false;
    	List<String> configValues = ConfigParamService.convertCommaSeparatedStringToList(
    			configParamService.getParamValue(ConfigParamName.EXCLUDE_STATES_TO_ADD_MEMBER.name()));
    	if(configValues.contains(stateCd)) {
    		return true;
    	}else {
    		return false;
    	}
    }
    
    public boolean isStateInInsuranceStateToRestrictFromCompass360MemberEnrolling(String stateCd) {
    	if(stateCd == null) return false;
    	List<String> configValues = ConfigParamService.convertCommaSeparatedStringToList(
    			configParamService.getParamValue(ConfigParamName.INSURANCE_STATES_TO_RESTRICT_COMPASS360_MEMBER_ENROLLING.name()));
    	if(configValues.contains(stateCd)) {
    		return true;
    	}else {
    		return false;
    	}
    }
    
    public boolean isUserAllowedForMemberEnrollingInInsuranceStates(String userName) {
    	if(userName == null) return false;
    	List<String> configValues = ConfigParamService.convertCommaSeparatedStringToList(
    			configParamService.getParamValue(ConfigParamName.INSURANCE_STATES_ALLOWED_USERS_FOR_MEMBER_ENROLLING.name()));
    	if(configValues.contains(userName)) {
    		return true;
    	}else {
    		return false;
    	}
    }

	//TODO determine which method to use based on performance & data pull 
	// findMemberById or getMemberDetails
	
	public Optional<Member> getMemberDetails(Integer memberId){
		Member member = memberRepository.getMemberDetails(memberId);
		if(null !=member) {
			setMemberActiveEmployeeId(member);
		}
		return Optional.ofNullable(member);
	}
	
	public Optional<Member> findMemberById(Integer memberId) {
		Optional<Member> optMem = this.memberRepository.findById(memberId);
		optMem.ifPresent(this::setMemberActiveEmployeeId);
		return optMem;
	}
	public void generateInsurancePolicyNumber(Integer memberId, String invoiceStatus, String emailStatus,
			String docuSignStatus, String eventSource) {
		memberRepository.generateInsurancePolicyNumber(memberId, invoiceStatus, emailStatus, docuSignStatus, eventSource);
	}
	/**
	 * @param renewDate
	 * @param premFrequency
	 * @param periodFee
	 * @param premiumTotal
	 * @return New Renew Date
	 */
	public  Date getMemberNextRenewDate(Date renewDate, int premFrequency, double periodFee,	double premiumTotal) {

		double ratio =  (periodFee/premiumTotal);
		double fractionalPart = ratio % 1;
		long integralPart = (long) (ratio - fractionalPart);

		LocalDate currentRenewDate = new java.util.Date(renewDate.getTime()).toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
		LocalDate newRenewDate = currentRenewDate;

		long fractionOfDaysMonthYear=0;
		int yearDays=0,monthsDays=0;

		switch (premFrequency)
		{
			case Constant.FREQ_WEEK:
				newRenewDate= currentRenewDate.plusDays(integralPart * 7);
				fractionOfDaysMonthYear = (long) Math.round(fractionalPart * 7);
				newRenewDate = newRenewDate.plusDays(fractionOfDaysMonthYear);
				break;
			case Constant.FREQ_BIWEEK:
				newRenewDate= currentRenewDate.plusDays(integralPart * 14);
				fractionOfDaysMonthYear = (long) Math.round(fractionalPart * 14);
				newRenewDate = newRenewDate.plusDays(fractionOfDaysMonthYear);
				break;
			case Constant.FREQ_SEMI_MONTHLY:
				newRenewDate= currentRenewDate.plusDays(integralPart * 15);
				fractionOfDaysMonthYear = (long) Math.round(fractionalPart * 15);
				newRenewDate = newRenewDate.plusDays(fractionOfDaysMonthYear);
				break;
			case Constant.FREQ_MONTH:
				newRenewDate= currentRenewDate.plusMonths(integralPart);
				monthsDays = Month.of(newRenewDate.getMonthValue()).length(newRenewDate.isLeapYear());
				fractionOfDaysMonthYear = (long) Math.round(fractionalPart * monthsDays);
				newRenewDate = newRenewDate.plusDays(fractionOfDaysMonthYear);
				break;
			case Constant.FREQ_YEAR:
				newRenewDate= currentRenewDate.plusYears(integralPart);
				yearDays = Year.of(newRenewDate.getYear()).length();
				fractionOfDaysMonthYear = (long) Math.round(fractionalPart * yearDays);
				newRenewDate = newRenewDate.plusDays(fractionOfDaysMonthYear);
				break;
			case Constant.FREQ_FIVE_YEAR:
				newRenewDate= currentRenewDate.plusYears(integralPart * 5);
				yearDays = Year.of(newRenewDate.getYear()).length();
				fractionOfDaysMonthYear = (long) Math.round(fractionalPart * yearDays * 5);
				newRenewDate = newRenewDate.plusDays(fractionOfDaysMonthYear);
				break;
			case Constant.FREQ_BIENNIAL:
				newRenewDate= currentRenewDate.plusYears(integralPart * 2);
				yearDays = Year.of(newRenewDate.getYear()).length();
				fractionOfDaysMonthYear = (long) Math.round(fractionalPart * yearDays * 2);
				newRenewDate = newRenewDate.plusDays(fractionOfDaysMonthYear);
				break;
			case Constant.FREQ_TRIENNIAL:
				newRenewDate= currentRenewDate.plusYears(integralPart * 3);
				yearDays = Year.of(newRenewDate.getYear()).length();
				fractionOfDaysMonthYear = (long) Math.round(fractionalPart * yearDays * 3);
				newRenewDate = newRenewDate.plusDays(fractionOfDaysMonthYear);
				break;
			case Constant.FREQ_QUADRENNIAL:
				newRenewDate= currentRenewDate.plusYears(integralPart * 4);
				yearDays = Year.of(newRenewDate.getYear()).length();
				fractionOfDaysMonthYear = (long) Math.round(fractionalPart * yearDays * 4);
				newRenewDate = newRenewDate.plusDays(fractionOfDaysMonthYear);
				break;
			case Constant.FREQ_DECENNIAL:
				newRenewDate= currentRenewDate.plusYears(integralPart * 10);
				yearDays = Year.of(newRenewDate.getYear()).length();
				fractionOfDaysMonthYear = (long) Math.round(fractionalPart * yearDays * 10);
				newRenewDate = newRenewDate.plusDays(fractionOfDaysMonthYear);
				break;
			case Constant.FREQ_LIFETIME:
				newRenewDate = new java.util.Date(DEFULT_DATE_1900.getTime()).toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
				break;
			default:
				return renewDate; // change from 1900 to current renew date
		}

		return Date.from(newRenewDate.atStartOfDay(java.time.ZoneId.systemDefault()).toInstant());
	}

	public  Date getMemberNextRenewDate(Date renewDate, int premFrequency, double ratio) {

		double fractionalPart = ratio % 1;
		long integralPart = (long) (ratio - fractionalPart);

		LocalDate currentRenewDate = new java.util.Date(renewDate.getTime()).toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
		LocalDate newRenewDate = currentRenewDate;

		long fractionOfDaysMonthYear=0;
		int yearDays=0,monthsDays=0;

		switch (premFrequency)
		{
			case Constant.FREQ_WEEK:
				newRenewDate= currentRenewDate.plusDays(integralPart * 7);
				fractionOfDaysMonthYear = (long) Math.round(fractionalPart * 7);
				newRenewDate = newRenewDate.plusDays(fractionOfDaysMonthYear);
				break;
			case Constant.FREQ_BIWEEK:
				newRenewDate= currentRenewDate.plusDays(integralPart * 14);
				fractionOfDaysMonthYear = (long) Math.round(fractionalPart * 14);
				newRenewDate = newRenewDate.plusDays(fractionOfDaysMonthYear);
				break;
			case Constant.FREQ_SEMI_MONTHLY:
				newRenewDate= currentRenewDate.plusDays(integralPart * 15);
				fractionOfDaysMonthYear = (long) Math.round(fractionalPart * 15);
				newRenewDate = newRenewDate.plusDays(fractionOfDaysMonthYear);
				break;
			case Constant.FREQ_MONTH:
				newRenewDate= currentRenewDate.plusMonths(integralPart);
				monthsDays = Month.of(newRenewDate.getMonthValue()).length(newRenewDate.isLeapYear());
				fractionOfDaysMonthYear = (long) Math.round(fractionalPart * monthsDays);
				newRenewDate = newRenewDate.plusDays(fractionOfDaysMonthYear);
				break;
			case Constant.FREQ_YEAR:
				newRenewDate= currentRenewDate.plusYears(integralPart);
				yearDays = Year.of(newRenewDate.getYear()).length();
				fractionOfDaysMonthYear = (long) Math.round(fractionalPart * yearDays);
				newRenewDate = newRenewDate.plusDays(fractionOfDaysMonthYear);
				break;
			case Constant.FREQ_FIVE_YEAR:
				newRenewDate= currentRenewDate.plusYears(integralPart * 5);
				yearDays = Year.of(newRenewDate.getYear()).length();
				fractionOfDaysMonthYear = (long) Math.round(fractionalPart * yearDays * 5);
				newRenewDate = newRenewDate.plusDays(fractionOfDaysMonthYear);
				break;
			case Constant.FREQ_BIENNIAL:
				newRenewDate= currentRenewDate.plusYears(integralPart * 2);
				yearDays = Year.of(newRenewDate.getYear()).length();
				fractionOfDaysMonthYear = (long) Math.round(fractionalPart * yearDays * 2);
				newRenewDate = newRenewDate.plusDays(fractionOfDaysMonthYear);
				break;
			case Constant.FREQ_TRIENNIAL:
				newRenewDate= currentRenewDate.plusYears(integralPart * 3);
				yearDays = Year.of(newRenewDate.getYear()).length();
				fractionOfDaysMonthYear = (long) Math.round(fractionalPart * yearDays * 3);
				newRenewDate = newRenewDate.plusDays(fractionOfDaysMonthYear);
				break;
			case Constant.FREQ_QUADRENNIAL:
				newRenewDate= currentRenewDate.plusYears(integralPart * 4);
				yearDays = Year.of(newRenewDate.getYear()).length();
				fractionOfDaysMonthYear = (long) Math.round(fractionalPart * yearDays * 4);
				newRenewDate = newRenewDate.plusDays(fractionOfDaysMonthYear);
				break;
			case Constant.FREQ_DECENNIAL:
				newRenewDate= currentRenewDate.plusYears(integralPart * 10);
				yearDays = Year.of(newRenewDate.getYear()).length();
				fractionOfDaysMonthYear = (long) Math.round(fractionalPart * yearDays * 10);
				newRenewDate = newRenewDate.plusDays(fractionOfDaysMonthYear);
				break;
			default:
				return renewDate; // change from 1900 to current renew date
		}

		return Date.from(newRenewDate.atStartOfDay(java.time.ZoneId.systemDefault()).toInstant());
	}


	/**
	 * @param currentRenewDate
	 * @param frequency
	 * @param periods
	 * @return
	 */
	public Date calcMemberNextRenewDate(Date currentRenewDate,int frequency,int periods) {

		LocalDate localCurrentRenewDate= new java.util.Date(currentRenewDate.getTime()).toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
		LocalDate newRenewDate = localCurrentRenewDate;

		Calendar time = Calendar.getInstance();
		time.setTime(currentRenewDate);
		switch (frequency)
		{
			case Constant.FREQ_WEEK:
				time.add(Calendar.DATE, periods * 7);
				newRenewDate = localCurrentRenewDate.plusWeeks(periods);
				break;
			case Constant.FREQ_BIWEEK:
				time.add(Calendar.DATE, periods * 14);
				newRenewDate = localCurrentRenewDate.plusWeeks(periods * 2);
				break;
			case Constant.FREQ_SEMI_MONTHLY:
				time.add(Calendar.DATE, periods * 15);
				newRenewDate = localCurrentRenewDate.plusMonths(periods/2);
				break;
			case Constant.FREQ_MONTH:
				time.add(Calendar.MONTH, periods);
				newRenewDate = localCurrentRenewDate.plusMonths(periods);
				break;
			case Constant.FREQ_YEAR:
				time.add(Calendar.YEAR, periods);
				newRenewDate = localCurrentRenewDate.plusYears(periods);
				break;
			case Constant.FREQ_FIVE_YEAR:
				time.add(Calendar.YEAR, periods * 5);
				newRenewDate = localCurrentRenewDate.plusYears(periods * 5);
				break;
			case Constant.FREQ_BIENNIAL:
				time.add(Calendar.YEAR, periods * 2);
				newRenewDate = localCurrentRenewDate.plusYears(periods * 2);
				break;
			case Constant.FREQ_TRIENNIAL:
				time.add(Calendar.YEAR, periods * 3);
				newRenewDate = localCurrentRenewDate.plusYears(periods * 3);
				break;
			case Constant.FREQ_QUADRENNIAL:
				time.add(Calendar.YEAR, periods * 4);
				newRenewDate = localCurrentRenewDate.plusYears(periods * 4);
				break;
			case Constant.FREQ_DECENNIAL:
				time.add(Calendar.YEAR, periods * 10);
				newRenewDate = localCurrentRenewDate.plusYears(periods * 10);
				break;
			default:
				return currentRenewDate;
		}

		return Date.from(newRenewDate.atStartOfDay(java.time.ZoneId.systemDefault()).toInstant());
	}

	/**
	 * @param reInstateDate
	 * @return
	 */
	public boolean isMemberReinstated(Date reInstateDate) {

		boolean memberReInstated=false;
		LocalDate localDateOf19000101 = LocalDate.of(1900, 1, 1);
		Date dateOf19000101 = Date.from(localDateOf19000101.atStartOfDay(java.time.ZoneId.systemDefault()).toInstant());

		if(reInstateDate==null) {
			memberReInstated = false;
		}
		else if(dateOf19000101.before(reInstateDate)) {
			memberReInstated = true;
		}
		return memberReInstated;
	}

	/*
	public boolean identifyMemberPartOfDownPaymentGroup(MemberSummary memberSummary) {

		List<String> downpaymentGrpList = environment.getProperty("pts.installment.groups", ArrayList.class);
		if (downpaymentGrpList.contains(memberSummary.getGroupGroupCode()))
			return true;

		return false;
	}

	public boolean identifyMemberPartOfDownPaymentGroup(String groupCode) {
		List<String> downpaymentGrpList = environment.getProperty("pts.installment.groups", ArrayList.class);
		return downpaymentGrpList.contains(groupCode);
	}

	public boolean identifyMemberPartOfLifetimeGroup(String groupCode) {
		List<String> lifetimeGrpList = environment.getProperty("pts.lifetime.groups", ArrayList.class);
		return lifetimeGrpList.contains(groupCode);
	}*/
	/**
	 * @param memberFees
	 * @return Frequency ID - Weekly/Monthly/etc.
	 * Find Frequency ID of the Product Fee which is not SetupFee and upgrade type =0
	 * If upgrade type=0 Product Fee not found then get then low upgrade type product, Freq ID
	 *
	 */
	public Integer getMemberFeesFrequencyForRenewal(Set<MemberFee> memberFees) {

		Integer freqId = 0;

		freqId = memberFees.stream().filter(item -> item.getUpgradeType() == 0 && item.getFeeDetails().getFeeId() != 1)
								.mapToInt(item-> item.getFrequencyDetails().getID())
												.findFirst().orElse(0);
		if(freqId==0)
		{
			int initUpgradeType=1,currentUpgrdaeType=1;
			for(MemberFee item : memberFees) {
				if( (initUpgradeType==1 || item.getUpgradeType() <= currentUpgrdaeType )   &&  !Constant.SETUP_FEE_IDS.contains(item.getFeeDetails().getFeeId()) ) {
					freqId = item.getFrequencyDetails().getID();
					currentUpgrdaeType = item.getUpgradeType();
					initUpgradeType++;
				}

			}
		}

		return freqId;
	}

	/**
	 * @param memberId
	 * @param paymentAmt
	 * @param ptsPaymentAppuser
	 * @param memEventType
	 * @param lastPayDate
	 * @param groupCode
	 * @param paymentInvoiceDate
	 * @return
	 */
	public Date updateMemberRenewDate(Integer memberId, Double paymentAmt, Integer ptsPaymentAppuser,MemberEventType memEventType,
			Date lastPayDate,String groupCode,Date paymentInvoiceDate) {

		//last pay date is null for chargeback/un chargeback - need to get original payment- invoice date.

		Member member = memberRepository.findById(memberId).get();

		Set<MemberFee> activeMemberFees = member.getMemberFee().stream()
				.filter(item-> isDateWithInRange(paymentInvoiceDate,item.getEffectiveStartDate(),item.getEffectiveEndDate()))
				.collect(Collectors.toSet());

		// amount due per month/freq
		double premiumAmt = activeMemberFees.stream().filter(item -> item.getFeeDetails().getFeeId() != 1).mapToDouble(MemberFee::getAmount).sum();

		Integer freqId = getMemberFeesFrequencyForRenewal(activeMemberFees);

		int paymentPeriods = (int) Math.floor(paymentAmt / premiumAmt);
		if (paymentPeriods < 1 && paymentPeriods >= 0)
			paymentPeriods = 1;

		Date newRenewDate;

		boolean memberPartOfDownpaymentGroup =  groupService.isDownPaymentGroup(member.getGroup().getGroupCode());
		if(memberPartOfDownpaymentGroup && MemberEventType.PAYMENT.compareTo(memEventType)==0)
		{
			Integer productId = activeMemberFees.stream().filter(item->item.getFeeDetails().getFeeId() !=1 && item.getProduct().getUpgradeProduct().intValue()==0)
					.map(item->item.getProduct().getProductId()).findFirst().orElse(0);
			boolean paymentsExists = paymentPostService.memberHasPayments(memberId, productId,lastPayDate);
			if(!paymentsExists)
			{
				log.info("First payment being posted to member, Member Id [{}] , Product Id [{}]",memberId,productId);
				//for down payment group member, first payment should move renew date to 1 month not based on payment(downpayment) amount.
				newRenewDate = getMemberNextRenewDate(member.getRenewDate(), freqId, premiumAmt, premiumAmt);
			}
			else
			{
				newRenewDate = getMemberNextRenewDate(member.getRenewDate(), freqId, paymentAmt, premiumAmt);
			}
		}
		else
		{
			newRenewDate = getMemberNextRenewDate(member.getRenewDate(), freqId, paymentAmt, premiumAmt);
		}

		member.setRenewDate(newRenewDate);
		if(null != lastPayDate)
		{
			member.setLastInvoiceDate(lastPayDate);
		}
		member.setModifiedDate(new Date());
		member.setModifiedBy(ptsPaymentAppuser);
		memberRepository.save(member);

		populateMemberAttributes(member,ptsPaymentAppuser,lastPayDate, groupCode);
		// TODO - move this to before renew update for lifetime product CB , for payment this should happen after renew date update.

		return newRenewDate;
	}

		/**
	 * @param memberId
	 * @return
	 */
	public String getMemberGroup(Integer memberId) {

		/*
		Optional<Member.MemberSummary> optMember = this.memberRepository.findByMemberId(memberId, Member.MemberSummary.class);

		if(optMember.isPresent() && optMember.get().getGroupGroupCode() !=null) {
			return optMember.get().getGroupGroupCode();
		}
		return null;
		*/
		return getMemberGroupOnEffectiveDate(memberId, new Date());
	}

	public String getMemberGroupOnEffectiveDate(Integer memberId,Date effectiveDate) {
		return memberFeeRepository.findMemberActiveGroup(memberId, effectiveDate);
	}

	/**
	 * @param memberId
	 * @param groupList
	 * @return
	 */
	public boolean isMemberPartOfGroup(Integer memberId,Set<String> groupList) {
		return groupList.contains(getMemberGroup(memberId));
	}

	/**
	 * @param memberId
	 * @param type
	 * @return
	 */
	public <T> Optional<T> getMemberDetails(Integer memberId,Class<T> type) {
		return this.memberRepository.findByMemberId(memberId, type);
	}

	/**
	 * @param requests
	 */
	public Iterable<MemberFulfillmentRequest> createMemberFullfillmentRequest(List<MemberFulfillmentRequest> requests) {
		return this.memberFulfillmentRequestRepository.saveAll(requests);
	}

	/**
	 * @param addressId
	 * @return
	 */
	public Address getMemberAddress(Integer addressId) {
		return this.addressService.getAddressById(addressId);
	}

	/**
	 * @param memberId
	 * @return
	 */
	public Double getMemberPremium(Integer memberId) {
		Set<MemberFee> memberFees = memberFeeRepository.findAllByMemberIdAndProductProductIdGreaterThanOrderByProduct(memberId,0);
		Double premiumAmt  = memberFees.stream().filter(item->item.getFeeDetails().getFeeId() !=1).mapToDouble(MemberFee::getAmount).sum();
		return premiumAmt;
	}

	/**
	 * @param memberId
	 * @param memberNote
	 */
	public boolean addNoteToMember(Integer memberId,Note memberNote) {
		Optional<Member> optMember = this.memberRepository.findByMemberId(memberId, Member.class);
		if(optMember.isPresent()) {
			optMember.get().addNote(memberNote);
			this.memberRepository.save(optMember.get());
			return true;
		}
		return false;
	}

	/**
	 * @param member
	 * @param noteMsg
	 * @param ptsUserId
	 * @return
	 */
	public void createMemberNote(Member member, String noteMsg,Integer ptsUserId)
	{
		Note note = new Note();
		note.setEnteredBy(ptsUserId);
		note.setNoteDate(new java.util.Date());
		note.setType(Note.NOTE_TYPE_MEMBER);
		note.setIsDelete(Boolean.FALSE);
		note.setDeleteDate(DEFULT_DATE_1900);
		note.setDeletedBy(0);
		note.setNote(noteMsg);
		note.setMember(member);
		member.addNote(note);
	}

	/**
	 * @param member
	 * @param noteMsg
	 * @param ptsUserId
	 * @return
	 */
	public boolean createNoteForMember(Member member,String noteMsg,Integer ptsUserId) {
		try {
			Note note = new Note();
			note.setEnteredBy(ptsUserId);
			note.setNoteDate(new java.util.Date());
			note.setType(Note.NOTE_TYPE_MEMBER);
			note.setIsDelete(Boolean.FALSE);
			note.setDeleteDate(DEFULT_DATE_1900);
			note.setDeletedBy(0);
			note.setNote(noteMsg);
			note.setMember(member);
			member.addNote(note);
		} catch (RuntimeException re) {
			log.error("Error adding note to Member [{}]", member.getFirstName().concat(" ").concat(member.getLastName()));
			return false;
		}
		return true;
	}

	/**
	 * @return
	 */
	public Member createMemberForPayment() {

		Member member = null;
		try {
			  member = new Member();
			  Integer ptsAppUser = ptsUserService.getPTSUserIDByUserName(Constant.DEFAULT_PTS_USERNAME);
			  member.setCreatedBy(ptsAppUser);
			  member.setModifiedBy(ptsAppUser);
			  member.setCreatedDate(new java.util.Date());
			  member.setModifiedDate(new java.util.Date());
			  member.setActive(CANCELLED.getStatus());
			this.memberRepository.save(member);
		}catch(RuntimeException re) {
			log.error("Error creating new Member record for Payment, Message [{}]",re.getMessage());
		}
		return member;
	}

	/**
	 * @param member
	 */
	public void saveMember(Member member) {
		saveMemberInTransaction(member);
	}

	@Transactional
	public void saveMember2(Member member) {
		memberRepository.save(member);
	}

	/*public void saveMemberWithNote(MemberSetup memberSetup) {
		populateMemberNotes(memberSetup);
		this.memberRepository.save(memberSetup.getMember());
	}*/

	/**
	 * @param member
	 * @param group
	 * @param agent
	 * @param soldDate
	 * @param product
	 * @param setupProduct
	 */
	public void addProductToMember(Member member, GroupEntity group, Agent agent, Date soldDate, Product product, MemberSetupProduct setupProduct)
	{
		log.info("====> call addProductToMember");
		Double overridePremAmt = setupProduct.getPayAmount();
		Double overrideMaxDueAmt = setupProduct.getMaxDueAmount();
		Integer productUpgradeType = setupProduct.getProductUpgradeType();

		Double overrideSetupFeePremAmt = setupProduct.getSetupFeePayAmount();
		Double overrideSetupFeeMaxAmt = setupProduct.getSetupFeeMaxDueAmount();

		Set<GroupProductFee> grpPrdFeeSet = new HashSet<>();

		Iterator<GroupProductFee> itr1 = group.getGrpPrdList().iterator();
		while (itr1.hasNext()) {
			GroupProductFee item = itr1.next();
			log.info("====> call addProductToMember prodict values:"+item.getProduct().getProductId()+" "+product.getProductId());
			if (item.getProduct().getProductId().compareTo(product.getProductId()) == 0) {
				grpPrdFeeSet.add(item);
			}
		}

		Set<MemberFee> memberFeeList = new HashSet<>();

		//assume only 1 product added at a time.
		MemberFee primaryFee = null ;
		//= member.getMemberFee().stream().filter(item->item.getId() == null && item.getFeeDetails().getFeeId().intValue() !=1).findFirst().orElse(null);

		Iterator<GroupProductFee> grpPrdFeeSetItr = grpPrdFeeSet.iterator();
		int feePosition=1;
		Integer setupFeeRefId = getSetupRefId(member.getMemberFee());
		log.info("====> call addProductToMember grpPrdFeeSetItr");
		while (grpPrdFeeSetItr.hasNext())
		{
			GroupProductFee grpPrdFeeSetItem = grpPrdFeeSetItr.next();
			MemberFee memberFee = new MemberFee();
			memberFee.setProduct(product);
			memberFee.setFeeDetails(this.feeRepository.findById(grpPrdFeeSetItem.getFeeDetails().getFeeId()).get());
			//memberFee.setFeeDetails(grpPrdFeeSetItem.getFeeDetails());
			memberFee.setFreeTrial(setupProduct.getFreeTrial());
			memberFee.setGroup(group);
			log.info("====> call addProductToMember freetrial values:"+setupProduct.getFreeTrial());
			if(Constant.SETUP_FEE_IDS.contains(grpPrdFeeSetItem.getFeeDetails().getFeeId()))
			{
				memberFee.setAmount(setupProduct.isOverrideFee() ? overrideSetupFeePremAmt : grpPrdFeeSetItem.getAmount() );
			}
			else
			{
				memberFee.setAmount(setupProduct.isOverrideFee() ? overridePremAmt : grpPrdFeeSetItem.getAmount() );
				primaryFee = memberFee;
			}

			memberFee.setFrequencyDetails(this.frequencyRepository.findById(grpPrdFeeSetItem.getFrequencyDetails().getID()).get());
			//memberFee.setFrequencyDetails(grpPrdFeeSetItem.getFrequencyDetails());
			memberFee.setProductDate(soldDate);
			if (grpPrdFeeSetItem.getFeeDetails().getFeeId() == 1)
			{
				memberFee.setPosition(0);
			}
			else
			{
				memberFee.setPosition(feePosition++);
			}
			memberFee.setUpgradeType(productUpgradeType);

			if(Constant.SETUP_FEE_IDS.contains(grpPrdFeeSetItem.getFeeDetails().getFeeId()))
			{
				memberFee.setMaxAmountDue(setupProduct.isOverrideFee() ? overrideSetupFeeMaxAmt : grpPrdFeeSetItem.getMaxAmountDue());
			}
			else
			{
				memberFee.setMaxAmountDue(setupProduct.isOverrideFee() ? overrideMaxDueAmt : grpPrdFeeSetItem.getMaxAmountDue());
			}

			memberFee.setRetailAmount(grpPrdFeeSetItem.getRetailAmount());
			memberFee.setEffectiveStartDate(setupProduct.getEffectiveStartDate());
			memberFee.setEffectiveEndDate(setupProduct.getEffectiveEndDate());
			memberFee.setCreatedBy(member.getModifiedBy());
			memberFee.setModifiedBy(member.getModifiedBy());
			memberFee.setCreatedDate(new Date());
			memberFee.setModifiedDate(new Date());
			memberFee.setSetupFeeRefId(setupFeeRefId);
			log.info("====> call addProductToMember memberFee values:"+memberFee.toString());
			memberFeeList.add(memberFee);
		}
		log.info("====> call addProductToMember memberFeeList size:"+memberFeeList.size());
		for (MemberFee item : memberFeeList) {
			member.addMemberFee(item);
		}
		// commissions update
		// member commissions
		if(agent!=null) {
			AgentCommission agentCommission = agentCommissionRepository.findAllByAgentAgentIdAndProductId(agent.getAgentId(), product.getProductId());
			Set<AgentManager> agentManagerList = agentManagerRepository.findAllByAgentIdOrderByManagerPosition(agent.getAgentId());
	
			Set<AgentCommission> agentCommissions = new LinkedHashSet<>();//new HashSet<AgentCommission>();// Agent
																					// &
																					// managers
																					// commissions
																					// list
	
			agentCommissions.add(agentCommission);// position-> 1 -agent
	
			agentManagerList.forEach(item -> {
				Integer managerId = item.getManagerId();
				AgentCommission mgrCommission = agentCommissionRepository.findAllByAgentAgentIdAndProductId(managerId, product.getProductId());
				agentCommissions.add(mgrCommission);// next position agents in the
													// commission list
			});
	
			Iterator<AgentCommission> itr = agentCommissions.iterator();
			for (int i = 0; itr.hasNext(); i++)
			{
				AgentCommission commissionItem = itr.next();
				if(commissionItem == null || commissionItem.getAgent() == null) {
					continue;
				}
				MemberCommission memCommission = new MemberCommission();
				memCommission.setAgentId(commissionItem.getAgent().getAgentId() );
				memCommission.setProductId(commissionItem.getProductId());
				memCommission.setAgentPosition((i + 1));
				memCommission.setUseFlatRate(commissionItem.getAgent().getUseFlatRate());
				if(Boolean.FALSE.equals(commissionItem.getAgent().getUseFlatRate()) )//!commissionItem.getAgent().getUseFlatRate())
				{
					if(commissionItem.getAgent().getFixedNew()!=null && (commissionItem.getAgent().getFixedNew().compareTo(BigDecimal.ZERO)>0 ||
							commissionItem.getAgent().getFixedRenew().compareTo(BigDecimal.ZERO)>0) )
					{
						memCommission.setNewSalePercent(commissionItem.getAgent().getFixedNew().doubleValue());
						memCommission.setRenewPercent(commissionItem.getAgent().getFixedRenew().doubleValue());
					}
					else
					{
						memCommission.setNewSalePercent(commissionItem.getNewSalePercent().doubleValue());
						memCommission.setRenewPercent(commissionItem.getRenewPercent().doubleValue());
					}
				}
				else
				{
					memCommission.setNewSalePercent(commissionItem.getNewSalePercent().doubleValue());
					memCommission.setRenewPercent(commissionItem.getRenewPercent().doubleValue());
				}
				memCommission.setCreatedDate(new java.util.Date());
				memCommission.setModifiedDate(new java.util.Date());
				memCommission.setCreatedBy(member.getModifiedBy());
				memCommission.setModifiedBy(member.getModifiedBy());
				memCommission.setMemberFee(primaryFee);
				primaryFee.addCommission(memCommission);
	
				memCommission.setMember(member);
				//member.addCommission(memCommission);
			}
		}
	}

	/*
	private void updateMemberProductSetupFeeRefId(Member member) {

		for(MemberFee feeItem: member.getMemberFee())
		{
			if(feeItem.getSetupFeeRefId()==null || (feeItem.getSetupFeeRefId() !=null && feeItem.getSetupFeeRefId().intValue()==0))
			{
				feeItem.setSetupFeeRefId(getSetupRefId(member.getMemberFee()));
			}
		}
	}
	*/

	private Integer getSetupRefId(Set<MemberFee> memberFees)
	{
		AtomicInteger aint = new AtomicInteger();

		Set<Integer> existingSetupFeeRefIdSet = memberFees.stream().map(MemberFee::getSetupFeeRefId).collect(Collectors.toSet());

		int newSetupFeeRefId = 0;

		while (newSetupFeeRefId == 0) {
			newSetupFeeRefId = aint.getAndIncrement();
			if (existingSetupFeeRefIdSet.contains(Integer.valueOf(newSetupFeeRefId)))
				newSetupFeeRefId = 0;
		}
		return newSetupFeeRefId;
	}

	/**
	 * @param member
	 * @return
	 */
	public boolean isMemberHasInstallamentProducts(Member member) {
		for(MemberFee memberFee: member.getMemberFee()) {
			if(Constant.INSTALLMENT_FEE_IDS.contains(memberFee.getFeeDetails().getFeeId()))
				return true;
		}
		return false;
	}
	/**
	 * @param member
	 * @param ptsUserId
	 * @param lastPaymentDate
	 * @param groupCode
	 */
	public void populateMemberAttributes(Member member, Integer ptsUserId,Date lastPaymentDate,String groupCode) {

		log.info("Updating Member attributes for Member ID [{}]",member.getMemberId());

		//boolean memberPartOfDownpaymentGroup = identifyMemberPartOfDownPaymentGroup(member.getGroup().getGroupCode());
		//boolean memberPartOfLifetimeGroup    = identifyMemberPartOfLifetimeGroup(member.getGroup().getGroupCode());

		boolean memberPartOfDownpaymentGroup =  groupService.isDownPaymentGroup(member.getGroup().getGroupCode());
		boolean memberPartOfLifetimeGroup    = groupService.isLifeTimeGroup(member.getGroup().getGroupCode());

		//Balances
		memberProductFeeService.getMemberProductBalance(member,memberPartOfDownpaymentGroup);

		Double outstandingBalance = member.getMemberFee().stream().mapToDouble(MemberFee::getOutstanndingBalance).sum();

		MemberAttributes attributes;
		if(null == member.getAttributes()) {
			attributes = new MemberAttributes();
			attributes.setCreatedBy(ptsUserId);
			attributes.setCreatedDate(new java.util.Date());
			attributes.setMember(member);
			member.setAttributes(attributes);
		}
		else {
			attributes = member.getAttributes();
		}
		attributes.setBalanceDue(new BigDecimal(Double.toString(outstandingBalance)));

		MemberPayStatus payStatus=getMemberPayStatus(member);
		attributes.setPayStatus(payStatus);

		if(null != lastPaymentDate)
			attributes.setLastPaymentDate(lastPaymentDate);

		attributes.setModifiedBy(ptsUserId);
		attributes.setModifiedDate(new java.util.Date());

		saveMemberAttributes(attributes);

		// if(memberPartOfLifetimeGroup) //Constant.PRECOA_GROUP_CODES.contains(groupCode))
		if (memberPartOfLifetimeGroup && Constant.PRECOA_GROUP_CODES.contains(groupCode)){
			updateFrequncyBasedOnPayStatus(member,payStatus);
		}
	}

	/**
	 * @param member
	 * @param payStatus
	 */
	private void updateFrequncyBasedOnPayStatus(Member member,MemberPayStatus payStatus) {

		for(MemberFee memberFee: member.getMemberFee())
		{
			if(memberFee.getMemberId() == null)//memberfee memberid is null on retrieve in some scenario, setting it for save below to update to db
				memberFee.setMemberId(member.getMemberId());

			if(!Constant.SETUP_FEE_IDS.contains(memberFee.getFeeDetails().getFeeId()) &&
			(Constant.FREQ_LIFETIME !=  memberFee.getFrequencyDetails().getID()  &&
			Constant.FREQ_ONCE != memberFee.getFrequencyDetails().getID() &&
			Constant.FREQ_FIVE_YEAR != memberFee.getFrequencyDetails().getID() )  &&
				member.getAttributes().getPayStatus().equals(MemberPayStatus.PAID_IN_FULL)		)
			{
				if("FIVE_YEAR".equalsIgnoreCase(memberFee.getProduct().getProductSet().getName()))
				{
					memberFee.setFrequencyDetails(frequencyRepository.findById(Constant.FREQ_FIVE_YEAR).get());
					memberFeeRepository.save(memberFee);
					log.info("Member [{}] Product frequency update to Five Year after [{}] status",member.getMemberId(),payStatus);
				}
				else
				{
					memberFee.setFrequencyDetails(frequencyRepository.findById(Constant.FREQ_LIFETIME).get());
					memberFeeRepository.save(memberFee);
					log.info("Member [{}] Product frequency update to Life Time after [{}] status",member.getMemberId(),payStatus);
				}
			}
			else if ( !Constant.SETUP_FEE_IDS.contains(memberFee.getFeeDetails().getFeeId()) &&
					(Constant.FREQ_LIFETIME ==  memberFee.getFrequencyDetails().getID()  ||
					Constant.FREQ_ONCE == memberFee.getFrequencyDetails().getID() &&
					Constant.FREQ_FIVE_YEAR == memberFee.getFrequencyDetails().getID() )  &&
						(member.getAttributes().getPayStatus().equals(MemberPayStatus.CURRENT) ||
						 member.getAttributes().getPayStatus().equals(MemberPayStatus.BALANCE_DUE))	)
			{
				memberFee.setFrequencyDetails(frequencyRepository.findById(Constant.FREQ_MONTH).get());
				memberFeeRepository.save(memberFee);
				log.info("Member [{}] Product frequency update to monthly after [{}] status",member.getMemberId(),payStatus);
			}
		}

	}

	/**
	 * @param member
	 * @return MemberPayStatus
	 */
	private MemberPayStatus getMemberPayStatus(Member member) {

		int prdPaidInFullCnt = 0;
		int prdBalDueCnt = 0;
		int prdCurrentCnt = 0;
		MemberPayStatus memberPaymentStatus=MemberPayStatus.DEFAULT;
		BigDecimal totalDue = BigDecimal.ZERO;

		boolean activePrdLifetime=false;

		for(MemberFee memberFee: member.getMemberFee())
		{
			if(isDateWithInRange(new Date(), memberFee.getEffectiveStartDate(), memberFee.getEffectiveEndDate())) {

				totalDue = totalDue.add(new BigDecimal(Double.toString(memberFee.getOutstanndingBalance())));

				if (Constant.SETUP_FEE_IDS.contains(memberFee.getFeeDetails().getFeeId())
						&& memberFee.getMaxAmountDue() == 0 && memberFee.getAmount() == 0) {
					prdPaidInFullCnt++;
				} else if (memberFee.getMaxAmountDue() > 0 && memberFee.getAmountPaid() >= memberFee.getMaxAmountDue()
						&& memberFee.getOutstanndingBalance() == 0) {
					prdPaidInFullCnt++;
				} else if (memberFee.getOutstanndingBalance() >= 0) {
					prdCurrentCnt++;
				} else {
					prdBalDueCnt++;
				}

				if((!Constant.SETUP_FEE_IDS.contains(memberFee.getFeeDetails().getFeeId())) && memberFee.getFrequencyDetails().getID().compareTo(Constant.FREQ_LIFETIME)==0) {
					activePrdLifetime=true;
				}
			}
		}
		if (member.getMemberFee().size() == prdPaidInFullCnt && activePrdLifetime) {
			memberPaymentStatus = MemberPayStatus.PAID_IN_FULL;
		} else if (member.getMemberFee().size() == prdCurrentCnt
				|| (member.getMemberFee().size() == (prdCurrentCnt + prdPaidInFullCnt))) {
			memberPaymentStatus = MemberPayStatus.CURRENT;
		} else if (prdBalDueCnt > 0) {
			memberPaymentStatus = MemberPayStatus.BALANCE_DUE;
		}
		return memberPaymentStatus;
	}

	/**
	 * @param groupCode
	 * @param employeeId
	 * @return
	 */
	public boolean isEmployeeExistsInGroup(String groupCode,String employeeId) {
		return this.memberRepository.existsByEmployeeIdAndGroupGroupCode(employeeId, groupCode);
	}

	/**
	 * @param memberId
	 * @return
	 */
	public boolean isMemberIdExists(Integer memberId) {
		return this.memberRepository.existsById(memberId);
	}

	public boolean isMemberIdExistsByStatus(Integer memberId,Integer...status)
	{
		List<Integer> statuses = Arrays.asList(status);
		return this.memberRepository.existsByMemberIdAndActiveIn(memberId, statuses);
	}

	public void saveMemberAttributes(MemberAttributes memberAttributes) {
		memberAttributesRepository.save(memberAttributes);
	}

	public Set<Dependant> getMemberDependants(Integer memberId){
		Set<Dependant> dependants = this.dependantRepository.findAllByMemberMemberId(memberId);

		LocalDate today = LocalDate.now();

		dependants.forEach(item->{
			LocalDate tmp = new java.util.Date(item.getBirthDate().getTime()).toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
			item.setAge(ChronoUnit.YEARS.between(tmp, today));
		});

		return dependants;
	}

	/*public Optional<MemberSummary> findByEmployeeIdAndGroupGroupCodeOrderByMemberIdDesc(String employeeId,String groupCode) {
		return memberRepository.findByEmployeeIdAndGroupGroupCodeOrderByMemberIdDesc(employeeId, groupCode);
	}
	*/
	public Optional<Member> getMemberByEmployeeIdAndGroupCode(String employeeId,String groupCode){
		return memberRepository.findByEmployeeIdAndGroupGroupCode(employeeId, groupCode);
	}

	public List<Member.MemberInfo> getAllMembersInGroupByEmployeeId(String groupCode,String employeeId)
	{
		return this.memberRepository.findByEmployeeIdAndGroupGroupCodeOrderByActive(employeeId,groupCode);
	}

	/**
	 * @param groupCode
	 * @param employeeId
	 * @return
	 * Group has multiple member with same employee id, some of them active. if only 1 active return else return empty.
	 */
	public Optional<Member.MemberInfo> findMemberByEmployeeIdInGroup(String groupCode,String employeeId)
	{
		List<Member.MemberInfo> memList = this.memberRepository.findByEmployeeIdAndGroupGroupCodeOrderByActive(employeeId,groupCode);
		Optional<MemberInfo> optMemberInfo = Optional.empty();

		if(memList.isEmpty() || (!memList.isEmpty() && memList.size() >=2))
		{
			if(memList.isEmpty())
			{
				return Optional.empty();
			}
			else if(memList.size() >=2)
			{	//list all active members
				List<Member.MemberInfo> activeMemSet = memList.stream().filter(item-> (item.getActive().compareTo(ACTIVE.getStatus()) == 0)).collect(Collectors.toList());

				if(activeMemSet.isEmpty() || activeMemSet.size()>=2)
					return Optional.empty();
				else
				{
					optMemberInfo = Optional.of(activeMemSet.get(0));
				}
			}
		}
		else
		{
			optMemberInfo = Optional.of(memList.get(0));
		}
		return optMemberInfo;
	}

	public Set<MemberSummary> findByLastNameAndBirthDateAndGroupGroupCodeAndActive(String lastName,Date birthDate,String groupCode,Integer active){
		return memberRepository.findByLastNameAndBirthDateAndGroupGroupCodeAndActive(lastName, birthDate,groupCode,active, MemberSummary.class);
	}

	/**
	 * @param groupCode
	 * @param cancelDate
	 * @return
	 */
	public Set<Integer> getAllActiveMembersByGroupCode(String groupCode, Date cancelDate) {

		Set<MemberSummaryForGrp> memSmrySet = memberRepository.findAllByGroupGroupCode(groupCode);

		return memSmrySet.stream()
				.filter(item -> (item.getActive().compareTo(ACTIVE.getStatus()) == 0)
						|| (item.getActive().compareTo(CANCELLED.getStatus()) == 0
						&& cancelDate.before(item.getCancelDate())))
				.map(item -> item.getMemberId()).collect(Collectors.toSet());

	}

	public List<MemberFulfillmentDataDto> getAllActiveMembersByGroupCode(String groupCode) {
		List<MemberFulfillmentDataDto> dtos = memberRepository.findAllActiveMembersFulfillmentData(groupCode);

        if (isEmpty(dtos)) {
            return Collections.emptyList();
        }

		populateFee(dtos);
		populateAddresses(dtos);

        return dtos;
	}

	private void populateAddresses(List<MemberFulfillmentDataDto> dtos) {
		List<Integer> addressesIds = dtos.stream()
				.flatMap(dto -> Stream.of(dto.getAddressDetailsId(), dto.getAlternateAddressDetailsId(), dto.getBurialAddressDetailsId()))
				.collect(Collectors.toList());
		List<List<Integer>> listList = Lists.partition(addressesIds, MAX_ALLOWED_SQL_PARAMETERS_COUNT);

		Set<Address> addresses = listList.stream()
				.filter(CollectionUtils::isNotEmpty)
				.map(idsSubList -> addressRepository.findAllByAddressIdIn(idsSubList))
				.flatMap(Collection::stream)
				.collect(Collectors.toSet());

		dtos.forEach(dto -> {
			dto.setAddressDetails(addresses.stream()
					.filter(address -> address.getAddressId().equals(dto.getAddressDetailsId()))
					.findFirst()
					.orElse(null));
			dto.setAlternateAddressDetails(addresses.stream()
					.filter(address -> address.getAddressId().equals(dto.getAlternateAddressDetailsId()))
					.findFirst()
					.orElse(null));
			dto.setBurialAddressDetails(addresses.stream()
					.filter(address -> address.getAddressId().equals(dto.getBurialAddressDetailsId()))
					.findFirst()
					.orElse(null)
			);
		});
	}

	private void populateFee(List<MemberFulfillmentDataDto> dtos) {
		List<List<MemberFulfillmentDataDto>> listList = Lists.partition(dtos, MAX_ALLOWED_SQL_PARAMETERS_COUNT);
		Set<MemberFee> fees = listList.stream()
				.map(subList -> subList.stream().map(MemberFulfillmentDataDto::getMemberId).collect(Collectors.toList()))
				.map(idsSubList -> memberFeeRepository.findAllByMemberIds(idsSubList))
				.flatMap(Collection::stream)
				.collect(Collectors.toSet());

		dtos.forEach(dto -> dto.setMemberFee(
				fees.stream()
						.filter(fee -> fee.getMemberId().equals(dto.getMemberId()))
						.findFirst()
						.orElse(null)
		));
	}

	/**
	 * @param memberSetup
	 */
	public void createMember(MemberSetup memberSetup)
	{
		populateMemberGroupAgentProductInfo(memberSetup);
		populateMemberNotes(memberSetup);
		populateMemberDependants(memberSetup);
		//assume only 1 product added at a time.
		MemberFee primaryFee = memberSetup.getMember().getMemberFee().stream()
				.filter(item->item.getId() == null && item.getFeeDetails().getFeeId().intValue() !=1 &&
						item.getProduct().getProductId() != 268 &&
                        item.getProduct().getProductId() != 182).findFirst().orElse(null);

		populateMemberEmployeeId(memberSetup.getMember(),primaryFee);
		Date productEffDate = primaryFee !=null ? primaryFee.getEffectiveStartDate() : new Date();
		boolean memberInfoSaved=false;
		boolean paymentPosted=false;
		boolean generatedAdvComm=false;
		boolean fulfillmentCreatred=false;
		try
		{
			saveMemberInTransaction(memberSetup);

			memberInfoSaved=true;

			log.info("Member info saved successfully , Member [{}]",memberSetup.getMember().getMemberId());

			boolean downPaymentGrp = memberSetup.getMember().getGroup().getDownPaymentGrp();
			boolean paymentToPost=paymentToPost(memberSetup);
			paymentPosted = !paymentToPost;

			if(paymentToPost && downPaymentGrp)
			{
				postMemberPayment(memberSetup);
				paymentPosted=true;
				generateAdvCommissions(memberSetup,productEffDate);
				generatedAdvComm=true;
			}
			else if(paymentToPost)
			{
				generateAdvCommissions(memberSetup,productEffDate);
				generatedAdvComm=true;
				postMemberPayment(memberSetup);
				paymentPosted=true;
			}
			else
			{
				generateAdvCommissions(memberSetup,productEffDate);
				generatedAdvComm=true;
				populateMemberAttributes(memberSetup.getMember(),memberSetup.getMember().getModifiedBy(),null,memberSetup.getMember().getGroup().getGroupCode());
			}
			if(memberSetup.isFulfillment()) {
				newMemberFulfillmentRequest(memberSetup);				
			}
			fulfillmentCreatred=true;
			memberSetup.setSuccess(Boolean.TRUE);
		}
		catch(Exception e)
		{
			memberSetup.setErrorMessage(e.getMessage());
			StringBuilder sb = new StringBuilder();
			sb.append("Exception while process member, Message [").append(e.getMessage()).append("] Cause [").append(e.getCause()).append("]");
			memberSetup.addError(sb.toString());
		}
		finally {
			memberSetup.setSuccess(memberInfoSaved);
			if(!paymentPosted) {
				memberSetup.addError("Error processing payment.");
			}
			if(!generatedAdvComm) {
				memberSetup.addError("Error processing advance commission.");
			}
			if(!fulfillmentCreatred) {
				memberSetup.addError("Error processing fulfillment request.");
			}
		}
	}

	private void addOrUpdateMemberEmployeeId(Member member) {
		member.getMemberFee().stream().filter(item -> item.getFeeDetails().getFeeId().intValue() != 1 && item.getProduct().getUpgradeProduct().compareTo(ProductUpgradeType.PRIMARY.getValue())==0)
				.forEach(memFee -> {
					if (memFee.getMemberEmployeeId() == null) {
						populateMemberEmployeeId(member, memFee);
					} else {
						updateMemberEmployeeId(member,memFee);
					}
				});
	}

	private void updateMemberEmployeeId(Member member,MemberFee primaryFee)
	{//update existing employee id record start/end date values
		MemberEmployeeIdEntity memEmployeeId = primaryFee.getMemberEmployeeId();
		memEmployeeId.setEffectiveStartDate(new java.util.Date(primaryFee.getEffectiveStartDate().getTime()).toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate());
		memEmployeeId.setEffectiveEndDate(new java.util.Date(primaryFee.getEffectiveEndDate().getTime()).toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate());
		memEmployeeId.setModifiedBy(member.getModifiedBy());
		memEmployeeId.setModifiedDate(new Date());
		log.info("Updating member employee id record {}",memEmployeeId);
	}
	
	private void populateMemberEmployeeId(Member member,MemberFee primaryFee) {
		if(StringUtils.hasText(member.getEmployeeId()))
		{
			MemberEmployeeIdEntity memEmployeeId = new MemberEmployeeIdEntity();

			if(primaryFee ==null)
			{
				log.error("Error primaryFee cannot found, First [{}] Last Name [{}]",member.getFirstName(),member.getLastName());
				return ;
			}
			
			primaryFee.setMemberEmployeeId(memEmployeeId);
			memEmployeeId.setMember(member);
			memEmployeeId.setMemberFee(primaryFee);
			memEmployeeId.setCreatedBy(primaryFee.getModifiedBy());
			memEmployeeId.setModifiedBy(primaryFee.getModifiedBy());
			memEmployeeId.setCreatedDate(new Date());
			memEmployeeId.setModifiedDate(new Date());
			memEmployeeId.setEffectiveStartDate(new java.util.Date(primaryFee.getEffectiveStartDate().getTime()).toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate());
			memEmployeeId.setEffectiveEndDate(new java.util.Date(primaryFee.getEffectiveEndDate().getTime()).toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate());
			memEmployeeId.setEmployeeId(member.getEmployeeId());
			memEmployeeId.setGroup(primaryFee.getGroup());
			
			
		}
	}

	/**
	 * @param memberSetup
	 */
	private void generateAdvCommissions(MemberSetup memberSetup,Date effectiveDate)
	{
		try {
			commissionService.createAdvanceCommission(memberSetup.getMember().getMemberId(), BigDecimal.ZERO,effectiveDate);
		} catch (RuntimeException re) {
			log.error("Error generating adv commission, Member ID[{}] , Message [{}] , Cause [{}]",memberSetup.getMember().getMemberId(),re.getMessage(),re.getCause());
			StringBuilder sb = new StringBuilder();
			sb.append("Generate Advance Comm. Error [");
			sb.append("Message: " + re.getMessage() + "  Cause: " + re.getCause());
			sb.append("]");
			memberSetup.addError(sb.toString());
		}
	}

	/**
	 * @param memberSetup
	 */
	public void postMemberPayment(MemberSetup memberSetup)
	{
		BigDecimal payAmt = memberSetup.getPaymentAmount();
		if(payAmt != null && payAmt.compareTo(BigDecimal.ZERO)>0)
		{
			List<MemberPayment> memberPaymentList = new ArrayList<>();
			MemberPayment memberPayment = new MemberPayment();
			memberPayment.setChargeback(Boolean.FALSE);
			memberPayment.setForteTransactionId(null);
			memberPayment.setGroupCode(memberSetup.getGroupCd());
			memberPayment.setIdentifierType(MemberIdentifierType.MEMBERID);
			memberPayment.setMemberIdentifier(String.valueOf(memberSetup.getMember().getMemberId()));
			memberPayment.setPaymentAmt(payAmt);
			String payMethod = MemberPayType.getPayTypeById(memberSetup.getMember().getPayType()).compareTo(MemberPayType.CREDITCARD)==0 ? "cc" : "echeck";
			memberPayment.setPayMethod(payMethod);
			memberPayment.setTransactionDate(new Date());
			String paymentNote="";
			String paymentCheck="";
			if(memberSetup.getFreeTrial()==1)
			{
				paymentNote="MASA MKTG CREDIT";
				paymentCheck="CREDIT";
			}
			memberPaymentList.add(memberPayment);
			Set<PaymentProcessOutput> paymentProcessOutputSet = this.paymentPostService.postGroupPaymentWithTransactionNew(memberPaymentList, memberSetup.getGroupCd(), Boolean.TRUE,
					memberSetup.getMember().getModifiedBy(),new Date(),new Date(),paymentNote,paymentCheck,BigDecimal.ZERO);

			StringBuilder sb = new StringBuilder();
			sb.append("Payment Post Error [");
			boolean success=true;
			for(PaymentProcessOutput item: paymentProcessOutputSet) {
				if(PaymentPostStatus.isStatusError(item.getStatus())) {
					sb.append(item.getComments());
					success=false;
				}
			}
			sb.append("]");
			if(!success)
			{
				memberSetup.addError(sb.toString());
			}
		}
	}

	/**
	 * @param memberSetup
	 */
	private void populateMemberNotes(MemberSetup memberSetup) {

		for(String note: memberSetup.getNotes())
		{
			createMemberNote(memberSetup.getMember(),note,memberSetup.getMember().getModifiedBy());
		}
	}

	/**
	 * @param memberSetup
	 */
	private void newMemberFulfillmentRequest(MemberSetup memberSetup) {
		Member member = memberSetup.getMember();
		List<MemberFulfillmentRequest> memFulfillReqList = new ArrayList<>();

		MemberSetupProduct memSetPrd = memberSetup.getProductSet().stream().filter(item->item.getProductUpgradeType().compareTo(ProductUpgradeType.PRIMARY.getValue())==0)
					.findFirst().orElse(null);

		if(memSetPrd ==null) {
			log.error("Error during fulfillment process ,  product is null ");
			return;
		}
		Integer newProductId = memSetPrd !=null ? memSetPrd.getProductId() : 0;
		Date prdEffDate = memSetPrd != null ? memSetPrd.getEffectiveStartDate() : new Date();

		log.info("Creating fulfillment request for Member ID [{}] Product ID[{}]",memberSetup.getMember().getMemberId(),newProductId);
		try
		{
			member.getMemberFee().forEach(memberFeeItem->{
				if( (!isSetupOrAdminFee(memberFeeItem)) && memberFeeItem.getProduct().getProductId().compareTo(newProductId)==0
						&& isDateWithInRange(prdEffDate, memberFeeItem.getEffectiveStartDate() , memberFeeItem.getEffectiveEndDate() ))	{
					MemberFulfillmentRequest memFulfillRequest = new MemberFulfillmentRequest();
					memFulfillRequest.setMemberId(member.getMemberId());
					memFulfillRequest.setProductId(memberFeeItem.getProduct().getProductId());
					memFulfillRequest.setFeeId(memberFeeItem.getFeeDetails().getFeeId());
					memFulfillRequest.setRequestedDate(new java.util.Date());
					memFulfillRequest.setProcessedDate(DEFULT_DATE_1900);
					memFulfillRequest.setUpgradeType(memberFeeItem.getProduct().getUpgradeProduct());
					memFulfillRequest.setFulfillmentType(1);
					memFulfillRequest.setCreatedBy(member.getModifiedBy());
					memFulfillRequest.setCreatedDate(new java.util.Date());
					memFulfillRequest.setModifiedBy(member.getModifiedBy());
					memFulfillRequest.setModifiedDate(new java.util.Date());
					memFulfillRequest.setCensusMember(Boolean.FALSE);
					memFulfillRequest.setPkgCnt(1);
					memFulfillRequest.setShippingMethod(Constant.DEFAULT_SHIPPING_METHOD_ID);
					String name = String.join(" ",member.getFirstName(),member.getMi() == null ? "" : member.getMi(),member.getLastName());
					memFulfillRequest.setShippingContact(name);
					populateMemberShippingAddress(member, memFulfillRequest);
					memFulfillReqList.add(memFulfillRequest);
				}
			});
			this.createMemberFullfillmentRequest(memFulfillReqList);
		}
		catch(RuntimeException re) {
			log.error("Error creating fulfillment request, Member ID[{}] , Message [{}] , Cause [{}]",memberSetup.getMember().getMemberId(),re.getMessage(),re.getCause());
			memberSetup.addError("Error creating fulfillment request, Message ["+re.getMessage()+"] Cause ["+re.getCause()+"]");
		}
	}

	/**
	 * Populate member fulfillment request shipping address based on member data.
	 * 
	 * @param member - source of address
	 * @param memFulfillmentReq - target object
	 * @return - true if populated successfully, false otherwise
	 */
	public boolean populateMemberShippingAddress(Member member, MemberFulfillmentRequest memFulfillmentReq) {

		Address address = null;
		try {

			if(addressService.isValidAddressForShipping(member.getAlternateAddressDetails())) {
				address = member.getAlternateAddressDetails();
			}
			else if(addressService.isValidAddressForShipping(member.getAddressDetails())) {
				address = member.getAddressDetails();
			}

			if (null == address) {
				log.error("Error populating shipping address for Member fulfillment [{}]", member.getMemberId());
				return false;
			} else {
				memFulfillmentReq.setShippingAddress1(address.getAddress1());
				memFulfillmentReq.setShippingAddress2(address.getAddress2());
				memFulfillmentReq.setShippingAddress3(address.getAddress3());
				memFulfillmentReq.setShippingCity(address.getCity());
				if(address.getStateDetails()!=null)
					memFulfillmentReq.setShippingState(address.getStateDetails().getSymbol());
				else
					memFulfillmentReq.setShippingState(addressService.getStateCodeById(address.getState()));

				if(address.getCountryDetails()!=null)
					memFulfillmentReq.setShippingCountry(address.getCountryDetails().getName());
				else
					memFulfillmentReq.setShippingCountry(addressService.getCountryNameById(address.getCountry()));

				memFulfillmentReq.setShippingZip(address.getZip());
				memFulfillmentReq.setShippingZip4(address.getZip4());
			}
		} catch (RuntimeException r) {
			log.error(
					"Error while populating member fulfillment shipping address,Member ID[{}] Message [{}] Cause [{}]",
					member.getMemberId(), r.getMessage(), r.getCause());
			return false;
		}
		return true;
	}
	/**
	 * @param memberFee
	 * @return True/False
	 */
	public boolean isSetupOrAdminFee(MemberFee memberFee) {

		if(Constant.SETUP_FEE_IDS.contains(memberFee.getFeeDetails().getFeeId()))
			return Boolean.TRUE;

		if(Constant.INSTALLMENT_ADMIN_PRODUCT_IDS.contains(memberFee.getProduct().getProductId()))
			return Boolean.TRUE;

		return Boolean.FALSE;
	}

	/**
	 * @param memberSetup
	 */
	public void updateMember(MemberUpdateSetup memberSetup) {
		// not needed to update agent/product - may be upgrade action needs it - populateMemberInformationForUpdate(memberSetup)
		try
		{
			if(isAddressChanged(AddressType.BENEFIT,memberSetup.getBenefitAddress(),memberSetup.getMember()))
			{
				addressService.saveAddressToHistory(
						memberSetup.getMember().getAddressDetails(), AddressType.BENEFIT, memberSetup.getMember());
				memberSetup.getMember().setAddressDetails(memberSetup.getBenefitAddress());
			}
			if(isAddressChanged(AddressType.MAILING,memberSetup.getMailingAddress(),memberSetup.getMember()))
			{
				addressService.saveAddressToHistory(
						memberSetup.getMember().getAlternateAddressDetails(), AddressType.MAILING, memberSetup.getMember());
				memberSetup.getMember().setAlternateAddressDetails(memberSetup.getMailingAddress());
			}
			populateMemberDependants(memberSetup);
			populateMemberNotes(memberSetup);
			if(memberSetup.isProductUpdate())
			{
				populateMemberGroupAgentProductInfoForUpdate(memberSetup);
				addOrUpdateMemberEmployeeId(memberSetup.getMember());
			}
			memberRepository.save(memberSetup.getMember());
			if(memberSetup.isFulfillment())
				newMemberFulfillmentRequest(memberSetup);

			memberSetup.setSuccess(true);
		}
		catch(Exception e)
		{
			memberSetup.setErrorMessage(e.getMessage());
			memberSetup.setSuccess(false);
		}
	}

	/**.
	 * @param memberSetup
	 */
	public void updateMemberInfo(MemberSetup memberSetup)
	{
		try
		{
			populateMemberNotes(memberSetup);
			saveMemberInTransaction(memberSetup);
		}
		catch(Exception e)
		{
			memberSetup.setErrorMessage(e.getMessage());
			StringBuilder sb = new StringBuilder();
			sb.append("Exception while process member info update, Message [").append(e.getMessage()).append("] Cause [").append(e.getCause()).append("]");
			memberSetup.addError(sb.toString());
		}
	}

	/**
	 * @param memberSetup
	 * @param oldStatus
	 * @param oldCancelReason
	 * @param oldCancelDate
	 * @param activateDate
	 */
	public void activateMember(MemberSetup memberSetup, int oldStatus, int oldCancelReason, Date oldCancelDate,Date activateDate)
	{
		try
		{
			if(SUSPENDED.getStatus() == oldStatus) {
				long daysToAdd = ptsUtilityService.calculateDaysBetween(oldCancelDate,activateDate);
				if(daysToAdd > 0)
				{
					Date oldRenewDate = memberSetup.getMember().getRenewDate();
					LocalDate newRenewDate = ChronoUnit.DAYS.addTo(convertUtilToLocalDate(oldRenewDate), daysToAdd);
					memberSetup.getMember().setRenewDate(convertLocalToUtilDate(newRenewDate));
					addNote(memberSetup, activateDate);
				}
			} else if (CANCELLED.getStatus() == oldStatus) {
				memberSetup.getMember().setRenewDate(activateDate);
				addNote(memberSetup, activateDate);
			}

			populateMemberNotes(memberSetup);
			saveMemberInTransaction(memberSetup);

			Optional<MemberCoverageLapse> optMemberCoverageLapse = getMemberCoverageLapseRequest(memberSetup.getMember(),
					oldStatus,oldCancelReason,oldCancelDate,activateDate);

			if(optMemberCoverageLapse.isPresent())
				addMemberCoverageLapseRequest(optMemberCoverageLapse.get());
		}
		catch(Exception e)
		{
			memberSetup.setErrorMessage(e.getMessage());
			StringBuilder sb = new StringBuilder();
			sb.append("Exception while process member info update, Message [").append(e.getMessage()).append("] Cause [").append(e.getCause()).append("]");
			memberSetup.addError(sb.toString());
		}
	}

	/**
	 * @param member
	 * @param oldStatus
	 * @param oldCancelReason
	 * @param startDate
	 * @return
	 */
	public Optional<MemberCoverageLapse> getMemberCoverageLapseRequest(Member member, int oldStatus, int oldCancelReason,
			Date startDate,Date endDate)
	{
		if( startDate.equals(endDate) || startDate.after(endDate))
			return Optional.empty();

		MemberCoverageLapse memberCoverageLapse = new MemberCoverageLapse();
		memberCoverageLapse.setMember(member);
		memberCoverageLapse.setLapseStartDate(convertLocalToUtilDate(convertUtilToLocalDate(startDate).plusDays(1)));
		memberCoverageLapse.setLapseEndDate(convertLocalToUtilDate(convertUtilToLocalDate(endDate).minusDays(1)));
		memberCoverageLapse.setStatusType(getStatusName(oldStatus));
		memberCoverageLapse.setReasonCode(oldCancelReason);
		memberCoverageLapse.setCreatedBy(member.getModifiedBy());
		memberCoverageLapse.setCreatedDate(new Date());
		memberCoverageLapse.setModifiedBy(member.getModifiedBy());
		memberCoverageLapse.setModifiedDate(new Date());
		memberCoverageLapse.setDeletedBy(0);
		memberCoverageLapse.setDeletedDate(DEFULT_DATE_1900);
		memberCoverageLapse.setIsDelete(Boolean.FALSE);
		return Optional.of(memberCoverageLapse);
	}

	private void addNote(MemberSetup memberSetup, Date activateDate) {
		memberSetup.addNote(
				"Member renewal date updated from " +
						"[" + ptsUtilityService.formatUtilDate(memberSetup.getMember().getRenewDate()) + "] " +
						"to " +
						"[" + ptsUtilityService.formatUtilDate(activateDate) + "]");
	}

	/**
	 * @param memberSetup
	 */
	private void populateMemberDependants(MemberSetup memberSetup) {

		  if(memberSetup.getDependantSet().isEmpty())
			  return;

		for(Dependant newDependant: memberSetup.getDependantSet()) {

			boolean noneMatch = memberSetup.getMember().getDependants().stream().noneMatch(item->
						item.getFirstName().equalsIgnoreCase(newDependant.getFirstName()) &&
						item.getLastName().equalsIgnoreCase(newDependant.getLastName())
							&& item.getBirthDate().compareTo(newDependant.getBirthDate())==0

					);

			if(noneMatch)
			{
				memberSetup.getMember().addDependant(newDependant);
			}
		}
	}

	/**
	 * @param addressType
	 * @param newAddress
	 * @param member
	 * @return
	 */
	private boolean isAddressChanged(AddressType addressType, Address newAddress, Member member) {

		Address currentAddr=null;

		if (addressType.compareTo(AddressType.BENEFIT) == 0) {
			currentAddr= member.getAddressDetails();
		} else if (addressType.compareTo(AddressType.MAILING) == 0) {
			currentAddr = member.getAlternateAddressDetails();
		}
		if(newAddress == null && currentAddr !=null) {
			return true;
		}
		if(newAddress == null) {
			return false;
		}
		if(newAddress.getAddress1()==null || newAddress.getCity()==null) {
			return false;
		}

		if (currentAddr == null) {
			return true;
		}

		if (currentAddr.getAddress1() == null || currentAddr.getCity() == null || currentAddr.getZip() == null) {
			return true;
		}

		if (!currentAddr.getAddress1().equalsIgnoreCase(newAddress.getAddress1())) {
			return true;
		}

		if (!currentAddr.getCity().equalsIgnoreCase(newAddress.getCity())) {
			return true;
		}

		if (!currentAddr.getZip().equalsIgnoreCase(newAddress.getZip())) {
			return true;
		}

		return false;
	}

	/**
	 * @param memberSetup
	 */
	private void populateMemberGroupAgentProductInfo(MemberSetup memberSetup)
	{
		log.info("====> call populateMemberGroupAgentProductInfo");
		GroupEntity group = groupService.getGroupByCode(memberSetup.getGroupCd());
		memberSetup.getMember().setGroup(group);
		log.info("====> call populateMemberGroupAgentProductInfo==> group code:"+group.getGroupCode());
		for(MemberSetupProduct productSetup: memberSetup.getProductSet())
		{
			Optional<Product> optProduct = productService.getProductById(productSetup.getProductId());
			Agent prdAgent = agentService.getAgentByAgentNum(productSetup.getAgentNum());
			if(optProduct.isPresent())
			{
				log.info("====> call populateMemberGroupAgentProductInfo next");
				addProductToMember(memberSetup.getMember(), group, prdAgent, productSetup.getSoldDate(), optProduct.get(),productSetup);
				if(productSetup.getProductUpgradeType().compareTo(ProductUpgradeType.PRIMARY.getValue())==0)
				{
					memberSetup.getMember().setAgent(prdAgent);
				}
			}
		}
	}
	//TODO - need to refactor
	private void populateMemberGroupAgentProductInfoForUpdate(MemberUpdateSetup memberSetup)
	{
		//memberSetup.getGroupCd--> to be group

		GroupEntity group = groupService.getGroupByCode(memberSetup.getGroupCd());

		//do not change member group if effective date is in future
		if(!memberSetup.getEffectiveDate().isAfter(LocalDate.now())) {
			memberSetup.getMember().setGroup(group);
		}
		
		Set<MemberFee> memberFeeItemSet = new HashSet<>();
		
		if (memberSetup.isProductUpdate()) {

			if(isMemberPartOfB2BDivision(memberSetup.getMember().getMemberId())) {
				setUpgradeProductEffectiveDate(memberSetup);
				setSoldDateForUpgradeProduct(memberSetup);
			}

			Date productEffEndDate = Date.from(memberSetup.getEffectiveDate().minusDays(1)
					.atStartOfDay(java.time.ZoneId.systemDefault()).toInstant());

			Date prdEffDtTmp = convertLocalToUtilDate(memberSetup.getEffectiveDate());
			
			if(memberSetup.isCopyProductAndCommission()) {				
				List<Integer> prdIdList = memberSetup.getProductSet().stream().map(MemberSetupProduct::getProductId).collect(Collectors.toList());
			memberFeeItemSet = memberSetup.getMember().getMemberFee().stream()
						.filter(feeItem-> prdIdList.contains(feeItem.getProduct().getProductId())
								&& PTSUtilityService.isDateWithInRange(prdEffDtTmp, feeItem.getEffectiveStartDate(), feeItem.getEffectiveEndDate()))
						.collect(Collectors.toSet());
			}

			Set<MemberFee> memFeeToBeDel = memberSetup.getMember().getMemberFee().stream()
					.filter(feeItem -> ((prdEffDtTmp.compareTo(feeItem.getEffectiveStartDate()) == 0
							|| prdEffDtTmp.before(feeItem.getEffectiveStartDate()))
							&& !paymentService.isValidPaymentExistsForMemberProduct(
									memberSetup.getMember().getMemberId(), feeItem.getId())))
					.collect(Collectors.toSet());

			Set<MemberFee> memFeeToBeExpired = memberSetup.getMember().getMemberFee().stream()
					.filter(feeItem -> (prdEffDtTmp.compareTo(feeItem.getEffectiveStartDate()) >= 0
							&& prdEffDtTmp.compareTo(feeItem.getEffectiveEndDate()) <= 0) )
					.collect(Collectors.toSet());

			memFeeToBeDel.stream().map(feeItem -> {
				boolean success = memberSetup.getMember().getMemberFee()
						.removeIf(item -> item.getId().compareTo(feeItem.getId()) == 0);

				if (success) {
					memberSetup.getMember().getMemberFee()
							.removeIf(item -> item.getSetupFeeRefId().compareTo(feeItem.getSetupFeeRefId()) == 0);
				}
				return feeItem;
			}).collect(Collectors.toSet());

			memFeeToBeExpired.forEach(feeItem -> feeItem.setEffectiveEndDate(productEffEndDate));

		}

		for(MemberSetupProduct productSetup: memberSetup.getProductSet())
		{
			Optional<Product> optProduct = productService.getProductById(productSetup.getProductId());
			Agent prdAgent = agentService.getAgentByAgentNum(productSetup.getAgentNum());
			if(optProduct.isPresent()){	
				
				if(memberSetup.isCopyProductAndCommission()) {
					copyProductAndCommission(productSetup,memberFeeItemSet,memberSetup.getMember(),memberSetup.getEffectiveDate());
				}
				else {
					addProductToMember(memberSetup.getMember(), group, prdAgent, productSetup.getSoldDate(), optProduct.get(),productSetup);
				}
				if(productSetup.getProductUpgradeType().compareTo(ProductUpgradeType.PRIMARY.getValue())==0){
					memberSetup.getMember().setAgent(prdAgent);
				}
			}
		}
	}

	private void copyProductAndCommission(MemberSetupProduct productSetup, Set<MemberFee> memberFeeItemSet,Member member,LocalDate effectiveDate) {
		
		for(MemberFee feeItem: memberFeeItemSet) {
			if(feeItem.getProduct().getProductId().compareTo(productSetup.getProductId())==0) {
				MemberFee memberFee = copyMemberFee(feeItem,member.getModifiedBy(),PTSUtilityService.convertLocalToUtilDate(effectiveDate),member);
				member.addMemberFee(memberFee);
			}
		}		
	}

	private MemberFee copyMemberFee(MemberFee source,Integer userId,Date productStartDate,Member member) {
		MemberFee dest = new MemberFee();
		dest.setAmount(source.getAmount());
		dest.setAmountPaid(source.getAmountPaid());
		dest.setAmountPaidMasa(source.getAmountPaidMasa());
		dest.setBalanceDue(source.getBalanceDue());
		dest.setCreatedBy(userId);
		dest.setCreatedDate(new Date());
		dest.setEffectiveEndDate(Constant.DEFAULT_PRODUCT_END_DATE);
		dest.setEffectiveStartDate(productStartDate);
		dest.setFeeDetails(source.getFeeDetails());
		dest.setFeeMultipler(source.getFeeMultipler());
		dest.setFrequencyDetails(source.getFrequencyDetails());
		dest.setGroup(source.getGroup());
		dest.setInitMultipler(source.getInitMultipler());
		dest.setMaxAmountDue(source.getMaxAmountDue());
		
		dest.setMemberId(source.getMemberId());
		dest.setModifiedBy(userId);
		dest.setModifiedDate(new Date());
		dest.setOutstanndingBalance(source.getOutstanndingBalance());
		dest.setOverrideNewComm(source.getOverrideNewComm());
		dest.setPosition(source.getPosition());
		dest.setPremMultipler(source.getPremMultipler());
		dest.setProduct(source.getProduct());
		dest.setProductDate(source.getProductDate());
		dest.setRetailAmount(source.getRetailAmount());
		dest.setSetupFeeAmt(source.getSetupFeeAmt());
		dest.setSetupFeeRefId(getSetupRefId(member.getMemberFee()));
		dest.setUpgradeType(source.getUpgradeType());
		
		dest.setMemberEmployeeId(null);
		
		for(MemberCommission memComm: source.getCommission()) {
			dest.addCommission(copyMemberCommission(memComm, dest, member));
		}		
		return dest;
	}
	
	private MemberCommission copyMemberCommission(MemberCommission sourceMemComm,MemberFee destMemFee,Member member) {
		MemberCommission destMemComm = new MemberCommission();
		destMemComm.setAgentId(sourceMemComm.getAgentId());
		destMemComm.setAgentPosition(sourceMemComm.getAgentPosition());
		destMemComm.setCommissionId(0);
		destMemComm.setCreatedBy(destMemFee.getModifiedBy());
		destMemComm.setCreatedDate(new Date());
		destMemComm.setMember(member);
		destMemComm.setMemberFee(destMemFee);
		destMemComm.setModifiedBy(destMemFee.getModifiedBy());
		destMemComm.setModifiedDate(new Date());
		destMemComm.setNewSalePercent(sourceMemComm.getNewSalePercent());
		destMemComm.setProductId(sourceMemComm.getProductId());
		destMemComm.setRenewPercent(sourceMemComm.getRenewPercent());
		destMemComm.setUseFlatRate(sourceMemComm.getUseFlatRate());
		return destMemComm;
	}
	
	//rules- if effective start date is > 3 months then set effective date as current month first day
	//	--possible scenario of b2b upgrade product added earlier
	private void setUpgradeProductEffectiveDate(MemberUpdateSetup memberSetup) {
		// check current product effective start date and if it same as current
		// effective date ,
		// possibility of B2B upgrade product

		boolean effDateMoreThan3months=false;

		LocalDate currentDayFirstOfMonth = LocalDate.now().withDayOfMonth(1);

		long months = ChronoUnit.MONTHS.between(memberSetup.getEffectiveDate(), LocalDate.now().withDayOfMonth(1));

		if (months > MONTHS_RANGE_FOR_ENROLL) {
			effDateMoreThan3months=true;
		}

		if (effDateMoreThan3months) {
				memberSetup.setEffectiveDate(currentDayFirstOfMonth);
				memberSetup.getProductSet().forEach(item -> {
					item.setEffectiveStartDate(Date.from(
							memberSetup.getEffectiveDate().atStartOfDay(java.time.ZoneId.systemDefault()).toInstant()));
				});
		}
	}
	private void setSoldDateForUpgradeProduct(MemberUpdateSetup memberSetup) {

		Date currentActivePrdSoldDate = memberSetup.getMember().getMemberFee().stream()
				.filter(item -> isDateWithInRange(new Date(), item.getEffectiveStartDate(),
						item.getEffectiveEndDate())
						&& !Constant.SETUP_FEE_IDS.contains(item.getFeeDetails().getFeeId()))
				.map(MemberFee::getProductDate).findFirst().orElse(new Date());

		memberSetup.getProductSet().forEach(item -> item.setSoldDate(currentActivePrdSoldDate));
	}

	/**
	 * @param member
	 * @param note
	 * @param ptsUserId
	 */
	public void cancelMember(Member member, String note, Integer ptsUserId)
	{
		member.setActive(CANCELLED.getStatus());
		if(member.getCancelDate().compareTo(DEFULT_DATE_1900)==0)
			member.setCancelDate(new Date());

		if(member.getCancelCode() ==null || (member.getCancelCode() != null && member.getCancelCode().intValue() <=0))
			member.setCancelCode(3);//opt out

		member.setModifiedBy(ptsUserId);
		member.setModifiedDate(new Date());
		createMemberNote(member, note, ptsUserId);
		memberRepository.save(member);
	}

	/**
	 * @param memberSetup
	 * clear current product , agent hierarchy
	 * change group / setup new product / hierarchy
	 */
	public void processProductChanges(MemberUpdateSetup memberSetup) {

		int primaryPrdId = memberProductFeeService.getMemberPrimaryProductId(memberSetup.getMember().getMemberFee());

		//populateMemberGroupAgentProductInfo(memberSetup);
		populateMemberGroupAgentProductInfoForUpdate(memberSetup);
		addOrUpdateMemberEmployeeId(memberSetup.getMember());
		populateMemberDependants(memberSetup);
		populateMemberNotes(memberSetup);

		try
		{
			log.info("Process product changes for Member Id [{}], updating member",memberSetup.getMember().getMemberId());
			saveMemberInTransaction(memberSetup);

			log.info("Process product changes for Member Id [{}], checking for unpaid advance commission cb",memberSetup.getMember().getMemberId());
			processMemberUnPaidAdvanceCommCB(memberSetup.getMember(),primaryPrdId,"Product changed, advance comm CB");

			if(paymentToPost(memberSetup))
			{
				log.info("Process product changes for Member Id [{}], processing member payment",memberSetup.getMember().getMemberId());
				postMemberPayment(memberSetup);
			}
			else
			{
				populateMemberAttributes(memberSetup.getMember(),memberSetup.getMember().getModifiedBy(),null,memberSetup.getMember().getGroup().getGroupCode());
			}
			newMemberFulfillmentRequest(memberSetup);
		}
		catch(Exception e)
		{
			memberSetup.setErrorMessage(e.getMessage());
			StringBuilder sb = new StringBuilder();
			sb.append("Exception while process member, Message [").append(e.getMessage()).append("] Cause [").append(e.getCause()).append("]");
			memberSetup.addError(sb.toString());
		}
	}


	/**
	 * @param member
	 * @param primaryPrdId
	 */
	private void processMemberUnPaidAdvanceCommCB(Member member,Integer primaryPrdId,String note)
	{
		GroupEntity group = member.getGroup();
		if(null != group)
		{
			Integer commType = CommissionType.GROUP.getType();
			if(group.getBillType() > GroupBillType.INDIVIDUAL.getBillType())
				commType = CommissionType.GROUP.getType();
			else if(group.getBillType().compareTo(GroupBillType.INDIVIDUAL.getBillType())==0)
				commType = CommissionType.NEW.getType();

			commissionService.chargebackUnPaidAdvComm(member.getMemberId(), primaryPrdId, note, commType);
		}
	}
	
	/*
	    1. Months Used: months used since effective date (if remainder days >= 15 add 1 month)
		2. Months to Credit: Months Payed for– Months Used
		3. Credit Money: (Months to Credit / Months paid for) * Amount Paid
		4. Credit Time: Months to Credit added to the renewal date.
	 */
	public double processMoneyCreditForProduct(MemberFee primaryPrdFee,Set<MemberFee> memberFeeSet
			,Integer memberId,Date reinstateDate,Date effectiveDate,Date soldDate, double upgradePrdAmt, boolean upgradeToLifetime)
	{
		int productFreqId = Constant.FREQ_MONTH;
		double creditMoney=0;
		boolean lifeTimePrd = "CLM".equalsIgnoreCase(primaryPrdFee.getFrequencyDetails().getFrequencyType());
		if(!lifeTimePrd)
		{
			Date upgradeEffectiveDate = getEffectiveDateForUpgrade(primaryPrdFee,effectiveDate);
			long monthsUsed =  getProductMonthsUsed(productFreqId,upgradeEffectiveDate,soldDate);
			double monthsPayedFor = getProductMonthsPayed(memberFeeSet,memberId,primaryPrdFee,reinstateDate);
			double paidAmt = memberProductFeeService.productPaidAmount(memberFeeSet,memberId,primaryPrdFee.getProduct().getProductId(),reinstateDate);
			double monthsToCredit = (monthsPayedFor - monthsUsed);
			if(monthsToCredit < 0)
				monthsToCredit =0;

			creditMoney = (monthsToCredit / monthsPayedFor) * (paidAmt);
			creditMoney = (double) Math.round(creditMoney * 100)/100;
		}
		else if(lifeTimePrd && upgradeToLifetime)
		{//life time product $2900 -> $3900
			creditMoney =  (primaryPrdFee.getAmount() - upgradePrdAmt < 0) ? primaryPrdFee.getAmount() : 0;
					//(upgradePrdAmt -  primaryPrdFee.getAmount()) >0 ? upgradePrdAmt -  primaryPrdFee.getAmount() : 0;
		}
		else
		{
			creditMoney=0;
		}
		log.info("Member Id [{}] Credit Money [{}] ",memberId,creditMoney);
		return creditMoney;
	}

	public double processTimeCreditForProduct(MemberFee primaryPrdFee, Date soldDate, Date effectiveDate ,Integer memberId,Set<MemberFee> memberFeeSet,
			Date reinstateDate)
	{
		int productFreqId = Constant.FREQ_MONTH;
		Date upgradeEffectiveDate = getEffectiveDateForUpgrade(primaryPrdFee,effectiveDate);
		long monthsUsed =  getProductMonthsUsed(productFreqId,upgradeEffectiveDate, soldDate);
		double monthsPayedFor = getProductMonthsPayed(memberFeeSet,memberId,primaryPrdFee,reinstateDate);
		double monthsToCredit = (monthsPayedFor - monthsUsed);
		if(monthsToCredit < 0)
			monthsToCredit = 0.0;
		//return getMemberNextRenewDate(currentRenewDate,primaryPrdFee.getFrequencyDetails().getID(),monthsToCredit);
		return monthsToCredit;
	}

	/**
	 * @param primaryPrdFee
	 * @param effectiveDate
	 * @return
	 */
	private Date getEffectiveDateForUpgrade(MemberFee primaryPrdFee,Date effectiveDate) {

		if(primaryPrdFee.getUpgradeType().intValue()==0)
		{
			return effectiveDate;
		}
		if(effectiveDate.after(primaryPrdFee.getProductDate()))
		{
			return effectiveDate;
		}
		else
		{
			return primaryPrdFee.getProductDate();
		}
	}


	/**
	 * @param primaryPrdFee
	 * @param memberId
	 * @param primaryPrdFee
	 * @param reinstateDate
	 * @return
	 */
	private double getProductMonthsPayed(Set<MemberFee> memberFeeSet,Integer memberId, MemberFee primaryPrdFee,Date reinstateDate ) {
		double paidAmt = memberProductFeeService.productPaidAmount(memberFeeSet,memberId,primaryPrdFee.getProduct().getProductId(),reinstateDate);

		double monthlyAmt = getMonthlyAmount(primaryPrdFee.getFrequencyDetails().getID(),primaryPrdFee.getAmount());

		return (paidAmt / monthlyAmt) ;

		/*if(primaryPrdFee.getFrequencyDetails().getID()==Constant.FREQ_MONTH)
		{
			return (paidAmt / primaryPrdFee.getAmount());
		}
		else
		{
			return ((paidAmt / primaryPrdFee.getAmount()) * primaryPrdFee.getFrequencyDetails().getYearParts());
		}
		*/
	}

	/**
	 * @param productFreqId
	 * @param memberEffectiveDate
	 * @param soldDate
	 * @return
	 */
	private long getProductMonthsUsed(int productFreqId,Date memberEffectiveDate,Date soldDate) {
		return memberProductFeeService.findMonthsBetweenForCredit(memberEffectiveDate,soldDate,productFreqId);
	}

	/**
	 * @param memberSetup
	 * @return
	 */
	private boolean paymentToPost(MemberSetup memberSetup) {
		BigDecimal payAmt = memberSetup.getPaymentAmount();
		if(payAmt != null && payAmt.compareTo(BigDecimal.ZERO)>0)
		{
			return true;
		}
		return false;
	}


	private MemberSetup saveMemberInTransaction(MemberSetup memberSetup) {
		TransactionTemplate transactionTemplate = createNewTransaction("SAVEMEMBER");
		return transactionTemplate.execute(action->saveMemberTransaction(memberSetup));
	}

	private Member saveMemberInTransaction(Member member) {
		TransactionTemplate transactionTemplate = createNewTransaction("SAVEMEMBER");
		return transactionTemplate.execute(action->saveMemberTransaction(member));
	}

	private Member saveMemberTransaction(Member member)
	{
		try
		{
			memberRepository.save(member);
			log.info("Member saved successfully , Member [{}]",member.getMemberId());
		}

		catch (Exception e)
		{
			log.error("Error saving member in method saveMemberTransaction , Message [{}] Cause [{}]",e.getMessage(),e.getCause());
		}
		return member;
	}

	/**
	 * @param memberSetup
	 * @return
	 */
	private MemberSetup saveMemberTransaction(MemberSetup memberSetup) {
		Member member = memberSetup.getMember();

		if (memberSetup.getBenefitAddress() != null
				&& StringUtils.hasText(memberSetup.getBenefitAddress().getAddress1())) {
			member.setAddressDetails(memberSetup.getBenefitAddress());
		}
		if (memberSetup.getMailingAddress() != null
				&& StringUtils.hasText(memberSetup.getMailingAddress().getAddress1())) {
			member.setAlternateAddressDetails(memberSetup.getMailingAddress());
		}
		memberRepository.save(member);

		if (!StringUtils.hasText(member.getEmployeeId())) {
			member.setEmployeeId(String.valueOf(member.getMemberId()));
			// assume only 1 product added at a time.
			MemberFee primaryFee = member.getMemberFee().stream()
					.filter(item -> item.getFeeDetails().getFeeId() != 1 && 
							item.getProduct().getProductId() != 268 &&
	                        item.getProduct().getProductId() != 182).findFirst().orElse(null);
			populateMemberEmployeeId(member, primaryFee);
			memberRepository.save(member);
		}

		return memberSetup;
	}

	/**
	 *  @return - returns error string if suspending is not successful, otherwise null
	 */
	@Transactional
	public String cancelMember(Integer memberId, Date cancelDate, Integer ptsUserId,
							   String note, int cancelReasonId,
							   String applicationSource)
	{
		return getMemberDetails(memberId)
				.map(member -> {
					member.setActive(CANCELLED.getStatus());
					member.setCancelDate(defineCancelDate(cancelDate));
					member.setCancelCode(cancelReasonId);
					createMemberNote(member,note,ptsUserId);
					member.setModifiedBy(ptsUserId);
					member.setModifiedDate(new Date());

					MemberSetup memberSetup = new MemberSetup();
					memberSetup.setMember(member);

					memberRepository.save(member);

					Integer primaryPrdId = memberProductFeeService.getMemberPrimaryProductId(member.getMemberFee());
					processMemberUnPaidAdvanceCommCB(member,primaryPrdId,"Member status cancelled,advance comm CB");

					memberAuditEventService.addMemberAuditEvent(member, AuditEventType.CANCEL, ptsUserId, applicationSource);
					return "";
				})
				.orElseGet(() -> "Member ID ["+memberId+"] not found , to process cancel.");
	}

	/**
	 * @return - returns error string if suspending is not successful, otherwise null
	 */
	@Transactional
	public String suspendMember(Integer memberId, Date cancelDate, Date reactivationDate, Integer ptsUserId,
								String note, int cancelReasonId,
								String applicationSource) {
		return memberRepository.findById(memberId)
				.map(member -> {
					member.setActive(SUSPENDED.getStatus());
					member.setCancelDate(defineCancelDate(cancelDate));

					member.setReactiveDate(reactivationDate);
					member.setCancelCode(cancelReasonId);
					member.setModifiedBy(ptsUserId);
					member.setModifiedDate(new Date());

					createMemberNote(member, note, ptsUserId);

					MemberSetup memberSetup = new MemberSetup();
					memberSetup.setMember(member);

					memberRepository.save(member);

					memberAuditEventService
							.addMemberAuditEvent(member, AuditEventType.SUSPEND, ptsUserId, applicationSource);
					return "";
				})
				.orElseGet(() -> "Member ID [" + memberId + "] not found , to process suspend.");
	}
	
	private Date defineCancelDate(Date cancelDate) {
		if (null == cancelDate
				|| cancelDate.before(DEFULT_DATE_1900)
				|| Objects.equals(DEFULT_DATE_1900, cancelDate)) {
			return new java.util.Date();
		} else {
			return cancelDate;
		}
	}

	/**
	 * @param lastName
	 * @param birthDate
	 * @return
	 */
	public boolean memberExistsByLastNameAndDOB(String lastName,Date birthDate)
	{
		return memberRepository.existsByLastNameAndBirthDate(lastName, birthDate);
	}

	public Set<Member> getMemberByLastNameAndDob(String lastName,Date birthDate)
	{
		return memberRepository.findByLastNameAndBirthDate(lastName,birthDate);
	}

	public Set<MemberSummaryWithGroup> getMemberSmryByFirstAndLastNameAndDob(String firstName,String lastName,Date birthDate)
	{
		return memberRepository.findAllByFirstNameAndLastNameAndBirthDate(firstName,lastName,birthDate);
	}

	public Set<MemberSummary> getMemberSmryByFirstAndLastNameAndDobAndEmployeeIdInGroups(String firstName,String lastName,Date birthDate,String employeeId,List<String> groupCodes){
		return memberRepository.findAllByFirstNameAndLastNameAndBirthDateAndEmployeeIdAndGroupGroupCodeIn(firstName,lastName,birthDate,employeeId,groupCodes);
	}
	public Set<MemberSummary> getMemberSmryByFirstAndLastNameAndEmployeeIdInGroups(String firstName,String lastName,String employeeId,List<String> groupCodes){
		return memberRepository.findAllByFirstNameAndLastNameAndEmployeeIdAndGroupGroupCodeIn(firstName,lastName,employeeId,groupCodes);
	}
	/**
	 * @param groupCode
	 * @return
	 */
	public boolean isMemberPartOfSeperateSpouseMembershipSalesChannel(String groupCode)
	{
		GroupEntity group = groupService.getGroupByCode(groupCode);
		if(null == group)
			return false;

		List<String> createMemberShip = ConfigParamService.convertCommaSeparatedStringToList(
    			configParamService.getParamValue(ConfigParamName.SINGLE_TO_ALTERNATE_SETUP_SC_NAMES.name()));
		if(createMemberShip.contains(group.getBusinessLineEntity().getSalesChannel().getName()))
			return true;

		return false;
	}

	/**
	 * @param group
	 * @return
	 */
	public boolean isMemberPartOfSeperateSpouseMembershipSalesChannel(GroupEntity group)
	{
		List<String> createMemberShip = ConfigParamService.convertCommaSeparatedStringToList(
    			configParamService.getParamValue(ConfigParamName.SINGLE_TO_ALTERNATE_SETUP_SC_NAMES.name()));
		return createMemberShip.contains(group.getBusinessLineEntity().getSalesChannel().getName());
	}

	/**
	 * @param groupCode
	 * @return
	 */
	public boolean isMemberPartOfSalesChannelWithMultipleMemberships(String groupCode)
	{
		GroupEntity group = groupService.getGroupByCode(groupCode);
		if(null == group)
			return false;
		
		List<String> allowNewMemberShipForDiffSC = ConfigParamService.convertCommaSeparatedStringToList(
				configParamService.getParamValue(ConfigParamName.ALLOW_NEW_WITH_EXISTING_MEMBERSHIP_IN_SC.name()));
		return ( allowNewMemberShipForDiffSC.contains(group.getBusinessLineEntity().getSalesChannel().getName()));
	}
	/**
	 * @param group
	 * @return
	 */
	public boolean isMemberPartOfSalesChannelWithMultipleMemberships(GroupEntity group)
	{
		List<String> allowNewMemberShipForDiffSC = ConfigParamService.convertCommaSeparatedStringToList(
				configParamService.getParamValue(ConfigParamName.ALLOW_NEW_WITH_EXISTING_MEMBERSHIP_IN_SC.name()));
		return ( allowNewMemberShipForDiffSC.contains(group.getBusinessLineEntity().getSalesChannel().getName()));
	}

	/**
	 * @param lastName
	 * @param dob
	 * @param enrollingGroup
	 * @return
	 */
	public boolean isDuplicateMember(String lastName,Date dob,String enrollingGroup,String firstName) {
		boolean flag=false;

		String dobStr = ptsUtilityService.formatUtilDate(dob);
		log.info("Checking for duplicate member, Last Name [{}] Dob [{}] ",lastName,dobStr);

		boolean enrollingToTRPPPrecoa = isMemberPartOfSalesChannelWithMultipleMemberships(enrollingGroup);
		log.info("Member enrolling to TRPP/Precoa GroupEntity [{}]",enrollingToTRPPPrecoa);

		flag = isMemberExists(lastName, dob, enrollingToTRPPPrecoa,firstName);
		log.info("Duplicate member check for member last name [{}] dob [{}] return value [{}]",lastName,dobStr,flag);
		return flag;
	}
	
	public boolean doesGroupHaveActiveMember(String groupCode) {
		return memberRepository.isAnyMemberActive(groupCode);
	}

	public void throwForUpdateCancelledMember(int memberId, UpdateMemberInfoInput input) {
		Optional<Member> memberOpt = memberRepository.findById(memberId);

		boolean validateAsCancelled = memberOpt
				.map(this::validateAsCancelled)
				.orElseThrow(() -> new MemberNotFoundException(memberId));

		if (validateAsCancelled) {
			memberOpt
				.filter(m -> isSpouseFieldsTheSame(m, input))
				.filter(m -> isTheSameDependents(input.getDependents(), m.getDependants()))
				.orElseThrow(
						() -> new ServerWebInputException(
								"Spouse or dependents cannot be updated for cancelled member."));
		}
	}

	private boolean validateAsCancelled(Member m) {
		return Objects.equals(m.getActive(), MemberActiveStatus.CANCELLED.getStatus())
				&& PTSUtilityService.convertUtilToLocalDate(m.getCancelDate()).isBefore(LocalDate.now())
				&& !Objects.equals(m.getCancelDate(), DEFULT_DATE_1900);
	}

	private boolean isSpouseFieldsTheSame(Member m, UpdateMemberInfoInput input) {
		return Objects.equals(input.getSpouseBirthDate(), m.getSpouseBirthDate())
				&& Objects.equals(input.getSpouseEmail(), m.getSpouseEmail())
				&& Objects.equals(input.getSpouseFirst(), m.getSpouseFirst())
				&& Objects.equals(input.getSpouseLast(), m.getSpouseLast())
				&& Objects.equals(input.getSpousePhone(), m.getSpousePhone())
				&& Objects.equals(input.getSpouseMi(), m.getSpouseMi());
	}

	private boolean isTheSameDependents(Set<MemberDependantInput> inputDependants, Set<Dependant> dependants) {
		return Objects.equals(
				inputDependants,
				dependants.stream()
						.map(Dependant::toMemberDependantInput)
						.collect(Collectors.toSet()));
	}

	/**
	 * @param lastName
	 * @param dob
	 * @param enrollingToTRPPPrecoa
	 * @return
	 */
	private boolean isMemberExists(String lastName,Date dob,boolean enrollingToTRPPPrecoa,String firstName)
	{
		Set<Member> memSet = getMemberByLastNameAndDobAndFirstName(lastName,dob,firstName);

		boolean flag=false;

		String dobStr = ptsUtilityService.formatUtilDate(dob);

		for(Member item: memSet)
		{
			boolean existingGrpTRPPPreoca = isMemberPartOfSalesChannelWithMultipleMemberships(item.getGroup());

			log.info("Found Member ID [{}] by last name [{}] and dob [{}], Part of TRPP/Precoa Group [{}]",item.getMemberId(),lastName,dobStr,existingGrpTRPPPreoca);

			//member current: non TRPP/Precoa , new: TRPP/Precoa -OK
			//current: TRPP/Precoa , new : TRPP/Precoa - NOT OK
			//current: TRPP/Precoa , new : non TRPP/Preoca - OK
			//current: non TRPP/Precoa , new : non TRPP/Precoa - NOT OK

			if(!existingGrpTRPPPreoca && enrollingToTRPPPrecoa)
			{
				flag = false;
			}
			else if(existingGrpTRPPPreoca && enrollingToTRPPPrecoa)
			{
				flag = true;
			}
			else if(existingGrpTRPPPreoca && !enrollingToTRPPPrecoa)
			{
				flag = false;
			}
			else if(!existingGrpTRPPPreoca && !enrollingToTRPPPrecoa)
			{
				flag = true;
			}
			log.info("Member ID [{}] duplicate membership found based on existing and new group TRPP/Preoca check  [{}]",item.getMemberId(),flag);
			if(flag)
				return flag;
		}
		return flag;
	}
	/**
	 * @param name
	 * @return
	 */
	private TransactionTemplate createNewTransaction(String name) {
		TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
		transactionTemplate.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
		transactionTemplate.setName(name);
		return transactionTemplate;
	}

	public Set<MemberFee> getMemberProducts(Integer memberId){
		return memberFeeRepository.findAllByMemberId(memberId);
	}

	public Integer getMemberDivision(Integer memberId) {
		Integer divisionId = memberRepository.getMemberDivision(memberId);
		return divisionId ==null ? 0: divisionId;
	}

	public boolean isMemberPartOfInternationalDivision(Integer memberId) {
		return (getMemberDivision(memberId).intValue()==1);
	}

	public boolean isMemberPartOfB2BDivision(Integer memberId) {
		return (getMemberDivision(memberId).intValue()== B2B_DIVISION_ID);
	}
	public boolean isGroupPartOfDivisionWithNewCommissionProcess(Integer memberId) {
		int divId = getMemberDivision(memberId).intValue();
		return (divId == B2B_DIVISION_ID || divId == Constant.B2C_DIVISION_ID || divId == Constant.INTERNATIONAL_DIVISION_ID
				|| divId == Constant.RETENTION_DIVISION_ID);
	}


	/**
	 * @param frequencyId
	 * @param premAmount
	 * @return
	 */
	private double getMonthlyAmount(int frequencyId,double premAmount) {
		double monthlyAmount = 0;
		switch (frequencyId)
		{
			case Constant.FREQ_WEEK:
				monthlyAmount += premAmount * 52 / 12;
				break;
			case Constant.FREQ_BIWEEK:
				monthlyAmount += premAmount * 26 / 12;
				break;
			case Constant.FREQ_SEMI_MONTHLY:
				monthlyAmount += premAmount * 24 / 12;
				break;
			case Constant.FREQ_MONTH:
				monthlyAmount += premAmount;
				break;
			case Constant.FREQ_YEAR:
				monthlyAmount += premAmount / 12;
				break;
			case Constant.FREQ_FIVE_YEAR:
				monthlyAmount += premAmount / 5 / 12;
				break;
			case Constant.FREQ_LIFETIME:
				monthlyAmount += premAmount;
				break;
			case Constant.FREQ_BIENNIAL:
				monthlyAmount += premAmount / 2 / 12;
				break;
			case Constant.FREQ_TRIENNIAL:
				monthlyAmount += premAmount / 3 / 12;
				break;
			case Constant.FREQ_QUADRENNIAL:
				monthlyAmount += premAmount / 4 / 12;
				break;
			case Constant.FREQ_DECENNIAL:
				monthlyAmount += premAmount / 10 / 12;
				break;
			default:
				break;
		}
		return monthlyAmount;
	}
	public void addMemberCoverageLapseRequest(MemberCoverageLapse memberCoverageLapse) {
		memberCoverageLapseRepository.save(memberCoverageLapse);
	}

	public Set<MemberCoverageLapse> getMemberCoverageLapses(Integer memberId)
	{
		return memberCoverageLapseRepository.findAllByMemberMemberIdAndIsDelete(memberId,Boolean.FALSE);
	}

	public Integer getApplicationSourceIDByCode(String code)
	{
		Optional<ApplicationSource> optAppSource = applicationSourceRepository.findByCode(code);
		return optAppSource.isPresent() ? optAppSource.get().getId() : 0; //0-default to pts
	}

	public void deleteAllMembersInGroup(String groupCode) {
		Set<MemberSummaryForGrp> memSmry = memberRepository.findAllByGroupGroupCode(groupCode);
		memSmry.forEach(item->deleteMember(item.getMemberId()));
	}

	/**
	 * @param memberId
	 */
	public void deleteMember(Integer memberId)
	{
		Optional<Member> optMem = memberRepository.findById(memberId);
		if(optMem.isPresent())
		{
				Member member = optMem.get();
				/*
				Integer benefitAddressId = member.getAddress();
				Integer mailingAddressId = membergetAlternateAddressDetailss();
				Integer burialAddressId = member.getBurialAddress();

				if(null != benefitAddressId && benefitAddressId.intValue() >0)
				{
					addressService.delete(benefitAddressId);
					member.setAddress(null);
				}

				if(null != mailingAddressId && mailingAddressId.intValue() >0)
				{
					addressService.delete(mailingAddressId);
					member.setAlternateAddress(null);
				}

				if(null != burialAddressId && burialAddressId.intValue() >0)
				{
					addressService.delete(burialAddressId);
					member.setBurialAddress(null);
				}*/
				memberRepository.delete(member);
				log.info("Member Id [{}] deleted successfully!.",memberId);
		}
		else
		{
			log.error("Member Id [{}] not found for delete",memberId);
		}
	}

	public void throwIfActivationDateIsNotValid(Date activationDate, Integer memberId) {
		validateWith3monthsPeriod(activationDate);
		validateWithEffectiveAndCancelDates(activationDate, memberId);
	}

	public void throwIfMemberGroupIsNotActive(Integer memberId) {
		boolean isGroupActive = memberRepository.findById(memberId)
				.map(m -> m.getGroup().isActive())
				.orElseThrow(() -> new MemberNotFoundException(memberId));

		if (!isGroupActive) {
			throw new ServerWebInputException("Group not active, member cannot be activated.");
		}
	}

	private void validateWith3monthsPeriod(Date activationDate) {
		Instant startAvailablePeriod = ZonedDateTime.now().minusMonths(MONTHS_RANGE_FOR_ENROLL).toInstant();
		Instant endAvailablePeriod = ZonedDateTime.now().plusMonths(MONTHS_RANGE_FOR_ENROLL).toInstant();

		if (startAvailablePeriod.isAfter(activationDate.toInstant())
				|| endAvailablePeriod.isBefore(activationDate.toInstant())) {
			throw new ServerWebInputException("Activation date should be within 3 month range past or future");
		}
	}

	private void validateWithEffectiveAndCancelDates(Date activationDate, Integer memberId) {
		Optional<MemberInfo> member = memberRepository.findByMemberId(memberId);
		LocalDate effectiveDate = member
				.map(MemberInfo::getEffectiveDate)
				.map(PTSUtilityService::convertUtilToLocalDate)
				.orElseThrow(() -> new UnprocessableEntityException("Member effective date should exists"));

		LocalDate cancelDate = member
				.map(MemberInfo::getCancelDate)
				.map(PTSUtilityService::convertUtilToLocalDate)
				.orElseThrow(() -> new UnprocessableEntityException("Member cancelled date should exists"));

		if (convertUtilToLocalDate(activationDate).isBefore(effectiveDate)
		    || convertUtilToLocalDate(activationDate).isBefore(cancelDate)) {
			throw new ServerWebInputException("Activation date should not be before member effective date and cancel date.");
		}
	}
	
	/**
	 * @param member
	 */
	private void setMemberActiveEmployeeId(Member member) {
		String activeGrpProductEmployeeId = member.getMemberFee().stream()
				.filter(memFeeItem-> PTSUtilityService.isDateWithInRange(new Date(), memFeeItem.getEffectiveStartDate(),memFeeItem.getEffectiveEndDate())
						&& memFeeItem.getUpgradeType().equals(ProductUpgradeType.PRIMARY.getValue())
						&& memFeeItem.getFeeDetails().getFeeId().intValue() != 1
						&& memFeeItem.getMemberEmployeeId() != null
						)
				.map(memFeeItem -> memFeeItem.getMemberEmployeeId().getEmployeeId())
				.findFirst().orElse(member.getEmployeeId());
			
			member.setEmployeeId(activeGrpProductEmployeeId);			
	}

	/**
	 * Group can be deactivated if no active member relate to it
	 *
	 * @return - {@code true} if no active members, otherwise {@code false}
	 */
	public boolean groupCanDeactivated(String groupCode) {
		return !memberRepository.isAnyMemberActive(groupCode);
	}

	public Set<MemberShortInfoDto> findMemberShortInfoDtos(Set<Integer> memberIds) {
		return memberRepository.findShortInfoByIds(memberIds);
	}

	public Member findMemberByProductIds(Integer memberId, Set<Integer> productIds) {
		return memberRepository.getMemberByProductsIds(memberId, productIds);
	}
	public Set<Member> getMemberByLastNameAndDobAndFirstName(String lastName,Date birthDate,String firstName)
	{
		return memberRepository.findByLastNameAndBirthDateAndFirstName(lastName,birthDate,firstName);
	}
}
