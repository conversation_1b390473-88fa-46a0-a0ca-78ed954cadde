package com.masa.pts.core.repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.masa.pts.core.domain.GroupEntity;
import com.masa.pts.core.domain.GroupEntity.GroupCodeSmry;
import com.masa.pts.core.domain.GroupEntity.GroupIDCodeSmry;
import com.masa.pts.core.model.AgentReducedViewDTO;
import com.masa.pts.core.model.DashboardGroupProductDTO;
import com.masa.pts.core.model.DashboardGroupSummaryDTO;
import com.masa.pts.core.model.GroupDivisionDTO;
import com.masa.pts.core.model.GroupReducedView;
import com.masa.pts.core.model.GroupSearchDTO;
import com.masa.pts.core.model.ProductSale;

public interface GroupRepository extends JpaRepository<GroupEntity, Integer> {

	Optional<GroupEntity> findByGroupCode(String groupCode);

	@Query("select distinct gr from GroupEntity gr " +
			"join fetch gr.company " +
			"join fetch gr.businessLineEntity bl " +
			"join fetch bl.salesChannel salesChannel " +
			"join fetch salesChannel.division " +
			"left join fetch gr.mailingAddressDetails address " +
			"left join fetch address.stateDetails " +
			"left join fetch address.countryDetails " +
			"left join fetch gr.contacts " +
			"left join fetch gr.agent " +
			"left join fetch gr.brokerAgent " +
			"left join fetch gr.grpPrdList groupProducts " +
			"left join fetch groupProducts.product product " +
			"left join fetch groupProducts.feeDetails " +
			"left join fetch groupProducts.frequencyDetails " +
			"left join fetch product.fees productFees " +
			"left join fetch productFees.frequencyDetails " +
			"left join fetch gr.userWhoModified " +
			"left join fetch gr.userWhoCreated " +
			"where gr.groupCode = :groupCode")
	GroupEntity findGroupWithDetailsByGroupCode(String groupCode);

	Page<GroupEntity.GroupSummary> findAllByOrderByGroupName(Pageable pageable);

	GroupEntity findByGroupNameAndActive(String groupName, boolean active);
	
	GroupEntity findByGroupNameLikeAndActive(String groupName, boolean active);
	
	GroupEntity findByGroupNameContains(String groupName);
	
	GroupEntity findByGroupNameStartsWithAndActive(String groupName, boolean active);
	
	boolean existsByGroupCode(String groupCode);

	boolean existsByGroupCodeAndActiveIsTrue(String groupCode);
	
	boolean existsByGroupCodeAndActive(String groupCode,boolean active);
	
	GroupEntity.GroupID findByGroupCodeOrderByGroupId(String groupCode);
	
	//did not work in batch -- ***
	<T> Optional<T> findByGroupCode(String groupCode,Class<T> type);
	
	Set<GroupCodeSmry> findAllByOrderByGroupCodeAsc();
	
	Set<GroupIDCodeSmry> findAllByGroupIdInOrderByGroupCodeAsc(Set<Integer> groupIdSet);
	
	Set<GroupEntity> findAllByGroupIdInOrderByGroupNameAsc(Set<Integer> groupIdSet);
	
	<T> Set<T> findAllByOrderByGroupCodeAsc(Class<T> type);

	@Query("select "+
			" gr.groupCode as groupCode " +
			",td.name as divisionName " +
			",sc.name as salesChannelName " +
			",td.id as divisionId " +
			",sc.id as salesChannelId " +
			"from GroupEntity gr " +
			"join GroupLineEntity as grl on gr.groupId = grl.groupLineEntityId.groupId " +
			"join BusinessLineEntity as bl on bl.id = grl.groupLineEntityId.lineId " +
			"join SalesChannel as sc on sc.id = bl.salesChannel.id " +
			"join Division as td on sc.division = td.id " +
			"where (coalesce(:divisionList, null) is null or td.name in (:divisionList)) " +
			"order by gr.groupCode")
	Set<GroupDivisionDTO> findAllByDivisionInOrderByGroupCodeAsc
			(List<String> divisionList);
	
	@Query("select "+
			" gr.groupCode as groupCode " +
			",td.name as divisionName " +
			",sc.name as salesChannelName " +
			",td.id as divisionId " +
			",sc.id as salesChannelId " +
			"from GroupEntity gr " +
			"join GroupLineEntity as grl on gr.groupId = grl.groupLineEntityId.groupId " +
			"join BusinessLineEntity as bl on bl.id = grl.groupLineEntityId.lineId " +
			"join SalesChannel as sc on sc.id = bl.salesChannel.id " +
			"join Division as td on sc.division = td.id " +
			"where gr.groupCode = (:groupCode) " )
	GroupDivisionDTO findGroupDivisionSalesChannelByGroupCode(String groupCode);
	
	Optional<GroupEntity> findByGroupCodeAndDownPaymentGrp(String groupCode, Boolean downPaymentGrp);
	Optional<GroupEntity> findByGroupCodeAndLifetimeGrp(String groupCode, Boolean lifetimeGrp);
	
	@Query(value = "select grp.pastDueEffectiveDate "
			+ "from GroupEntity grp "
			+ "join Member mem on grp.groupId = mem.group.groupId "
			+ "WHERE mem.memberId = (:memberId) ")
	Date findGroupPastDueEffectiveDateByMemberId(@Param("memberId") Integer memberId);


	@Query("select distinct " +
			"a.agentId as agentId, " +
			"a.agentFirst as agentFirst, " +
			"a.agentLast as agentLast, " +
			"a.agentNum as agentNum, " +
			"a.agentFullName as agentName " +
			"from GroupEntity as gr " +
			"join GroupProductFee as gpf on gr.groupId = gpf.group.groupId " +
			"join AgentCommission as ac on ac.productId = gpf.product.productId and gpf.feeDetails.feeId <> 1 " +
			"join Agent as a on a.agentId = ac.agent.agentId " +
			"left join AgentManager AS am ON am.agentId = a.agentId AND am.managerPosition = 1 " +
			"where gr.groupCode = :groupCode " +
			"and a.active = 1 " +
			"and a.isBroker = :isBroker " +
			"and (:searchString = '' or lower(a.agentFullName) like lower(concat('%', :searchString,'%'))) " +
			"order by a.agentFullName asc")
	Page<AgentReducedViewDTO> findReducedAgent(@Param("searchString") String searchString,
											   @Param("groupCode") String groupCode,
											   @Param("isBroker") boolean isBroker,
											   Pageable pageable);

	@Query("select case when count(gr) > 0 then true else false end " +
			"from GroupEntity as gr " +
			"join GroupProductFee as gpf on gr.groupId = gpf.group.groupId " +
			"join AgentCommission as ac on ac.productId = gpf.product.productId and gpf.feeDetails.feeId <> 1 " +
			"join Agent as a on a.agentId = ac.agent.agentId " +
			"where gr.groupCode = :groupCode and a.agentId = :agentId " +
			"and a.isBroker = :isBroker")
	boolean checkAgentIdExistenceByGroupCode(@Param("groupCode") String groupCode,
											 @Param("agentId") Integer agentId,
											 @Param("isBroker") boolean isBroker);

	@Query(" SELECT new com.masa.pts.core.model.ProductSale(TGF.product.productId, TP.type AS productType,TP.upgradeProduct AS upgradeProduct,TGF.feeDetails.feeId as feeId,"
			+ " TF2.name AS feeName,TGF.frequencyDetails.ID as frequency, TF.description AS freqDesc, TGF.amount,"
			+ " TGF.maxAmountDue, TGF.retailAmount,TPC.code AS categoryCode, TPC.name AS categoryName,"
			+ " TG.groupCode, coalesce(TGF2.amount, 0) AS setupFeeAmt, coalesce(TGF2.maxAmountDue, 0) AS setupFeeMaxAmtDue, TA.agentNum) "
			+ " FROM GroupProductFee TGF "
			+ " JOIN Product TP ON TGF.product.productId = TP.productId and TGF.feeDetails.feeId <> 1 "
			+ " JOIN ProductToProductCategoryEntity TPPC ON TPPC.productId = TGF.product.productId and TPPC.productId = TP.productId "
			+ " JOIN ProductCategoryEntity TPC ON TPC.id = TPPC.productCategoryId "
			+ " JOIN Frequency TF ON TF.ID = TGF.frequencyDetails.ID "
			+ " JOIN Fee TF2 ON TF2.feeId = TGF.feeDetails.feeId "
			+ " JOIN GroupEntity TG ON TG.groupId = TGF.group.groupId "
			+ " LEFT JOIN GroupProductFee TGF2 on TGF2.group.groupId = TGF.group.groupId and TGF.product.productId = TGF2.product.productId and TGF2.feeDetails.feeId = 1 "
			+ " LEFT JOIN Agent TA on TA.agentId = TG.agent.agentId "
			+ " WHERE TG.groupCode = :groupCode "
			+ " ORDER BY TPC.sequence , TGF.product.upgradeProduct ")
	List<ProductSale> getGroupProductsForExternal(String groupCode);

	@Query(value = "" +
			"select " +
			" new com.masa.pts.core.model.GroupSearchDTO( gr.groupId as groupId, " +
			"gr.groupCode as groupCode, " +
			"gr.groupName as groupName, " +			
			"gr.billType as billType, " +
			"gr.billCompany as billCompany, " +
			"gr.planType as planType, " +
			"gr.active as active, " +
			"pt.name as planTypeName ) " +
			"from GroupEntity gr " +
			"join PTSUserGroup as teg on teg.userGroupId.group.groupId = gr.groupId " +
			"left join PlanTypeEntity pt on pt.id = gr.planType " +
			"where teg.userGroupId.user.employeeId = :userId " +			
			"and (coalesce(:groupCodes, null) is null or gr.groupCode in (:groupCodes)) " +
			"and (:active is null or gr.active = :active) " +
			"and (:billCompany is null or gr.billCompany = :billCompany) " +
			"and (:planType is null or gr.planType = :planType) " +
			"and (:billType is null or gr.billType = :billType) " +
			"and (:groupNameOrCode is null or (lower(gr.groupCode) like lower(concat('%', :groupNameOrCode,'%')) " +
			"or lower(gr.groupName) like lower(concat('%', :groupNameOrCode,'%'))))")
	Page<GroupSearchDTO> searchGroupsByParamsExternalUser(
			@Param("groupNameOrCode") String groupNameOrCode,
			@Param("groupCodes") List<String> groupCodes,
			@Param("active") Boolean active,
			@Param("billCompany") Boolean billCompany,
			@Param("planType") Integer planType,
			@Param("billType") Integer billType,
			@Param("userId") Integer userId,
			Pageable pageable);
	
	@Query(value = "" +
			"select " +
			" new com.masa.pts.core.model.GroupSearchDTO( gr.groupId as groupId, " +
			"gr.groupCode as groupCode, " +
			"gr.groupName as groupName, " +
			"sc.id, " +
			"sc.name, " +
			"gr.billType as billType, " +
			"gr.billCompany as billCompany, " +
			"gr.planType as planType, " +
			"gr.active as active, " +
			"pt.name as planTypeName, di.name as division ) " +
			"from GroupEntity gr " +
			"join GroupLineEntity as grl on gr.groupId = grl.groupLineEntityId.groupId " +
			"join BusinessLineEntity as bl on bl.id = grl.groupLineEntityId.lineId " +
			"join SalesChannel as sc on sc.id = bl.salesChannel.id " +
			"join Division as di on di.id = sc.division.id " +
			"join PTSUserSalesChannel as tesc on tesc.userSalesChannelId.salesChannel.id = sc.id " +
			"left join PlanTypeEntity pt on pt.id = gr.planType " +
			"where tesc.userSalesChannelId.user.employeeId = :userId " +
			"and (coalesce(:salesChannelIds, null) is null or sc.id in (:salesChannelIds)) " +
			"and (coalesce(:groupCodes, null) is null or gr.groupCode in (:groupCodes)) " +
			"and (:divisionId is null or di.id = (:divisionId) ) " + 
			"and (:active is null or gr.active = :active) " +
			"and (:billCompany is null or gr.billCompany = :billCompany) " +
			"and (:planType is null or gr.planType = :planType) " +
			"and (:billType is null or gr.billType = :billType) " +
			"and (:groupNameOrCode is null or (lower(gr.groupCode) like lower(concat('%', :groupNameOrCode,'%')) " +
			"or lower(gr.groupName) like lower(concat('%', :groupNameOrCode,'%'))))")
	Page<GroupSearchDTO> searchGroupsByParamsInternalUser(
			@Param("groupNameOrCode") String groupNameOrCode,
			@Param("groupCodes") List<String> groupCodes,
			@Param("salesChannelIds") List<Integer> salesChannelIds,
			@Param("divisionId") Integer divisionId,
			@Param("active") Boolean active,
			@Param("billCompany") Boolean billCompany,
			@Param("planType") Integer planType,
			@Param("billType") Integer billType,
			@Param("userId") Integer userId,
			Pageable pageable);

	@Query("select distinct new com.masa.pts.core.model.GroupReducedView(gr.groupId, gr.groupCode, gr.groupName) " +
			"from GroupEntity gr " +
			"join PTSUserGroup as teg on teg.userGroupId.group.groupId = gr.groupId " +
			"where (teg.userGroupId.user.employeeId = :userId ) " +
			"and ((lower(coalesce(:groupCode, '')) = lower(gr.groupCode)) " +
			"or coalesce(:groupCode, '') = '' and (lower(gr.groupCode) like lower(concat('%', coalesce(:nameOrCode, ''),'%')) " +
			"or coalesce(:groupCode, '') = '' and lower(gr.groupName) like lower(concat('%', coalesce(:nameOrCode, ''),'%')))) ")
	Page<GroupReducedView> findReducedGroupsByCodeOrNameExternalUser(
			@Param("nameOrCode") String nameOrCode,
			@Param("groupCode") String groupCode,
			@Param("userId") Integer userId,
			Pageable pageable);
	
	@Query("select distinct new com.masa.pts.core.model.GroupReducedView(gr.groupId, gr.groupCode, gr.groupName) " +
			"from GroupEntity gr " +
			"join GroupLineEntity as grl on gr.groupId = grl.groupLineEntityId.groupId " +
			"join BusinessLineEntity as bl on bl.id = grl.groupLineEntityId.lineId " +
			"join SalesChannel as sc on sc.id = bl.salesChannel.id " +
			"join PTSUserSalesChannel as tesc on tesc.userSalesChannelId.salesChannel.id = sc.id " +
			"where (tesc.userSalesChannelId.user.employeeId = :userId) " +
			"and (coalesce(:salesChannelIds, null) is null or sc.id in :salesChannelIds) " +
			"and ((lower(coalesce(:groupCode, '')) = lower(gr.groupCode)) " +
			"or coalesce(:groupCode, '') = '' and (lower(gr.groupCode) like lower(concat('%', coalesce(:nameOrCode, ''),'%')) " +
			"or coalesce(:groupCode, '') = '' and lower(gr.groupName) like lower(concat('%', coalesce(:nameOrCode, ''),'%')))) ")
	Page<GroupReducedView> findReducedGroupsByCodeOrNameInternalUser(
			@Param("nameOrCode") String nameOrCode,
			@Param("groupCode") String groupCode,
			@Param("salesChannelIds") List<Integer> salesChannelIds,
			@Param("userId") Integer userId,
			Pageable pageable);

	@Query("select new com.masa.pts.core.model.GroupReducedView(gr.groupId, gr.groupCode, gr.groupName) from GroupEntity gr " +
			"join GroupLineEntity as grl on gr.groupId = grl.groupLineEntityId.groupId " +
			"join BusinessLineEntity as bl on bl.id = grl.groupLineEntityId.lineId " +
			"where bl.salesChannel.id in :salesChannelIds ")
	List<GroupReducedView> findBySalesChannels(@Param("salesChannelIds") List<Integer> salesChannelIds);

	@Query("SELECT CASE WHEN COUNT(TG.groupId) > 0 THEN 'true' ELSE 'false' end "
			+ " FROM GroupEntity TG "
			+ " JOIN GroupLineEntity TGL ON TG.groupId = TGL.groupLineEntityId.groupId"
			+ " JOIN BusinessLineEntity TBL ON TGL.groupLineEntityId.lineId = TBL.id "
			+ " JOIN PTSUserSalesChannel TESC ON TBL.salesChannel = TESC.userSalesChannelId.salesChannel.id"
			+ " WHERE TESC.userSalesChannelId.user.employeeId = (:employeeId)"
			+ " and TG.groupCode = (:groupCode) ")
	Boolean internalUserHasAccessToGroup(String groupCode,Integer employeeId);
	
	@Query(""
			+ "SELECT new com.masa.pts.core.model.DashboardGroupSummaryDTO (TG.groupId,TG.groupCode,"
			+ " TG.groupName ,TG.active, COUNT(mem.memberId) as memberCount ) "
			+ "FROM GroupEntity TG "
			+ "JOIN PTSUserGroup as TEG on TG.groupId = TEG.userGroupId.group.groupId "
			+ "LEFT JOIN Member mem ON TG.groupId = mem.group.groupId and mem.active=1 "
			+ "WHERE TEG.userGroupId.user.employeeId = (:employeeId)"
			+ "GROUP BY TG.groupId ,TG.groupCode, TG.active,TG.groupName ")
	Page<DashboardGroupSummaryDTO> getExternalUserDashboardGroupData(Integer employeeId,Pageable pageable);
	
	@Query(""
			+ "SELECT new com.masa.pts.core.model.DashboardGroupProductDTO (TG.groupId "
			+ " ,TG.groupName, TPC.name as productName, COUNT(distinct mem.memberId) as memberCount ) "
			+ "FROM GroupEntity TG "
			+ "JOIN PTSUserGroup as TEG on TG.groupId = TEG.userGroupId.group.groupId "
			+ "LEFT JOIN Member mem ON TG.groupId = mem.group.groupId and mem.active=1 "
			+ "LEFT JOIN MemberFee TMF on TMF.group.groupId=TG.groupId AND  TMF.memberId = mem.memberId and TMF.upgradeType=0 "
			+ "LEFT JOIN ProductToProductCategoryEntity TPPC ON TPPC.productId = TMF.product.productId "
			+ "LEFT JOIN ProductCategoryEntity TPC ON TPC.id = TPPC.productCategoryId "			
			+ "WHERE TEG.userGroupId.user.employeeId = (:employeeId) "
			+ "and TG.groupId in (:groupIds) "
			+ "and TMF.effectiveEndDate > CURRENT_DATE() "
            + "and TMF.effectiveStartDate <= CURRENT_DATE() "
			+ "GROUP BY TG.groupId, TG.groupName ,TPC.name ")
	List<DashboardGroupProductDTO> getExternalUserDashboardProductData(Integer employeeId,List<Integer> groupIds);
}
