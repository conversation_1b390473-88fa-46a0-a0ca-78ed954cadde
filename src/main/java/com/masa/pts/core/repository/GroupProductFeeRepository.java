package com.masa.pts.core.repository;

import java.util.List;
import java.util.Set;

import org.springframework.data.repository.CrudRepository;

import com.masa.pts.core.domain.GroupProductFee;

public interface GroupProductFeeRepository extends CrudRepository<GroupProductFee, Integer> {
	Set<GroupProductFee> findAllByGroupGroupCodeAndProductProductId(String groupCode,Integer productId);
	Set<GroupProductFee> findAllByGroupGroupIdAndProductProductId(Integer groupId,Integer productId);
	Set<GroupProductFee> findAllByGroupGroupCode(String groupCode);
	Set<GroupProductFee> findAllByGroupGroupCodeAndProductProductIdIn(String groupCode,List<Integer> productIdList);
}
