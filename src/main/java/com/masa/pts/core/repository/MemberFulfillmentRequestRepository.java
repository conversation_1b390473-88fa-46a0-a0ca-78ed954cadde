package com.masa.pts.core.repository;

import com.masa.pts.core.model.MemberFulfillmentDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;

import com.masa.pts.core.domain.MemberFulfillmentRequest;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface MemberFulfillmentRequestRepository extends PagingAndSortingRepository<MemberFulfillmentRequest, Integer> {

	@Query("SELECT new com.masa.pts.core.model.MemberFulfillmentDto(mf.id, mf.fulfillmentType, mf.requestedDate, " +
			"mf.processedDate, mf.uid, mf.pkgCnt, mf.shippingAddress1, mf.shippingAddress2, mf.shippingAddress3," +
			"mf.shippingCity, mf.shippingState, mf.shippingCountry, mf.shippingZip, mf.shippingZip4, p.name, p.type, " +
			"empMod.username, sm.name, p.productId, p.upgradeProduct ) " +
			"FROM MemberFulfillmentRequest mf " +
			"JOIN Product p ON p.productId = mf.productId " +
			"LEFT JOIN PTSUser AS empMod ON mf.createdBy = empMod.employeeId " +
			"LEFT JOIN ShippingMethod AS sm ON sm.id = mf.shippingMethod " +
			"WHERE mf.memberId = (:memberId) " +
			"AND mf.isDelete = false")
	Page<MemberFulfillmentDto> getFulfillmentByMemberId(@Param("memberId") Integer memberId, Pageable pageable);

	@Query("SELECT new com.masa.pts.core.model.MemberFulfillmentDto(mf.id, mf.fulfillmentType, mf.requestedDate, " +
			"mf.processedDate, mf.uid, mf.pkgCnt, mf.shippingAddress1, mf.shippingAddress2, mf.shippingAddress3," +
			"mf.shippingCity, mf.shippingState, mf.shippingCountry, mf.shippingZip, mf.shippingZip4, p.name, p.type, " +
			"empMod.username, sm.name, p.productId, p.upgradeProduct ) " +
			"FROM MemberFulfillmentRequest mf " +
			"JOIN Product p ON p.productId = mf.productId " +
			"LEFT JOIN PTSUser AS empMod ON mf.createdBy = empMod.employeeId " +
			"LEFT JOIN ShippingMethod AS sm ON sm.id = mf.shippingMethod " +
			"WHERE mf.id = :fulfillmentId")
	MemberFulfillmentDto getFulfillmentById(@Param("fulfillmentId") Integer fulfillmentId);

	Optional<MemberFulfillmentRequest> findByIdAndIsDeleteIsFalse(Integer id);

	@Query(value="SELECT CASE WHEN COUNT(mf) > 0 THEN 'true' ELSE 'false' END " +
			"FROM MemberFulfillmentRequest AS mf " +
			"WHERE mf.memberId = :memberId " +
			"AND mf.productId = :productId " +
			"AND mf.processedDate = '1900-01-01 00:00:00.000' " +
			"AND mf.isDelete = false")
	Boolean isFulfillmentPending(@Param("memberId") Integer memberId, @Param("productId") Integer productId);
}
