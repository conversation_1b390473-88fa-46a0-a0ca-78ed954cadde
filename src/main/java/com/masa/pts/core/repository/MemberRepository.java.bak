package com.masa.pts.core.repository;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.PagingAndSortingRepository;

import com.masa.pts.core.domain.Member;

public interface MemberRepository extends PagingAndSortingRepository<Member, Integer> {

	List<Member> findByMemberId(Integer memberId);
	
	//List<Member> findAllByOrderByMemberIdDesc(Pageable pageable);
	
	List<Member> findAllByActiveOrderByMemberIdDesc(int active,Pageable pageable);	
	
	Page<Member.MemberSummary> findAllByOrderByMemberIdDesc(Pageable pageable);
	
	List<Member> findAllByEmployeeIdAndGroupGroupCode(String employeeId,String groupCode);	
		
}
