package com.masa.pts.core.repository;

import java.util.Date;
import java.util.List;
import java.util.Set;

import javax.persistence.TemporalType;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.Temporal;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;

import com.masa.pts.core.domain.MemberFee;

public interface MemberFeeRepository extends CrudRepository<MemberFee, Integer> {

	Set<MemberFee> findAllByMemberIdAndProductProductIdGreaterThanOrderByProduct(Integer memberId,Integer productId);
	
	Set<MemberFee> findAllByMemberIdAndProductProductId(Integer memberId,Integer productId);
	
	Set<MemberFee> findAllByMemberId(Integer memberId);
	
	@Query(value= " SELECT TMF FROM MemberFee TMF  "
					+ " WHERE :effectiveDate BETWEEN TMF.effectiveStartDate AND TMF.effectiveEndDate " 
					+ "	AND TMF.memberId = :memberId ")
	Set<MemberFee> findAllActiveProductsByMemberId(@Param("memberId") Integer memberId,@Param("effectiveDate") @Temporal(TemporalType.DATE) Date effectiveDate);
	
	@Query(value= " SELECT TG.groupCode FROM MemberFee TMF  "
			+ "JOIN GroupEntity TG ON TMF.group.groupId = TG.groupId"
			+ " WHERE :effectiveDate BETWEEN TMF.effectiveStartDate AND TMF.effectiveEndDate "
			+ " and TMF.feeDetails.feeId <> 1 and TMF.upgradeType =0 " 
			+ "	AND TMF.memberId = :memberId ")
	String findMemberActiveGroup(@Param("memberId") Integer memberId,@Param("effectiveDate") @Temporal(TemporalType.DATE) Date effectiveDate);

	/**
	 * Gets primary products (upgradeType = 0) for given member ids
	 */
	@Query("SELECT f FROM MemberFee f " +
			"JOIN FETCH f.product " +
			"JOIN FETCH f.feeDetails " +
			"JOIN FETCH f.frequencyDetails " +
			"JOIN FETCH f.memberEmployeeId " +
			"WHERE f.memberId IN :memberIds AND f.id <> 1 AND f.upgradeType = 0 " +
			// todo group id is not null is hotfix because DB data is inconsistent. see details COMPASS-596
			"AND f.group.groupId <> 0")
	Set<MemberFee> findAllByMemberIds(@Param("memberIds") List<Integer> memberIds);
}
