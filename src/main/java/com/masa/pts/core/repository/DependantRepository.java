package com.masa.pts.core.repository;

import java.util.Optional;
import java.util.Set;

import org.springframework.data.repository.CrudRepository;

import com.masa.pts.core.domain.Dependant;

public interface DependantRepository extends CrudRepository<Dependant, Integer> {

	Set<Dependant> findAllByMemberMemberId(Integer memberId);
	
	boolean existsByDependantIdAndMemberMemberId(Integer dependantId,Integer memberId);
	
	Optional<Dependant> findByDependantIdAndMemberMemberId(Integer dependantId,Integer memberId);
}
