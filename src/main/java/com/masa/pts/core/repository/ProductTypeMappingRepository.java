package com.masa.pts.core.repository;

import java.util.Date;
import java.util.List;

import javax.persistence.TemporalType;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.Temporal;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;

import com.masa.pts.core.domain.ProductTypeMapping;

public interface ProductTypeMappingRepository extends CrudRepository<ProductTypeMapping, Integer> {

	@Query(nativeQuery = true, value = "select efpm.* from dbo.EDI_FILE_PRODUCT_MAPPING efpm WHERE efpm.product_type = :productType and efpm.payment_type = :paymentType and :soldDate between"
			+ " efpm.sold_date_start and efpm.sold_date_end")
	ProductTypeMapping findByProductTypeAndPaymentTypeAndSoldDate(@Param("productType")String productType,@Param("paymentType")String paymentType, 
			@Temporal(TemporalType.DATE) @Param("soldDate") Date soldDate);
	
	@Query(nativeQuery = true, value = "SELECT EFPM.* FROM dbo.EDI_FILE_PRODUCT_MAPPING AS EFPM " + 
			"JOIN dbo.tlnk_Group_Fee AS TGF ON TGF.product_id = EFPM.product_id  AND TGF.fee_id <> 1 " + 
			"JOIN dbo.tdat_Group AS TG ON TG.group_id = TGF.group_id " + 
			"WHERE EFPM.product_type= :productType AND EFPM.payment_type = :paymentType " + 
			"AND :soldDate BETWEEN EFPM.sold_date_start AND EFPM.sold_date_end " + 
			"AND TG.group_code = :groupCode " + 
			"ORDER BY EFPM.single_family DESC")
	List<ProductTypeMapping> findByProductTypeAndPaymentTypeAndSoldDateAndGroup(@Param("productType") String productType,@Param("paymentType") String paymentType, 
			@Temporal(TemporalType.DATE) @Param("soldDate") Date soldDate, @Param("groupCode") String groupCode);
	
	@Query(value=" SELECT CASE WHEN COUNT(EFPM) > 0 THEN true ELSE false END "
			+ "FROM ProductTypeMapping EFPM "
			+ "JOIN GroupProductFee TGF ON EFPM.productId = TGF.product.productId "
			+ "JOIN GroupEntity TG ON TG.groupId = TGF.group.groupId "
			+ "WHERE TG.groupCode = (:groupCode) and EFPM.prdFamilyType = 1 ")
	boolean groupHasSingleProduct(@Param("groupCode") String groupCode);
}
