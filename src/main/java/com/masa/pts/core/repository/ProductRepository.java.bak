package com.masa.pts.core.repository;

import java.util.List;

import org.springframework.data.repository.CrudRepository;

import com.masa.pts.core.domain.Product;

public interface ProductRepository extends CrudRepository<Product, Integer> {
	List<Product.ProductSummary> findAllByCompanyIdOrderByNameAsc(Integer companyId);
	Product findByNameEquals(String name);
	//Integer findProductIdByNameEquals(String name);
}
