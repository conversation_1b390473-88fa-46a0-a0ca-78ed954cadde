package com.masa.pts.core.repository;

import java.util.Optional;
import java.util.Set;

import org.springframework.data.repository.CrudRepository;

import com.masa.pts.core.domain.PaymentDetailForteTransaction;

public interface PaymentForteTransactionRepository extends CrudRepository<PaymentDetailForteTransaction, Integer> {

	//member with multiple products have only payment/settlement , so transaction can have multiple payment details.
	//each for one product.
	Set<PaymentDetailForteTransaction> findByTransactionId(String transactionId);
	
	Optional<PaymentDetailForteTransaction> findByPaymentDetailIdAndTransactionIdAndMemberId(Integer paymentDetailId,String trasnactionId,Integer memberId);
}
