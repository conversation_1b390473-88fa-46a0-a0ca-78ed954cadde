package com.masa.pts.core.repository;

import java.util.Set;

import org.springframework.data.repository.CrudRepository;

import com.masa.pts.core.domain.Commission;

public interface CommissionRepository extends CrudRepository<Commission, Integer> {
	
	Set<Commission> findAllByCommDetailsMemberIdAndCommDetailsProductId(Integer memberId,Integer productId);
	
	Set<Commission> findAllByCommDetailsPayDetailIdAndCommDetailsAdvanceAndCommDetailsChargeback(Integer payDetailId,Integer advance,Boolean chargeback);	
}
