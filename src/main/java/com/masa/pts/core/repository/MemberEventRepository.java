package com.masa.pts.core.repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import org.springframework.data.repository.CrudRepository;

import com.masa.pts.core.domain.MemberEvent;

public interface MemberEventRepository extends CrudRepository<MemberEvent, Integer> {

	Optional<MemberEvent> findByMemberIdAndEventTypeAndStatusIn(Integer memberId,Integer eventType,List<Integer> statuses);
	
	Set<MemberEvent> findByMemberIdAndStatusIn(Integer memberId,List<Integer> statuses);
}
