package com.masa.pts.core.repository;

import java.util.List;

import org.springframework.data.repository.CrudRepository;

import com.masa.pts.core.domain.State;

public interface StateRepository extends CrudRepository<State, Integer> {
	boolean existsBySymbol(String symbol);
	boolean existsBySymbolAndCountryId(String symbol,Integer countryId);
	State findBySymbolAndCountryId(String symbol,Integer countryId);	
	Iterable<State> findAllByCountryId(Integer countryId);
	Iterable<State> findAllByCountryIdIn(List<Integer> countryIdList);
}
