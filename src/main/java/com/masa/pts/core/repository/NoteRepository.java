package com.masa.pts.core.repository;

import com.masa.pts.core.domain.Note;
import com.masa.pts.core.model.NoteDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.Optional;

public interface NoteRepository extends PagingAndSortingRepository<Note, Integer> {

	@Query("SELECT new com.masa.pts.core.model.NoteDto(" +
			"n.noteId, n.note, n.noteDate, u.username, u.firstName, u.lastName) " +
			 "FROM Note n " +
			"LEFT JOIN PTSUser AS u ON n.enteredBy = u.employeeId " +
			"WHERE n.member.memberId = :memberId " +
			"AND n.type = com.masa.pts.core.domain.Note.NOTE_TYPE_MEMBER " +
			"AND n.isDelete = false")
	Page<NoteDto> findMemberNotes(Integer memberId, Pageable pageable);

	Optional<Note> findByNoteIdAndIsDeleteIsFalse(Integer id);
}
