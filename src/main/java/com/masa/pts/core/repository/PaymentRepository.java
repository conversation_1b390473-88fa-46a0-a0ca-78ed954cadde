/**
 * 
 */
package com.masa.pts.core.repository;

import com.masa.pts.core.domain.Payment;
import com.masa.pts.core.model.PaymentDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.Temporal;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;

import javax.persistence.TemporalType;
import java.util.Date;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 *
 */
public interface PaymentRepository extends CrudRepository<Payment, Integer> {
	Set<Payment> findAllByPaymentDetailMemberIdAndPaymentDetailProductId(Integer memberId,Integer productId);
	//does not work on sql 2005-> findTop1ByPaymentDetailMemberIdOrderByPaymentDetailId 	
	@Query(nativeQuery = true, value = "select top 1 pay.* from dbo.tdat_payment pay join dbo.tdat_payment_detail detail on pay.pay_id = detail.pay_id WHERE detail.member_id = :memberId and detail.is_void <> 1 order by detail.id")
	Optional<Payment> findFirstByPaymentDetailMemberIdOrderByPaymentDetailId(@Param("memberId") Integer memberId);
	Stream<Payment> readAllByPayId(Integer payId);
	Stream<Payment> readAllByPayIdGreaterThan(Integer payId);
	
	Set<Payment> findAllByPayMasaAndPaymentDetailMemberIdAndPaymentDetailProductIdAndPaymentDetailVoidPayment(Boolean payMasa,Integer memberId,Integer productId,Boolean voidPayment);
	
	Set<Payment> findAllByPayMasaAndPaymentDetailMemberIdAndPaymentDetailVoidPayment(Boolean payMasa,Integer memberId,Boolean voidPayment);
	
	Set<Payment> findAllByPayDateBetweenAndPaymentDetailMemberIdAndPaymentDetailVoidPaymentOrderByPayDate(@Temporal(TemporalType.DATE) Date startTransactionDate, @Temporal(TemporalType.DATE) Date endTransactionDate,Integer memberId,Boolean voidPayment);
	
	@Query(value="SELECT  CASE WHEN COUNT(*) > 0 THEN 'true' ELSE 'false' END " + 
			"FROM GroupEntity AS TG " + 
			"JOIN Invoice AS TI ON TG.groupId = TI.group.groupId " + 
			"JOIN Payment AS TP ON TI.payment.payId = TP.payId " + 
			"JOIN PaymentDetail AS TPD ON TPD.payment.payId = TP.payId " + 
			"JOIN MemberFee AS TMF ON TMF.memberId = TPD.memberId AND TMF.group.groupId = TG.groupId " + 
			"AND TP.payInvoiceDate BETWEEN TMF.effectiveStartDate AND TMF.effectiveEndDate " + 
			"AND TG.groupId = TMF.group.groupId " + 
			"WHERE TPD.voidPayment = 0 " + 
			"AND TPD.isChargeBack = 0 " + 
			"AND TP.payMasa = 0 " + 
			"AND TPD.memberId = (:memberId) " + 
			"AND TMF.id = (:productFeeId) " )
	Boolean isPaymentExistsForMemberProductFeeId(@Param("memberId") Integer memberId, @Param("productFeeId") Integer productFeeId);

	@Query(value = "SELECT DISTINCT new com.masa.pts.core.model.PaymentDTO(" +
			"                TPD.id," +
			"                TP.payDate," +
			"                TP.createDate," +
			"                TI.invoiceDate," +
			"                TID.invoiceDate," +
			"                TP2.name," +
			"                TPD.amountPaid," +
			"                TE.username," +
			"                TPD.voidPayment," +
			"                TPD.isChargeBack," +
			"                TP.payMasa," +
			"                TPD.isNew)" +
			" FROM PaymentDetail as TPD" +
			"         INNER JOIN Payment AS TP ON TP.payId = TPD.payment.payId" +
			"         LEFT JOIN Invoice AS TI ON TI.payment.payId = TP.payId" +
			"         LEFT JOIN InvoiceDetail AS TID ON TID.id = TI.invoiceId AND TID.member.memberId = TPD.memberId" +
			"         LEFT JOIN Product AS TP2 ON TP2.productId = TPD.productId" +
			"         LEFT JOIN PTSUser AS TE ON TPD.modifiedBy = TE.employeeId" +
			"         LEFT JOIN GroupEntity AS TG ON TG.groupId = TI.group.groupId" +
			" WHERE TP.cancel = false" +
			"  AND TPD.memberId = :memberId" +
			"  AND (:from IS NULL OR TP.payDate >= :from)" +
			"  AND (:to IS NULL OR TP.payDate <= :to)" +
			"  AND TPD.isCommission <> 10")
	Page<PaymentDTO> findByParams(@Param("memberId") Integer memberId,
								  @Param("from") Date from,
								  @Param("to") Date to,
								  Pageable pageable);
}
