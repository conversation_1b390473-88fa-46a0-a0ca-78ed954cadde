package com.masa.pts.core.repository;

import java.util.Set;

import org.springframework.data.repository.CrudRepository;

import com.masa.pts.core.domain.MemberHistory;

public interface MemberHistoryRepository extends CrudRepository<MemberHistory, Integer> {

	java.util.Optional<MemberHistory> findByMemberIdAndEventType(Integer memberId,Integer eventType);
	
	Set<MemberHistory> findByMemberId(Integer memberId);
}
