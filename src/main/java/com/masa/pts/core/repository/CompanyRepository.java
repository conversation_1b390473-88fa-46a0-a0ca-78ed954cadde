package com.masa.pts.core.repository;

import java.util.List;

import org.springframework.data.repository.PagingAndSortingRepository;

import com.masa.pts.core.domain.Company;
import com.masa.pts.core.domain.Company.CompanyInfo;

public interface CompanyRepository extends PagingAndSortingRepository<Company, Integer> {

	List<Company> findByCompanyId(Integer companyid);
	
	List<CompanyInfo> findByActive(Boolean active);
}
