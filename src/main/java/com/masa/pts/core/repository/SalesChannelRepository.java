package com.masa.pts.core.repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import com.masa.pts.core.model.SalesChannelSearchDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import com.masa.pts.core.domain.SalesChannel;

public interface SalesChannelRepository extends PagingAndSortingRepository<SalesChannel, Integer> {

	Set<SalesChannel> findAll();

	@Query(value = "SELECT new com.masa.pts.core.model.SalesChannelSearchDto(" +
			"sc.id, sc.name, d.name, fm.merchantId, sc.isObsolete) " +
			"FROM SalesChannel sc " +
			"JOIN sc.division d " +
			"left JOIN sc.forteMerchant fm")
	Page<SalesChannelSearchDto> getSalesChannelPage(Pageable page);

	@Query(value = "SELECT sc from SalesChannel as sc " +
			"JOIN FETCH sc.division d " +
			"LEFT JOIN FETCH sc.forteMerchant " +
			"WHERE sc.id = :id")
	Optional<SalesChannel> getSalesChannelOptional(@Param("id") Integer id);

	Set<SalesChannel> findAllByDivisionName(String divisionName);
	
	Set<SalesChannel> findAllByDivisionId(Integer divisionId);

	boolean existsByName(String name);

	Set<SalesChannel> findAllByDivisionIdAndIsObsolete(Integer divisionId, Boolean isObsolete);

	Set<SalesChannel> findAllByIsObsolete(Boolean isObsolete);

	@Query(value = "SELECT new com.masa.pts.core.domain.SalesChannel(TSC.id, TSC.name) " +
					"FROM PTSUserSalesChannel AS TESC " +
					"JOIN SalesChannel AS TSC ON TSC.id = TESC.userSalesChannelId.salesChannel.id " +
					"WHERE TESC.userSalesChannelId.user.employeeId = (:userId) "+
					"GROUP BY TSC.id, TSC.name " +
					"ORDER BY TSC.name")
	List<SalesChannel> findSalesChannelByUser(@Param("userId") Integer userId);
	
	@Query("select sc.name from SalesChannel sc where sc.id = :salesChannelId")
	String findSalesChannelName(@Param("salesChannelId") Integer salesChannelId);
}
