package com.masa.pts.core.repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import javax.persistence.TemporalType;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Temporal;

import com.masa.pts.core.domain.GroupDocument;

public interface GroupDocumentRepository extends JpaRepository<GroupDocument, Integer> {

	Optional<GroupDocument> findByDocumentIdAndGroupGroupCode(String documentId,String groupCode);
	
	Set<GroupDocument> findAllByDocumentTypeAndGroupGroupCodeAndIsDeleteOrderByModifiedDate(String documentType,String groupCode,Boolean isDelete);
	
	Set<GroupDocument> findAllByDocumentTypeAndGroupGroupCodeInAndIsDeleteOrderByModifiedDate(String documentType,Set<String> groupCodes,Boolean isDelete);
	
	List<GroupDocument> findAllByDocumentTypeAndGroupGroupIdAndDocumentInvDepositDateAndIsDeleteOrderByModifiedDate(String documentType,Integer groupId,@Temporal(TemporalType.DATE) Date invoiceDate,Boolean isDelete);
	
	Page<GroupDocument> findByGroupGroupCodeAndIsDeleteIsFalse(String groupCode, Pageable pageable);

	Optional<GroupDocument> findByIdAndIsDeleteIsFalse(Integer documentId);
}
