package com.masa.pts.core.repository;

import com.masa.pts.core.domain.MemberDocument;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.CrudRepository;

import java.util.Optional;

public interface MemberDocumentRepository extends CrudRepository<MemberDocument, Integer> {

	Optional<MemberDocument> findByDocumentNameAndMemberMemberId(String documentName,Integer memberId);
	
	Optional<MemberDocument> findByDocumentIdAndMemberMemberId(String documentId,Integer memberId);
	
	Page<MemberDocument> findByMemberMemberIdAndIsDeleteIsFalse(Integer memberId, Pageable pageable);
	
	Optional<MemberDocument> findByIdAndIsDeleteIsFalse(Integer id);
}
