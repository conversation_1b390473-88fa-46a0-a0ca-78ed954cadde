package com.masa.pts.core.repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import com.masa.pts.core.domain.MemberEmployeeIdEntity;

public interface MemberEmployeeIdRepository extends CrudRepository<MemberEmployeeIdEntity, Integer> {

	Set<MemberEmployeeIdEntity> findByEmployeeIdAndGroupGroupId(String employeeId,Integer groupId);
	
	Set<MemberEmployeeIdEntity> findByEmployeeIdAndGroupGroupCode(String employeeId,String groupCode);
	
	@Query(nativeQuery = true, value = "SELECT TM.member_id FROM dbo.tdat_Member_EmployeeId AS TMEI JOIN dbo.tdat_Member AS TM ON TM.member_id = TMEI.member_id "
			+ "JOIN dbo.tdat_Group AS TG ON TG.group_id = TMEI.group_id "
			+ "WHERE TMEI.employee_id = :employeeId AND TG.group_code = :groupCode AND GETDATE() BETWEEN TMEI.effective_start_date AND TMEI.effective_end_date "
			+ "ORDER BY TM.member_id DESC ")
	Set<Integer> findActiveMemberIdByEmployeeIdAndGroupCodeOrderByMemberId(String employeeId,String groupCode);
	
	@Query(value = "SELECT DISTINCT TM.memberId "
			+ "FROM MemberEmployeeIdEntity AS TMEI "
			+ "JOIN Member AS TM ON TM.memberId = TMEI.member.memberId "
			+ "JOIN GroupEntity AS TG ON TG.groupId = TMEI.group.groupId "
			+ "WHERE TMEI.employeeId = (:employeeId) "
			+ "AND TG.groupCode = (:groupCode) "
			+ "AND (:activeDate) BETWEEN TMEI.effectiveStartDate AND TMEI.effectiveEndDate "
			)
	List<Integer> findMemberIdsByEmployeeIdAndGroupCodeActiveOnDate(String employeeId,String groupCode,
			LocalDate activeDate);
	
}
