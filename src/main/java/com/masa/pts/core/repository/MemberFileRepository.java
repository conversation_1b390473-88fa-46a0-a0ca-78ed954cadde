package com.masa.pts.core.repository;

import com.masa.pts.core.domain.MemberFile;
import com.masa.pts.core.domain.ReviewStatus;
import com.masa.pts.core.domain.UploadStatus;
import com.masa.pts.core.model.MemberFileDTO;
import com.masa.pts.core.model.MemberFileIdStatusDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import java.util.Date;
import java.util.List;
import java.util.Set;

public interface MemberFileRepository extends PagingAndSortingRepository<MemberFile, Integer> {

	Set<MemberFile> findAllByRelationshipAndSourceFileNameAndJobExecutionIdAndStatus(String relationShip,String sourceFileName,Long jobExecutionId,UploadStatus status);
	
	Set<MemberFile> findAllByGroupCodeAndContractNumberAndRelationshipNotInAndJobExecutionIdAndProcessed(
			String groupCode, String contractNumber, String relationship, Long jobExecutionId,boolean processed);
	
	Set<MemberFile> findAllByGroupCodeAndContractNumberAndRelationshipNotInAndJobExecutionId(
			String groupCode, String contractNumber, String relationship, Long jobExecutionId);
	
	List<MemberFile> findAllBySourceFileNameAndJobExecutionId(String sourceFileName,Long jobExecutionId);
	
	@Query(value = "SELECT new com.masa.pts.core.model.MemberFileDTO ("
			+ " MF.id "
			+ ", MF.groupCode "
			+ ", MF.sourceFileName  "
			+ ", MF.relationship  "
			+ ", MF.createdDate  "
			+ ", MF.masaMemberId  "
			+ ", MF.contractNumber  "
			+ ", MF.status  "
			+ ", MF.reviewStatus  "
			+ ", MF.statusUpdatedBy  "
			+ ", MFREE.errorCode "
			+ ", MFREE.errorMessage  "
			+ ", MF.modification  "
			+ ", MF.memberAction  "
			+ ", MF.firstName "
			+ ", MF.middleName  "
			+ ", MF.lastName "
			+ ", MF.productType  "
			+ ", MF.agentCode  "
			+ ", MF.effectiveDate "
			+ ", MF.processComments "
			+ ", MF.birthDate "
			+ ", MF.fileOwner "
			+ ") "
			+ " FROM MemberFile MF "
			+ " LEFT JOIN MemberFileRecordErrorEntity MFREE ON MF.id = MFREE.memberFile.id "
			+ " WHERE (:groupCode is null or MF.groupCode = :groupCode) "			
			+ " AND (:masaMemberId is null or MF.masaMemberId = :masaMemberId) "
			+ " AND (coalesce(:status,null) is null or MF.status in (:status) ) "
			+ " AND (:sourceFileName is null or MF.sourceFileName LIKE CONCAT('%',:sourceFileName,'%') ) "
			+ " AND (:relationship is null or MF.relationship = :relationship) "
			+ " AND (:contractNumber is null or MF.contractNumber = :contractNumber ) "
			+ " AND (coalesce(:reviewStatus,null) is null or MF.reviewStatus in (:reviewStatus) ) "
			+ " AND (:createdDateStart is null or :createdDateEnd IS null OR  "
			+ " MF.createdDate BETWEEN :createdDateStart AND :createdDateEnd) "
			+ " AND (:fulfillmentPending is null OR MF.fulfillmentPending = :fulfillmentPending)")
	Page<MemberFileDTO> searchMemberFileByParams(
			@Param("groupCode") String groupCode,
			@Param("masaMemberId") Integer masaMemberId,
			@Param("status") List<UploadStatus> status,
			@Param("sourceFileName") String sourceFileName,
			@Param("relationship") String relationship,
			@Param("contractNumber") String contractNumber,
			@Param("reviewStatus") List<ReviewStatus> reviewStatus,
			@Param("createdDateStart") Date createdDateStart,
			@Param("createdDateEnd")  Date createdDateEnd,
			@Param("fulfillmentPending") Boolean fulfillmentPending,
			Pageable pageable);
	
	@Query(value = "SELECT new com.masa.pts.core.model.MemberFileDTO ("
			+ " MF.id "
			+ ", MF.groupCode "
			+ ", MF.sourceFileName  "
			+ ", MF.relationship  "
			+ ", MF.createdDate  "
			+ ", MF.masaMemberId  "
			+ ", MF.contractNumber  "
			+ ", MF.status  "
			+ ", MF.reviewStatus  "
			+ ", MF.statusUpdatedBy  "
			+ ", MFREE.errorCode "
			+ ", MFREE.errorMessage  "
			+ ", MF.modification  "
			+ ", MF.memberAction  "
			+ ", MF.firstName "
			+ ", MF.middleName  "
			+ ", MF.lastName "
			+ ", MF.productType  "
			+ ", MF.agentCode  "
			+ ", MF.effectiveDate "
			+ ", MF.processComments "
			+ ", MF.birthDate "
			+ ", MF.fileOwner "
			+ ") "
			+ " FROM MemberFile MF "
			+ " INNER JOIN MemberFileRecordErrorEntity MFREE ON MF.id = MFREE.memberFile.id "
			+ " WHERE (:groupCode is null or MF.groupCode = :groupCode) "			
			+ " AND (:masaMemberId is null or MF.masaMemberId = :masaMemberId) "
			+ " AND (coalesce(:status,null) is null or MF.status in (:status) ) "
			+ " AND (:sourceFileName is null or MF.sourceFileName LIKE CONCAT('%',:sourceFileName,'%') ) "
			+ " AND (:relationship is null or MF.relationship = :relationship) "
			+ " AND (:contractNumber is null or MF.contractNumber = :contractNumber ) "
			+ " AND (coalesce(:reviewStatus,null) is null or MF.reviewStatus in (:reviewStatus) ) "
			+ " AND (:createdDateStart is null or :createdDateEnd IS null OR  "
			+ " MF.createdDate BETWEEN :createdDateStart AND :createdDateEnd) "
			+ " AND (coalesce(:errorCode,null) is null or MFREE.errorCode in (:errorCode))"
			+ " AND (:fulfillmentPending is null OR MF.fulfillmentPending = :fulfillmentPending)")
	Page<MemberFileDTO> searchMemberFileByParamsWithErrorCodes(
			@Param("groupCode") String groupCode,
			@Param("masaMemberId") Integer masaMemberId,
			@Param("status") List<UploadStatus> status,
			@Param("sourceFileName") String sourceFileName,
			@Param("relationship") String relationship,
			@Param("contractNumber") String contractNumber,
			@Param("errorCode") List<String> errorCode,
			@Param("reviewStatus") List<ReviewStatus> reviewStatus,
			@Param("createdDateStart") Date createdDateStart,
			@Param("createdDateEnd")  Date createdDateEnd,
			@Param("fulfillmentPending") Boolean fulfillmentPending,
			Pageable pageable);	

	// where clause should be exactly the same as for MemberFileRepository#searchMemberFileIdStatusDto()
	// because both queries use for one bulk update
	@Query(value = "SELECT new com.masa.pts.core.model.MemberFileIdStatusDTO(MF.id, MF.reviewStatus) "
			+ " FROM MemberFile MF"
			+ " WHERE (:groupCode is null or MF.groupCode = :groupCode) "
			+ " AND (:masaMemberId is null or MF.masaMemberId = :masaMemberId) "
			+ " AND (coalesce(:status,null) is null or MF.status in (:status) ) "
			+ " AND (:sourceFileName is null or MF.sourceFileName LIKE CONCAT('%',:sourceFileName,'%') ) "
			+ " AND (:relationship is null or MF.relationship = :relationship) "
			+ " AND (:contractNumber is null or MF.contractNumber = :contractNumber ) "
			+ " AND (coalesce(:reviewStatus,null) is null or MF.reviewStatus in (:reviewStatus) ) "
			+ " AND (:createdDateStart is null or :createdDateEnd IS null OR  "
			+ " MF.createdDate BETWEEN :createdDateStart AND :createdDateEnd) "
			+ " AND (:errorCode is null or MF.id in " +
			"(SELECT MF.id FROM MemberFile MF JOIN MF.recordErrorSet MFREE" +
			" WHERE (coalesce(:errorCode,null) is null or MFREE.errorCode in (:errorCode))))")
	List<MemberFileIdStatusDTO> searchMemberFileIdStatusDto(
			@Param("groupCode") String groupCode,
			@Param("masaMemberId") Integer masaMemberId,
			@Param("status") List<UploadStatus> status,
			@Param("sourceFileName") String sourceFileName,
			@Param("relationship") String relationship,
			@Param("contractNumber") String contractNumber,
			@Param("errorCode") List<String> errorCode,
			@Param("reviewStatus") List<ReviewStatus> reviewStatus,
			@Param("createdDateStart") Date createdDateStart,
			@Param("createdDateEnd")  Date createdDateEnd);

	// where clause should be exactly the same as for MemberFileRepository#searchMemberFileIdStatusDto()
	// because both queries use for one bulk update
	@Modifying(clearAutomatically = true)
	@Query("UPDATE MemberFile MF set MF.reviewStatus = :newReviewStatus, MF.statusUpdatedBy = :username"
			+ " WHERE (:groupCode is null or MF.groupCode = :groupCode) "
			+ " AND (:masaMemberId is null or MF.masaMemberId = :masaMemberId) "
			+ " AND (coalesce(:status,null) is null or MF.status in (:status) ) "
			+ " AND (:sourceFileName is null or MF.sourceFileName LIKE CONCAT('%',:sourceFileName,'%') ) "
			+ " AND (:relationship is null or MF.relationship = :relationship) "
			+ " AND (:contractNumber is null or MF.contractNumber = :contractNumber ) "
			+ " AND (coalesce(:reviewStatus,null) is null or MF.reviewStatus in (:reviewStatus) ) "
			+ " AND (:createdDateStart is null or :createdDateEnd IS null OR  "
			+ " MF.createdDate BETWEEN :createdDateStart AND :createdDateEnd) "
			+ " AND (:errorCode is null or MF.id in " +
			"(SELECT MF.id FROM MemberFile MF JOIN MF.recordErrorSet MFREE" +
			" WHERE (coalesce(:errorCode,null) is null or MFREE.errorCode in (:errorCode))))")
	int updateMemberFileWhere(@Param("newReviewStatus") ReviewStatus newReviewStatus,
							  @Param("username") String username,
							  @Param("groupCode") String groupCode,
							  @Param("masaMemberId") Integer masaMemberId,
							  @Param("status") List<UploadStatus> status,
							  @Param("sourceFileName") String sourceFileName,
							  @Param("relationship") String relationship,
							  @Param("contractNumber") String contractNumber,
							  @Param("errorCode") List<String> errorCode,
							  @Param("reviewStatus") List<ReviewStatus> reviewStatus,
							  @Param("createdDateStart") Date createdDateStart,
							  @Param("createdDateEnd") Date createdDateEnd);
	
	
	
}
