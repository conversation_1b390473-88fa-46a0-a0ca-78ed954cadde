package com.masa.pts.core.repository;

import java.util.Optional;
import java.util.Set;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;

import com.masa.pts.core.domain.PTSUser;
import com.masa.pts.core.model.PTSUserSearchDTO;

public interface PTSUserRepository extends CrudRepository<PTSUser, Integer> {
	
	@Query("SELECT DISTINCT TE "
			+ "FROM PTSUser TE "
			+ "LEFT JOIN FETCH TE.userGroups USERGRP "
			+ "LEFT JOIN FETCH USERGRP.userGroupId.group "
			+ "LEFT JOIN FETCH TE.userSalesChannels USERSC "
			+ "LEFT JOIN FETCH USERSC.userSalesChannelId.salesChannel SC "
			+ "LEFT JOIN FETCH SC.forteMerchant "
			+ "LEFT JOIN FETCH TE.userRoles USERROLE "
			+ "LEFT JOIN FETCH USERROLE.userRoleId.role "
			+ "LEFT JOIN FETCH SC.division "			
			+ "WHERE TE.username = (:username) ")
	Optional<PTSUser> findByUsername(String username);
		
	Optional<PTSUser> findByUsernameAndFirstNameAndLastNameAndActiveAndUserType(String userName,String firstName,String lastName,boolean active,Integer userType);
	
	Optional<PTSUser> findByUsernameAndUserType(String username,Integer userType);
	
	Set<PTSUser> findAllByUserTypeIn(Set<Integer> userTypeSet);	
	
	
	@Query(value="SELECT DISTINCT new com.masa.pts.core.model.PTSUserSearchDTO ( "
			+ "TE.employeeId, TE.username, TE.active, TE.firstName,TE.lastName,TE.userType,"
			+ "TE.createdDate, TE.createdBy, TE.modifiedDate,TE.modifiedBy ) "
			+ "FROM PTSUser TE "
			+ "LEFT JOIN PTSUserRole usr on TE.employeeId = usr.userRoleId.user.employeeId "
			+ "WHERE TE.userType not in (0) "
			+ " AND (:userType is null or TE.userType = (:userType) )"
			+ " AND (:userName is null or TE.username like concat('%',:userName,'%' ) )"
			+ " AND (:firstName is null or TE.firstName like concat('%',:firstName,'%') )"
			+ " AND (:lastName is null or TE.lastName like  concat('%', :lastName,'%') ) "
			+ " AND (:roleId is null or usr.userRoleId.role.id = (:roleId) )"
			)
	Page<PTSUserSearchDTO> searchUsersByParams(
			@Param("userType") Integer userType,
			@Param("userName") String userName,
			@Param("firstName") String firstName,
			@Param("lastName") String lastName,
			@Param("roleId") Integer roleId,
			Pageable pageable
			);
	
	@Query("SELECT CASE WHEN COUNT(TER.roleCode) > 0 THEN 'true' ELSE 'false' end "
			+ " FROM PTSUserRole TER "
			+ " JOIN PTSUserRoleAccess TRA ON TER.userRoleId.role.id = TRA.roleId "
			+ " JOIN UserAccess TAC ON TRA.accessId = TAC.id "
			+ " WHERE TER.userRoleId.user.employeeId = (:employeeId)"
			+ " and TAC.code = (:accessCode) ")
	Boolean userHasAccessToAccessCode(Integer employeeId,String accessCode);
	
	PTSUser findByemployeeId(Integer employeeId);
}
