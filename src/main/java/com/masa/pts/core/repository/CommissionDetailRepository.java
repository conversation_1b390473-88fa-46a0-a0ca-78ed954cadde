package com.masa.pts.core.repository;

import java.math.BigDecimal;
import java.util.Set;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;

import com.masa.pts.core.domain.CommissionDetail;

public interface CommissionDetailRepository extends CrudRepository<CommissionDetail, Integer> {

	Set<CommissionDetail> findAllByMemberIdAndAdvanceAndPayDetailIdOrLeftCommAmount(Integer memberId,Integer advance,Integer payDetailId,BigDecimal leftCommAmt);
	
	Set<CommissionDetail> findAllByMemberIdAndProductIdAndAdvanceAndChargebackOrderById(Integer memberId,Integer productId,Integer advance,Boolean chargeback);
	
	Set<CommissionDetail> findAllByPayDetailIdOrderById(Integer paymentDetailId);
	
	boolean existsByMemberIdAndProductIdAndAdvanceAndChargeback(Integer memberId,Integer productId,Integer advance,Boolean chargeback);
	
	@Query(nativeQuery = true, value = "SELECT TCD.* FROM dbo.tdat_Commission_Detail AS TCD  " + 
			"WHERE TCD.member_id = (:memberId) AND TCD.product_id = (:productId) " + 
			"AND  ( TCD.paydetail_id <=0 OR TCD.paydetail_id IS NULL OR TCD.left_comm >0) " + 
			"AND TCD.is_advance > 0 AND TCD.is_advance <> 3 AND TCD.is_chargeBack <> 1 ")
	Set<CommissionDetail> findAllByParams(@Param("memberId") Integer memberId, @Param("productId") Integer productId);
}
