package com.masa.pts.core.repository;

import java.util.Set;

import org.springframework.data.repository.CrudRepository;

import com.masa.pts.core.domain.PTSUserSalesChannel;
import com.masa.pts.core.domain.PTSUserSalesChannelId;

public interface PTSUserSalesChannelRepository extends CrudRepository<PTSUserSalesChannel, PTSUserSalesChannelId> {

	Set<PTSUserSalesChannel> findAllByUserSalesChannelIdUserEmployeeIdOrderByUserSalesChannelIdSalesChannelName(Integer employeeId);
}
