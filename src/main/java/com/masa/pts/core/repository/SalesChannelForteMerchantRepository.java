package com.masa.pts.core.repository;

import com.masa.pts.core.domain.SalesChannelForteMerchant;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import java.util.Optional;

public interface SalesChannelForteMerchantRepository extends CrudRepository<SalesChannelForteMerchant, Integer> {

	SalesChannelForteMerchant findBySalesChannelId(Integer salesChannelId);
	
	//@Query(value="SELECT MAX(ID) FROM TDAT_NOTICE WHERE (PAY_ID = 0 OR PAY_ID IS NULL) AND MEMBER_ID = ?1",
	   //nativeQuery=true)
	
	@Query(value="select TOP 1 a.MERCHANT_ID " + 
			"from " + 
			"tlnk_sales_channel_forte_merchant a " + 
			"join tdat_Sales_Channel b on a.sales_channel_id = b.id  " + 
			"join tdat_Business_Line c on c.channel = b.id " + 
			"join tlnk_Group_Line d on d.idLine = c.id " + 
			"join tdat_group e on d.idGroup = e.group_id " + 
			"join tdat_member f on e.group_id = f.group_id " + 
			"where f.member_id = ?1" ,nativeQuery=true)
	Optional<String> findMerchantIdByMemberId(Integer memberId);

	@Query(value = "select TOP 1 a.MERCHANT_ID " +
			"from " +
			"tlnk_sales_channel_forte_merchant a " +
			"join tdat_Sales_Channel b on a.sales_channel_id = b.id  " +
			"join tdat_Business_Line c on c.channel = b.id " +
			"join tlnk_Group_Line d on d.idLine = c.id " +
			"join tdat_group e on d.idGroup = e.group_id " +
			"where e.group_code = ?1 and is_insurance = 0",
			nativeQuery = true)
	Optional<String> findMerchantIdByGroupCode(String groupCode);
	
	@Query(value = " select TOP 1 a.MERCHANT_ID from tlnk_sales_channel_forte_merchant a"
			+ " join tdat_Sales_Channel b on a.sales_channel_id = b.id  "
			+ " join tdat_Business_Line c on c.channel = b.id "
			+ " join tlnk_Group_Line d on d.idLine = c.id "
			+ " join tdat_group e on d.idGroup = e.group_id "
			+ " join tlnk_Group_Fee gf on gf.group_id=e.group_id "
			+ " left join tdat_product prd on prd.product_id = gf.product_id "
			+ " where e.group_code = ?1 and prd.product_id = ?2 and (( prd.company_id=22  and a.is_insurance= 1 ) or (a.is_insurance= 0 and prd.company_id <> 22))",
			nativeQuery = true)
	Optional<String> findMerchantIdByGroupCodeAndProductId(String groupCode,Integer productId);
}
