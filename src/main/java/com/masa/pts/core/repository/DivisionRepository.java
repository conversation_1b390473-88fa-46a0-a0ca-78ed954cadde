package com.masa.pts.core.repository;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import com.masa.pts.core.domain.Division;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface DivisionRepository extends CrudRepository<Division, Integer> {

	@Query(	value = "SELECT new com.masa.pts.core.domain.Division( TD.id as id , TD.name as name ) "
			+ "FROM Division as TD "
			+ "JOIN SalesChannel as TSC on TSC.division.id = TD.id "
			+ "JOIN PTSUserSalesChannel as TESC ON TESC.userSalesChannelId.salesChannel.id = TSC.id "
			+ "WHERE TESC.userSalesChannelId.user.employeeId = (:userId) "
			+ "GROUP BY TD.id, TD.name "
			+ "ORDER BY TD.name " )
	List<Division> getUsersDivisionByUser(@Param("userId") Integer userId);
	
}
