package com.masa.pts.core.repository;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import com.masa.pts.core.domain.UserAccess;
import org.springframework.data.repository.query.Param;

import java.util.Collection;
import java.util.Optional;
import java.util.Set;

public interface UserAccessRepository extends CrudRepository<UserAccess, Integer> {
    
    Optional<UserAccess> findByIdAndIsObsoleteIsFalse(Integer id);
    
    Set<UserAccess> findAllByIsObsoleteIsFalse();

    @Query("SELECT u FROM UserAccess u WHERE u.id IN :ids AND u.isObsolete = false")
    Set<UserAccess> findAllByIdAndIsObsoleteIsFalse(@Param("ids") Collection<Integer> ids);

    Optional<UserAccess> findByCodeAndIsObsoleteIsFalse(String code);
}
