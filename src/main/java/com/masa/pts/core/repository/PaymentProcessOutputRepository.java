package com.masa.pts.core.repository;

import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.querydsl.binding.QuerydslBinderCustomizer;
import org.springframework.data.querydsl.binding.QuerydslBindings;
import org.springframework.data.repository.CrudRepository;

import com.masa.pts.core.domain.PaymentProcessOutput;
import com.masa.pts.core.domain.QPaymentProcessOutput;
import com.querydsl.core.types.dsl.StringPath;

public interface PaymentProcessOutputRepository extends CrudRepository<PaymentProcessOutput, Integer> , QuerydslPredicateExecutor<PaymentProcessOutput>,
QuerydslBinderCustomizer<QPaymentProcessOutput>  {

	@Override
	default void customize(QuerydslBindings bindings, QPaymentProcessOutput root) {
		bindings.bind(String.class).first((StringPath path, String value) -> path.containsIgnoreCase(value));			
	}
}
