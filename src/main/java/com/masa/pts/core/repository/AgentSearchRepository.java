package com.masa.pts.core.repository;

import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.querydsl.binding.QuerydslBinderCustomizer;
import org.springframework.data.querydsl.binding.QuerydslBindings;
import org.springframework.data.repository.CrudRepository;

import com.masa.pts.core.domain.AgentSearch;
import com.masa.pts.core.domain.QAgentSearch;
import com.querydsl.core.types.dsl.StringPath;

public interface AgentSearchRepository extends CrudRepository<AgentSearch, Integer>, QuerydslPredicateExecutor<AgentSearch>,
								QuerydslBinderCustomizer<QAgentSearch> {

	@Override
	default void customize(QuerydslBindings bindings, QAgentSearch root) {
		bindings.bind(String.class).first((StringPath path, String value) -> path.containsIgnoreCase(value));			
	}
}
