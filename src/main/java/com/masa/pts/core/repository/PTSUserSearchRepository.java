package com.masa.pts.core.repository;

import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.querydsl.binding.QuerydslBinderCustomizer;
import org.springframework.data.querydsl.binding.QuerydslBindings;
import org.springframework.data.repository.CrudRepository;

import com.masa.pts.core.domain.PTSUserSearch;
import com.masa.pts.core.domain.QPTSUserSearch;
import com.querydsl.core.types.dsl.StringPath;

public interface PTSUserSearchRepository extends CrudRepository<PTSUserSearch, Integer>,
		QuerydslPredicateExecutor<PTSUserSearch>, QuerydslBinderCustomizer<QPTSUserSearch> {

	@Override
	default void customize(QuerydslBindings bindings, QPTSUserSearch root) {
		bindings.bind(String.class).first((StringPath path, String value) -> path.containsIgnoreCase(value));
	}
}
