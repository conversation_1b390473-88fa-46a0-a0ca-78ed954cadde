package com.masa.pts.core.repository;

import java.util.Date;
import java.util.List;
import java.util.Set;

import javax.persistence.TemporalType;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.Temporal;
import org.springframework.data.repository.CrudRepository;

import com.masa.pts.core.domain.PaymentDetail;
import com.masa.pts.core.model.PaymentGroupProductDTO;

public interface PaymentDetailRepository extends CrudRepository<PaymentDetail, Integer> {

	List<PaymentDetail> findAllByMemberIdAndProductIdAndVoidPaymentOrderByTransactionDate(Integer memberId, Integer productId,Boolean isVoidPayment);
	
	Set<PaymentDetail> findAllByMemberIdAndTransactionDateBetweenAndVoidPaymentOrderByTransactionDate(Integer memberId,@Temporal(TemporalType.DATE) Date startTransactionDate, @Temporal(TemporalType.DATE) Date endTransactionDate,Boolean isVoidPayment);
	
	Set<PaymentDetail> findAllByMemberIdAndTransactionDateAndVoidPayment(Integer memberId, @Temporal(TemporalType.DATE) Date transactionDate,Boolean isVoidPayment);
	
	List<PaymentDetail> findAllByMemberId(Integer memberId);

	<T> List<T> findAllByMemberIdAndProductIdOrderByTransactionDate(Integer memberId,Integer productId,Class <T> type);
	
	Set<PaymentDetail> findTop5ByMemberIdOrderByTransactionDateDesc(Integer memberId);
		
	<T> Set<T> findAllByMemberIdAndProductIdAndPaymentPayDateGreaterThanOrderByTransactionDate(Integer memberId,Integer productId,@Temporal(TemporalType.DATE) Date postDate,Class <T> type);
	
	<T> Set<T> findAllByMemberIdAndProductIdAndPaymentPayDateLessThanAndVoidPaymentOrderByTransactionDate(Integer memberId,Integer productId,@Temporal(TemporalType.DATE) Date postDate,Boolean isVoidPayment,Class <T> type);
	
	boolean existsByMemberIdAndProductIdAndIsChargeBackAndVoidPayment(Integer memberId,Integer productId,Boolean isChargeback,Boolean isVoidPayment);
	
	<T> List<T> findAllByMemberIdAndVoidPayment(Integer memberId,Boolean isVoidPayment,Class <T> type);
	
	@Query(value="select TP.payInvoiceDate from PaymentDetail TPD join Payment TP on TPD.payment.payId = TP.payId"
			+ " where TPD.id = :paymentDetailId")
	Date getPaymentInvDate(Integer paymentDetailId);
	
	@Query(value ="SELECT DISTINCT TPD.productId "
			+ " FROM PaymentDetail TPD "
			+ " JOIN Product TP ON TPD.productId = TP.productId "
			+ " WHERE TPD.memberId = :memberId "
			+ " AND TP.upgradeProduct =0 "
			+ "")
	Set<Integer> getMemberPaymentPrimaryProducts(Integer memberId);

	
	@Query(" SELECT new com.masa.pts.core.model.PaymentGroupProductDTO(TPD.id as payDetailId , COALESCE(TI.group.groupId,0) as groupId"
			+ " , TPD.productId as productId , TPD.amountPaid as amountPaid,TPD.hasTax as hasTax , TPD.taxRate as taxRate ,TPD.periodFee as periodFee) "
			+ "FROM PaymentDetail TPD "
			+ "JOIN Payment TP ON TPD.payment.payId = TP.payId "
			+ "LEFT JOIN Invoice TI ON TI.payment.payId = TP.payId "
			+ "WHERE TPD.memberId = (:memberId) "
			+ "AND TP.payDate >= (:payDate) "
			+ "AND TPD.isChargeBack <> 1 "
			+ "AND TPD.voidPayment <> 1 ")
	Set<PaymentGroupProductDTO> getMemberPaymentsByGroupAndProduct(Integer memberId,@Temporal(TemporalType.DATE) Date payDate);			
	
}
