package com.masa.pts.core.repository;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import com.masa.pts.core.domain.Address;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface AddressRepository extends CrudRepository<Address, Integer> {
	
	Address findByaddressId(Integer addressId);
	
	@Query("SELECT a FROM Address a " +
			"JOIN FETCH a.countryDetails " +
			"JOIN FETCH a.stateDetails " +
			"WHERE a.addressId IN :ids")
	List<Address> findAllByAddressIdIn(@Param("ids") List<Integer> ids);
}
