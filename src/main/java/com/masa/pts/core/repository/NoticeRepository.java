package com.masa.pts.core.repository;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import com.masa.pts.core.domain.Notice;

public interface NoticeRepository extends CrudRepository<Notice, Integer> {

	@Query(value="SELECT MAX(ID) FROM TDAT_NOTICE WHERE (PAY_ID = 0 OR PAY_ID IS NULL) AND MEMBER_ID = ?1",
		   nativeQuery=true)
	Integer getMaxNoticeIdForMember(Integer memberId);
}
