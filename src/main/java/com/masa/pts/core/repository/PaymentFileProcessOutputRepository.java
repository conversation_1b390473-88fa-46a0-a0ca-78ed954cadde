package com.masa.pts.core.repository;

import com.masa.pts.core.domain.PaymentFileProcessOutput;
import com.masa.pts.core.domain.ReviewStatus;
import com.masa.pts.core.model.PaymentPostStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import java.util.Date;
import java.util.List;

public interface PaymentFileProcessOutputRepository
		extends PagingAndSortingRepository<PaymentFileProcessOutput, Integer> {
	
	@Query(""
			+ "SELECT TPFP "
			+ "FROM PaymentFileProcessOutput TPFP "
			+ " WHERE (:groupCode is null or TPFP.groupCd = :groupCode) "			
			+ " AND (:masaMemberId is null or TPFP.memberId = :masaMemberId) "
			+ " AND ( coalesce(:status,null) is null or TPFP.status in (:status) ) "
			+ " AND (:sourceFileName is null or TPFP.fileName LIKE CONCAT('%',:sourceFileName,'%') ) "
			+ " AND (:employerId is null or TPFP.employerId = :employerId ) "
			+ " AND ( coalesce(:reviewStatus,null) is null or TPFP.reviewStatus in (:reviewStatus) ) "
			+ " AND (:createdDateStart is null or :createdDateEnd IS null OR  "
			+ " TPFP.processedDate BETWEEN :createdDateStart AND :createdDateEnd) ")
	Page<PaymentFileProcessOutput> searchByParams(
			@Param("groupCode") String groupCode,
			@Param("masaMemberId") Integer masaMemberId,
			@Param("status") List<PaymentPostStatus> status,
			@Param("sourceFileName") String sourceFileName,
			@Param("employerId") String employerId,
			@Param("reviewStatus") List<ReviewStatus> reviewStatus,
			@Param("createdDateStart") Date createdDateStart,
			@Param("createdDateEnd")  Date createdDateEnd,
			Pageable pageable);

	@Modifying(clearAutomatically = true)
	@Query("UPDATE PaymentFileProcessOutput TPFP " +
					"SET TPFP.reviewStatus = :newReviewStatus, TPFP.statusUpdatedBy = :statusUpdatedBy"
			+ " WHERE (:groupCode is null or TPFP.groupCd = :groupCode) "
			+ " AND (:masaMemberId is null or TPFP.memberId = :masaMemberId) "
			+ " AND ( coalesce(:status,null) is null or TPFP.status in (:status) ) "
			+ " AND (:sourceFileName is null or TPFP.fileName LIKE CONCAT('%',:sourceFileName,'%') ) "
			+ " AND (:employerId is null or TPFP.employerId = :employerId ) "
			+ " AND ( coalesce(:reviewStatus,null) is null or TPFP.reviewStatus in (:reviewStatus) ) "
			+ " AND (:createdDateStart is null or :createdDateEnd IS null OR  "
			+ " TPFP.processedDate BETWEEN :createdDateStart AND :createdDateEnd) ")
	int bulkUpdatePaymentFileProcessOutputStatusByFilter(
			@Param("newReviewStatus") ReviewStatus newReviewStatus,
			@Param("statusUpdatedBy") String statusUpdatedBy,
			@Param("groupCode") String groupCode,
			@Param("masaMemberId") Integer masaMemberId,
			@Param("status") List<PaymentPostStatus> status,
			@Param("sourceFileName") String sourceFileName,
			@Param("employerId") String employerId,
			@Param("reviewStatus") List<ReviewStatus> reviewStatus,
			@Param("createdDateStart") Date createdDateStart,
			@Param("createdDateEnd") Date createdDateEnd);
}
