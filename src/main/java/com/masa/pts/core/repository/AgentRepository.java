/**
 * 
 */
package com.masa.pts.core.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;

import com.masa.pts.core.domain.Agent;
import com.masa.pts.core.domain.AgentSaleschannel;
import com.masa.pts.core.domain.GroupEntity;


/**
 * <AUTHOR>
 *
 */
public interface AgentRepository extends CrudRepository<Agent, Integer> {

	<T> Optional<T> findByAgentNum(String agentNum,Class<T> type);
	
	<T> Optional<T> findByAgentId(Integer agentId,Class<T> type);
	
	boolean existsByAgentNum(String agentNum);
	
	@Query( value = "select a.agent_id as agentId , a.agent_first as agentFirst,a.agent_last as agentLast,"
			+ " a.assigned_sales_channel_id as assignedSalesChannelId,a.assigned_sales_channel_name as  assignedSalesChannelName,a.agent_num as agentNumber "
			+ " from v_agent_saleschannel a where a.agent_num = ?1",nativeQuery = true)
	List<AgentSaleschannel> findAgentsAssocitedWithEapp( String agentNum);
	
}
