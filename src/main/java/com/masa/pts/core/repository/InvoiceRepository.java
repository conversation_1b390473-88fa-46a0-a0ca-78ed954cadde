package com.masa.pts.core.repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import javax.persistence.TemporalType;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.Temporal;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import com.masa.pts.core.domain.Invoice;
import com.masa.pts.core.model.InvoiceSearchDTO;

public interface InvoiceRepository extends PagingAndSortingRepository<Invoice, Integer> {
	
	@Query("select distinct TI from Invoice TI " +
			"join fetch TI.group " +
			"left join fetch TI.invoiceDetail invoiceDetail " +
			"left join fetch invoiceDetail.member " +
			"left join fetch invoiceDetail.product " +
			"where TI.invoiceId = (:invoiceId)")
	Invoice findInvoiceWithDetailsById(Integer invoiceId);
	
	List<Invoice> findAllByOrderByInvoiceIdDesc();
	
	Set<Invoice> findByGroupGroupIdAndIsPaid(@Param("groupId") Integer groupId,@Param("isPaid") Integer isPaid);
	
	Set<Invoice> findAllByGroupGroupCodeAndIsPaidOrderByInvoiceDateDesc(@Param("groupCode") String groupCode,@Param("isPaid") Boolean isPaid);
	
	List<Invoice> findByGroupGroupIdAndInvoiceDateAndIsPaid(@Param("groupId") Integer groupId,@Param("invoiceDate") java.util.Date invoiceDate ,@Param("isPaid") Integer isPaid);
	
	Page<Invoice> findAllByOrderByInvoiceIdDesc(Pageable pageable);	
	
	long countByGroupGroupCodeAndIsPaid(String groupCode,Integer isPaid);
	
	long countByGroupGroupCodeAndInvoiceDate(String groupCode,@Temporal(TemporalType.DATE) java.util.Date invoiceDate);
	
	Optional<Invoice> findByGroupGroupCodeAndInvoiceDate(String groupCode,@Temporal(TemporalType.DATE) java.util.Date invoiceDate);
	
	Set<Invoice> findTop10ByGroupGroupIdOrderByInvoiceDateDesc(Integer groupId);
	
	List<Invoice.GroupInvoiceDates> findByGroupGroupCodeAndInvoiceDateGreaterThanEqualAndInvoiceDateLessThanEqualOrderByInvoiceDate(String groupCode,@Temporal(TemporalType.DATE) java.util.Date invoiceStartDate
			,@Temporal(TemporalType.DATE) java.util.Date invoiceEndDate);
	
	<T> Optional<T> findByInvoiceId(Integer invoiceId,Class<T> type);
	
	@Query(value=""
			+ "SELECT new com.masa.pts.core.model.InvoiceSearchDTO ( TI.invoiceId , TI.group.groupCode , TI.group.groupName "
			+ " ,TI.invoiceType, TI.totalDue , TI.invoiceDate, TI.isPaid ,TI.createdDate "
			+ " ,TI.modifiedDate ) "
			+ "FROM Invoice TI "		
			+ "JOIN PTSUserGroup AS TEG on TEG.userGroupId.group.groupId = TI.group.groupId "
			+ "WHERE TI.invoiceType in (1,2)  "
			+ " and TEG.userGroupId.user.employeeId = (:userId) "
			+ " and (:groupId is null or TI.group.groupId = (:groupId))  "
			+ " and (:groupCode is null or TI.group.groupCode = (:groupCode) ) "
			+ " and (:invoiceId is null or TI.invoiceId = (:invoiceId))  "
			+ " and (:invoiceDate is null or TI.invoiceDate = (:invoiceDate) ) "
			+ " and (:isPaid is null or TI.isPaid = (:isPaid) )"
			+ " ")
	Page<InvoiceSearchDTO> searchInvoicesByParamsExternalUser(
			@Param("groupId") Integer groupId,
			@Param("groupCode") String groupCode,
			@Param("invoiceId") Integer invoiceId,
			@Param("invoiceDate") @Temporal(TemporalType.DATE) Date invoiceDate,
			@Param("isPaid") Boolean isPaid,
			@Param("userId") Integer userId,
			Pageable pageable);	
	
	@Query(value=""
			+ "SELECT new com.masa.pts.core.model.InvoiceSearchDTO ( TI.invoiceId , TI.group.groupCode , TI.group.groupName "
			+ " ,TI.invoiceType, TI.totalDue , TI.invoiceDate, TI.isPaid ,TI.createdDate "
			+ " ,TI.modifiedDate ) "
			+ "FROM Invoice TI "
			+ "join GroupLineEntity as grl on TI.group.groupId = grl.groupLineEntityId.groupId " 
			+ "join BusinessLineEntity as bl on bl.id = grl.groupLineEntityId.lineId "
			+ "join SalesChannel as sc on sc.id = bl.salesChannel.id " 
			+ "join PTSUserSalesChannel as tesc on tesc.userSalesChannelId.salesChannel.id = sc.id " 
			+ "WHERE TI.invoiceType in (1,2) "
			+ " and tesc.userSalesChannelId.user.employeeId = (:userId) "
			+ " and (:groupId is null or TI.group.groupId = (:groupId) ) "
			+ " and (:groupCode is null or TI.group.groupCode = (:groupCode) ) "
			+ " and (:invoiceId is null or TI.invoiceId = (:invoiceId))  "
			+ " and (:invoiceDate is null or TI.invoiceDate = (:invoiceDate) ) "
			+ " and (:isPaid is null or TI.isPaid = (:isPaid) )"
			+ " ")
	Page<InvoiceSearchDTO> searchInvoicesByParamsInternalUser(
			@Param("groupId") Integer groupId,
			@Param("groupCode") String groupCode,
			@Param("invoiceId") Integer invoiceId,
			@Param("invoiceDate") @Temporal(TemporalType.DATE) Date invoiceDate, 
			@Param("isPaid") Boolean isPaid,
			@Param("userId") Integer userId,
			Pageable pageable);
	
	
	@Query(value=" SELECT CASE WHEN COUNT(TI.id) > 0 THEN 'true' ELSE 'false' end "
			+ "FROM Invoice TI "
			+ "JOIN PTSUserGroup TEG ON TI.group.groupId = TEG.userGroupId.group.groupId "
			+ "WHERE TI.id = (:invoiceId) "
			+ "AND TEG.userGroupId.user.employeeId = (:employeeId) "
			)
	Boolean externalUserHasAccessToInvoice(Integer invoiceId,Integer employeeId);
	
	@Query(value="SELECT CASE WHEN COUNT(TI.id) > 0 THEN 'true' ELSE 'false' end "
			+ " FROM Invoice TI"
			+ " JOIN GroupLineEntity TGL ON TI.group.groupId = TGL.groupLineEntityId.groupId "
			+ " JOIN BusinessLineEntity TBL ON TGL.groupLineEntityId.lineId = TBL.id "
			+ " JOIN PTSUserSalesChannel TESC ON TBL.salesChannel = TESC.userSalesChannelId.salesChannel.id "
			+ " WHERE TESC.userSalesChannelId.user.employeeId = (:employeeId) "
			+ " AND TI.id = (:invoiceId) "
			)
	Boolean internalUserHasAccessToInvoice(Integer invoiceId,Integer employeeId);
}
