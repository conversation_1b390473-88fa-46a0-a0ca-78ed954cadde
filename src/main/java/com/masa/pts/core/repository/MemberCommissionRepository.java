package com.masa.pts.core.repository;

import java.util.Set;

import org.springframework.data.repository.CrudRepository;

import com.masa.pts.core.domain.MemberCommission;

public interface MemberCommissionRepository extends CrudRepository<MemberCommission, Integer> {

	Set<MemberCommission> findByMemberMemberId(Integer memberId);
	
	Set<MemberCommission> findByMemberMemberIdAndProductId(Integer memberId,Integer productId);
}
