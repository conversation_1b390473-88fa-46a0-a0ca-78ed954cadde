package com.masa.pts.core.utils.db;

import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

public class ChunkRequest implements Pageable {

    private int limit;
    private int offset;
    private Sort sort;

    public ChunkRequest(int offset, int limit, Sort sort) {
        if (offset < 0) {
            throw new IllegalArgumentException("Skip must not be less than zero!");
        }

        if (limit < 0) {
            throw new IllegalArgumentException("Offset must not be less than zero!");
        }

        this.limit = limit;
        this.offset = offset;
        this.sort = sort;
    }

    @Override
    public int getPageNumber() {
        return 0;
    }

    @Override
    public int getPageSize() {
        return limit;
    }

    @Override
    public long getOffset() {
        return offset;
    }

    @Override
    public Sort getSort() {
        return sort;
    }

    @Override
    public Pageable next() {
        return null;
    }

    @Override
    public Pageable previousOrFirst() {
        return this;
    }

    @Override
    public Pageable first() {
        return this;
    }

    @Override
    public boolean hasPrevious() {
        return false;
    }

    public static ChunkRequest of(int offset, int limit, Sort sort) {
        return new ChunkRequest(offset, limit, sort);
    }

}
