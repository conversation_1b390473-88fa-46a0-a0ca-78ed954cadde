package com.masa.pts.core.utils.common;

import com.google.common.collect.ImmutableList;
import com.masa.pts.core.model.PaymentDTO;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public class ItemPaymentsExcelDocument implements ExcelDocument<PaymentDTO> {

    private List<List<Object>> dataRows;
    private List<List<Object>> totalRow;

    public ItemPaymentsExcelDocument(List<PaymentDTO> dataRows) {
        setDataRow(dataRows);
    }

    @Override
    public void setDataRow(List<PaymentDTO> rowData) {
        dataRows = rowData.stream()
                .map(r -> 
                    Arrays.<Object>asList(
                            r.getPaymentDetailsId(),
                            r.getPayDate(),
                            r.getCreateDate(),
                            r.getInvoiceDate(),
                            r.getProductName(),
                            r.getAmountPaid(),
                            r.getUsername(),
                            r.isVoid(),
                            r.isChargeback(),
                            r.isWaived(),
                            r.getCommissionType())
                ).collect(Collectors.toList());
    }

    @Override
    public void setTotalRow(List<PaymentDTO> detailsList) {
        this.totalRow = Collections.emptyList();
    }

    @Override
    public List<String> getColumnNames() {
        return ImmutableList.of(
                "paymentDetailsId",
                "payDate",
                "createDate",
                "invoiceDate",
                "productName",
                "amountPaid",
                "username",
                "isVoid",
                "isChargeback",
                "isWaived",
                "commissionType");
    }

    @Override
    public List<List<Object>> getDataRows() {
        return dataRows;
    }

    @Override
    public List<List<Object>> getTotalRow() {
        return totalRow;
    }
}
