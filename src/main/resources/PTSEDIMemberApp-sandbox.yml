# this is comment
spring:
  jmx:
    default-domain: PTSEDIMemberApp
  datasource:
        driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
  application:
        name: PTSEDIMemberApp
  jpa:
    show-sql: true # if true sql statement logged to console.
    hibernate:
        dialect: org.hibernate.dialect.SQLServer2012Dialect
        ddl-auto: none #do not change
  batch:
      job:
        enabled: false #if true on application start, Process executed once.
        names: forteSettlementProcessJob
logging:
  file: logs\PTSEDIMemberAppSandbox.log        

management:
  endpoints:
    web:
      exposure:
        include: health,info,configprops,env,logfile,loggers,metrics,scheduledtasks,httptrace
    jmx:
      domain: PTSEDIMemberApp
      unique-names: true
       
# PTS Properties

pts:
  scifile:
    cron: 0 30 7 ? * MON-FRI *
    host: ftp.svccorp.com
    port: 22
    user: Masa
    password: 3rHjMnTm
    folder: \\\\masa-ser7\\Customer Service\PTSEDIFileProcessing\Sandbox\SCI/
    inbound: ${pts.scifile.folder}/inbound    
    acceptFilesModifiedAfter: 2020-08-18T00:00:00
  edifile:
    addressValidationEnabled: true
    folder: \\\\masa-ser7\\Customer Service\PTSEDIFileProcessing\Sandbox
    read: ${pts.edifile.folder}\\read
    processed: ${pts.edifile.folder}\\processed
    output: ${pts.edifile.folder}\\output
    failed: ${pts.edifile.folder}\\failed
    birthDate:
      format: MM/dd/yyyy
    soldDate:
      format: yyyyMMdd
    fileOutput:
      dateFormat: yyyyMMdd_HH_mm_ss_SSS
    address:
      country:
        default: United States
    columns:
        contractNumber:
            - Contract Number
            - Policy ID
            - ﻿Policy ID
            - Policy-ID
            - PolicyID            
        modification:
            - Modification (Refresh/Update/Add/Cancel)
            - Modification (Update/Add/Cancel)
            - Modification
        payment:
            - Payment
            - Payment (YR/MN)
        productType:
            - Product Type
            - Product Type (EM/EMP/EMGR/PL)
            - Product Type (EMG/EMP/PL)
            - Product Type (EM/EMGr/EMP/PL)
            - Product Type (EMGr/EMP/PL)
            - Product Type (EMP/PL)            
            - Product (EM/PL)
            - Product Type (EMP/EMPGr/EMPP/PL)
            - 'Product Type '
        effectiveDate:
            - Effective Date
            - Effective Date (mm/dd/yyyy)
            - Effective Date (mmyyyy)
            - Effective-Date
        cancelDate:
            - Cancel Date
            - Cancel Date (mm/dd/yyyy)
        relationship:
            - Member Type (P/S/D)
            - Member Type
        firstName:
            - First Name
        middleName:
            - Middle Name
        lastName:
            - Last Name
        birthDate:
            - Birth Date
            - Birth Date (mm/dd/yyyy)
            - Birth-Date
            - Birth Date (dd/mm/yyyy)
        benefitAddress:
            - Benefit Address
            - Benefit-Address
        benefitCity:
            - Benefit City
        benefitState:
            - Benefit State
            - Benefit-State
        benefitZip:
            - Benefit Zip
            - Benefit-Zip
            - Benefit ZIP            
        mailingAddress:
            - Mailing Address
        mailingCity:
            - Mailing City
        mailingState:
            - Mailing State
        mailingZip:
            - Mailing Zip
        email:
            - Email
        phone:
            - Phone
errors:
    messages:
        EGROUPINVALID: Invalid group code
        EGROUPINACTIVE: Group inactive
        EGROUPINVALIDINACTIVE: Group not found or inactive
        EPRODUCTINVALIDFORPYMT: Cannot find product with payment type(MN/YR) in group
        EDATAVALDEXP: Data validation error
        EAGENTNOTSETUPGRP: Agent not setup for group
        EAGENTCOMMNOTSETUP: Agent commission not setup for product
        EAGENTHIERNOTSETUP: Agent hierarchy not setup for product
        EINVALIDFILEACTION: Invalid file action(Add/Update/Cancel/Refresh)
        EEFFDATEREQUIRED: Effective date cannot be blank
        EEFFDATEFIRSTMONTH: Effective date should be first day of the month
        EEFFDATE3MONTHINVALID: Effective date cannot be more than 3 months past or 3 months in future
        EMULTIPLEMEMBERFOUNDADD: Multiple members found for employee id in group for add
        EMEMBERDIFFFIRSTDOB: Member found with different first name and dob for employee id
        EMULTIPLEACTMEMBERFOUND: Multiple active members found for employee id in group for update
        EMULTIPLEINACTMEMBERFOUND: Multiple inactive members found for employee id in group for update
        EEFFDATE3MONTHINVALIDACTIVE: Member being activated with effective dates more than 3 months
        EMEMBERNOTFOUNDUPDATE: Member not found by employee id in group for update
        EMEMBERNOTFOUNCANCEL: Member not found by employee id in group for cancel
        EMULTEACTMBRFOUNDBYLNDBINGROUP: Multiple active members found by last name and dob in group for cancel
        EMULTIPLEACTMEMBERFOUNDCNCL: Multiple active members found for employee id in group for cancel
        EMEMBERDIFFLASTNAMEDOB: Member found with different last name and DOB
        EMEMBERNOACTIVEFORCANCEL: No active member found in group for cancel
        ESOLDDATENOTPARSE: Sold data cannot be parsed from file name
        EEMPLOYEEIDREQD: Contract Number/Employee ID is required
        EMODIFICATIONREQD: Modification/Action is required (Add/Update/Cancel)
        EPRODUCTTYPEREQD: Product type is required(EMGR/EM/EMP/PL)
        ERELATIONSHIPREQD: Relationship is required(P/S/D)
        EFIRSTNAMEREQD: First name is required
        ELASTNAMEREQD: Last name is required
        EPAYMENTTYPEREQD: Payment code is required(MN/YR)
        EBENEFITADDRREQD: Benefit address is required
        EBENEFITCITYREQD: Benefit city is required
        EBENEFITSTATEREQD: Benefit state is required
        EBENEFITZIPREQD: Benefit zip is required
        EMAILINGADDRREQDCITY: Mailing address required if mailing city is provided
        EBENEFITSTATEINVALID: Benefit state code invalid
        EMAILINGSTATEINVALID: Mailing state code invalid
        EBIRTHDATEREQD: Birth date is required
        EBIRTHDATEINVALID: Birth date is invalid
        EBIRTHDATEINVALIDF: Birth date cannot be today or future date
        EBIRTHDATEDEPINVALID: Dependent age cannot be more than 26 years
        ECANCELDATEREQD: Cancel date is required
        ECANCELDATE3MONINVVALID: Cancel date cannot be more than 3 months past or 3 months in future
        EEFFDATEBEFOREMEMEFFDATE: Effective date before member effective date
        EREADERROR: Record read error
        EPRIMARYHASERRORS: Primary record has errors
        ERECORDUPDATE: Error updating record
        ECANCELDATEPAST30DAYS: Cancel date past 30 days from member effective date
        ESCIFILETYPENOTVALID: SCI File type not valid,check file name
        EBENEFITCNTRYREQD: Benefit country code requried
        EBENEFITCNTRYINVALID: Valid benefit country code required
        EDEPENDENTINVFORPRD: Dependent records not applicable for product type