IF COL_LENGTH('dbo.tlnk_MSA','state') IS NULL
BEGIN
    ALTER TABLE dbo.tlnk_MSA
		ADD state INT NULL,
		CONSTRAINT fk_tlnk_MSA_state FOREIGN KEY (state) REFERENCES dbo.tlst_State2(state_id);
END

IF NOT EXISTS(
        SELECT *
        FROM dbo.config_param
        WHERE param_name = 'MASA_INSURANCE_COMPANY_IDS'
    )
    BEGIN
      
        INSERT INTO dbo.config_param (param_name,param_value,[description]) VALUES
		('MASA_INSURANCE_COMPANY_IDS','22','MASA Insurance product company ids');
        
     END
     
     UPDATE dbo.config_param SET param_value = '22' WHERE
     	param_name = 'MASA_INSURANCE_COMPANY_IDS';

IF NOT EXISTS(
        SELECT *
        FROM dbo.config_param
        WHERE param_name = 'INS_MSA_BY_GROUP_ADDRESS_DIVISIONS'
    )
    BEGIN
      
        INSERT INTO dbo.config_param (param_name,param_value,[description]) VALUES
		('INS_MSA_BY_GROUP_ADDRESS_DIVISIONS','B2B','MASA Insurance Group address divisions');
        
     END
     
     UPDATE dbo.config_param SET param_value = 'B2B, MTS' WHERE
     	param_name = 'INS_MSA_BY_GROUP_ADDRESS_DIVISIONS';
     	
IF NOT EXISTS(
        SELECT *
        FROM dbo.config_param
        WHERE param_name = 'INS_MSA_BY_SC_NAME_DIVISIONS'
    )
    BEGIN
      
        INSERT INTO dbo.config_param (param_name,param_value,[description]) VALUES
		('INS_MSA_BY_SC_NAME_DIVISIONS','B2C','MASA Insurance SC names division');
        
     END
     
     UPDATE dbo.config_param SET param_value = 'B2C' WHERE
     	param_name = 'INS_MSA_BY_SC_NAME_DIVISIONS';     	
     	
/*
The below are for reference
SELECT TOP 10 
	d.id [division_id], d.name [division_name],
	s.id [sales_channel_id],s.name  [sales_channel_name],
	c.company_id [company_id], c.companyname [company_name],
	g.group_name,g.group_id,g.group_code,
	gas.state_id [group_state_id],gas.state [group_state_name],gas.symbol [group_state_symbol],
	mas.state_id [member_state_id],mas.state [member_state_name],mas.symbol [member_state_symbol]
FROM dbo.tdat_Member m
INNER JOIN dbo.tdat_Group g ON g.group_id=m.group_id -- AND m.member_id=2127170
INNER JOIN dbo.tdat_Company c ON c.company_id=g.company_id AND c.companyname='MASA Insurance Services, Inc. - Obsidian'
INNER JOIN dbo.tlnk_Group_Line gl ON g.group_id=gl.idGroup
INNER JOIN dbo.tdat_Business_Line b ON b.id=gl.idLine
INNER JOIN dbo.tdat_Sales_Channel s ON s.id=b.channel
INNER JOIN dbo.tdat_Division d ON d.id=s.division AND d.name NOT IN ('B2B','B2C')
LEFT JOIN dbo.tdat_Address ma ON ma.address_id=m.address
LEFT JOIN dbo.tlst_State2 mas ON mas.state_id=ma.state
LEFT JOIN dbo.tdat_Address ga ON ga.address_id=g.mailing_address
LEFT JOIN dbo.tlst_State2 gas ON gas.state_id=ga.state
ORDER BY m.member_id DESC;

SELECT TOP 10 m.id [member_id],
	d.id [division_id], d.name [division_name],
	s.id [sales_channel_id],s.name  [sales_channel_name],
	c.id [company_id], c.company_name [company_name],
	g.group_name,g.id [group_id],g.group_code,
	gas.id [group_state_id],gas.state [group_state_name],gas.symbol [group_state_symbol],
	mas.id [member_state_id],mas.state [member_state_name],mas.symbol [member_state_symbol]
FROM member.member m
INNER JOIN member.company_group g ON g.id=m.group_id
INNER JOIN reference.company c ON c.id=g.company_id AND c.company_name='MASA Insurance Services, Inc. - Obsidian'
INNER JOIN reference.sales_channel s ON s.id= g.sales_channel_id  --ISNULL(m.sales_channel_id,g.sales_channel_id)
INNER JOIN reference.division d ON d.id=s.division AND d.name NOT IN ('B2B','B2C')
LEFT JOIN member.address ma ON ma.id=m.address
LEFT JOIN reference.state mas ON mas.id=ma.state
LEFT JOIN member.address ga ON ga.id=g.mailing_address
LEFT JOIN reference.state gas ON gas.id=ga.state
ORDER BY m.id DESC;

INSERT INTO dbo.tlnk_MSA ( name, material_id, display_name, file_loc, created_by, created_dt, modified_by, modified_dt, is_deleted) 
VALUES('MTS CLM (Insurance) P&C',108,'MTS CLM (Insurance) P&C','https://s3.amazonaws.com/docs.masaglobal.com/MSA/OIC-MASA-PC-CHARTER+LIFETIME+01.22.pdf',322,getDate(),322,getDate(),0);
 
INSERT INTO dbo.tlnk_MSA ( name, material_id, display_name, file_loc, created_by, created_dt, modified_by, modified_dt, is_deleted) 
VALUES('MTS CLM Plus (Insurance) P&C',109,'MTS CLM Plus (Insurance) P&C',' https://s3.amazonaws.com/docs.masaglobal.com/MSA/OIC-MASA-PC-+LIFETIME+PLUS+01.22.pdf',322,getDate(),322,getDate(),0);
 
INSERT INTO dbo.tlnk_MSA ( name, material_id, display_name, file_loc, created_by, created_dt, modified_by, modified_dt, is_deleted) 
VALUES('MTS Platinum (Insurance) P&C',110,'MTS Platinum (Insurance) P&C','https://s3.amazonaws.com/docs.masaglobal.com/MSA/OIC-MASA-PC-PLATINUM+01.22.pdf',322,getDate(),322,getDate(),0);
 

INSERT INTO dbo.tlnk_MSA ( name, material_id, display_name, file_loc, created_by, created_dt, modified_by, modified_dt, is_deleted) 
VALUES('MTS Platinum Plus (Insurance) P&C',111,'MTS Platinum Plus (Insurance) P&C','https://s3.amazonaws.com/docs.masaglobal.com/MSA/OIC-MASA-PC-PLAT+PLUS+01.22.pdf',322,getDate(),322,getDate(),0);

UPDATE tm SET tm.start_date = '2022-04-25', tm.end_date = '2099-12-31'
FROM dbo.tlnk_MSA AS tm where material_id in (110,108,109,111) and tm.start_date IS NULL;

**/