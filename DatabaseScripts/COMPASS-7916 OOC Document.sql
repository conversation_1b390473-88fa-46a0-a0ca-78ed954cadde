USE [compass]
GO

/****** Object:  Table [reference].[ooc_document]    Script Date: 10/29/2023 9:20:37 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [reference].[ooc_document](
	[ooc_document_id] [int] IDENTITY(1,1) NOT FOR REPLICATION NOT NULL,
	[name] [varchar](50) NOT NULL,
	[product_id] [int] NOT NULL,
	[display_name] [varchar](50) NULL,
	[file_loc] [varchar](250) NULL,
	[start_date] [datetime] NULL,
	[end_date] [datetime] NULL,
	[created_by] [int] NOT NULL,
	[created_dt] [datetime] NOT NULL,
	[modified_by] [int] NOT NULL,
	[modified_dt] [datetime] NOT NULL,
	[is_deleted] [bit] NOT NULL,
	[state] [int] NULL,
 CONSTRAINT [PK_ooc_document] PRIMARY KEY CLUSTERED 
(
	[ooc_document_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [reference].[ooc_document] ADD  DEFAULT (getdate()) FOR [created_dt]
GO

ALTER TABLE [reference].[ooc_document] ADD  DEFAULT (getdate()) FOR [modified_dt]
GO

ALTER TABLE [reference].[ooc_document] ADD  DEFAULT ((0)) FOR [is_deleted]
GO

ALTER TABLE [reference].[ooc_document]  WITH CHECK ADD  CONSTRAINT [fk_ooc_document_employee_creator] FOREIGN KEY([created_by])
REFERENCES [member].[employee] ([id])
GO

ALTER TABLE [reference].[ooc_document] CHECK CONSTRAINT [fk_ooc_document_employee_creator]
GO

ALTER TABLE [reference].[ooc_document]  WITH CHECK ADD  CONSTRAINT [fk_ooc_document_employee_modifier] FOREIGN KEY([modified_by])
REFERENCES [member].[employee] ([id])
GO

ALTER TABLE [reference].[ooc_document] CHECK CONSTRAINT [fk_ooc_document_employee_modifier]
GO

ALTER TABLE [reference].[ooc_document]  WITH CHECK ADD  CONSTRAINT [fk_ooc_document_product_id] FOREIGN KEY([product_id])
REFERENCES [reference].[product] ([id])
GO

ALTER TABLE [reference].[ooc_document] CHECK CONSTRAINT [fk_ooc_document_product_id]
GO

ALTER TABLE [reference].[ooc_document]  WITH CHECK ADD  CONSTRAINT [fk_ooc_document_state] FOREIGN KEY([state])
REFERENCES [reference].[state] ([id])
GO

ALTER TABLE [reference].[ooc_document] CHECK CONSTRAINT [fk_ooc_document_state]
GO


USE [masa]
GO

/****** Object:  Table [dbo].[ooc_document]    Script Date: 10/29/2023 9:34:59 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[ooc_document](
	[ooc_document_id] [int] IDENTITY(1,1) NOT FOR REPLICATION NOT NULL,
	[name] [varchar](50) NOT NULL,
	[product_id] [int] NOT NULL,
	[display_name] [varchar](50) NULL,
	[file_loc] [varchar](250) NULL,
	[start_date] [datetime] NULL,
	[end_date] [datetime] NULL,
	[created_by] [int] NOT NULL,
	[created_dt] [datetime] NOT NULL,
	[modified_by] [int] NOT NULL,
	[modified_dt] [datetime] NOT NULL,
	[is_deleted] [bit] NOT NULL,
	[state] [int] NULL,
 CONSTRAINT [PK_ooc_document] PRIMARY KEY CLUSTERED 
(
	[ooc_document_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[ooc_document] ADD  DEFAULT (getdate()) FOR [created_dt]
GO

ALTER TABLE [dbo].[ooc_document] ADD  DEFAULT (getdate()) FOR [modified_dt]
GO

ALTER TABLE [dbo].[ooc_document] ADD  DEFAULT ((0)) FOR [is_deleted]
GO

ALTER TABLE [dbo].[ooc_document]  WITH CHECK ADD  CONSTRAINT [fk_ooc_document_employee_creator] FOREIGN KEY([created_by])
REFERENCES [dbo].[tdat_Employee] ([employee_id])
GO

ALTER TABLE [dbo].[ooc_document] CHECK CONSTRAINT [fk_ooc_document_employee_creator]
GO

ALTER TABLE [dbo].[ooc_document]  WITH CHECK ADD  CONSTRAINT [fk_ooc_document_employee_modifier] FOREIGN KEY([modified_by])
REFERENCES [dbo].[tdat_Employee] ([employee_id])
GO

ALTER TABLE [dbo].[ooc_document] CHECK CONSTRAINT [fk_ooc_document_employee_modifier]
GO

ALTER TABLE [dbo].[ooc_document]  WITH CHECK ADD  CONSTRAINT [fk_ooc_document_product_id] FOREIGN KEY([product_id])
REFERENCES [dbo].[tdat_product] ([product_id])
GO

ALTER TABLE [dbo].[ooc_document] CHECK CONSTRAINT [fk_ooc_document_product_id]
GO

ALTER TABLE [dbo].[ooc_document]  WITH CHECK ADD  CONSTRAINT [fk_ooc_document_state] FOREIGN KEY([state])
REFERENCES [dbo].[tlst_State2] ([state_id])
GO

ALTER TABLE [dbo].[ooc_document] CHECK CONSTRAINT [fk_ooc_document_state]
GO


