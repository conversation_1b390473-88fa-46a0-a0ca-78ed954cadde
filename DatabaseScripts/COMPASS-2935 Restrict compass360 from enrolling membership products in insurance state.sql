IF NOT EXISTS(
        SELECT *
        FROM dbo.config_param
        WHERE param_name = 'INSURANCE_STATES_TO_RESTRICT_COMPASS360_MEMBER_ENROLLING'
    )
    BEGIN
      
        INSERT INTO dbo.config_param (param_name,param_value,[description]) VALUES
		('INSURANCE_STATES_TO_RESTRICT_COMPASS360_MEMBER_ENROLLING','WV, IA','Insurance states to restrict from compass360 member enrolling');
        
     END
     
     UPDATE dbo.config_param SET param_value = 'WV, IA' WHERE
     	param_name = 'INSURANCE_STATES_TO_RESTRICT_COMPASS360_MEMBER_ENROLLING';     
     
IF NOT EXISTS(
        SELECT *
        FROM dbo.config_param
        WHERE param_name = 'INSURANCE_STATES_ALLOWED_USERS_FOR_MEMBER_ENROLLING'
    )
    BEGIN
      
        INSERT INTO dbo.config_param (param_name,param_value,[description]) VALUES
		('INSURANCE_STATES_ALLOWED_USERS_FOR_MEMBER_ENROLLING','webapp','Users to allow member enrollment for Insurance states');
        
     END    
     
     UPDATE dbo.config_param SET param_value = 'webapp' WHERE
     	param_name = 'INSURANCE_STATES_ALLOWED_USERS_FOR_MEMBER_ENROLLING';