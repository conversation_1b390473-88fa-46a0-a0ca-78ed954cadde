if not EXISTS(
        SELECT *
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = 'dbo'
          AND TABLE_NAME = 'config_param'
    )
    BEGIN

        CREATE TABLE dbo.config_param
        (
          id int identity PRIMARY KEY,
          param_name VARCHAR(255),
          param_value VARCHAR(255),
          description VARCHAR(255)
        );
        
        CREATE UNIQUE INDEX IX_config_param_param_name ON dbo.config_param (param_name); 
        
        INSERT INTO dbo.config_param (param_name,param_value,[description]) VALUES
		('EXCLUDE_STATES_TO_ADD_MEMBER','WA','States to exclude from member add');
        
     END
     
     