USE [masa]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE VIEW [dbo].[v_agent_saleschannel]
AS
	select distinct 
		g.sales_channel_id as assigned_sales_channel_id,
		g.sales_channel_name as assigned_sales_channel_name,
		a.*
	from tlnk_Promotion_Group pg
	join masasite.dbo.AgentToProductLink apl on apl.PromotionId = pg.idPromotion
	join tdat_Agent a on apl.AgentNumber = a.agent_num
	join masa.dbo.v_group g on pg.idGroup = g.group_id  
GO


