# EDI File processing application

The EDI application module provides automated loading of membership enrollment files from business partners such as brokers and customers.  As part of the loading process a series of validation steps are done for each membership enrollment file to match the data into the appropriate group to drive commissions and populate the membership database. 

# Business Value

Loading members based on daily enrollment files is a very manual process, fraught with errors.  This module automates these manual steps allowing for hundreds of files to be processed weekly for our Business to Business (B2B) Division.  

# Technology Stack 

Database Platform (Size):  Microsoft SQL Server 2019 (50 GB) 

Development Platform/Toolsets:  Java 8 with Spring (managed by GitHub) 

Environments:  2 VM environment for UAT and Production (4 cores)  

User Interface (# users):  Web interface through Compass 360 

# Dependencies 

Webservices for membership management 

Address validation component for member address validation 

SFTP (Network Files on MASA-Ser7) to store membership files 
